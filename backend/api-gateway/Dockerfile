FROM maven:3.9.9-amazoncorretto-21 AS build

WORKDIR /app

#COPY pom.xml .
#RUN mvn dependency:go-offline dependency:resolve-plugins -B

COPY . .
RUN --mount=type=cache,target=/root/.m2,rw mvn -B package -DskipTests
RUN #mvn package -DskipTests

FROM amazoncorretto:21.0.6
WORKDIR /app
COPY --from=build /app/target/*.jar /app/app.jar
COPY --from=build /app/logback.xml /app/logback.xml
COPY --from=build /app/application.yml /app/application.yml
#COPY --from=build /app/application-dev.yaml application-dev.yaml
#COPY --from=build /app/application-stag.yaml application-stag.yaml

# Command to run the application
EXPOSE 8081
ENTRYPOINT ["java", "-jar", "app.jar"]