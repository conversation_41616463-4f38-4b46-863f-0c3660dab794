def remote = [:]
remote.name = "root"
remote.host = "************"
remote.allowAnyHosts = true

pipeline {
//     agent {
//         label 'omni_channel'
//     }

    agent any

    environment {
        VIVAS_DOCKER_REGISTRY = 'https://registry.vivas.vn'
        BRANCH_BUILD = "dev"
        URL_GIT = "https://gitlab.vivas.vn/omni-channel/api-gateway.git"
        ROOT_CREDS = credentials('ssh_10.84.86.35')
    }

    stages {
        stage('Clone code') {
             steps {
                 script {
                     def GIT_TAG = sh(script: "git tag --sort=-creatordate | head -1", returnStdout: true).trim()

                     if (!GIT_TAG) {
                         GIT_TAG = "no-tag"
                     }

                     if (GIT_TAG.startsWith('dev')){
                        echo "trigger clone and build dev, tag: ${GIT_TAG}"
                        BRANCH_BUILD = 'dev'
                     } else if (GIT_TAG.startsWith('stag')) {
                        echo "trigger clone and build stag, tag: ${GIT_TAG}"
                        BRANCH_BUILD = 'master'
                     } else {
                        echo "trigger default clone and build dev"
                        BRANCH_BUILD = 'dev'
                     }

                     git branch: "${BRANCH_BUILD}", credentialsId: 'gitlab_huy', url: "${URL_GIT}"
                     def COMMIT_HASH = sh(script: "git rev-parse --short HEAD", returnStdout: true).trim()

                     env.HASH_TAG = "${GIT_TAG}_${COMMIT_HASH}"
                     env.IMAGE_NAME = "registry.vivas.vn/benv/omni/gateway:${env.HASH_TAG}"

                     echo "Git Tag: ${env.HASH_TAG}"
                     echo "Docker Image Name: ${env.IMAGE_NAME}"
                 }
             }
        }
//         stage('SonarQube') {
// //             environment {
// //                 SONAR_HOST_URL = 'https://cq.vivas.vn/'
// //                 SONAR_AUTH_TOKEN = credentials('sonarqube_access_token')
// //             }
// //             steps {
// //                 sh 'mvn sonar:sonar -Dsonar.projectKey=cd_khcn -Dsonar.host.url=$SONAR_HOST_URL -Dsonar.login=$SONAR_AUTH_TOKEN'
// //             }
//             environment {
//                 scannerHome = tool 'SonarScanner'
//             }
//             steps {
//
//                 sh 'chmod +x mvnw'
//                 sh "./mvnw clean install -B -DskipTests -Dmaven.repo.local=/var/jenkins_home/.m2/repository"
//
//                 withSonarQubeEnv('Vivas Sonarqube') {
//                     echo 'scan sonar'
//                     sh '${scannerHome}/bin/sonar-scanner'
//                 }
//             }
//         }
        stage('Build') {
            steps {
                withDockerRegistry(credentialsId: 'cd_khcn', url: VIVAS_DOCKER_REGISTRY) {
                    sh "echo ENV RUN_ENV=${env.HASH_TAG} >> ./Dockerfile"
                    sh "DOCKER_BUILDKIT=1 docker build --build-arg MAVEN_OPTS='-Dmaven.repo.local=/root/.m2/repository' -t ${env.IMAGE_NAME} ."
                    sh "docker push ${env.IMAGE_NAME}"
                }
            }
        }
        stage('Deploy') {
            steps {
                echo "deploy project branch : ${BRANCH_BUILD}, env build: ${BRANCH_BUILD}"
//                 script {
//                     remote.user=env.ROOT_CREDS_USR
//                     remote.password=env.ROOT_CREDS_PSW
//                 }
//
//                 sshCommand(remote: remote, command: """
//                     export T_CI_TAG=${env.HASH_TAG}
//                     export T_TAG_COMMIT=${env.IMAGE_NAME}
//                     bash ./cd_khcn.sh
//                     exit
//                 """)
//                 sh """
//                 nslookup gitlab-dvkt.vivas.vn
//                 export T_CI_TAG=${env.HASH_TAG}
//                 export T_TAG_COMMIT=CURRENT_IMAGE=${env.IMAGE_NAME}
//                 echo run file call deploy
//                 chmod +x .run.sh
//                 bash ./.run.sh
//                 """
            }
        }
    }
}