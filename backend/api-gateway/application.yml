spring:
  application:
    name: api-gateway
  cloud:
    gateway:
      httpserver:
        max-header-size: 32768  # Hoặc cao hơn như 32768
      routes:
        - id: identity-service
          uri: lb://identity-service
          predicates:
            - Path=/identity/**
        - id: business-service
          uri: lb://identity-service
          predicates:
            - Path=/business/**
        - id: cdp-service
          uri: lb://cdp-service
          predicates:
            - Path=/cdp/**
        - id: report-service
          uri: lb://report-service
          predicates:
            - Path=/report/**
        - id: campaign-service
          uri: lb://campaign-service
          predicates:
            - Path=/campaign/**
#  data:
#    redis:
#      sentinel:
#        master: mymaster
#        nodes:
#          - ************:26379
#          - ************:26380
#          - ************:26381
#      timeout: 3000
#      lettuce:
#        pool:
#          max-active: 10
#          max-idle: 10
#          min-idle: 2
#          max-wait: 5s
  redis:
    enabled: false
  cache:
    type: redis
server:
  port: 8081
#  servlet:
#    context-path: /api
eureka:
  client:
    initial-instance-info-replication-interval-seconds: 5
    instance-info-replication-interval-seconds: 5
    registry-fetch-interval-seconds: 5
    serviceUrl:
      defaultZone: http://${EUREKA_HOSTNAME:************}:${EUREKA_PORT:8761}/eureka/
    enabled: true
  instance:
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 5
management:
  endpoints:
    web:
      exposure:
        include: health, info, gateway
  info:
    build:
      enabled: true
    env:
      enabled: true
    git:
      mode: simple
      enabled: true
info:
  health-check: OK

springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
#    path: /swagger-ui/index.html
    enabled: true
    doc-expansion: LIST
    url: /v3/api-docs
    tagsSorter: alpha

service:
  identity:
    base-url: ${BASE_URL_IDENTITY_SERVICE:http://identity-service/identity}
    request-timeout: ${REQUEST_TIMEOUT_IDENTITY_SERVICE:10000}

jwt:
  secret: 0f68466e2e60e0892a0fcbf183926cfc10b4db9a
  expired: 900000