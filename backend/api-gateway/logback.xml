<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--    config log dev-->
    <property name="LOGS" value="log"/>

    <!--        config log prod-->
    <!--    <property name="LOGS" value="/home/<USER>/log/administration_service"/>-->

    <springProperty scope="context" name="hostName" source="HOSTNAME"/>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <springProperty scope="context" name="applicationName" source="spring.application.name"/>

    <appender name="Console"
              class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                ${hostName} ${applicationName} %X{traceId} %X{spanId} %d{yyyy-MM-dd HH:mm:ss.SSS} %-5p %C{1} %M %L %m%n
            </Pattern>
        </layout>
    </appender>

    <appender name="jsonFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGS}/administration_service.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOGS}/archived/administration_service-%d{yyyy-MM-dd}.%i.log.gz
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <fieldName>createdDate</fieldName>
                    <pattern>yyyy-MM-dd HH:mm:ss.SSS</pattern>
                </timestamp>
                <mdc/>
                <pattern>
                    <pattern>
                        {
                        "hostname": "${HOSTNAME:-unknown}",
                        "application-name": "${applicationName}",
                        "level": "%level",
                        "class":"%C{1}",
                        "method": "%M",
                        "line": "%L",
                        "message": "%message"
                        }
                    </pattern>
                </pattern>
                <logstashMarkers/>
                <arguments/>
                <stackTrace/>
            </providers>
        </encoder>
    </appender>

    <!-- LOG everything at INFO level -->
    <root level="info">
        <appender-ref ref="jsonFileAppender"/>
        <appender-ref ref="Console"/>
    </root>

    <logger name="vivas.omni" level="info" additivity="false">
        <appender-ref ref="jsonFileAppender"/>
        <appender-ref ref="Console"/>
    </logger>
    <logger name="org.hibernate" level="error" additivity="false">
        <appender-ref ref="jsonFileAppender"/>
        <appender-ref ref="Console"/>
    </logger>
<!--    <logger name="com.netflix.discovery" level="info" additivity="false">-->
<!--        <appender-ref ref="jsonFileAppender"/>-->
<!--        <appender-ref ref="Console"/>-->
<!--    </logger>-->
<!--    <logger name="org.hibernate.SQL" level="debug">-->
<!--       <appender-ref ref="Console"/>-->
<!--    </logger>-->
</configuration>