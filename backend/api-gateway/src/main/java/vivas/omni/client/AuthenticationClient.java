package vivas.omni.client;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import vivas.omni.response.auth.LoginUserDto;
import vivas.omni.utils.JsonUtils;
import vivas.omni.utils.MappingHelper;

@Slf4j
@Repository
public class AuthenticationClient {

    @Value("${service.identity.base-url}")
    private String BASE_URL_IDENTITY_SERVICE;

    private final WebClient.Builder webClient;

    private final MappingHelper mappingHelper;

    private final ObjectMapper objectMapper;

    public AuthenticationClient(@Qualifier("identity-client") WebClient.Builder webClient, MappingHelper mappingHelper, ObjectMapper objectMapper) {
        this.webClient = webClient;
        this.mappingHelper = mappingHelper;
        this.objectMapper = objectMapper;
    }

    public Mono<LoginUserDto> validateToken(String token) {
        String api = BASE_URL_IDENTITY_SERVICE + "/api/v1/auth/validate_token";
        return webClient.build().post()
                .uri(api)
                .header("Authorization", token)
                .retrieve()
                .bodyToMono(String.class)
                .flatMap(res -> {
                    try {
                        return Mono.just(JsonUtils.getDataResponse(res, LoginUserDto.class));
                    } catch (JsonProcessingException e) {
                        return Mono.error(new RuntimeException(e));
                    }
                })
                .onErrorResume(throwable -> {
                    log.error("error call validate token: {}", throwable.getMessage(), throwable);
                    LoginUserDto loginUserDto = new LoginUserDto();
                    loginUserDto.setUsername(null);
                    return Mono.just(loginUserDto);
                }).doOnSuccess(s -> log.info("success call validateToken {}", s));
    }

}
