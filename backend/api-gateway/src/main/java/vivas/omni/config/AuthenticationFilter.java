package vivas.omni.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.netty.channel.ConnectTimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.constant.RedisKey;
import vivas.omni.response.auth.LoginUserDto;
import vivas.omni.service.AuthenticationService;
import vivas.omni.utils.ResponseUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Configuration
@Slf4j
public class AuthenticationFilter implements GlobalFilter, Ordered {

    private final ObjectMapper objectMapper;

    private final AuthenticationService authenticationService;

    private final RedisTemplate<String, Object> redisTemplate;

    @Value(value = "${spring.redis.enabled}")
    private boolean redisEnabled;

    public String[] whiteListApi = new String[]{
            "/identity/api/v1/auth/login",
            "/identity/api/v1/auth/admin/register",
            "/identity/api/v1/auth/logout",
            "/swagger",
            "/api/v1/health-check",
            "/v3/api-docs"
    };

    @Autowired
    public AuthenticationFilter(ObjectMapper objectMapper, AuthenticationService authenticationService, RedisTemplate<String, Object> redisTemplate) {
        this.objectMapper = objectMapper;
        this.authenticationService = authenticationService;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        log.info("handle global filter: {}", LocalDateTime.now());

        // ignore validate token api
        String path = exchange.getRequest().getURI().getPath();
        log.info("method: {}, path: {}", exchange.getRequest().getMethod(), path);

        boolean noFilter = Arrays.stream(whiteListApi).anyMatch(path::contains);
        if (noFilter) {
            log.info("whitelist api found: {}", path);
            return buildResponseExchange(exchange, chain, path);
        }
        // token from header
        List<String> tokens = exchange.getRequest().getHeaders().get(HttpHeaders.AUTHORIZATION);

        if (CollectionUtils.isEmpty(tokens)) {
            return ResponseUtils.errorMono(exchange.getResponse(), MessageResponseDict.UNAUTHORIZED);
        }
        String token = tokens.getFirst();
        try {
            if (redisEnabled) {
                Object loginInfo = redisTemplate.opsForValue().get(RedisKey.tokenKey + token);
                if (loginInfo != null) {
                    LoginUserDto loginUserDto = (LoginUserDto) loginInfo;
                    if (redisTemplate.opsForValue().get(RedisKey.userKey + loginUserDto.getId()) == null) {
                        redisTemplate.opsForValue().set(RedisKey.userKey + loginUserDto.getId(), loginUserDto, Duration.ofMinutes(30));
                    }
//                    exchange.getRequest().getHeaders().put("user_id", List.of(String.valueOf(loginUserDto.getId())));
//                    exchange.getRequest().getHeaders().put("username", List.of(String.valueOf(loginUserDto.getUsername())));

                    ServerWebExchange header = appendRequestHeader(exchange, loginUserDto);
                    return buildResponseExchange(header, chain, path);
                } else {
                    return authenticationService.validateToken(token)
                            .flatMap(loginUserDto -> {
                                if (StringUtils.isEmpty(loginUserDto.getUsername())) {
                                    log.info("token not valid: {}", token);
                                    return ResponseUtils.errorMono(exchange.getResponse(), MessageResponseDict.UNAUTHORIZED);
                                } else {
                                    redisTemplate.opsForValue().set(RedisKey.tokenKey + token, loginUserDto, Duration.ofMinutes(30));
                                    redisTemplate.opsForValue().set(RedisKey.userKey + loginUserDto.getId(), loginUserDto, Duration.ofMinutes(30));

                                    ServerWebExchange header = appendRequestHeader(exchange, loginUserDto);
                                    return buildResponseExchange(header, chain, path);
                                }
                            })
                            .onErrorResume(error -> {
                                log.error("error filter: {}", error.getMessage(), error);
                                return ResponseUtils.errorMono(exchange.getResponse(), MessageResponseDict.UNAUTHORIZED);
                            });
                }
            } else {
                return authenticationService.validateToken(token)
                        .flatMap(loginUserDto -> {
                            if (StringUtils.isEmpty(loginUserDto.getUsername())) {
                                log.info("token not valid: {}", token);
                                return ResponseUtils.errorMono(exchange.getResponse(), MessageResponseDict.UNAUTHORIZED);
                            } else {
                                ServerWebExchange header = appendRequestHeader(exchange, loginUserDto);
                                return buildResponseExchange(header, chain, path);
                            }
                        })
                        .onErrorResume(error -> {
                            log.error("error filter: {}", error.getMessage(), error);
                            return ResponseUtils.errorMono(exchange.getResponse(), MessageResponseDict.UNAUTHORIZED);
                        });
            }

        } catch (Exception e) {
            log.error("error token {}", e.getMessage(), e);
            return ResponseUtils.errorMono(exchange.getResponse(), MessageResponseDict.UNAUTHORIZED);
        }
    }

    private static ServerWebExchange appendRequestHeader(ServerWebExchange exchange, LoginUserDto loginUserDto) {
        ServerHttpRequest modifiedRequest = exchange.getRequest().mutate()
                .header("user_id", String.valueOf(loginUserDto.getId()))
                .header("username", loginUserDto.getUsername())
                .build();

        return exchange.mutate()
                .request(modifiedRequest)
                .build();
    }

    private Mono<Void> buildResponseExchange(ServerWebExchange exchange, GatewayFilterChain chain, String path) {
        return chain.filter(exchange)
                .onErrorResume(error -> {
                    log.info("whitelist api found error: {}", error.getMessage(), error);
                    if (error instanceof ConnectTimeoutException) {
                        return ResponseUtils.errorMono(exchange.getResponse(), MessageResponseDict.CONNECT_TIMEOUT, path);
                    } else {
                        return ResponseUtils.errorMono(exchange.getResponse(), MessageResponseDict.UNAUTHORIZED);
                    }
                });
    }


    @Override
    public int getOrder() {
        return -1;
    }
}
