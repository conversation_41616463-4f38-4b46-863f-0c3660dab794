package vivas.omni.config;

import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import vivas.omni.utils.MappingHelper;

@Configuration
public class ModelMapperFactoryConfig {

    public interface ModelMapperFactory {
        ModelMapper getMapper();
    }

    @Bean
    public ModelMapperFactory modelMapperFactory() {
        return () -> {
            ModelMapper modelMapper = new ModelMapper();
            modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
//            Converter<AppUser, UserDto> userUserDtoConverter = context -> {
//                AppUser appUser = context.getSource();
//                UserDto userDto = context.getDestination();
//                return userDto;
//            };
//            modelMapper.addConverter(userUserDtoConverter);
//            TypeMap<CompanyOrg, CompanyPageResponse> propertyMapper = modelMapper.createTypeMap(CompanyOrg.class, CompanyPageResponse.class);
//            propertyMapper.addMappings(
//                    mapper -> mapper.map(
//                            CompanyOrg::getLevel, (destination, value) -> destination.setName((String) value)
//                    )
//            );
            return modelMapper;
        };
    }

    @Bean
    public MappingHelper getModelMapper() {
        return new MappingHelper(modelMapperFactory());
    }
}
