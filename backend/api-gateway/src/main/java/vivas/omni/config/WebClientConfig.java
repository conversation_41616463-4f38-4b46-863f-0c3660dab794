package vivas.omni.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.netty.http.client.HttpClient;
import vivas.omni.client.AuthenticationClient;


import java.time.Duration;

@Configuration
public class WebClientConfig {

    @Value("${service.identity.request-timeout}")
    private Long REQUEST_TIMEOUT_IDENTITY_SERVICE;


    @Bean(value = "identity-client")
    @LoadBalanced
    public WebClient.Builder getWebClient() {

        HttpClient httpClient = HttpClient.create();
        ReactorClientHttpConnector connector = new ReactorClientHttpConnector(httpClient);

        return WebClient.builder()
                .clientConnector(connector)
                .filter((request, next) ->
                        next.exchange(request)
                                .timeout(Duration.ofMillis(REQUEST_TIMEOUT_IDENTITY_SERVICE))
                );
    }

//    @Bean
//    public AuthenticationClient getAuthenticationClient(WebClient webClient) {
//        return HttpServiceProxyFactory
//                .builder(WebClientAdapter.forClient(webClient))
//                .build()
//                .createClient(AuthenticationClient.class);
//    }

}
