package vivas.omni.constant;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public enum MessageResponseDict {
    SUCCESS(HttpStatus.OK, 0, "success"),
    ERROR(HttpStatus.INTERNAL_SERVER_ERROR, 500, "Lỗi x<PERSON>y ra trong quá trình xử lý"),
    UNAUTHORIZED(HttpStatus.UNAUTHORIZED, 401, "unauthorized"),
    NOT_FOUND(HttpStatus.NOT_FOUND, 404, "Resource not found"),
    CONNECT_TIMEOUT(HttpStatus.REQUEST_TIMEOUT, 408, "connection timeout %s"),
    ;
    private final HttpStatus status;
    private final Integer code;
    private final String message;

    MessageResponseDict(HttpStatus status, Integer code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }
}
