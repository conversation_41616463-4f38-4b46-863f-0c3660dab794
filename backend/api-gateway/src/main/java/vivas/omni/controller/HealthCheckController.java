package vivas.omni.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.utils.ResponseUtils;

@Tag(name = "health check")
@RestController
@RequestMapping("/api/v1/health-check")
public class HealthCheckController {

    private final RedisTemplate<String, Object> redisTemplate;

    @Autowired
    public HealthCheckController(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @GetMapping
    public String healthCheckApi() {
        return "OK";
    }

    @GetMapping("/ping")
    public ResponseEntity<String> ping() {
        // Set key "ping" -> "pong"
        redisTemplate.opsForValue().set("ping", "pong");

        // Get value from Redis
        String response = (String) redisTemplate.opsForValue().get("ping");

        return ResponseEntity.ok("Redis replied: " + response);
    }
}
