package vivas.omni.controller.advice;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.web.reactive.error.AbstractErrorWebExceptionHandler;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.exception.AppException;
import vivas.omni.exception.BusinessException;
import vivas.omni.response.common.ResponseCommon;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ControllerAdvice
@Slf4j
public class ExceptionController {

    @ExceptionHandler({ ConstraintViolationException.class })
    public ResponseEntity<Object> handleConstraintViolation(ConstraintViolationException ex, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
            errors.add(violation.getRootBeanClass().getName() + " " + violation.getPropertyPath() + ": " + violation.getMessage());
        }
        ResponseCommon<MessageResponseDict> response = new ResponseCommon<>();
        response.setCode(HttpStatus.BAD_REQUEST.value());
        response.setMessage(errors.get(0));
        return ResponseEntity.badRequest().body(response);
    }

    @ExceptionHandler({ MethodArgumentTypeMismatchException.class })
    public ResponseEntity<Object> handleMethodArgumentTypeMismatch(MethodArgumentTypeMismatchException ex, WebRequest request) {
        String error = ex.getName() + " should be of type " + ex.getRequiredType().getName();

        ResponseCommon<MessageResponseDict> response = new ResponseCommon<>();
        response.setCode(HttpStatus.BAD_REQUEST.value());
        response.setMessage(error);
        return ResponseEntity.badRequest().body(response);
    }

    @ExceptionHandler({ BusinessException.class })
    public ResponseEntity<Object> handleBusinessException(BusinessException ex, WebRequest request) {
        log.error("Exception handleBusinessException: {}, trace: {}", ex.getMessage(), ex.getStackTrace(), ex);
        ResponseCommon<MessageResponseDict> response = new ResponseCommon<>();
        response.setCode(ex.getResponseDict().getCode());
        response.setMessage(ex.getMessage());
        return ResponseEntity.status(ex.getResponseDict().getStatus()).body(response);
    }

    @ExceptionHandler({ RuntimeException.class })
    public ResponseEntity<Object> handleRuntimeException(RuntimeException ex, WebRequest request) {
        log.error("Exception handleRuntimeException: {}", ex.getMessage(), ex);
        ResponseCommon<MessageResponseDict> response = new ResponseCommon<>();
        response.setCode(MessageResponseDict.ERROR.getCode());
        response.setMessage(MessageResponseDict.ERROR.getMessage());
        return ResponseEntity.status(MessageResponseDict.ERROR.getStatus()).body(response);
    }

    @ExceptionHandler({ AppException.class })
    public ResponseEntity<Object> handleAppException(AppException ex, WebRequest request) {
        log.error("Exception handleAppException: {}", ex.getMessage(), ex);
        ResponseCommon<MessageResponseDict> response = new ResponseCommon<>();
        response.setCode(MessageResponseDict.ERROR.getCode());
        response.setMessage(MessageResponseDict.ERROR.getMessage());
        return ResponseEntity.status(MessageResponseDict.ERROR.getStatus()).body(response);
    }

    @ExceptionHandler({ Exception.class })
    public ResponseEntity<Object> handleException(Exception ex, WebRequest request) {
        log.error("Exception handleException: {}", ex.getMessage(), ex);
        ResponseCommon<MessageResponseDict> response = new ResponseCommon<>();
        response.setCode(MessageResponseDict.ERROR.getCode());
        response.setMessage(MessageResponseDict.ERROR.getMessage());
        return ResponseEntity.status(MessageResponseDict.ERROR.getStatus()).body(response);
    }
}
