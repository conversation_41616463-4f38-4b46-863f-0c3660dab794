package vivas.omni.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import vivas.omni.constant.MessageResponseDict;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class BusinessException extends RuntimeException {

    public BusinessException(MessageResponseDict responseDict) {
        super();
        this.responseDict = responseDict;
    }

    private MessageResponseDict responseDict;
}
