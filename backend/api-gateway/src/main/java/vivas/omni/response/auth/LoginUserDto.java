package vivas.omni.response.auth;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class LoginUserDto {

    private long id;

    private String username;

    @JsonIgnore
    private String avatar = "";

    private long expired;

    private List<String> permissions;

}
