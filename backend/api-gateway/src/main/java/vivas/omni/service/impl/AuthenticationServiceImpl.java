package vivas.omni.service.impl;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import vivas.omni.client.AuthenticationClient;
import vivas.omni.response.auth.LoginUserDto;
import vivas.omni.service.AuthenticationService;

@Service
public class AuthenticationServiceImpl implements AuthenticationService {

    private final AuthenticationClient authenticationClient;

    public AuthenticationServiceImpl(AuthenticationClient authenticationClient) {
        this.authenticationClient = authenticationClient;
    }

    @Override
    public Mono<LoginUserDto> validateToken(String token) {

        return authenticationClient.validateToken(token);
    }
}
