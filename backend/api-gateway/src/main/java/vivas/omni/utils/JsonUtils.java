package vivas.omni.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import vivas.omni.response.common.ResponseCommon;

public class JsonUtils {

    private static final ObjectMapper mapper = new ObjectMapper();

    public static <T> ResponseCommon<T> convertResponse(String json, Class<T> clazz) throws JsonProcessingException {
        JsonNode jsonObject = mapper.readTree(json);
        String data = "{}";
        String code = "";
        String message = "";
        if (jsonObject.has("code") && !jsonObject.get("code").isNull()) {
            code = jsonObject.get("code").toString();
        }
        if (jsonObject.has("message") && !jsonObject.get("message").isNull()) {
            message = jsonObject.get("message").toString();
        }
        if (jsonObject.has("data") && !jsonObject.get("data").isNull()) {
            data = jsonObject.get("data").toString();
        }
        T objectConvert = mapper.readValue(data, clazz);
        ResponseCommon<T> responseCommon = new ResponseCommon<>();
        responseCommon.setMessage(message);
        responseCommon.setCode(code != null ? Integer.parseInt(code) : null);
        responseCommon.setData(objectConvert);
        return responseCommon;
    }

    public static <T> T getDataResponse(String json, Class<T> clazz) throws JsonProcessingException {
        JsonNode jsonObject = mapper.readTree(json);
        String data = "{}";

        if (jsonObject.has("data") && !jsonObject.get("data").isNull()) {
            data = jsonObject.get("data").toString();
        }
        return mapper.readValue(data, clazz);
    }
}
