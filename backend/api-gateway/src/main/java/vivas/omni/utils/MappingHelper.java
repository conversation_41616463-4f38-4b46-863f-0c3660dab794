package vivas.omni.utils;

import org.modelmapper.ModelMapper;
import vivas.omni.config.ModelMapperFactoryConfig;


public class MappingHelper {
    private final ModelMapperFactoryConfig.ModelMapperFactory factory;
    private ModelMapper modelMapper;

    public MappingHelper(ModelMapperFactoryConfig.ModelMapperFactory factory) {
        this.factory = factory;
        this.modelMapper = factory.getMapper();
    }

    public <D, S> D map(S source, Class<D> destinationType) {
        return modelMapper.map(source, destinationType);
    }

    public <D, S> void map(S source, D destination) {
        modelMapper.map(source, destination);
    }
}
