package vivas.omni.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpResponse;
import reactor.core.publisher.Mono;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.response.common.ResponseCommon;

@Slf4j
public class ResponseUtils {

    private static final ObjectMapper mapper = new ObjectMapper();

    public static <T> ResponseEntity<ResponseCommon<T>> ok(T t) {
        ResponseCommon<T> responseCommon = new ResponseCommon<>();
        responseCommon.setCode(MessageResponseDict.SUCCESS.getCode());
        responseCommon.setMessage(MessageResponseDict.SUCCESS.getMessage());
        responseCommon.setData(t);
        return ResponseEntity.ok(responseCommon);
    }

    public static <T> ResponseEntity<ResponseCommon<T>> ok(MessageResponseDict response) {
        ResponseCommon<T> responseCommon = new ResponseCommon<>();
        responseCommon.setCode(response.getCode());
        responseCommon.setMessage(response.getMessage());
        return ResponseEntity.status(response.getStatus()).body(responseCommon);
    }

    public static <T> ResponseEntity<ResponseCommon<T>> unauthenticated() {
        ResponseCommon<T> responseCommon = new ResponseCommon<>();
        responseCommon.setCode(MessageResponseDict.UNAUTHORIZED.getCode());
        responseCommon.setMessage(MessageResponseDict.UNAUTHORIZED.getMessage());
        return ResponseEntity.status(MessageResponseDict.UNAUTHORIZED.getStatus()).body(responseCommon);
    }

    public static Mono<Void> errorMono(ServerHttpResponse response, MessageResponseDict messageResponseDict) {
        response.setStatusCode(MessageResponseDict.UNAUTHORIZED.getStatus());
        ResponseCommon<MessageResponseDict> responseCommon = new ResponseCommon<>();
        responseCommon.setCode(messageResponseDict.getCode());
        responseCommon.setMessage(messageResponseDict.getMessage());
        String responseError = "error";
        try {
            responseError = mapper.writeValueAsString(responseCommon);
        } catch (Exception e) {
            log.error("error convert response: {}", e.getMessage(), e);
        }
        return response.writeWith(
                Mono.just(response.bufferFactory().wrap(responseError.getBytes()))
        );
    }

    public static Mono<Void> errorMono(ServerHttpResponse response, MessageResponseDict messageResponseDict, String... params) {
        response.setStatusCode(MessageResponseDict.UNAUTHORIZED.getStatus());
        ResponseCommon<MessageResponseDict> responseCommon = new ResponseCommon<>();
        responseCommon.setCode(messageResponseDict.getCode());
        responseCommon.setMessage(buildMessage(messageResponseDict, params));
        String responseError = "error";
        try {
            responseError = mapper.writeValueAsString(responseCommon);
        } catch (Exception e) {
            log.error("error convert response: {}", e.getMessage(), e);
        }
        return response.writeWith(
                Mono.just(response.bufferFactory().wrap(responseError.getBytes()))
        );
    }


    public static String buildMessage(MessageResponseDict msg, String... params) {
        return String.format(msg.getMessage(), params);
    }
}
