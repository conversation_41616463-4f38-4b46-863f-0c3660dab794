# API Rules
Tuân thủ quy tắc đặt tên snake_case cho các endpoint API.

## Response
```json 
{
  "code": 0,
  "message": "OK",
  "data": {
    "paging": {
      "current": 1,
      "size": 20,
      "total_pages": 0,
      "total_records": 0
    },
    "items": []
  }
}
```
Một api sẽ bắt buộc phải trả về 3 trường sau:
code:
- 0: Thành công
- 4xxxxx: <PERSON><PERSON><PERSON> lỗi khác tự định nghĩa
message: Thông báo chi tiết
data: Dữ liệu trả về

## Paging
paging:
- current: Trang hiện tại
- size: Số lượng bản ghi trên 1 trang
- totalPages: Tổng số trang
- totalRecords: Tổng số bản ghi

items: Mảng dữ liệu trả về

## Request
 - page_index: Trang hiện tại
 - page_size: <PERSON><PERSON> lượng bản ghi trên 1 trang
 - không có sort trong api list
 - 
