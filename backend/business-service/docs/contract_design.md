# THIẾT KẾ HỆ THỐNG QUẢN LÝ HỢP ĐỒNG CMS

## 1. MÔ HÌNH ERD (Entity Relationship Diagram)

### 1.1. <PERSON><PERSON> hình ERD tổng quan

```mermaid
erDiagram
    ACCOUNT_INFO {
        bigint id PK
        varchar password
        integer role
        varchar email
        bigint group_permission
        integer status
        varchar full_name
        integer gender
        varchar avatar
        varchar phone
        varchar address
        integer account_type
        bigint id_agent FK
        bigint id_business FK
        integer otp_active
        integer is_first_login
        datetime created_at
        datetime updated_at
    }
    
    BUSINESS {
        bigint id PK
        varchar business_code
        varchar business_name
        varchar address
        varchar tax_code
        varchar business_phone
        varchar business_email
        varchar contact_name
        varchar contact_phone
        varchar contact_email
        integer customer_source
        bigint agent_id FK
        integer status
        datetime created_at
        datetime updated_at
    }
    
    AGENT {
        bigint id PK
        varchar agent_code
        varchar agent_name
        varchar address
        varchar tax_code
        varchar agent_phone
        varchar agent_email
        varchar contact_name
        varchar contact_phone
        varchar contact_email
        integer agent_type
        integer status
        datetime created_at
        datetime updated_at
    }
    
    CONTRACT {
        bigint id PK
        varchar contract_number
        varchar contract_name
        varchar contract_type
        bigint admin_id FK
        bigint business_id FK
        bigint agent_id FK
        varchar services
        varchar contract_duration_type
        date effective_date
        date expiry_date
        varchar notes
        varchar status
        date liquidation_date
        bigint created_by FK
        bigint updated_by FK
        datetime created_at
        datetime updated_at
    }
    
    CONTRACT_ATTACHMENT {
        bigint id PK
        bigint contract_id FK
        varchar attachment_number
        varchar attachment_name
        date effective_date
        varchar notes
        bigint created_by FK
        datetime created_at
        datetime updated_at
    }
    
    CONTRACT_FILE {
        bigint id PK
        bigint contract_id FK
        bigint attachment_id FK
        varchar file_name
        varchar file_path
        varchar file_type
        bigint file_size
        integer file_category
        datetime created_at
    }
    
    LIQUIDATION_FILE {
        bigint id PK
        bigint contract_id FK
        varchar file_name
        varchar file_path
        varchar file_type
        bigint file_size
        datetime created_at
    }
    
    %% Relationships
    ACCOUNT_INFO ||--o{ BUSINESS : "id_business"
    ACCOUNT_INFO ||--o{ AGENT : "id_agent"
    BUSINESS ||--o{ AGENT : "agent_id"
    
    ACCOUNT_INFO ||--o{ CONTRACT : "created_by"
    ACCOUNT_INFO ||--o{ CONTRACT : "updated_by"
    ACCOUNT_INFO ||--o{ CONTRACT : "admin_id"
    BUSINESS ||--o{ CONTRACT : "business_id"
    AGENT ||--o{ CONTRACT : "agent_id"
    
    CONTRACT ||--o{ CONTRACT_ATTACHMENT : "contract_id"
    CONTRACT ||--o{ CONTRACT_FILE : "contract_id"
    CONTRACT ||--o{ LIQUIDATION_FILE : "contract_id"
    CONTRACT_ATTACHMENT ||--o{ CONTRACT_FILE : "attachment_id"
    
    ACCOUNT_INFO ||--o{ CONTRACT_ATTACHMENT : "created_by"
```

### 1.2. Giải thích mối quan hệ

**Mối quan hệ chính:**
- `ACCOUNT_INFO` là bảng trung tâm chứa thông tin đăng nhập cho tất cả user (Admin, Agent, Business)
- `BUSINESS` và `AGENT` chứa thông tin chi tiết của từng loại đối tượng
- `CONTRACT` thể hiện 3 loại hợp đồng:
  - **Admin ký với Doanh nghiệp**: `admin_id` + `business_id` có giá trị, `agent_id` = null
  - **Admin ký với Đại lý**: `admin_id` + `agent_id` có giá trị, `business_id` = null
  - **Đại lý ký với Doanh nghiệp**: `agent_id` + `business_id` có giá trị, `admin_id` = null
- `CONTRACT_ATTACHMENT` là phụ lục của hợp đồng
- `CONTRACT_FILE` lưu file đính kèm cho cả hợp đồng và phụ lục
- `LIQUIDATION_FILE` lưu file biên bản thanh lý riêng biệt

### 1.3. Các loại hợp đồng và trạng thái

**Loại hợp đồng (contract_type):**
- `ADMIN_BUSINESS`: Admin ký hợp đồng với Doanh nghiệp
- `ADMIN_AGENT`: Admin ký hợp đồng với Đại lý
- `AGENT_BUSINESS`: Đại lý ký hợp đồng với Doanh nghiệp

**Trạng thái hợp đồng (status):**
- `ACTIVE`: Hợp đồng đang hoạt động
- `EXPIRED`: Hợp đồng hết hiệu lực
- `LIQUIDATED`: Hợp đồng đã thanh lý

**Loại thời hạn (contract_duration_type):**
- `FIXED_TERM`: Có thời hạn
- `INDEFINITE`: Vô thời hạn

**Dịch vụ (services):**
- `SMS`: Dịch vụ tin nhắn SMS
- `ZNS`: Dịch vụ Zalo Notification Service
- `ALL_SERVICES`: Tất cả dịch vụ

## 2. THIẾT KẾ DATABASE ORACLE

### 2.1. Các bảng đã tồn tại (KHÔNG thay đổi)

**Bảng ACCOUNT_INFO**: Đã tồn tại - Lưu thông tin đăng nhập cho tất cả user
**Bảng BUSINESS**: Đã tồn tại - Lưu thông tin chi tiết doanh nghiệp
**Bảng AGENT**: Đã tồn tại - Lưu thông tin chi tiết đại lý

### 2.2. Bảng CONTRACT (Hợp đồng chính) - BẢNG MỚI

```sql
-- Bảng CONTRACT: Lưu thông tin hợp đồng chính
CREATE TABLE CONTRACT (
    ID NUMBER(19) NOT NULL,
    CONTRACT_NUMBER VARCHAR2(25) NOT NULL,           -- Số hợp đồng (bắt buộc, duy nhất)
    CONTRACT_NAME VARCHAR2(50),                      -- Tên hợp đồng
    CONTRACT_TYPE VARCHAR2(20) NOT NULL,             -- Loại hợp đồng: ADMIN_BUSINESS, ADMIN_AGENT, AGENT_BUSINESS
    ADMIN_ID NUMBER(19),                             -- ID Admin (nếu admin tham gia ký hợp đồng)
    BUSINESS_ID NUMBER(19),                          -- ID Doanh nghiệp (nếu doanh nghiệp tham gia)
    AGENT_ID NUMBER(19),                             -- ID Đại lý (nếu đại lý tham gia)
    SERVICES VARCHAR2(500),                          -- Dịch vụ: JSON array ["SMS", "ZNS"] hoặc ["ALL_SERVICES"]
    CONTRACT_DURATION_TYPE VARCHAR2(20) DEFAULT 'FIXED_TERM', -- Loại thời hạn: FIXED_TERM, INDEFINITE
    EFFECTIVE_DATE DATE NOT NULL,                    -- Ngày hiệu lực (bắt buộc)
    EXPIRY_DATE DATE,                               -- Ngày hết hạn (null nếu vô thời hạn)
    NOTES VARCHAR2(250),                            -- Ghi chú (tối đa 250 ký tự)
    STATUS VARCHAR2(20) DEFAULT 'ACTIVE',           -- Trạng thái: ACTIVE, EXPIRED, LIQUIDATED
    LIQUIDATION_DATE DATE,                          -- Ngày thanh lý (chỉ có khi status=LIQUIDATED)
    CREATED_BY NUMBER(19) NOT NULL,                 -- ID admin tạo hợp đồng
    UPDATED_BY NUMBER(19),                          -- ID admin cập nhật cuối
    CREATED_AT DATE DEFAULT SYSDATE,               -- Thời gian tạo
    UPDATED_AT DATE DEFAULT SYSDATE,               -- Thời gian cập nhật cuối

    -- Constraints
    CONSTRAINT PK_CONTRACT PRIMARY KEY (ID),
    CONSTRAINT UK_CONTRACT_NUMBER UNIQUE (CONTRACT_NUMBER),
    CONSTRAINT FK_CONTRACT_CREATED_BY FOREIGN KEY (CREATED_BY) REFERENCES ACCOUNT_INFO(ID),
    CONSTRAINT FK_CONTRACT_UPDATED_BY FOREIGN KEY (UPDATED_BY) REFERENCES ACCOUNT_INFO(ID),
    CONSTRAINT FK_CONTRACT_ADMIN FOREIGN KEY (ADMIN_ID) REFERENCES ACCOUNT_INFO(ID),
    CONSTRAINT FK_CONTRACT_BUSINESS FOREIGN KEY (BUSINESS_ID) REFERENCES BUSINESS(ID),
    CONSTRAINT FK_CONTRACT_AGENT FOREIGN KEY (AGENT_ID) REFERENCES AGENT(ID),
    CONSTRAINT CK_CONTRACT_TYPE CHECK (CONTRACT_TYPE IN ('ADMIN_BUSINESS', 'ADMIN_AGENT', 'AGENT_BUSINESS')),
    CONSTRAINT CK_DURATION_TYPE CHECK (CONTRACT_DURATION_TYPE IN ('FIXED_TERM', 'INDEFINITE')),
    CONSTRAINT CK_CONTRACT_STATUS CHECK (STATUS IN ('ACTIVE', 'EXPIRED', 'LIQUIDATED')),
    CONSTRAINT CK_EXPIRY_DATE CHECK (EXPIRY_DATE IS NULL OR EXPIRY_DATE > EFFECTIVE_DATE),
    -- Business logic constraints cho từng loại hợp đồng
    CONSTRAINT CK_CONTRACT_PARTIES CHECK (
        (CONTRACT_TYPE = 'ADMIN_BUSINESS' AND ADMIN_ID IS NOT NULL AND BUSINESS_ID IS NOT NULL AND AGENT_ID IS NULL) OR
        (CONTRACT_TYPE = 'ADMIN_AGENT' AND ADMIN_ID IS NOT NULL AND AGENT_ID IS NOT NULL AND BUSINESS_ID IS NULL) OR
        (CONTRACT_TYPE = 'AGENT_BUSINESS' AND AGENT_ID IS NOT NULL AND BUSINESS_ID IS NOT NULL AND ADMIN_ID IS NULL)
    )
);

-- Sequence và Trigger cho auto-increment
CREATE SEQUENCE CONTRACT_ID_SEQ START WITH 1 INCREMENT BY 1;

CREATE OR REPLACE TRIGGER TRG_CONTRACT_ID
    BEFORE INSERT ON CONTRACT
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := CONTRACT_ID_SEQ.NEXTVAL;
    END IF;
END;

CREATE OR REPLACE TRIGGER TRG_CONTRACT_UPDATE
    BEFORE UPDATE ON CONTRACT
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSDATE;
END;
```

### 2.3. Bảng CONTRACT_ATTACHMENT (Phụ lục hợp đồng) - BẢNG MỚI

```sql
-- Bảng CONTRACT_ATTACHMENT: Lưu thông tin phụ lục hợp đồng
CREATE TABLE CONTRACT_ATTACHMENT (
    ID NUMBER(19) NOT NULL,
    CONTRACT_ID NUMBER(19) NOT NULL,                -- ID hợp đồng chính
    ATTACHMENT_NUMBER VARCHAR2(25) NOT NULL,        -- Số phụ lục (bắt buộc)
    ATTACHMENT_NAME VARCHAR2(50),                   -- Tên phụ lục
    EFFECTIVE_DATE DATE NOT NULL,                   -- Ngày hiệu lực phụ lục (bắt buộc)
    NOTES VARCHAR2(250),                           -- Ghi chú phụ lục
    CREATED_BY NUMBER(19) NOT NULL,                -- ID admin tạo phụ lục
    CREATED_AT DATE DEFAULT SYSDATE,              -- Thời gian tạo
    UPDATED_AT DATE DEFAULT SYSDATE,              -- Thời gian cập nhật
    
    -- Constraints
    CONSTRAINT PK_CONTRACT_ATTACHMENT PRIMARY KEY (ID),
    CONSTRAINT FK_ATTACHMENT_CONTRACT FOREIGN KEY (CONTRACT_ID) REFERENCES CONTRACT(ID) ON DELETE CASCADE,
    CONSTRAINT FK_ATTACHMENT_CREATED_BY FOREIGN KEY (CREATED_BY) REFERENCES ACCOUNT_INFO(ID),
    CONSTRAINT UK_ATTACHMENT_NUMBER UNIQUE (CONTRACT_ID, ATTACHMENT_NUMBER)
);

-- Sequence và Trigger
CREATE SEQUENCE CONTRACT_ATTACHMENT_ID_SEQ START WITH 1 INCREMENT BY 1;

CREATE OR REPLACE TRIGGER TRG_CONTRACT_ATTACHMENT_ID
    BEFORE INSERT ON CONTRACT_ATTACHMENT
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := CONTRACT_ATTACHMENT_ID_SEQ.NEXTVAL;
    END IF;
END;

CREATE OR REPLACE TRIGGER TRG_CONTRACT_ATTACHMENT_UPDATE
    BEFORE UPDATE ON CONTRACT_ATTACHMENT
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSDATE;
END;
```

### 2.4. Bảng CONTRACT_FILE (File đính kèm) - BẢNG MỚI

```sql
-- Bảng CONTRACT_FILE: Lưu thông tin file đính kèm hợp đồng và phụ lục
CREATE TABLE CONTRACT_FILE (
    ID NUMBER(19) NOT NULL,
    CONTRACT_ID NUMBER(19) NOT NULL,                -- ID hợp đồng
    ATTACHMENT_ID NUMBER(19),                       -- ID phụ lục (null nếu là file hợp đồng chính)
    FILE_NAME VARCHAR2(255) NOT NULL,               -- Tên file gốc
    FILE_PATH VARCHAR2(500) NOT NULL,               -- Đường dẫn lưu file trên server
    FILE_TYPE VARCHAR2(10) NOT NULL,                -- Loại file: PDF, DOC, DOCX, PNG, JPG, JPEG
    FILE_SIZE NUMBER(19) NOT NULL,                  -- Kích thước file (bytes)
    FILE_CATEGORY INTEGER DEFAULT 1,                -- Loại file: 1=Hợp đồng, 2=Phụ lục
    CREATED_AT DATE DEFAULT SYSDATE,               -- Thời gian upload
    
    -- Constraints
    CONSTRAINT PK_CONTRACT_FILE PRIMARY KEY (ID),
    CONSTRAINT FK_CONTRACT_FILE_CONTRACT FOREIGN KEY (CONTRACT_ID) REFERENCES CONTRACT(ID) ON DELETE CASCADE,
    CONSTRAINT FK_CONTRACT_FILE_ATTACHMENT FOREIGN KEY (ATTACHMENT_ID) REFERENCES CONTRACT_ATTACHMENT(ID) ON DELETE CASCADE,
    CONSTRAINT CK_FILE_TYPE CHECK (FILE_TYPE IN ('PDF', 'DOC', 'DOCX', 'PNG', 'JPG', 'JPEG')),
    CONSTRAINT CK_FILE_CATEGORY CHECK (FILE_CATEGORY IN (1, 2)),
    CONSTRAINT CK_FILE_SIZE CHECK (FILE_SIZE > 0 AND FILE_SIZE <= 5242880) -- Max 5MB
);

-- Sequence và Trigger
CREATE SEQUENCE CONTRACT_FILE_ID_SEQ START WITH 1 INCREMENT BY 1;

CREATE OR REPLACE TRIGGER TRG_CONTRACT_FILE_ID
    BEFORE INSERT ON CONTRACT_FILE
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := CONTRACT_FILE_ID_SEQ.NEXTVAL;
    END IF;
END;
```

### 2.5. Bảng LIQUIDATION_FILE (File biên bản thanh lý) - BẢNG MỚI

```sql
-- Bảng LIQUIDATION_FILE: Lưu file biên bản thanh lý hợp đồng
CREATE TABLE LIQUIDATION_FILE (
    ID NUMBER(19) NOT NULL,
    CONTRACT_ID NUMBER(19) NOT NULL,                -- ID hợp đồng
    FILE_NAME VARCHAR2(255) NOT NULL,               -- Tên file biên bản thanh lý
    FILE_PATH VARCHAR2(500) NOT NULL,               -- Đường dẫn file
    FILE_TYPE VARCHAR2(10) NOT NULL,                -- Loại file
    FILE_SIZE NUMBER(19) NOT NULL,                  -- Kích thước file
    CREATED_AT DATE DEFAULT SYSDATE,               -- Thời gian upload
    
    -- Constraints
    CONSTRAINT PK_LIQUIDATION_FILE PRIMARY KEY (ID),
    CONSTRAINT FK_LIQUIDATION_FILE_CONTRACT FOREIGN KEY (CONTRACT_ID) REFERENCES CONTRACT(ID) ON DELETE CASCADE,
    CONSTRAINT CK_LIQUIDATION_FILE_TYPE CHECK (FILE_TYPE IN ('PDF', 'DOC', 'DOCX', 'PNG', 'JPG', 'JPEG')),
    CONSTRAINT CK_LIQUIDATION_FILE_SIZE CHECK (FILE_SIZE > 0 AND FILE_SIZE <= 5242880)
);

-- Sequence và Trigger
CREATE SEQUENCE LIQUIDATION_FILE_ID_SEQ START WITH 1 INCREMENT BY 1;

CREATE OR REPLACE TRIGGER TRG_LIQUIDATION_FILE_ID
    BEFORE INSERT ON LIQUIDATION_FILE
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := LIQUIDATION_FILE_ID_SEQ.NEXTVAL;
    END IF;
END;
```

### 2.6. Indexes để tối ưu performance

```sql
-- Indexes cho bảng CONTRACT
CREATE INDEX IDX_CONTRACT_TYPE ON CONTRACT(CONTRACT_TYPE);
CREATE INDEX IDX_CONTRACT_ADMIN ON CONTRACT(ADMIN_ID);
CREATE INDEX IDX_CONTRACT_BUSINESS ON CONTRACT(BUSINESS_ID);
CREATE INDEX IDX_CONTRACT_AGENT ON CONTRACT(AGENT_ID);
CREATE INDEX IDX_CONTRACT_STATUS ON CONTRACT(STATUS);
CREATE INDEX IDX_CONTRACT_EFFECTIVE_DATE ON CONTRACT(EFFECTIVE_DATE);
CREATE INDEX IDX_CONTRACT_CREATED_BY ON CONTRACT(CREATED_BY);
CREATE INDEX IDX_CONTRACT_NUMBER ON CONTRACT(CONTRACT_NUMBER);

-- Indexes cho bảng CONTRACT_ATTACHMENT
CREATE INDEX IDX_ATTACHMENT_CONTRACT ON CONTRACT_ATTACHMENT(CONTRACT_ID);
CREATE INDEX IDX_ATTACHMENT_CREATED_BY ON CONTRACT_ATTACHMENT(CREATED_BY);

-- Indexes cho bảng CONTRACT_FILE
CREATE INDEX IDX_CONTRACT_FILE_CONTRACT ON CONTRACT_FILE(CONTRACT_ID);
CREATE INDEX IDX_CONTRACT_FILE_ATTACHMENT ON CONTRACT_FILE(ATTACHMENT_ID);

-- Indexes cho bảng LIQUIDATION_FILE
CREATE INDEX IDX_LIQUIDATION_FILE_CONTRACT ON LIQUIDATION_FILE(CONTRACT_ID);
```

### 2.7. Views hỗ trợ truy vấn

```sql
-- View hiển thị thông tin hợp đồng với thông tin bên liên quan
CREATE OR REPLACE VIEW V_CONTRACT_DETAILS AS
SELECT
    c.ID,
    c.CONTRACT_NUMBER,
    c.CONTRACT_NAME,
    c.CONTRACT_TYPE,
    c.ADMIN_ID,
    c.BUSINESS_ID,
    c.AGENT_ID,
    -- Thông tin Admin (nếu có)
    admin_acc.FULL_NAME AS ADMIN_NAME,
    admin_acc.EMAIL AS ADMIN_EMAIL,
    -- Thông tin Business (nếu có)
    b.BUSINESS_NAME,
    b.BUSINESS_CODE,
    b.CONTACT_NAME AS BUSINESS_CONTACT_NAME,
    b.CONTACT_PHONE AS BUSINESS_CONTACT_PHONE,
    -- Thông tin Agent (nếu có)
    a.AGENT_NAME,
    a.AGENT_CODE,
    a.CONTACT_NAME AS AGENT_CONTACT_NAME,
    a.CONTACT_PHONE AS AGENT_CONTACT_PHONE,
    -- Thông tin hợp đồng
    c.SERVICES,
    c.CONTRACT_DURATION_TYPE,
    c.EFFECTIVE_DATE,
    c.EXPIRY_DATE,
    c.NOTES,
    c.STATUS,
    c.LIQUIDATION_DATE,
    c.CREATED_AT,
    c.UPDATED_AT,
    acc_created.FULL_NAME AS CREATED_BY_NAME,
    acc_updated.FULL_NAME AS UPDATED_BY_NAME
FROM CONTRACT c
LEFT JOIN ACCOUNT_INFO admin_acc ON c.ADMIN_ID = admin_acc.ID
LEFT JOIN BUSINESS b ON c.BUSINESS_ID = b.ID
LEFT JOIN AGENT a ON c.AGENT_ID = a.ID
LEFT JOIN ACCOUNT_INFO acc_created ON c.CREATED_BY = acc_created.ID
LEFT JOIN ACCOUNT_INFO acc_updated ON c.UPDATED_BY = acc_updated.ID;
```

## 3. FLOW GIAO TIẾP CLIENT-SERVER

### 3.1. Luồng Xác thực (Authentication Flow)

```mermaid
sequenceDiagram
    participant Client as Trình duyệt
    participant Gateway as API Gateway
    participant AuthService as Dịch vụ Xác thực
    participant Database as Cơ sở dữ liệu

    Client->>Gateway: Gửi yêu cầu đăng nhập<br/>POST /auth/login {username, password}
    Gateway->>AuthService: Chuyển tiếp yêu cầu đăng nhập
    AuthService->>Database: Truy vấn bảng ACCOUNT_INFO<br/>tìm user theo username
    Database-->>AuthService: Trả về thông tin user + role
    AuthService->>AuthService: Kiểm tra mật khẩu<br/>và trạng thái tài khoản
    AuthService->>AuthService: Tạo JWT token<br/>chứa thông tin user
    AuthService-->>Gateway: Trả về JWT token + thông tin user
    Gateway-->>Client: Phản hồi thành công<br/>{success: true, data: {token, userInfo}}

    Note over Client: Lưu JWT token để sử dụng<br/>cho các request tiếp theo

    Client->>Gateway: Gửi request với Authorization header<br/>GET /contracts
    Gateway->>Gateway: Kiểm tra và xác thực JWT token
    Gateway->>ContractService: Chuyển tiếp request<br/>kèm thông tin user context
    ContractService-->>Gateway: Trả về dữ liệu hợp đồng
    Gateway-->>Client: Phản hồi dữ liệu
```

### 3.2. Luồng Use Case - Xem danh sách hợp đồng doanh nghiệp

```mermaid
sequenceDiagram
    participant Admin as Admin vận hành
    participant Frontend as Giao diện người dùng
    participant Gateway as API Gateway
    participant ContractService as Dịch vụ Hợp đồng
    participant Database as Cơ sở dữ liệu

    Admin->>Frontend: Nhấp vào menu<br/>"Hợp đồng > Doanh nghiệp"
    Frontend->>Gateway: Gửi yêu cầu lấy danh sách<br/>GET /api/v1/contracts/admin_business?page_index=0&page_size=20
    Gateway->>ContractService: Chuyển tiếp yêu cầu
    ContractService->>Database: Truy vấn V_CONTRACT_DETAILS<br/>WHERE contract_type='ADMIN_BUSINESS'
    Database-->>ContractService: Trả về danh sách hợp đồng<br/>có phân trang
    ContractService->>ContractService: Chuyển đổi Entity sang DTO<br/>sử dụng MapStruct
    ContractService-->>Gateway: Trả về ResponseCommon<br/>chứa PageResponse<ContractDto>
    Gateway-->>Frontend: Phản hồi JSON
    Frontend->>Frontend: Hiển thị danh sách hợp đồng<br/>trên giao diện

    Note over Admin: Admin có thể thực hiện:
    Note over Admin: - Tìm kiếm theo số/tên hợp đồng
    Note over Admin: - Lọc theo doanh nghiệp, dịch vụ, trạng thái
    Note over Admin: - Xem chi tiết, cập nhật, thanh lý
```

### 3.3. Luồng Use Case - Thêm mới hợp đồng Admin-Doanh nghiệp

```mermaid
sequenceDiagram
    participant Admin as Admin vận hành
    participant Frontend as Giao diện người dùng
    participant Gateway as API Gateway
    participant ContractService as Dịch vụ Hợp đồng
    participant FileService as Dịch vụ File
    participant Database as Cơ sở dữ liệu

    Admin->>Frontend: Nhấp "Thêm hợp đồng"
    Frontend->>Gateway: Lấy danh sách doanh nghiệp hoạt động<br/>GET /api/v1/enterprises/active
    Gateway->>ContractService: Chuyển tiếp yêu cầu
    ContractService-->>Frontend: Trả về danh sách doanh nghiệp<br/>để hiển thị dropdown

    Admin->>Frontend: Nhập thông tin hợp đồng<br/>+ upload file hợp đồng
    Frontend->>Gateway: Upload file trước<br/>POST /api/v1/files/upload
    Gateway->>FileService: Xử lý upload file
    FileService->>FileService: Kiểm tra loại file và kích thước<br/>(PDF/DOC/PNG, max 5MB)
    FileService->>FileService: Lưu file vào storage<br/>và tạo đường dẫn
    FileService-->>Frontend: Trả về thông tin file<br/>{fileName, filePath, fileSize}

    Frontend->>Gateway: Tạo hợp đồng mới<br/>POST /api/v1/contracts/admin_business
    Gateway->>ContractService: Chuyển tiếp yêu cầu tạo hợp đồng
    ContractService->>ContractService: Kiểm tra business rules:<br/>- Số hợp đồng duy nhất<br/>- Doanh nghiệp đang hoạt động<br/>- Ngày hết hạn > ngày hiệu lực
    ContractService->>Database: Bắt đầu transaction
    ContractService->>Database: INSERT vào bảng CONTRACT<br/>với contract_type='ADMIN_BUSINESS'
    ContractService->>Database: INSERT vào bảng CONTRACT_FILE<br/>cho từng file đính kèm
    ContractService->>Database: COMMIT transaction
    ContractService-->>Gateway: Phản hồi thành công
    Gateway-->>Frontend: {success: true, message: "Thêm mới thành công"}
    Frontend->>Frontend: Hiển thị thông báo thành công<br/>và chuyển đến trang chi tiết
```

### 3.4. Use Case Flow - Thanh lý hợp đồng

```mermaid
sequenceDiagram
    participant Admin
    participant Frontend
    participant Gateway
    participant ContractService
    participant FileService
    participant Database

    Admin->>Frontend: Click "Thanh lý hợp đồng"
    Frontend->>Frontend: Show popup form
    Admin->>Frontend: Nhập ngày thanh lý + upload biên bản

    Frontend->>Gateway: POST /api/v1/files/upload (upload liquidation files)
    Gateway->>FileService: Process file upload
    FileService-->>Frontend: File info

    Frontend->>Gateway: POST /api/v1/contracts/{id}/liquidate
    Gateway->>ContractService: Liquidate contract request
    ContractService->>ContractService: Validate: contract status must be ACTIVE/EXPIRED
    ContractService->>Database: BEGIN TRANSACTION
    ContractService->>Database: UPDATE CONTRACT SET status=3, liquidation_date=?
    ContractService->>Database: INSERT INTO LIQUIDATION_FILE
    ContractService->>Database: COMMIT TRANSACTION
    ContractService-->>Gateway: Success response
    Gateway-->>Frontend: {success: true, message: "Thanh lý hợp đồng thành công"}
    Frontend->>Frontend: Close popup + refresh list
```

### 3.5. Error Handling Flow

```mermaid
sequenceDiagram
    participant Client
    participant Gateway
    participant Service
    participant Database

    Client->>Gateway: Invalid request
    Gateway->>Service: Forward request
    Service->>Service: Validate input

    alt Validation Error
        Service-->>Gateway: ValidationException
        Gateway-->>Client: 400 Bad Request
    else Business Logic Error
        Service->>Database: Query/Update
        Database-->>Service: Constraint violation
        Service-->>Gateway: BusinessException
        Gateway-->>Client: 422 Unprocessable Entity
    else System Error
        Service->>Database: Query
        Database-->>Service: SQLException
        Service-->>Gateway: SystemException
        Gateway-->>Client: 500 Internal Server Error
    end

    Note over Client: All errors return Vietnamese messages
    Note over Client: Format: {success: false, message: "Lỗi...", errors: [...]}
```

### 3.6. File Upload/Download Flow

```mermaid
sequenceDiagram
    participant Client
    participant Gateway
    participant FileService
    participant Storage

    %% Upload Flow
    Client->>Gateway: POST /api/v1/files/upload (multipart/form-data)
    Gateway->>FileService: Process upload
    FileService->>FileService: Validate file type & size
    FileService->>Storage: Save file to storage
    Storage-->>FileService: File path
    FileService-->>Gateway: File metadata
    Gateway-->>Client: {fileName, filePath, downloadUrl}

    %% Download Flow
    Client->>Gateway: GET /api/v1/files/download/{fileName}
    Gateway->>FileService: Get file
    FileService->>Storage: Read file
    Storage-->>FileService: File content
    FileService-->>Gateway: File stream
    Gateway-->>Client: File download

    %% Preview Flow
    Client->>Gateway: GET /api/v1/files/preview/{fileName}
    Gateway->>FileService: Get file for preview
    FileService->>FileService: Check file type (PDF/Image)
    FileService->>Storage: Read file
    Storage-->>FileService: File content
    FileService-->>Gateway: File stream with preview headers
    Gateway-->>Client: File preview in browser
```

## 4. MÔ TẢ CHI TIẾT API

### 4.1. API Quản lý Hợp đồng

#### 4.1.1. Lấy danh sách hợp đồng

**Endpoint**: `GET /contracts`

**Mô tả**: Lấy danh sách hợp đồng với phân trang và tìm kiếm. Hỗ trợ tất cả loại hợp đồng thông qua tham số contract_type

**Request JSON**:
```json
{
  "page_index": 0,
  "page_size": 20,
  "search": "HD001",
  "contract_type": "ADMIN_BUSINESS",
  "admin_id": 1,
  "business_id": 2,
  "agent_id": null,
  "services": "SMS",
  "status": "ACTIVE"
}
```

**Giải thích các trường request**:
- `page_index` (integer, optional): Số trang, bắt đầu từ 0. Mặc định: 0
- `page_size` (integer, optional): Số bản ghi trên mỗi trang. Mặc định: 20, tối đa: 100
- `search` (string, optional): Tìm kiếm theo số hợp đồng hoặc tên hợp đồng. Tối đa 100 ký tự
- `contract_type` (string, optional): Loại hợp đồng. Giá trị: "ADMIN_BUSINESS", "ADMIN_AGENT", "AGENT_BUSINESS"
- `admin_id` (long, optional): Lọc theo ID admin (dùng cho ADMIN_BUSINESS, ADMIN_AGENT)
- `business_id` (long, optional): Lọc theo ID doanh nghiệp (dùng cho ADMIN_BUSINESS, AGENT_BUSINESS)
- `agent_id` (long, optional): Lọc theo ID đại lý (dùng cho ADMIN_AGENT, AGENT_BUSINESS)
- `services` (string, optional): Lọc theo dịch vụ. Giá trị: "SMS", "ZNS", "ALL_SERVICES"
- `status` (string, optional): Lọc theo trạng thái. Giá trị: "ACTIVE", "EXPIRED", "LIQUIDATED"

**Response thành công (200 OK)**:
```json
{
  "code": 0,
  "message": "Lấy danh sách hợp đồng thành công",
  "data": {
    "paging": {
      "current": 1,
      "size": 20,
      "total_pages": 5,
      "total_records": 100
    },
    "items": [
      {
        "id": 1,
        "contract_number": "HD001",
        "contract_name": "Hợp đồng SMS ABC",
        "contract_type": "ADMIN_BUSINESS",
        "admin_id": 1,
        "admin_name": "Admin Hệ thống",
        "business_id": 2,
        "business_name": "Công ty TNHH ABC",
        "business_code": "ENT001",
        "agent_id": null,
        "agent_name": null,
        "services": ["SMS", "ZNS"],
        "contract_duration_type": "FIXED_TERM",
        "effective_date": "2024-01-01",
        "expiry_date": "2024-12-31",
        "status": "ACTIVE",
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00",
        "created_by_name": "Admin Hệ thống"
      }
    ]
  }
}
```

**Giải thích các trường response**:
- `code` (integer): Mã trạng thái (0 = thành công)
- `message` (string): Thông báo kết quả
- `data.paging.current` (integer): Trang hiện tại
- `data.paging.size` (integer): Số bản ghi trên trang
- `data.paging.total_pages` (integer): Tổng số trang
- `data.paging.total_records` (integer): Tổng số bản ghi
- `data.items[].id` (long): ID hợp đồng
- `data.items[].contract_number` (string): Số hợp đồng
- `data.items[].contract_name` (string): Tên hợp đồng
- `data.items[].contract_type` (string): Loại hợp đồng
- `data.items[].admin_id` (long): ID admin (null nếu không có)
- `data.items[].business_id` (long): ID doanh nghiệp (null nếu không có)
- `data.items[].agent_id` (long): ID đại lý (null nếu không có)
- `data.items[].services` (array): Danh sách dịch vụ
- `data.items[].status` (string): Trạng thái hợp đồng


#### 4.1.2. Lấy chi tiết hợp đồng

**Endpoint**: `GET /contracts/{id}`

**Mô tả**: Lấy thông tin chi tiết của một hợp đồng cụ thể (tất cả loại hợp đồng)

**Path Parameters**:
- `id` (long, required): ID của hợp đồng cần xem chi tiết

**Response thành công (200 OK)**:
```json
{
  "code": 0,
  "message": "Lấy chi tiết hợp đồng thành công",
  "data": {
    "id": 1,
    "contract_number": "HD001",
    "contract_name": "Hợp đồng SMS ABC",
    "contract_type": "ADMIN_BUSINESS",
    "admin_id": 1,
    "admin_name": "Admin Hệ thống",
    "admin_email": "<EMAIL>",
    "business_id": 1,
    "business_name": "Công ty TNHH ABC",
    "business_code": "ENT001",
    "business_contact_name": "Nguyễn Văn A",
    "business_contact_phone": "0987654321",
    "agent_id": null,
    "agent_name": null,
    "agent_code": null,
    "services": ["SMS", "ZNS"],
    "contract_files": [
      {
        "id": 1,
        "file_name": "hop-dong-abc.pdf",
        "file_path": "/files/contracts/hop-dong-abc.pdf",
        "file_type": "PDF",
        "file_size": 1024000,
        "download_url": "/api/v1/files/download/hop-dong-abc.pdf"
      }
    ],
    "contract_duration_type": "FIXED_TERM",
    "effective_date": "2024-01-01",
    "expiry_date": "2024-12-31",
    "notes": "Ghi chú hợp đồng",
    "status": "ACTIVE",
    "liquidation_date": null,
    "liquidation_files": [],
    "created_at": "2024-01-01T00:00:00",
    "updated_at": "2024-01-01T00:00:00",
    "created_by_name": "Admin Hệ thống"
  }
}
```

#### 4.1.3. Tạo mới hợp đồng

**Endpoint**: `POST /contracts`

**Mô tả**: Tạo mới hợp đồng (tất cả loại hợp đồng thông qua contract_type)

**Request JSON**:
```json
{
  "contract_number": "HD001",
  "contract_name": "Hợp đồng SMS ABC",
  "contract_type": "ADMIN_BUSINESS",
  "admin_id": 1,
  "business_id": 2,
  "agent_id": null,
  "services": ["SMS", "ZNS"],
  "contract_files": [
    {
      "file_name": "hop-dong-abc.pdf",
      "file_content": "base64_encoded_content",
      "file_type": "PDF"
    }
  ],
  "contract_duration_type": "FIXED_TERM",
  "effective_date": "2024-01-01",
  "expiry_date": "2024-12-31",
  "notes": "Ghi chú hợp đồng"
}
```

**Giải thích các trường request**:
- `contract_number` (string, required): Số hợp đồng. Tối đa 25 ký tự, duy nhất
- `contract_name` (string, optional): Tên hợp đồng. Tối đa 50 ký tự
- `contract_type` (string, required): Loại hợp đồng. Giá trị: "ADMIN_BUSINESS", "ADMIN_AGENT", "AGENT_BUSINESS"
- `admin_id` (long, conditional): ID Admin. Bắt buộc cho ADMIN_BUSINESS, ADMIN_AGENT
- `business_id` (long, conditional): ID Doanh nghiệp. Bắt buộc cho ADMIN_BUSINESS, AGENT_BUSINESS
- `agent_id` (long, conditional): ID Đại lý. Bắt buộc cho ADMIN_AGENT, AGENT_BUSINESS
- `services` (array, required): Danh sách dịch vụ. Giá trị: ["SMS"], ["ZNS"], ["SMS", "ZNS"], ["ALL_SERVICES"]
- `contract_files` (array, required): File hợp đồng. Tối đa 5 file, mỗi file tối đa 5MB
- `contract_files[].file_name` (string): Tên file gốc
- `contract_files[].file_content` (string): Nội dung file encode base64
- `contract_files[].file_type` (string): Loại file. Giá trị: PDF, DOC, DOCX, PNG, JPG, JPEG
- `contract_duration_type` (string, required): Loại thời hạn. Giá trị: "FIXED_TERM", "INDEFINITE"
- `effective_date` (string, required): Ngày hiệu lực. Format: "yyyy-MM-dd"
- `expiry_date` (string, optional): Ngày hết hạn. Format: "yyyy-MM-dd"
- `notes` (string, optional): Ghi chú. Tối đa 250 ký tự

**Response thành công (201 Created)**:
```json
{
  "code": 0,
  "message": "Tạo mới hợp đồng thành công",
  "data": {
    "id": 2,
    "contract_number": "HD002",
    "contract_name": "Hợp đồng ZNS XYZ"
  }
}
```

#### 4.1.4. Cập nhật hợp đồng

**Endpoint**: `PUT /contracts/{id}`

**Mô tả**: Cập nhật thông tin hợp đồng (tất cả loại hợp đồng)

**Request JSON**: Tương tự như tạo mới, không bao gồm các ID của các bên ký hợp đồng

**Business Rules**:
- Chỉ cho phép cập nhật hợp đồng có status = "ACTIVE" hoặc "EXPIRED"
- Không cho phép thay đổi contract_type và các ID của bên ký hợp đồng

**Response thành công (200 OK)**:
```json
{
  "code": 0,
  "message": "Cập nhật hợp đồng thành công",
  "data": {
    "id": 1,
    "contract_number": "HD001",
    "contract_name": "Hợp đồng SMS ABC - Updated"
  }
}
```

#### 4.1.5. Thanh lý hợp đồng

**Endpoint**: `POST /contracts/{id}/liquidate`

**Mô tả**: Thanh lý hợp đồng (tất cả loại hợp đồng)

**Request JSON**:
```json
{
  "liquidation_date": "2024-06-30",
  "liquidation_files": [
    {
      "file_name": "bien-ban-thanh-ly.pdf",
      "file_content": "base64_encoded_content",
      "file_type": "PDF"
    }
  ]
}
```

**Giải thích các trường request**:
- `liquidation_date` (string, required): Ngày thanh lý. Format: "yyyy-MM-dd"
- `liquidation_files` (array, required): File biên bản thanh lý. Tối đa 5 file, mỗi file tối đa 5MB
- `liquidation_files[].file_name` (string): Tên file biên bản
- `liquidation_files[].file_content` (string): Nội dung file encode base64
- `liquidation_files[].file_type` (string): Loại file

**Response thành công (200 OK)**:
```json
{
  "code": 0,
  "message": "Thanh lý hợp đồng thành công",
  "data": null
}
```

### 4.2. API Quản lý Phụ lục Hợp đồng

#### 4.2.1. Lấy danh sách phụ lục hợp đồng

**Endpoint**: `GET /contracts/{contract_id}/attachments`

**Mô tả**: Lấy danh sách phụ lục của một hợp đồng cụ thể với tìm kiếm

**Query Parameters JSON**:
```json
{
  "search": "PL001"
}
```

**Giải thích các trường request**:
- `search` (string, optional): Từ khóa tìm kiếm theo số phụ lục hoặc tên phụ lục. Tối đa 100 ký tự

**Response thành công (200 OK)**:
```json
{
  "code": 0,
  "message": "Lấy danh sách phụ lục thành công",
  "data": [
    {
      "id": 1,
      "attachment_number": "PL001",
      "attachment_name": "Phụ lục 1",
      "effective_date": "2024-03-01",
      "notes": "Ghi chú phụ lục",
      "attachment_files": [
        {
          "id": 1,
          "file_name": "phu-luc-1.pdf",
          "file_path": "/files/attachments/phu-luc-1.pdf",
          "file_type": "PDF",
          "file_size": 512000,
          "download_url": "/api/v1/files/download/phu-luc-1.pdf"
        }
      ],
      "created_at": "2024-03-01T00:00:00",
      "created_by_name": "Admin Hệ thống"
    }
  ]
}
```

#### 4.2.2. Tạo mới phụ lục hợp đồng

**Endpoint**: `POST /contracts/{contract_id}/attachments`

**Mô tả**: Tạo mới phụ lục cho hợp đồng cụ thể

**Request JSON**:
```json
{
  "attachment_number": "PL001",
  "attachment_name": "Phụ lục 1 - Bổ sung dịch vụ",
  "effective_date": "2024-03-01",
  "notes": "Phụ lục bổ sung dịch vụ ZNS cho hợp đồng",
  "attachment_files": [
    {
      "file_name": "phu-luc-1.pdf",
      "file_content": "base64_encoded_content",
      "file_type": "PDF"
    }
  ]
}
```

**Giải thích các trường request**:
- `attachment_number` (string, required): Số phụ lục. Tối đa 25 ký tự, duy nhất trong hợp đồng
- `attachment_name` (string, optional): Tên phụ lục. Tối đa 50 ký tự
- `effective_date` (string, required): Ngày hiệu lực phụ lục. Format: "yyyy-MM-dd"
- `notes` (string, optional): Ghi chú phụ lục. Tối đa 250 ký tự
- `attachment_files` (array, required): File phụ lục. Tối đa 5 file, mỗi file tối đa 5MB
- `attachment_files[].file_name` (string): Tên file gốc
- `attachment_files[].file_content` (string): Nội dung file encode base64
- `attachment_files[].file_type` (string): Loại file. Giá trị: PDF, DOC, DOCX, PNG, JPG, JPEG

**Response thành công (201 Created)**:
```json
{
  "code": 0,
  "message": "Tạo mới phụ lục thành công",
  "data": {
    "id": 2,
    "attachment_number": "PL002",
    "attachment_name": "Phụ lục 2"
  }
}
```

#### 4.2.3. Xóa phụ lục hợp đồng

**Endpoint**: `DELETE /contracts/{contract_id}/attachments/{attachment_id}`

**Business Rules**:
- Chỉ cho phép xóa phụ lục của hợp đồng có status != 3 (Thanh lý)
- Xóa phụ lục sẽ xóa luôn tất cả file đính kèm

**Response thành công (200 OK)**:
```json
{
  "code": 0,
  "message": "Xóa phụ lục thành công",
  "data": null
}
```

### 4.3. API Quản lý File

#### 4.3.1. Upload file

**Endpoint**: `POST /files/upload`

**Request**: Multipart form data
- `file`: File to upload (MultipartFile)
- `category`: "CONTRACT" | "ATTACHMENT" | "LIQUIDATION"

**Validation Rules**:
- File types: PDF, DOC, DOCX, PNG, JPG, JPEG
- Max file size: 5MB
- Max files per request: 5

**Response thành công (200 OK)**:
```json
{
  "code": 0,
  "message": "Upload file thành công",
  "data": {
    "file_name": "document.pdf",
    "file_path": "/files/uploads/document.pdf",
    "file_type": "PDF",
    "file_size": 1024000,
    "download_url": "/api/v1/files/download/document.pdf"
  }
}
```

#### 4.3.2. Download file

**Endpoint**: `GET /files/download/{file_name}`

**Response**: File stream với appropriate headers

#### 4.3.3. Preview file

**Endpoint**: `GET /files/preview/{file_name}`

**Response**: File stream cho preview (PDF/Image)

### 4.4. API Master Data

#### 4.4.1. Lấy danh sách doanh nghiệp

**Endpoint**: `GET /enterprises`

**Mô tả**: Lấy danh sách doanh nghiệp để hiển thị trong dropdown khi tạo hợp đồng

**Query Parameters JSON**:
```json
{
  "search": "ABC",
  "status": "ACTIVE"
}
```

**Giải thích các trường request**:
- `search` (string, optional): Từ khóa tìm kiếm theo mã doanh nghiệp hoặc tên doanh nghiệp. Tối đa 100 ký tự
- `status` (string, optional): Lọc theo trạng thái. Giá trị: "ACTIVE", "INACTIVE". Mặc định: "ACTIVE"

**Response thành công (200 OK)**:
```json
{
  "code": 0,
  "message": "Lấy danh sách doanh nghiệp thành công",
  "data": [
    {
      "id": 1,
      "business_code": "ENT001",
      "business_name": "Công ty TNHH ABC",
      "contact_name": "Nguyễn Văn A",
      "contact_phone": "0987654321",
      "status": "ACTIVE"
    }
  ]
}
```

#### 4.4.2. Lấy danh sách đại lý

**Endpoint**: `GET /agents`

**Mô tả**: Lấy danh sách đại lý để hiển thị trong dropdown khi tạo hợp đồng

**Query Parameters JSON**:
```json
{
  "search": "DL001",
  "status": "ACTIVE"
}
```

**Giải thích các trường request**:
- `search` (string, optional): Từ khóa tìm kiếm theo mã đại lý hoặc tên đại lý. Tối đa 100 ký tự
- `status` (string, optional): Lọc theo trạng thái. Giá trị: "ACTIVE", "INACTIVE". Mặc định: "ACTIVE"

**Response thành công (200 OK)**:
```json
{
  "code": 0,
  "message": "Lấy danh sách đại lý thành công",
  "data": [
    {
      "id": 1,
      "agent_code": "DL001",
      "agent_name": "Đại lý ABC",
      "contact_name": "Trần Văn B",
      "contact_phone": "0987654321",
      "status": "ACTIVE"
    }
  ]
}
```

### 4.5. MapStruct Mapping Configuration

**Mô tả**: Sử dụng MapStruct để mapping giữa Entity và DTO

**Contract Mapper**:
- Mapping từ Contract Entity sang ContractDto
- Mapping từ CreateContractRequest sang Contract Entity
- Tự động mapping các trường tương ứng
- Custom mapping cho các trường đặc biệt (business_name, agent_name, created_by_name)

**File Mapper**:
- Mapping từ ContractFile Entity sang FileDto
- Tự động tạo download_url từ file_name
- Mapping từ FileUploadDto sang ContractFile Entity

---

## TÓM TẮT THIẾT KẾ

Bản thiết kế này cung cấp:

1. **Mô hình ERD hoàn chỉnh** với 4 bảng mới và 3 bảng đã tồn tại, hỗ trợ 3 loại hợp đồng khác nhau
2. **Database Oracle** với text constants có ý nghĩa và business logic constraints
3. **Flow giao tiếp Client-Server** được giải thích bằng tiếng Việt chi tiết
4. **RESTful APIs gộp chung** thông qua tham số contract_type, sử dụng JSON request/response
5. **Business rules** rõ ràng cho từng loại hợp đồng
6. **MapStruct mapping** configuration cho Entity-DTO conversion

**Bảng đã tồn tại (KHÔNG thay đổi):**
- **ACCOUNT_INFO**: Thông tin đăng nhập cho tất cả user
- **BUSINESS**: Thông tin chi tiết doanh nghiệp
- **AGENT**: Thông tin chi tiết đại lý

**Bảng mới cần tạo:**
- **CONTRACT**: Hợp đồng chính với 3 loại (ADMIN_BUSINESS, ADMIN_AGENT, AGENT_BUSINESS)
- **CONTRACT_ATTACHMENT**: Phụ lục hợp đồng
- **CONTRACT_FILE**: File đính kèm hợp đồng và phụ lục
- **LIQUIDATION_FILE**: File biên bản thanh lý

**API được gộp chung:**
- `GET/POST/PUT /contracts` - Quản lý tất cả loại hợp đồng thông qua contract_type
- `GET/POST/DELETE /contracts/{id}/attachments` - Quản lý phụ lục
- `GET/POST /files` - Quản lý file upload/download

**Thiết kế tuân thủ:**
- Spring Boot 3.x + Java 17 best practices
- Oracle Database với snake_case naming
- Text constants thay vì số (ACTIVE, EXPIRED, LIQUIDATED)
- JSON request/response với giải thích chi tiết từng trường
- Vietnamese flow descriptions
- API Rules compliance với snake_case endpoints
