# A. Trang quản trị CMS quản trị vận hành

## 1. QL Hợp đồng doanh nghiệp - Actor: <PERSON><PERSON><PERSON> hành

### 1.1. [UC-HĐ001] Xem danh sách hợp đồng khách hàng doanh nghiệp

**Usecase ID & Name**: [UC-HĐ001] Xem danh sách hợp đồng  
**Description**:  
- **Mục đích**: Xem danh sách hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình đăng nhập hệ thống.  
**Expected Result**:  
- Ng<PERSON>ời dùng xem danh sách hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  
- [Flow trạng thái]  

**Screen Description**:  
**Screen 1: <PERSON><PERSON><PERSON> hình danh sách**

| # | Name | Type | Note |
|---|------|------|------|
| 1 | STT | Label | Hệ thống tự gen |
| 2 | <PERSON><PERSON> hợp đồng | Label | Hiển thị số hợp đồng |
| 3 | Tên hợp đồng | Label | Hiển thị tên hợp đồng |
| 4 | Tên doanh nghiệp | Label | Hiển thị tên doanh nghiệp |
| 5 | Dịch vụ | Label | Hiển thị các dịch vụ của hợp đồng |
| 6 | Loại hợp đồng | Label | Hiển thị loại hợp đồng bao gồm các giá trị: Có thời hạn, Vô thời hạn |
| 7 | Ngày hiệu lực | Label | - Thời gian hiệu lực của hợp đồng<br>- Định dạng: dd/mm/yyyy |
| 8 | Ngày hết hạn | Label | - Thời gian hết hạn của hợp đồng<br>- Định dạng: dd/mm/yyyy |
| 9 | Trạng thái | Label | Trạng thái hiện tại của hợp đồng bao gồm các giá trị: Hoạt động, Hết hiệu lực, Thanh lý |
| 10 | Thao tác | Button | Xem tại Bảng trạng thái và thao tác tương ứng |
| 11 | Bộ lọc | Button | - Bấm hiển thị danh sách tiêu chí lọc<br>- Mô tả chi tiết tại Alternative Flows |
| 12 | Searchbox | Button | - Hintext: Tìm kiếm theo số hợp đồng, tên hợp đồng<br>- Mô tả chi tiết tại Alternative Flows |
| 13 | Thêm hợp đồng | Button | Xem chi tiết tại UC Thêm mới hợp đồng |

**Bảng trạng thái và thao tác tương ứng**:

| Trạng thái | Action | Notes and References |
|------------|--------|----------------------|
| Hoạt động | Xem, Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Hết hiệu lực | Xem, Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Thanh lý | Xem | |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Trang chủ chọn Hợp đồng > Doanh nghiệp | |
| BF-2 | Hệ thống | - Hiển thị giao diện danh sách Hợp đồng<br>- Ưu tiên Hợp đồng có thời gian cập nhật mới nhất lên đầu | |
|  |  | - AF-1: Người dùng bấm Xem chi tiết => Xem chi tiết tại UC Xem chi tiết<br>- AF-2: Người dùng bấm Cập nhật => Xem chi tiết tại UC Cập nhật<br>- AF-3: Người dùng bấm Thêm hợp đồng => Xem chi tiết tại UC Thêm mới hợp đồng<br>- AF-4: Người dùng bấm Thanh lý hợp đồng<br>- AF-5: Người dùng nhập giá trị và bấm tìm kiếm<br>- AF-6: Người dùng bấm Bộ lọc<br>- AF-7: Người dùng bấm Thêm phụ lục => Xem tại UC Thêm phụ lục | |

**Alternative Flows**:

**AF-4: Người dùng bấm Thanh lý hợp đồng**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-1-1 | Hệ thống | Hiển thị popup<br>Yêu cầu nhập các thông tin:<br>**Ngày thanh lý**:<br>Calendar picker<br>Cho phép chọn: Ngày/ tháng/ năm<br>Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường].<br>- Placeholder: Chọn [tên trường]<br><br>**File Biên bản thanh lý**:<br>Cho phép upload file tài liệu<br>Giới hạn upload tối đa 5 file<br>Định dạng PDF, Doc, PNG => upload sai hiển thị thông báo: Chỉ upload định dạng PDF, Doc, PNG<br>Dung lượng tập tin tối đa 5MB => upload tối đa hiển thị thông báo: File tối đa 5MB<br>Bắt buộc upload => để trống hiển thị thông báo: Vui lòng bổ sung file Biên bản thanh lý.<br><br>Kèm 2 lựa chọn:<br>Xác nhận => đóng popup và lưu lại dữ liệu. Thay đổi trạng thái của hợp đồng là “Thanh lý”<br>Đóng => Đóng popup | |

**AF-5: Người dùng nhập giá trị và bấm tìm kiếm**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-5-1 | Hệ thống | - Hiển thị các kết quả có Hợp đồng đúng tuyệt đối với Số hợp đồng và đúng tương đối với Tên hợp đồng<br>- Trường hợp không có kết quả hiển thị Không có dữ liệu để hiển thị | |

**AF-6: Người dùng bấm Bộ lọc**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-6-1 | Hệ thống | Hiển thị popup bộ lọc cho phép lọc các thông tin:<br>1. **Doanh nghiệp**:<br>Drop List + Search box tương đối<br>Giá trị: Các doanh nghiệp hệ thống quản lý<br>Placeholder: Chọn giá trị<br>2. **Dịch vụ**:<br>Droplist<br>Giá trị: SMS, ZNS<br>Placeholder: Chọn giá trị<br>3. **Trạng thái**:<br>Droplist<br>Giá trị: Hoạt động, Hết hiệu lực, Thanh lý<br>Placeholder: Chọn giá trị | |
| AF-6-2 | Người dùng | Bấm Áp dụng<br>Bấm Thiết lập lại: Hệ thống thực hiện xóa các giá trị lọc | |
| AF-6-3 | Hệ thống | - Hiển thị các kết quả Hợp đồng chính xác với bộ lọc<br>- Trường hợp không có kết quả hiển thị Không có dữ liệu để hiển thị | |

**Exception Flows**:  
*(Trống)*

---

**Xem tại Quản lý Doanh nghiệp**

**Usecase ID & Name**: [UC-TC001] Xem danh sách hợp đồng  
**Description**:  
- **Mục đích**: Xem danh sách hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình đăng nhập hệ thống.  
**Expected Result**:  
- Người dùng xem danh sách hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  
- [Flow trạng thái]  

**Screen Description**:  
**Screen 1: Màn hình danh sách**

| # | Name | Type | Note |
|---|------|------|------|
| 1 | STT | Label | Hệ thống tự gen |
| 2 | Số hợp đồng | Label | Hiển thị số hợp đồng |
| 3 | Tên hợp đồng | Label | Hiển thị tên hợp đồng |
| 4 | Dịch vụ | Label | Hiển thị các dịch vụ của hợp đồng |
| 5 | Loại hợp đồng | Label | Hiển thị loại hợp đồng bao gồm các giá trị: Có thời hạn, Vô thời hạn |
| 6 | Ngày hiệu lực | Label | - Thời gian hiệu lực của hợp đồng<br>- Định dạng: dd/mm/yyyy |
| 7 | Ngày hết hạn | Label | - Thời gian hết hạn của hợp đồng<br>- Định dạng: dd/mm/yyyy |
| 8 | Trạng thái | Label | Trạng thái hiện tại của hợp đồng bao gồm các giá trị: Hoạt động, Hết hiệu lực, Thanh lý |
| 9 | Thao tác | Button | Xem tại Bảng trạng thái và thao tác tương ứng |
| 10 | Thêm hợp đồng | Button | Xem chi tiết tại UC Thêm mới hợp đồng<br>**Lưu ý**: Khi thêm hợp đồng tại đây, màn thêm mới hợp đồng sẽ tự động chọn Doanh nghiệp tương ứng và cho phép sửa lại |

**Bảng trạng thái và thao tác tương ứng**:

| Trạng thái | Action | Notes and References |
|------------|--------|----------------------|
| Hoạt động | Xem, Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Hết hiệu lực | Xem, Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Thanh lý | Xem | |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Trang chủ chọn Doanh nghiệp > Xem thông tin chi tiết doanh- nghiệp muốn xem hợp đồng > Tab Hợp đồng | |
| BF-2 | Hệ thống | - Hiển thị giao diện danh sách Hợp đồng<br>- Ưu tiên Hợp đồng có thời gian cập nhật mới nhất lên đầu | |
|  |  | - AF-1: Người dùng bấm Xem => Xem chi tiết tại UC Xem chi tiết<br>- AF-2: Người dùng bấm Cập nhật => Xem chi tiết tại UC Cập nhật<br>- AF-3: Người dùng bấm Thêm hợp đồng => Xem chi tiết tại UC Thêm mới hợp đồng<br>- AF-4: Người dùng bấm Thanh lý hợp đồng<br>- AF-5: Người dùng bấm Thêm phụ lục => Xem tại UC Thêm phụ lục | |

**Alternative Flows**:

**AF-4: Người dùng bấm Thanh lý hợp đồng**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-1-1 | Hệ thống | Hiển thị popup<br>Yêu cầu nhập các thông tin:<br>**Ngày thanh lý**:<br>Calendar picker<br>Cho phép chọn: Ngày/ tháng/ năm<br>Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường].<br>- Placeholder: Chọn [tên trường]<br><br>**File Biên bản thanh lý**:<br>Cho phép upload file tài liệu<br>Giới hạn upload tối đa 5 file<br>Định dạng PDF, Doc, PNG => upload sai hiển thị thông báo: Chỉ upload định dạng PDF, Doc, PNG<br>Dung lượng tập tin tối đa 5MB => upload tối đa hiển thị thông báo: File tối đa 5MB<br>Bắt buộc upload => để trống hiển thị thông báo: Vui lòng bổ sung file Biên bản thanh lý.<br><br>Kèm 2 lựa chọn:<br>Xác nhận => đóng popup và lưu lại dữ liệu. Thay đổi trạng thái của hợp đồng là “Thanh lý”<br>Đóng => Đóng popup | |

**Exception Flows**:  
*(Trống)*

---

### 1.2. [UC-HĐ002] Xem chi tiết hợp đồng khách hàng doanh nghiệp

**Usecase ID & Name**: [UC-HĐ002] Xem chi tiết hợp đồng  
**Description**:  
- **Mục đích**: Xem chi tiết hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình danh sách Hợp đồng.  
**Expected Result**:  
- Người dùng xem chi tiết hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  

**Screen Description**:  
**Screen 1**:

| # | Name | Type | Note |
|---|------|------|------|
| 1 | Tab: Thông tin chung, Phụ lục hợp đồng | Button | - Bấm để xem thông tin hợp đồng hoặc danh sách phụ lục hợp đồng<br>- Xem danh sách phụ lục chi tiết tại UC Xem danh sách phụ lục hợp đồng |
| 2 | Doanh nghiệp | Droplist + Searchbox | Không cho phép cập nhật |
| 3 | Số hợp đồng | Text field | Không cho phép cập nhật |
| 4 | Tên hợp đồng | Text field | Không cho phép cập nhật |
| 5 | Dịch vụ | Droplist | Không cho phép cập nhật |
| 6 | File hợp đồng | Button | - Không cho phép cập nhật<br>- Cho phép xem trước file đã upload: Hệ thống thực hiện mở file xem trước tại tab mới và cho phép tải về |
| 7 | Loại hợp đồng | Droplist | Không cho phép cập nhật |
| 8 | Ngày hiệu lực | Calendar picker | - Không cho phép cập nhật<br>- Format: dd/mm/yyyy |
| 9 | Ngày hết hạn | Calendar picker | - Không cho phép cập nhật<br>- Format: dd/mm/yyyy |
| 10 | Ghi chú | Textarea | Không cho phép cập nhật |
| 11 | Trạng thái | Label | Hiển thị trạng thái hợp đồng |
| 12 | Ngày thanh lý | Label | - Hiển thị khi hợp đồng ở trạng thái “Thanh lý”<br>- Không cho phép cập nhật<br>- Format: dd/mm/yyyy |
| 13 | File Biên bản thanh lý | Label | - Hiển thị khi hợp đồng ở trạng thái “Thanh lý”<br>- Không cho phép cập nhật |
| 14 | Quay lại | Button | Điều hướng về màn hình trước đó |
| 15 | Các thao tác khác | Button | Xem tại Bảng trạng thái và thao tác tương ứng |

**Bảng trạng thái và thao tác tương ứng**:

| Trạng thái | Action | Notes and References |
|------------|--------|----------------------|
| Hoạt động | Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Hết hiệu lực | Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Thanh lý | | |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Danh sách Hợp đồng bấm Thao tác Xem tại hợp đồng muốn thực hiện | |
| BF-2 | Hệ thống | Hiển thị giao diện Xem chi tiết hợp đồng | |
|  |  | - AF-1: Người dùng bấm Cập nhật => Xem chi tiết tại UC Cập nhật<br>- AF-2: Người dùng bấm Thêm phụ lục => Xem chi tiết tại UC Thêm mới phụ lục<br>- AF-3: Người dùng bấm Thanh lý hợp đồng => Xem chi tiết tại UC Thêm mới phụ lục | |

**Alternative Flows**:

**AF-3: Người dùng bấm Thanh lý hợp đồng**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-1-1 | Hệ thống | Hiển thị popup<br>Yêu cầu nhập các thông tin:<br>**Ngày thanh lý**:<br>Calendar picker<br>Cho phép chọn: Ngày/ tháng/ năm<br>Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường].<br>Placeholder: Chọn [tên trường]<br><br>**File Biên bản thanh lý**:<br>Cho phép upload file tài liệu<br>Giới hạn upload tối đa 5 file<br>Định dạng PDF, Doc, PNG => upload sai hiển thị thông báo: Chỉ upload định dạng PDF, Doc, PNG<br>Dung lượng tập tin tối đa 5MB => upload tối đa hiển thị thông báo: File tối đa 5MB<br>Bắt buộc upload => để trống hiển thị thông báo: Vui lòng bổ sung file Biên bản thanh lý.<br><br>Kèm 2 lựa chọn:<br>Xác nhận => đóng popup và lưu lại dữ liệu. Thay đổi trạng thái của hợp đồng là “Thanh lý”<br>Đóng => Đóng popup | |

**Exception Flows**:  
*(Trống)*

---

### 1.3. [UC-HĐ003] Thêm mới hợp đồng khách hàng doanh nghiệp

**Usecase ID & Name**: [UC-HĐ003] Thêm mới hợp đồng  
**Description**:  
- **Mục đích**: Thêm mới hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình danh sách Hợp đồng.  
**Expected Result**:  
- Người dùng thêm mới hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  

**Screen Description**:  
**Screen 1**:

| # | Name | Type | Note |
|---|------|------|------|
| 1 | Doanh nghiệp | Droplist + Searchbox | - Droplist có giá trị là các Doanh nghiệp trong trạng thái Hoạt động<br>- Bắt buộc chọn => để trống hiển thị thông báo: [Tên trường] không được để trống<br>- Placeholder: Chọn [tên trường] |
| 2 | Số hợp đồng | Text field | - Cho phép nhập số hợp đồng<br>- Giới hạn 25 ký tự<br>- Bắt buộc nhập => để trống hiển thị thông báo: [Tên trường] không được để trống<br>- Placeholder: Nhập [tên trường] |
| 3 | Tên hợp đồng | Text field | - Cho phép nhập tên hợp đồng<br>- Giới hạn 50 ký tự<br>- Placeholder: Nhập [tên trường] |
| 4 | Dịch vụ | Droplist | - Giá trị: SMS, ZNS, Tất cả dịch vụ<br>- Cho phép chọn nhiều<br>- Placeholder: Chọn [tên trường]<br>- Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường]. |
| 5 | File hợp đồng | Button | - Cho phép upload file tài liệu<br>- Giới hạn upload tối đa 5 file<br>- Định dạng PDF, Doc, PNG => upload sai hiển thị thông báo: Chỉ upload định dạng PDF, Doc, PNG<br>- Dung lượng tập tin tối đa 5MB => upload tối đa hiển thị thông báo: File tối đa 5MB<br>- Bắt buộc upload => để trống hiển thị thông báo: Vui lòng bổ sung file hợp đồng. |
| 6 | | Hyperlink | - File đã upload<br>- Cho phép xóa hoặc xem trước file đã upload<br>- Xem trước: Hệ thống thực hiện mở file xem trước tại tab mới và cho phép tải về |
| 7 | Loại hợp đồng | Droplist | - Giá trị: Có thời hạn (mặc định chọn), Vô thời hạn<br>- Chỉ chọn 1<br>- Placeholder: Chọn [tên trường] |
| 8 | Ngày hiệu lực | Calendar picker | - Cho phép chọn: Ngày/ tháng/ năm<br>- Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường].<br>- Placeholder: Chọn [tên trường] |
| 9 | Ngày hết hạn | Calendar picker | - Hiển thị khi người dùng chọn Loại hợp đồng là “Có thời hạn”<br>- Cho phép chọn: Ngày/ tháng/ năm<br>- Không cho phép chọn thời gian nhỏ hơn Ngày hiệu lực => chọn sai hiển thị thông báo: Ngày hết hạn không hợp lệ<br>- Placeholder: Chọn [tên trường] |
| 10 | Ghi chú | Textarea | - Cho phép nhập ghi chú<br>- Giới hạn 250 ký tự<br>- Placeholder: Nhập [tên trường] |
| 11 | Hủy | Button | - Trường hợp chưa nhập hay thay đổi gì thì hệ thống điều hướng về màn hình danh sách và không lưu lại dữ liệu.<br>- Trường hợp đã nhập dữ liệu thì hiển thị popup thông báo xác nhận hủy: "Bạn có chắc chắn muốn hủy bỏ hành động đang thực hiện mà không lưu?" kèm 2 lựa chọn:<br>Xác nhận => điều hướng về màn hình danh sách và không lưu lại dữ liệu<br>Hủy => Đóng popup |
| 12 | Lưu | Button | - Điều hướng về màn hình xem chi tiết và lưu lại dữ liệu<br>- Báo toast: Thêm mới thành công |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Danh sách Hợp đồng bấm Thêm hợp đồng | |
| BF-2 | Hệ thống | Hiển thị giao diện thêm mới hợp đồng | |
| BF-3 | Người dùng | Nhập các thông tin | |
| BF-4 | Người dùng | Bấm Lưu<br>AF-1: Người dùng bấm Hủy | |
| BF-5 | Hệ thống | Thực hiện validate các trường thông tin nhập | |
| BF-6 | Hệ thống | - Điều hướng về màn hình xem chi tiết và lưu lại dữ liệu<br>- Báo toast: Thêm mới thành công | |

**Alternative Flows**:

**AF-1: Người dùng bấm Hủy**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-1-1 | Hệ thống | Trường hợp chưa nhập hay thay đổi gì thì hệ thống điều hướng về màn hình danh sách và không lưu lại dữ liệu.<br>Trường hợp đã nhập dữ liệu thì hiển thị popup thông báo xác nhận hủy: "Bạn có chắc chắn muốn hủy bỏ hành động đang thực hiện mà không lưu?" kèm 2 lựa chọn:<br>Xác nhận => điều hướng về màn hình danh sách và không lưu lại dữ liệu<br>Hủy => Đóng popup | |

**Exception Flows**:  
*(Trống)*

---

### 1.4. [UC-HĐ004] Cập nhật hợp đồng khách hàng doanh nghiệp

**Usecase ID & Name**: [UC-HĐ004] Cập nhật hợp đồng  
**Description**:  
- **Mục đích**: Cập nhật hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình danh sách Hợp đồng.  
- Chỉ cho phép cập nhật Hợp đồng trong trạng thái: Hoạt động, Hết hiệu lực  
**Expected Result**:  
- Người dùng cập nhật hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  

**Screen Description**:  
**Screen 1**:

| # | Name | Type | Note |
|---|------|------|------|
| 1 | Doanh nghiệp | Droplist + Searchbox | Không cho phép cập nhật trường này |
| 2 | Số hợp đồng | Text field | - Cho phép nhập số hợp đồng<br>- Giới hạn 25 ký tự<br>- Bắt buộc nhập => để trống hiển thị thông báo: [Tên trường] không được để trống<br>- Placeholder: Nhập [tên trường] |
| 3 | Tên hợp đồng | Text field | - Cho phép nhập tên hợp đồng<br>- Giới hạn 50 ký tự<br>- Placeholder: Nhập [tên trường] |
| 4 | Dịch vụ | Droplist | - Giá trị: SMS, ZNS, Tất cả dịch vụ<br>- Cho phép chọn nhiều<br>- Placeholder: Chọn [tên trường]<br>- Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường]. |
| 5 | File hợp đồng | Button | - Cho phép upload file tài liệu<br>- Giới hạn upload tối đa 5 file<br>- Định dạng PDF, Doc, PNG => upload sai hiển thị thông báo: Chỉ upload định dạng PDF, Doc, PNG<br>- Dung lượng tập tin tối đa 5MB => upload tối đa hiển thị thông báo: File tối đa 5MB<br>- Bắt buộc upload => để trống hiển thị thông báo: Vui lòng bổ sung file hợp đồng. |
| 6 | | Hyperlink | - File đã upload<br>- Cho phép xóa hoặc xem trước file đã upload<br>- Xem trước: Hệ thống thực hiện mở file xem trước tại tab mới và cho phép tải về |
| 7 | Loại hợp đồng | Droplist | - Giá trị: Có thời hạn (mặc định chọn), Vô thời hạn<br>- Chỉ chọn 1<br>- Placeholder: Chọn [tên trường] |
| 8 | Ngày hiệu lực | Calendar picker | - Cho phép chọn: Ngày/ tháng/ năm<br>- Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường].<br>- Placeholder: Chọn [tên trường] |
| 9 | Ngày hết hạn | Calendar picker | - Hiển thị khi người dùng chọn Loại hợp đồng là “Có thời hạn”<br>- Cho phép chọn: Ngày/ tháng/ năm<br>- Không cho phép chọn thời gian nhỏ hơn Ngày hiệu lực => chọn sai hiển thị thông báo: Ngày hết hạn không hợp lệ<br>- Placeholder: Chọn [tên trường] |
| 10 | Ghi chú | Textarea | - Cho phép nhập ghi chú<br>- Giới hạn 250 ký tự<br>- Placeholder: Nhập [tên trường] |
| 11 | Trạng thái | Label | Hiển thị trạng thái hợp đồng |
| 12 | Hủy | Button | - Trường hợp chưa nhập hay thay đổi gì thì hệ thống điều hướng về màn hình danh sách và không lưu lại dữ liệu.<br>- Trường hợp đã nhập dữ liệu thì hiển thị popup thông báo xác nhận hủy: "Bạn có chắc chắn muốn hủy bỏ hành động đang thực hiện mà không lưu?" kèm 2 lựa chọn:<br>Xác nhận => điều hướng về màn hình danh sách và không lưu lại dữ liệu<br>Hủy => Đóng popup |
| 13 | Lưu | Button | - Điều hướng về màn hình xem chi tiết và lưu lại dữ liệu<br>- Báo toast: Cập nhật thành công |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Danh sách Hợp đồng bấm Thao tác Cập nhật tại hợp đồng muốn thực hiện | |
| BF-2 | Hệ thống | Hiển thị giao diện cập nhật hợp đồng | |
| BF-3 | Người dùng | Nhập các thông tin | |
| BF-4 | Người dùng | Bấm Lưu<br>AF-1: Người dùng bấm Hủy | |
| BF-5 | Hệ thống | Thực hiện validate các trường thông tin nhập | |
| BF-6 | Hệ thống | - Điều hướng về màn hình xem chi tiết và lưu lại dữ liệu<br>- Báo toast: Cập nhật thành công | |

**Alternative Flows**:

**AF-1: Người dùng bấm Hủy**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-1-1 | Hệ thống | Trường hợp chưa nhập hay thay đổi gì thì hệ thống điều hướng về màn hình danh sách và không lưu lại dữ liệu.<br>Trường hợp đã nhập dữ liệu thì hiển thị popup thông báo xác nhận hủy: "Bạn có chắc chắn muốn hủy bỏ hành động đang thực hiện mà không lưu?" kèm 2 lựa chọn:<br>Xác nhận => điều hướng về màn hình danh sách và không lưu lại dữ liệu<br>Hủy => Đóng popup | |

**Exception Flows**:  
*(Trống)*

---

### 1.5. [UC-HĐ005] Xem danh sách phụ lục hợp đồng khách hàng doanh nghiệp

**Usecase ID & Name**: [UC-HĐ005] Xem danh sách phụ lục hợp đồng  
**Description**:  
- **Mục đích**: Xem danh sách phụ lục hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình đăng nhập hệ thống.  
**Expected Result**:  
- Người dùng xem danh sách phụ lục hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  
- [Flow trạng thái]  

**Screen Description**:  
**Screen 1: Màn hình danh sách**

| # | Name | Type | Note |
|---|------|------|------|
| 1 | STT | Label | Hệ thống tự gen |
| 2 | Số phụ lục | Label | Hiển thị số phụ lục hợp đồng |
| 3 | Tên phụ lục | Label | Hiển thị tên hợp đồng |
| 4 | Ghi chú | Label | Hiển thị tên doanh nghiệp |
| 5 | Ngày hiệu lực PL | Label | - Thời gian hiệu lực của hợp đồng<br>- Định dạng: dd/mm/yyyy |
| 6 | Thao tác | Button | Các thao tác:<br>Xem (mô tả chi tiết tại AF)<br>Xóa (ẩn khi hợp đồng ở trạng thái Thanh lý) |
| 7 | Searchbox | Button | - Hintext: Tìm kiếm theo số hợp đồng, tên hợp đồng<br>- Mô tả chi tiết tại Alternative Flows |
| 8 | Thêm Phụ lục | Button | - Xem chi tiết tại UC Thêm mới Phụ lục<br>- Ẩn khi hợp đồng ở trạng thái “Thanh lý” |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Trang chủ chọn Hợp đồng > Doanh nghiệp > Chọn hợp đồng muốn thực hiện xem Phụ lục > Chọn tab Phụ lục hợp đồng | |
| BF-2 | Hệ thống | - Hiển thị giao diện danh sách phụ lục hợp đồng<br>- Ưu tiên phụ lục hợp đồng có thời gian tạo mới nhất lên đầu | |
|  |  | - AF-1: Người dùng Thêm phụ lục => Xem tại UC thêm phụ lục<br>- AF-2: Người dùng bấm Xem<br>- AF-3: Người dùng bấm Xóa<br>- AF-4: Người dùng nhập giá trị và bấm tìm kiếm | |

**Alternative Flows**:

**AF-2: Người dùng bấm Xem**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-5-1 | Hệ thống | Hệ thống thực hiện mở file xem trước tại tab mới và cho phép tải về | |

**AF-3: Người dùng bấm Xóa**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-5-1 | Hệ thống | Hiển thị popup: "Bạn có chắc chắn muốn xóa [Tên đối tượng đang thao tác]?" kèm 2 lựa chọn:<br>Hủy => Đóng popup<br>Đồng ý => Đóng popup và lưu lại dữ liệu<br>Báo toast: Xóa thành công | |

**AF-5: Người dùng nhập giá trị và bấm tìm kiếm**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-5-1 | Hệ thống | - Hiển thị các kết quả có phụ lục đúng tuyệt đối với Số phụ lục và đúng tương đối với Tên phụ lục<br>- Trường hợp không có kết quả hiển thị Không có dữ liệu để hiển thị | |

**Exception Flows**:  
*(Trống)*

---

### 1.6. [UC-HĐ006] Thêm mới phụ lục hợp đồng khách hàng doanh nghiệp

**Usecase ID & Name**: [UC-HĐ006] Thêm mới phụ lục hợp đồng  
**Description**:  
- **Mục đích**: Thêm mới phụ lục hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình danh sách Hợp đồng.  
**Expected Result**:  
- Người dùng thêm mới phụ lục hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  

**Screen Description**:  
**Screen 1**:

| # | Name | Type | Note |
|---|------|------|------|
| 1 | Số phụ lục | Text field | - Cho phép nhập số phụ lục<br>- Giới hạn 25 ký tự<br>- Bắt buộc nhập => để trống hiển thị thông báo: [Tên trường] không được để trống<br>- Placeholder: Nhập [tên trường] |
| 2 | Tên phụ lục | Text field | - Cho phép nhập tên phụ lục<br>- Giới hạn 50 ký tự<br>- Placeholder: Nhập [tên trường] |
| 3 | Ngày hiệu lực PL | Calendar picker | - Cho phép chọn: Ngày/ tháng/ năm<br>- Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường].<br>- Placeholder: Chọn [tên trường] |
| 4 | Ghi chú | Textarea | - Cho phép nhập ghi chú<br>- Giới hạn 250 ký tự<br>- Placeholder: Nhập [tên trường] |
| 5 | File phụ lục | Button | - Cho phép upload file tài liệu<br>- Giới hạn upload tối đa 5 file<br>- Định dạng PDF, Doc, PNG => upload sai hiển thị thông báo: Chỉ upload định dạng PDF, Doc, PNG<br>- Dung lượng tập tin tối đa 5MB => upload tối đa hiển thị thông báo: File tối đa 5MB<br>- Bắt buộc upload => để trống hiển thị thông báo: Vui lòng bổ sung file phụ lục. |
| 6 | | Hyperlink | - File đã upload<br>- Cho phép xóa hoặc xem trước file đã upload<br>- Xem trước: Hệ thống thực hiện mở file xem trước tại tab mới và cho phép tải về |
| 7 | Đóng, close (x) | Button | Đóng popup |
| 8 | Lưu | Button | - Điều hướng về màn hình danh sách và lưu lại dữ liệu<br>- Báo toast: Thêm mới thành công |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Trang chủ chọn Hợp đồng > Doanh nghiệp > Chọn hợp đồng muốn thực hiện thêm Phụ lục > Chọn tab Phụ lục hợp đồng > Bấm Thêm phụ lục | |
| BF-2 | Hệ thống | Hiển thị giao diện thêm mới phụ lục | |
| BF-3 | Người dùng | Nhập các thông tin | |
| BF-4 | Người dùng | Bấm Lưu<br>AF-1: Người dùng bấm Đóng, close (x) | |
| BF-5 | Hệ thống | Thực hiện validate các trường thông tin nhập | |
| BF-6 | Hệ thống | - Điều hướng về màn hình danh sách và lưu lại dữ liệu<br>- Báo toast: Thêm mới thành công | |

**Alternative Flows**:

**AF-1: Người dùng bấm Đóng, close (x)**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-1-1 | Hệ thống | Đóng popup | |

**Exception Flows**:  
*(Trống)*

---

## 2. QL Hợp đồng đại lý - Actor: Vận hành

### 2.1. [UC-HĐ007] Xem danh sách hợp đồng của đại lý

**Usecase ID & Name**: [UC-HĐ007] Xem danh sách hợp đồng  
**Description**:  
- **Mục đích**: Xem danh sách hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình đăng nhập hệ thống.  
**Expected Result**:  
- Người dùng xem danh sách hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  
- [Flow trạng thái]  

**Screen Description**:  
**Screen 1: Màn hình danh sách**

| # | Name | Type | Note |
|---|------|------|------|
| 1 | STT | Label | Hệ thống tự gen |
| 2 | Số hợp đồng | Label | Hiển thị số hợp đồng |
| 3 | Tên hợp đồng | Label | Hiển thị tên hợp đồng |
| 4 | Tên đại lý | Label | Hiển thị tên đại lý |
| 5 | Dịch vụ | Label | Hiển thị các dịch vụ của hợp đồng |
| 6 | Loại hợp đồng | Label | Hiển thị loại hợp đồng bao gồm các giá trị: Có thời hạn, Vô thời hạn |
| 7 | Ngày hiệu lực | Label | - Thời gian hiệu lực của hợp đồng<br>- Định dạng: dd/mm/yyyy |
| 8 | Ngày hết hạn | Label | - Thời gian hết hạn của hợp đồng<br>- Định dạng: dd/mm/yyyy |
| 9 | Trạng thái | Label | Trạng thái hiện tại của hợp đồng bao gồm các giá trị: Hoạt động, Hết hiệu lực, Thanh lý |
| 10 | Thao tác | Button | Xem tại Bảng trạng thái và thao tác tương ứng |
| 11 | Bộ lọc | Button | - Bấm hiển thị danh sách tiêu chí lọc<br>- Mô tả chi tiết tại Alternative Flows |
| 12 | Searchbox | Button | - Hintext: Tìm kiếm theo số hợp đồng, tên hợp đồng<br>- Mô tả chi tiết tại Alternative Flows |
| 13 | Thêm hợp đồng | Button | Xem chi tiết tại UC Thêm mới hợp đồng |

**Bảng trạng thái và thao tác tương ứng**:

| Trạng thái | Action | Notes and References |
|------------|--------|----------------------|
| Hoạt động | Xem, Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Hết hiệu lực | Xem, Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Thanh lý | Xem | |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Trang chủ chọn Hợp đồng > Đại lý | |
| BF-2 | Hệ thống | - Hiển thị giao diện danh sách Hợp đồng<br>- Ưu tiên Hợp đồng có thời gian cập nhật mới nhất lên đầu | |
|  |  | - AF-1: Người dùng bấm Xem chi tiết => Xem chi tiết tại UC Xem chi tiết<br>- AF-2: Người dùng bấm Cập nhật => Xem chi tiết tại UC Cập nhật<br>- AF-3: Người dùng bấm Thêm hợp đồng => Xem chi tiết tại UC Thêm mới hợp đồng<br>- AF-4: Người dùng bấm Thanh lý hợp đồng<br>- AF-5: Người dùng nhập giá trị và bấm tìm kiếm<br>- AF-6: Người dùng bấm Bộ lọc<br>- AF-7: Người dùng bấm Thêm phụ lục => Xem tại UC Thêm phụ lục | |

**Alternative Flows**:

**AF-4: Người dùng bấm Thanh lý hợp đồng**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-1-1 | Hệ thống | Hiển thị popup<br>Yêu cầu nhập các thông tin:<br>**Ngày thanh lý**:<br>Calendar picker<br>Cho phép chọn: Ngày/ tháng/ năm<br>Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường].<br>- Placeholder: Chọn [tên trường]<br><br>**File Biên bản thanh lý**:<br>Cho phép upload file tài liệu<br>Giới hạn upload tối đa 5 file<br>Định dạng PDF, Doc, PNG => upload sai hiển thị thông báo: Chỉ upload định dạng PDF, Doc, PNG<br>Dung lượng tập tin tối đa 5MB => upload tối đa hiển thị thông báo: File tối đa 5MB<br>Bắt buộc upload => để trống hiển thị thông báo: Vui lòng bổ sung file Biên bản thanh lý.<br><br>Kèm 2 lựa chọn:<br>Xác nhận => đóng popup và lưu lại dữ liệu. Thay đổi trạng thái của hợp đồng là “Thanh lý”<br>Đóng => Đóng popup | |

**AF-5: Người dùng nhập giá trị và bấm tìm kiếm**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-5-1 | Hệ thống | - Hiển thị các kết quả có Hợp đồng đúng tuyệt đối với Số hợp đồng và đúng tương đối với Tên hợp đồng<br>- Trường hợp không có kết quả hiển thị Không có dữ liệu để hiển thị | |

**AF-6: Người dùng bấm Bộ lọc**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-6-1 | Hệ thống | Hiển thị popup bộ lọc cho phép lọc các thông tin:<br>1. **Đại lý**:<br>Drop List + Search box tương đối<br>Giá trị: Các đại lý hệ thống quản lý<br>Placeholder: Chọn giá trị<br>2. **Dịch vụ**:<br>Droplist<br>Giá trị: SMS, ZNS<br>Placeholder: Chọn giá trị<br>3. **Trạng thái**:<br>Droplist<br>Giá trị: Hoạt động, Hết hiệu lực, Thanh lý<br>Placeholder: Chọn giá trị | |
| AF-6-2 | Người dùng | Bấm Áp dụng<br>Bấm Thiết lập lại: Hệ thống thực hiện xóa các giá trị lọc | |
| AF-6-3 | Hệ thống | - Hiển thị các kết quả Hợp đồng chính xác với bộ lọc<br>- Trường hợp không có kết quả hiển thị Không có dữ liệu để hiển thị | |

**Exception Flows**:  
*(Trống)*

---

**Xem tại Quản lý Đại lý**

**Usecase ID & Name**: [UC-TC001] Xem danh sách hợp đồng  
**Description**:  
- **Mục đích**: Xem danh sách hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình đăng nhập hệ thống.  
**Expected Result**:  
- Người dùng xem danh sách hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  
- [Flow trạng thái]  

**Screen Description**:  
**Screen 1: Màn hình danh sách**

| # | Name | Type | Note |
|---|------|------|------|
| 1 | STT | Label | Hệ thống tự gen |
| 2 | Số hợp đồng | Label | Hiển thị số hợp đồng |
| 3 | Tên hợp đồng | Label | Hiển thị tên hợp đồng |
| 4 | Dịch vụ | Label | Hiển thị các dịch vụ của hợp đồng |
| 5 | Loại hợp đồng | Label | Hiển thị loại hợp đồng bao gồm các giá trị: Có thời hạn, Vô thời hạn |
| 6 | Ngày hiệu lực | Label | - Thời gian hiệu lực của hợp đồng<br>- Định dạng: dd/mm/yyyy |
| 7 | Ngày hết hạn | Label | - Thời gian hết hạn của hợp đồng<br>- Định dạng: dd/mm/yyyy |
| 8 | Trạng thái | Label | Trạng thái hiện tại của hợp đồng bao gồm các giá trị: Hoạt động, Hết hiệu lực, Thanh lý |
| 9 | Thao tác | Button | Xem tại Bảng trạng thái và thao tác tương ứng |
| 10 | Thêm hợp đồng | Button | Xem chi tiết tại UC Thêm mới hợp đồng<br>**Lưu ý**: Khi thêm hợp đồng tại đây, màn thêm mới hợp đồng sẽ tự động chọn Doanh nghiệp tương ứng và cho phép sửa lại |

**Bảng trạng thái và thao tác tương ứng**:

| Trạng thái | Action | Notes and References |
|------------|--------|----------------------|
| Hoạt động | Xem, Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Hết hiệu lực | Xem, Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Thanh lý | Xem | |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Trang chủ chọn Đại lý > Xem thông tin chi tiết đại lý muốn xem hợp đồng > Tab Hợp đồng | |
| BF-2 | Hệ thống | - Hiển thị giao diện danh sách Hợp đồng<br>- Ưu tiên Hợp đồng có thời gian cập nhật mới nhất lên đầu | |
|  |  | - AF-1: Người dùng bấm Xem => Xem chi tiết tại UC Xem chi tiết<br>- AF-2: Người dùng bấm Cập nhật => Xem chi tiết tại UC Cập nhật<br>- AF-3: Người dùng bấm Thêm hợp đồng => Xem chi tiết tại UC Thêm mới hợp đồng<br>- AF-4: Người dùng bấm Thanh lý hợp đồng<br>- AF-5: Người dùng bấm Thêm phụ lục => Xem tại UC Thêm phụ lục | |

**Alternative Flows**:

**AF-4: Người dùng bấm Thanh lý hợp đồng**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-1-1 | Hệ thống | Hiển thị popup<br>Yêu cầu nhập các thông tin:<br>**Ngày thanh lý**:<br>Calendar picker<br>Cho phép chọn: Ngày/ tháng/ năm<br>Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường].<br>- Placeholder: Chọn [tên trường]<br><br>**File Biên bản thanh lý**:<br>Cho phép upload file tài liệu<br>Giới hạn upload tối đa 5 file<br>Định dạng PDF, Doc, PNG => upload sai hiển thị thông báo: Chỉ upload định dạng PDF, Doc, PNG<br>Dung lượng tập tin tối đa 5MB => upload tối đa hiển thị thông báo: File tối đa 5MB<br>Bắt buộc upload => để trống hiển thị thông báo: Vui lòng bổ sung file Biên bản thanh lý.<br><br>Kèm 2 lựa chọn:<br>Xác nhận => đóng popup và lưu lại dữ liệu. Thay đổi trạng thái của hợp đồng là “Thanh lý”<br>Đóng => Đóng popup | |

**Exception Flows**:  
*(Trống)*

---

### 2.2. [UC-HĐ008] Xem chi tiết hợp đồng của đại lý

**Usecase ID & Name**: [UC-HĐ008] Xem chi tiết hợp đồng  
**Description**:  
- **Mục đích**: Xem chi tiết hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình danh sách Hợp đồng.  
**Expected Result**:  
- Người dùng xem chi tiết hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  

**Screen Description**:  
**Screen 1**:

| # | Name | Type | Note |
|---|------|------|------|
| 1 | Tab: Thông tin chung, Phụ lục hợp đồng | Button | - Bấm để xem thông tin hợp đồng hoặc danh sách phụ lục hợp đồng<br>- Xem danh sách phụ lục chi tiết tại UC Xem danh sách phụ lục hợp đồng |
| 2 | Đại lý | Droplist + Searchbox | Không cho phép cập nhật |
| 3 | Số hợp đồng | Text field | Không cho phép cập nhật |
| 4 | Tên hợp đồng | Text field | Không cho phép cập nhật |
| 5 | Dịch vụ | Droplist | Không cho phép cập nhật |
| 6 | File hợp đồng | Button | - Không cho phép cập nhật<br>- Cho phép xem trước file đã upload: Hệ thống thực hiện mở file xem trước tại tab mới và cho phép tải về |
| 7 | Loại hợp đồng | Droplist | Không cho phép cập nhật |
| 8 | Ngày hiệu lực | Calendar picker | - Không cho phép cập nhật<br>- Format: dd/mm/yyyy |
| 9 | Ngày hết hạn | Calendar picker | - Không cho phép cập nhật<br>- Format: dd/mm/yyyy |
| 10 | Ghi chú | Textarea | Không cho phép cập nhật |
| 11 | Trạng thái | Label | Hiển thị trạng thái hợp đồng |
| 12 | Ngày thanh lý | Label | - Hiển thị khi hợp đồng ở trạng thái “Thanh lý”<br>- Không cho phép cập nhật<br>- Format: dd/mm/yyyy |
| 13 | File Biên bản thanh lý | Label | - Hiển thị khi hợp đồng ở trạng thái “Thanh lý”<br>- Không cho phép cập nhật |
| 14 | Quay lại | Button | Điều hướng về màn hình trước đó |
| 15 | Các thao tác khác | Button | Xem tại Bảng trạng thái và thao tác tương ứng |

**Bảng trạng thái và thao tác tương ứng**:

| Trạng thái | Action | Notes and References |
|------------|--------|----------------------|
| Hoạt động | Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Hết hiệu lực | Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Thanh lý | | |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Danh sách Hợp đồng bấm Thao tác Xem tại hợp đồng muốn thực hiện | |
| BF-2 | Hệ thống | Hiển thị giao diện Xem chi tiết hợp đồng | |
|  |  | - AF-1: Người dùng bấm Cập nhật => Xem chi tiết tại UC Cập nhật<br>- AF-2: Người dùng bấm Thêm phụ lục => Xem chi tiết tại UC Thêm mới phụ lục<br>- AF-3: Người dùng bấm Thanh lý hợp đồng => Xem chi tiết tại UC Thêm mới phụ lục | |

**Alternative Flows**:

**AF-3: Người dùng bấm Thanh lý hợp đồng**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-1-1 | Hệ thống | Hiển thị popup<br>Yêu cầu nhập các thông tin:<br>**Ngày thanh lý**:<br>Calendar picker<br>Cho phép chọn: Ngày/ tháng/ năm<br>Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường].<br>Placeholder: Chọn [tên trường]<br><br>**File Biên bản thanh lý**:<br>Cho phép upload file tài liệu<br>Giới hạn upload tối đa 5 file<br>Định dạng PDF, Doc, PNG => upload sai hiển thị thông báo: Chỉ upload định dạng PDF, Doc, PNG<br>Dung lượng tập tin tối đa 5MB => upload tối đa hiển thị thông báo: File tối đa 5MB<br>Bắt buộc upload => để trống hiển thị thông báo: Vui lòng bổ sung file Biên bản thanh lý.<br><br>Kèm 2 lựa chọn:<br>Xác nhận => đóng popup và lưu lại dữ liệu. Thay đổi trạng thái của hợp đồng là “Thanh lý”<br>Đóng => Đóng popup | |

**Exception Flows**:  
*(Trống)*

---

### 2.3. [UC-HĐ009] Thêm mới hợp đồng của đại lý

**Usecase ID & Name**: [UC-HĐ009] Thêm mới hợp đồng  
**Description**:  
- **Mục đích**: Thêm mới hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình danh sách Hợp đồng.  
**Expected Result**:  
- Người dùng thêm mới hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  

**Screen Description**:  
**Screen 1**:

| # | Name | Type | Note |
|---|------|------|------|
| 1 | Đại lý | Droplist + Searchbox | - Droplist có giá trị là các Đại lý trong trạng thái Hoạt động<br>- Bắt buộc chọn => để trống hiển thị thông báo: [Tên trường] không được để trống<br>- Placeholder: Chọn [tên trường] |
| 2 | Số hợp đồng | Text field | - Cho phép nhập số hợp đồng<br>- Giới hạn 25 ký tự<br>- Bắt buộc nhập => để trống hiển thị thông báo: [Tên trường] không được để trống<br>- Placeholder: Nhập [tên trường] |
| 3 | Tên hợp đồng | Text field | - Cho phép nhập tên hợp đồng<br>- Giới hạn 50 ký tự<br>- Placeholder: Nhập [tên trường] |
| 4 | Dịch vụ | Droplist | - Giá trị: SMS, ZNS, Tất cả dịch vụ<br>- Cho phép chọn nhiều<br>- Placeholder: Chọn [tên trường]<br>- Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường]. |
| 5 | File hợp đồng | Button | - Cho phép upload file tài liệu<br>- Giới hạn upload tối đa 5 file<br>- Định dạng PDF, Doc, PNG => upload sai hiển thị thông báo: Chỉ upload định dạng PDF, Doc, PNG<br>- Dung lượng tập tin tối đa 5MB => upload tối đa hiển thị thông báo: File tối đa 5MB<br>- Bắt buộc upload => để trống hiển thị thông báo: Vui lòng bổ sung file hợp đồng. |
| 6 | | Hyperlink | - File đã upload<br>- Cho phép xóa hoặc xem trước file đã upload<br>- Xem trước: Hệ thống thực hiện mở file xem trước tại tab mới và cho phép tải về |
| 7 | Loại hợp đồng | Droplist | - Giá trị: Có thời hạn (mặc định chọn), Vô thời hạn<br>- Chỉ chọn 1<br>- Placeholder: Chọn [tên trường] |
| 8 | Ngày hiệu lực | Calendar picker | - Cho phép chọn: Ngày/ tháng/ năm<br>- Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường].<br>- Placeholder: Chọn [tên trường] |
| 9 | Ngày hết hạn | Calendar picker | - Hiển thị khi người dùng chọn Loại hợp đồng là “Có thời hạn”<br>- Cho phép chọn: Ngày/ tháng/ năm<br>- Không cho phép chọn thời gian nhỏ hơn Ngày hiệu lực => chọn sai hiển thị thông báo: Ngày hết hạn không hợp lệ<br>- Placeholder: Chọn [tên trường] |
| 10 | Ghi chú | Textarea | - Cho phép nhập ghi chú<br>- Giới hạn 250 ký tự<br>- Placeholder: Nhập [tên trường] |
| 11 | Hủy | Button | - Trường hợp chưa nhập hay thay đổi gì thì hệ thống điều hướng về màn hình danh sách và không lưu lại dữ liệu.<br>- Trường hợp đã nhập dữ liệu thì hiển thị popup thông báo xác nhận hủy: "Bạn có chắc chắn muốn hủy bỏ hành động đang thực hiện mà không lưu?" kèm 2 lựa chọn:<br>Xác nhận => điều hướng về màn hình danh sách và không lưu lại dữ liệu<br>Hủy => Đóng popup |
| 12 | Lưu | Button | - Điều hướng về màn hình xem chi tiết và lưu lại dữ liệu<br>- Báo toast: Thêm mới thành công |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Danh sách Hợp đồng bấm Thêm hợp đồng | |
| BF-2 | Hệ thống | Hiển thị giao diện thêm mới hợp đồng | |
| BF-3 | Người dùng | Nhập các thông tin | |
| BF-4 | Người dùng | Bấm Lưu<br>AF-1: Người dùng bấm Hủy | |
| BF-5 | Hệ thống | Thực hiện validate các trường thông tin nhập | |
| BF-6 | Hệ thống | - Điều hướng về màn hình xem chi tiết và lưu lại dữ liệu<br>- Báo toast: Thêm mới thành công | |

**Alternative Flows**:

**AF-1: Người dùng bấm Hủy**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-1-1 | Hệ thống | Trường hợp chưa nhập hay thay đổi gì thì hệ thống điều hướng về màn hình danh sách và không lưu lại dữ liệu.<br>Trường hợp đã nhập dữ liệu thì hiển thị popup thông báo xác nhận hủy: "Bạn có chắc chắn muốn hủy bỏ hành động đang thực hiện mà không lưu?" kèm 2 lựa chọn:<br>Xác nhận => điều hướng về màn hình danh sách và không lưu lại dữ liệu<br>Hủy => Đóng popup | |

**Exception Flows**:  
*(Trống)*

---

### 2.4. [UC-HĐ010] Cập nhật hợp đồng đại lý

**Usecase ID & Name**: [UC-HĐ010] Cập nhật hợp đồng  
**Description**:  
- **Mục đích**: Cập nhật hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình danh sách Hợp đồng.  
- Chỉ cho phép cập nhật Hợp đồng trong trạng thái: Hoạt động, Hết hiệu lực  
**Expected Result**:  
- Người dùng cập nhật hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  

**Screen Description**:  
**Screen 1**:

| # | Name | Type | Note |
|---|------|------|------|
| 1 | Đại lý | Droplist + Searchbox | Không cho phép cập nhật trường này |
| 2 | Số hợp đồng | Text field | - Cho phép nhập số hợp đồng<br>- Giới hạn 25 ký tự<br>- Bắt buộc nhập => để trống hiển thị thông báo: [Tên trường] không được để trống<br>- Placeholder: Nhập [tên trường] |
| 3 | Tên hợp đồng | Text field | - Cho phép nhập tên hợp đồng<br>- Giới hạn 50 ký tự<br>- Placeholder: Nhập [tên trường] |
| 4 | Dịch vụ | Droplist | - Giá trị: SMS, ZNS, Tất cả dịch vụ<br>- Cho phép chọn nhiều<br>- Placeholder: Chọn [tên trường]<br>- Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường]. |
| 5 | File hợp đồng | Button | - Cho phép upload file tài liệu<br>- Giới hạn upload tối đa 5 file<br>- Định dạng PDF, Doc, PNG => upload sai hiển thị thông báo: Chỉ upload định dạng PDF, Doc, PNG<br>- Dung lượng tập tin tối đa 5MB => upload tối đa hiển thị thông báo: File tối đa 5MB<br>- Bắt buộc upload => để trống hiển thị thông báo: Vui lòng bổ sung file hợp đồng. |
| 6 | | Hyperlink | - File đã upload<br>- Cho phép xóa hoặc xem trước file đã upload<br>- Xem trước: Hệ thống thực hiện mở file xem trước tại tab mới và cho phép tải về |
| 7 | Loại hợp đồng | Droplist | - Giá trị: Có thời hạn (mặc định chọn), Vô thời hạn<br>- Chỉ chọn 1<br>- Placeholder: Chọn [tên trường] |
| 8 | Ngày hiệu lực | Calendar picker | - Cho phép chọn: Ngày/ tháng/ năm<br>- Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường].<br>- Placeholder: Chọn [tên trường] |
| 9 | Ngày hết hạn | Calendar picker | - Hiển thị khi người dùng chọn Loại hợp đồng là “Có thời hạn”<br>- Cho phép chọn: Ngày/ tháng/ năm<br>- Không cho phép chọn thời gian nhỏ hơn Ngày hiệu lực => chọn sai hiển thị thông báo: Ngày hết hạn không hợp lệ<br>- Placeholder: Chọn [tên trường] |
| 10 | Ghi chú | Textarea | - Cho phép nhập ghi chú<br>- Giới hạn 250 ký tự<br>- Placeholder: Nhập [tên trường] |
| 11 | Trạng thái | Label | Hiển thị trạng thái hợp đồng |
| 12 | Hủy | Button | - Trường hợp chưa nhập hay thay đổi gì thì hệ thống điều hướng về màn hình danh sách và không lưu lại dữ liệu.<br>- Trường hợp đã nhập dữ liệu thì hiển thị popup thông báo xác nhận hủy: "Bạn có chắc chắn muốn hủy bỏ hành động đang thực hiện mà không lưu?" kèm 2 lựa chọn:<br>Xác nhận => điều hướng về màn hình danh sách và không lưu lại dữ liệu<br>Hủy => Đóng popup |
| 13 | Lưu | Button | - Điều hướng về màn hình xem chi tiết và lưu lại dữ liệu<br>- Báo toast: Cập nhật thành công |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Danh sách Hợp đồng bấm Thao tác Cập nhật tại hợp đồng muốn thực hiện | |
| BF-2 | Hệ thống | Hiển thị giao diện cập nhật hợp đồng | |
| BF-3 | Người dùng | Nhập các thông tin | |
| BF-4 | Người dùng | Bấm Lưu<br>AF-1: Người dùng bấm Hủy | |
| BF-5 | Hệ thống | Thực hiện validate các trường thông tin nhập | |
| BF-6 | Hệ thống | - Điều hướng về màn hình xem chi tiết và lưu lại dữ liệu<br>- Báo toast: Cập nhật thành công | |

**Alternative Flows**:

**AF-1: Người dùng bấm Hủy**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-1-1 | Hệ thống | Trường hợp chưa nhập hay thay đổi gì thì hệ thống điều hướng về màn hình danh sách và không lưu lại dữ liệu.<br>Trường hợp đã nhập dữ liệu thì hiển thị popup thông báo xác nhận hủy: "Bạn có chắc chắn muốn hủy bỏ hành động đang thực hiện mà không lưu?" kèm 2 lựa chọn:<br>Xác nhận => điều hướng về màn hình danh sách và không lưu lại dữ liệu<br>Hủy => Đóng popup | |

**Exception Flows**:  
*(Trống)*

---

### 2.5. [UC-HĐ011] Xem danh sách phụ lục hợp đồng đại lý

**Usecase ID & Name**: [UC-HĐ011] Xem danh sách phụ lục hợp đồng  
**Description**:  
- **Mục đích**: Xem danh sách phụ lục hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình đăng nhập hệ thống.  
**Expected Result**:  
- Người dùng xem danh sách phụ lục hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  
- [Flow trạng thái]  

**Screen Description**:  
**Screen 1: Màn hình danh sách**

| # | Name | Type | Note |
|---|------|------|------|
| 1 | STT | Label | Hệ thống tự gen |
| 2 | Số phụ lục | Label | Hiển thị số hợp đồng |
| 3 | Tên phụ lục | Label | Hiển thị tên hợp đồng |
| 4 | Ghi chú | Label | Hiển thị tên doanh nghiệp |
| 5 | Ngày hiệu lực PL | Label | - Thời gian hiệu lực của hợp đồng<br>- Định dạng: dd/mm/yyyy |
| 6 | Thao tác | Button | Các thao tác:<br>Xem<br>Xóa (ẩn khi hợp đồng ở trạng thái Thanh lý) |
| 7 | Searchbox | Button | - Hintext: Tìm kiếm theo số hợp đồng, tên hợp đồng<br>- Mô tả chi tiết tại Alternative Flows |
| 8 | Thêm Phụ lục | Button | - Xem chi tiết tại UC Thêm mới Phụ lục<br>- Ẩn khi hợp đồng ở trạng thái “Thanh lý” |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Trang chủ chọn Hợp đồng > Đại lý > Chọn hợp đồng muốn thực hiện xem Phụ lục > Chọn tab Phụ lục hợp đồng | |
| BF-2 | Hệ thống | - Hiển thị giao diện danh sách phụ lục hợp đồng<br>- Ưu tiên phụ lục hợp đồng có thời gian tạo mới nhất lên đầu | |
|  |  | - AF-1: Người dùng Thêm phụ lục => Xem tại UC thêm phụ lục<br>- AF-2: Người dùng bấm Xem<br>- AF-3: Người dùng bấm Xóa<br>- AF-4: Người dùng nhập giá trị và bấm tìm kiếm | |

**Alternative Flows**:

**AF-2: Người dùng bấm Xem**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-5-1 | Hệ thống | Hệ thống thực hiện mở file xem trước tại tab mới và cho phép tải về | |

**AF-3: Người dùng bấm Xóa**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-5-1 | Hệ thống | Hiển thị popup: "Bạn có chắc chắn muốn xóa [Tên đối tượng đang thao tác]?" kèm 2 lựa chọn:<br>Hủy => Đóng popup<br>Đồng ý => Đóng popup và lưu lại dữ liệu<br>Báo toast: Xóa thành công | |

**AF-5: Người dùng nhập giá trị và bấm tìm kiếm**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-5-1 | Hệ thống | - Hiển thị các kết quả có phụ lục đúng tuyệt đối với Số phụ lục và đúng tương đối với Tên phụ lục<br>- Trường hợp không có kết quả hiển thị Không có dữ liệu để hiển thị | |

**Exception Flows**:  
*(Trống)*

---

### 2.6. [UC-HĐ012] Thêm mới phụ lục hợp đồng đại lý

**Usecase ID & Name**: [UC-HĐ012] Thêm mới phụ lục hợp đồng  
**Description**:  
- **Mục đích**: Thêm mới phụ lục hợp đồng  
**Actors**:  
- Admin vận hành  
**Pre-condition**:  
- Người dùng truy cập vào màn hình danh sách Hợp đồng.  
**Expected Result**:  
- Người dùng thêm mới phụ lục hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  

**Screen Description**:  
**Screen 1**:

| # | Name | Type | Note |
|---|------|------|------|
| 1 | Số phụ lục | Text field | - Cho phép nhập số phụ lục<br>- Giới hạn 25 ký tự<br>- Bắt buộc nhập => để trống hiển thị thông báo: [Tên trường] không được để trống<br>- Placeholder: Nhập [tên trường] |
| 2 | Tên phụ lục | Text field | - Cho phép nhập tên phụ lục<br>- Giới hạn 50 ký tự<br>- Placeholder: Nhập [tên trường] |
| 3 | Ngày hiệu lực PL | Calendar picker | - Cho phép chọn: Ngày/ tháng/ năm<br>- Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường].<br>- Placeholder: Chọn [tên trường] |
| 4 | Ghi chú | Textarea | - Cho phép nhập ghi chú<br>- Giới hạn 250 ký tự<br>- Placeholder: Nhập [tên trường] |
| 5 | File phụ lục | Button | - Cho phép upload file tài liệu<br>- Giới hạn upload tối đa 5 file<br>- Định dạng PDF, Doc, PNG => upload sai hiển thị thông báo: Chỉ upload định dạng PDF, Doc, PNG<br>- Dung lượng tập tin tối đa 5MB => upload tối đa hiển thị thông báo: File tối đa 5MB<br>- Bắt buộc upload => để trống hiển thị thông báo: Vui lòng bổ sung file phụ lục. |
| 6 | | Hyperlink | - File đã upload<br>- Cho phép xóa hoặc xem trước file đã upload<br>- Xem trước: Hệ thống thực hiện mở file xem trước tại tab mới và cho phép tải về |
| 7 | Đóng, close (x) | Button | Đóng popup |
| 8 | Lưu | Button | - Điều hướng về màn hình danh sách và lưu lại dữ liệu<br>- Báo toast: Thêm mới thành công |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Trang chủ chọn Hợp đồng > Đại lý > Chọn hợp đồng muốn thực hiện Thêm Phụ lục > Chọn tab Phụ lục hợp đồng > Bấm Thêm phụ lục | |
| BF-2 | Hệ thống | Hiển thị giao diện thêm mới phụ lục | |
| BF-3 | Người dùng | Nhập các thông tin | |
| BF-4 | Người dùng | Bấm Lưu<br>AF-1: Người dùng bấm Đóng, close (x) | |
| BF-5 | Hệ thống | Thực hiện validate các trường thông tin nhập | |
| BF-6 | Hệ thống | - Điều hướng về màn hình danh sách và lưu lại dữ liệu<br>- Báo toast: Thêm mới thành công | |

**Alternative Flows**:

**AF-1: Người dùng bấm Đóng, close (x)**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-1-1 | Hệ thống | Đóng popup | |

**Exception Flows**:  
*(Trống)*

---

## 3. QL Hợp đồng doanh nghiệp - Actor: Đại lý

### 3.1. [UC-HĐ013] Xem danh sách hợp đồng

**Usecase ID & Name**: [UC-HĐ013] Xem danh sách hợp đồng  
**Description**:  
- **Mục đích**: Xem danh sách hợp đồng  
**Actors**:  
- Đại lý  
**Pre-condition**:  
- Người dùng truy cập vào màn hình đăng nhập hệ thống.  
**Expected Result**:  
- Người dùng xem danh sách hợp đồng thành công.  
**Flow diagram**:  
- [Flow nghiệp vụ]  
- [Flow trạng thái]  

**Screen Description**:  
**Screen 1: Màn hình danh sách**

| # | Name | Type | Note |
|---|------|------|------|
| 1 | STT | Label | Hệ thống tự gen |
| 2 | Số hợp đồng | Label | Hiển thị số hợp đồng |
| 3 | Tên hợp đồng | Label | Hiển thị tên hợp đồng |
| 4 | Tên doanh nghiệp | Label | Hiển thị tên doanh nghiệp |
| 5 | Dịch vụ | Label | Hiển thị các dịch vụ của hợp đồng |
| 6 | Loại hợp đồng | Label | Hiển thị loại hợp đồng bao gồm các giá trị: Có thời hạn, Vô thời hạn |
| 7 | Ngày hiệu lực | Label | - Thời gian hiệu lực của hợp đồng<br>- Định dạng: dd/mm/yyyy |
| 8 | Ngày hết hạn | Label | - Thời gian hết hạn của hợp đồng<br>- Định dạng: dd/mm/yyyy |
| 9 | Trạng thái | Label | Trạng thái hiện tại của hợp đồng bao gồm các giá trị: Hoạt động, Hết hiệu lực, Thanh lý |
| 10 | Thao tác | Button | Xem tại Bảng trạng thái và thao tác tương ứng |
| 11 | Bộ lọc | Button | - Bấm hiển thị danh sách tiêu chí lọc<br>- Mô tả chi tiết tại Alternative Flows |
| 12 | Searchbox | Button | - Hintext: Tìm kiếm theo số hợp đồng, tên hợp đồng<br>- Mô tả chi tiết tại Alternative Flows |
| 13 | Thêm hợp đồng | Button | Xem chi tiết tại UC Thêm mới hợp đồng |

**Bảng trạng thái và thao tác tương ứng**:

| Trạng thái | Action | Notes and References |
|------------|--------|----------------------|
| Hoạt động | Xem, Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Hết hiệu lực | Xem, Cập nhật, Thêm phụ lục, Thanh lý hợp đồng | |
| Thanh lý | Xem | |

**Basic Flow**:

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| BF-1 | Người dùng | Người dùng đang ở màn hình Trang chủ chọn Hợp đồng | |
| BF-2 | Hệ thống | - Hiển thị giao diện danh sách Hợp đồng<br>- Ưu tiên Hợp đồng có thời gian cập nhật mới nhất lên đầu | |
|  |  | - AF-1: Người dùng bấm Xem chi tiết => Xem chi tiết tại UC Xem chi tiết<br>- AF-2: Người dùng bấm Cập nhật => Xem chi tiết tại UC Cập nhật<br>- AF-3: Người dùng bấm Thêm hợp đồng => Xem chi tiết tại UC Thêm mới hợp đồng<br>- AF-4: Người dùng bấm Thanh lý hợp đồng<br>- AF-5: Người dùng nhập giá trị và bấm tìm kiếm<br>- AF-6: Người dùng bấm Bộ lọc<br>- AF-7: Người dùng bấm Thêm phụ lục => Xem tại UC Thêm phụ lục | |

**Alternative Flows**:

**AF-4: Người dùng bấm Thanh lý hợp đồng**

| Step ID | Actor | Action | Notes and References |
|---------|-------|--------|----------------------|
| AF-1-1 | Hệ thống | Hiển thị popup<br>Yêu cầu nhập các thông tin:<br>**Ngày thanh lý**:<br>Calendar picker<br>Cho phép chọn: Ngày/ tháng/ năm<br>Bắt buộc chọn => để trống hiển thị thông báo: Vui lòng chọn [Tên trường].<br>- Placeholder: Chọn [tên trường]<br><br>**File Biên bản thanh lý**:<br>Cho phép upload file tài liệu<br>Giới hạn upload tối đa 5 file<br>Định dạng PDF, Doc, PNG => upload sai hiển thị thông báo: Chỉ upload định dạng PDF, Doc, PNG<br>Dung lượng tập tin tối đa 5MB => upload tối đa hiển thị thông báo: File tối đa 5MB