package vivas.omni.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class OpenAPI30Configuration {

    @Bean
    public OpenAPI customizeOpenAPI() {
        final String securitySchemeName = "Bearer Authentication";
        final String securitySchemeNameApp = "Bearer AuthenticationV";

        Server localServer = new Server();
        localServer.setUrl("http://localhost:8029/business");
        localServer.setDescription("Server URL in Local environment");

        Server devServer = new Server();
        devServer.setUrl("https://api-dev-omni.vivas.vn/business");
        devServer.setDescription("Server URL in dev environment");

        Server stagServer = new Server();
        stagServer.setUrl("https://api-stage-omni.vivas.vn/business");
        stagServer.setDescription("Server URL in stag environment");

        Info info = new Info()
                .title("OMNI BUSINESS API")
                .version("1.0")
                .description("Api của hệ thống OMNI - Business Channel");

        return new OpenAPI()
                .addSecurityItem(new SecurityRequirement()
                        .addList(securitySchemeName, securitySchemeNameApp))
                .components(new Components()
                        .addSecuritySchemes(securitySchemeName, new SecurityScheme()
                                .name(securitySchemeName)
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("Bearer")
                                .bearerFormat("JWT"))
                )
                .servers(List.of(localServer, devServer, stagServer))
                .info(info);
    }

}
