package vivas.omni.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

@Component
@EnableCaching
public class RedisCacheConfig {

//    @Value(value = "${spring.data.redis.sentinel.master}")
//    private String masterName;
//    @Value(value = "${spring.data.redis.sentinel.nodes[0]}")
//    private String redisSentinel1;
//
//    @Value(value = "${spring.data.redis.sentinel.nodes[1]}")
//    private String redisSentinel2;
//
//    @Value(value = "${spring.data.redis.sentinel.nodes[2]}")
//    private String redisSentinel3;

    @Value(value = "${spring.redis.host}")
    private String redisHost;

    @Value(value = "${spring.redis.port}")
    private Integer redisPort;

    @Bean
    public RedisConnectionFactory connectionFactory() {
        return new LettuceConnectionFactory(new RedisStandaloneConfiguration(redisHost, redisPort));
    }

//    @Bean
//    public RedisConnectionFactory connectionFactory() {
//        String redisHost1 = redisSentinel1.split(":")[0];
//        Integer redisPort1 = Integer.parseInt(redisSentinel1.split(":")[1]);
//
//        String redisHost2 = redisSentinel2.split(":")[0];
//        Integer redisPort2 = Integer.parseInt(redisSentinel2.split(":")[1]);
//
//        String redisHost3 = redisSentinel3.split(":")[0];
//        Integer redisPort3 = Integer.parseInt(redisSentinel3.split(":")[1]);
//        RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration()
//                .master(masterName)
//                .sentinel(redisHost1, redisPort1)
//                .sentinel(redisHost2, redisPort2)
//                .sentinel(redisHost3, redisPort3);
//        return new LettuceConnectionFactory(sentinelConfig);
//    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());

        template.afterPropertiesSet();
        return template;
    }

    @Bean
    public RedisCacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        return RedisCacheManager.create(connectionFactory);
    }

}
