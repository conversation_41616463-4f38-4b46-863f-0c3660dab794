package vivas.omni.constant;

import lombok.Getter;

@Getter
public enum FileUploadType {
    FILE_AGENT(10),
    FILE_CUSTOMER(20),
    FILE_LABEL(30),
//    FILE_LABEL_QC_REQUIRED_OFFICIAL_DISPATCH(31),
//    FILE_LABEL_QC_REQUIRED_BUSINESS_REGISTRATION(32),
//    FILE_LABEL_QC_REQUIRED_CCCD(33),
//    FILE_LABEL_QC_SIGNATORY(34),
//    FILE_LABEL_QC_FIELD(35),
//    FILE_LABEL_QC_ADDITIONAL(36),
//    FILE_LABEL_CSKH_REQUIRED_OFFICIAL_DISPATCH(41),
//    FILE_LABEL_CSKH_REQUIRED_BUSINESS_REGISTRATION(42),
//    FILE_LABEL_CSKH_REQUIRED_CCCD(43),
//    FILE_LABEL_CSKH_SIGNATORY(44),
//    FILE_LABEL_CSKH_FIELD(45),
//    FILE_LABEL_CSKH_ADDITIONAL(46),
    FILE_CAMPAIGN(50),
    FILE_LOGO(40)
    ;

    private final int type;
    FileUploadType(final int type) {
        this.type = type;
    }

}
