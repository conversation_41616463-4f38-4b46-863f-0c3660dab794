package vivas.omni.constant;

import lombok.Getter;

@Getter
public enum LabelStatusDict {
    DRAFT(-1),
    DELETE(0),
    ACTIVE(1),
    PENDING(2),
    PAUSE(3),
    REJECT(4);

    LabelStatusDict(int value) {
        this.value = value;
    }

    private final int value;

    public static LabelStatusDict of(Integer value) {
        for (LabelStatusDict labelStatusDict : LabelStatusDict.values()) {
            if (labelStatusDict.value == value) {
                return labelStatusDict;
            }
        }
        return null;
    }
}
