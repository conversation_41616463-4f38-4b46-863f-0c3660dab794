package vivas.omni.constant;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public enum MessageResponseDict {
    SUCCESS(HttpStatus.OK, 0, "Thành công"),
    FAIL(HttpStatus.OK, 2004, "Không thành công"),
    ERROR(HttpStatus.INTERNAL_SERVER_ERROR, 500,
            " Không thể kết nối tới máy chủ do hệ thống đang bận. Vui lòng thử lại!"),

    ADD_OBJECT_SUCCESS(HttpStatus.OK, 0, "Thêm mới %s thành công!"),
    UPDATE_OBJECT_SUCCESS(HttpStatus.OK, 0, "Cập nhật %s thành công!"),
    DELETE_OBJECT_SUCCESS(HttpStatus.OK, 0, "Xóa %s thành công!"),
    CANNOT_DELETE_CONSTRAINT(HttpStatus.OK, 100001, "Không thể xóa %s vì ràng buộc dữ liệu"),
    OBJECT_NOT_FOUND(HttpStatus.OK, 100002, "%s bạn chọn không tồn tại"),

    INCORRECT_FORMAT_FILE(HttpStatus.BAD_REQUEST, 400, "File không đúng định dạng, vui lòng chọn lại."),
    INVALID_FILE_UPLOAD_TYPE(HttpStatus.BAD_REQUEST, 400, "type không đúng."),

    UPLOAD_FILE_FAILED(HttpStatus.INTERNAL_SERVER_ERROR, 500, "Lưu file thất bại!"),
    FILE_UPLOAD_NOT_FOUND(HttpStatus.BAD_REQUEST, 400, "file không tồn tại."),
    ERROR_DELETE_FILE(HttpStatus.INTERNAL_SERVER_ERROR, 500, "Xóa file thất bại!"),

    ADD_BUSINESS_SUCCESS(HttpStatus.OK, 0, "Thêm mới doanh nghiệp thành công"),
    UPDATE_BUSINESS_SUCCESS(HttpStatus.OK, 0, "Cập nhật doanh nghiệp thành công"),
    BUSINESS_NOT_FOUND(HttpStatus.NOT_FOUND, 100000, "Doanh nghiệp không tồn tại!"),
    BUSINESS_NOT_DELETE(HttpStatus.BAD_REQUEST, 100001, "Không thể xóa %s vì ràng buộc dữ liệu"),
    AGENT_NOT_FOUND(HttpStatus.NOT_FOUND, 100002, "Đại lý không tồn tại!"),

    ADD_AGENT_SUCCESS(HttpStatus.OK, 0, "Thêm mới đại lý thành công"),
    UPDATE_AGENT_SUCCESS(HttpStatus.OK, 0, "Cập nhật đại lý thành công"),
    AGENT_NOT_DELETE(HttpStatus.BAD_REQUEST, 100001, "Không thể xóa đại lý vì ràng buộc dữ liệu"),
    DELETE_AGENT_SUCCESS(HttpStatus.OK, 0, "Xoá đại lý thành công"),

    // brandname
    ADD_BRANDNAME_SUCCESS(HttpStatus.OK, 0, "Thêm mới thành công"),
    UPDATE_BRANDNAME_SUCCESS(HttpStatus.OK, 0, "Cập nhật thành công"),
    REQUEST_APPROVE_BRANDNAME_SUCCESS(HttpStatus.OK, 0,
            "Gửi xét duyệt thành công. Kết quả sẽ được cập nhật sau 3-5 ngày làm việc kể từ ngày cung cấp đầy đủ hồ sơ."),
    CONTRACT_NOT_FOUND(HttpStatus.BAD_REQUEST, 20000, "Hợp đồng khách hàng không tồn tại!"),
    BRANDNAME_NOT_EXSIT(HttpStatus.BAD_REQUEST, 20001, "Không tìm thấy brand name với ID:!"),
    BRANDNAME_APPROVE_SUCCSESS(HttpStatus.OK, 0, "Duyệt Brandname thành công!"),
    BRANDNAME_REJECT_SUCCSESS(HttpStatus.OK, 0, "Từ chối Brandname thành công!"),
    BRANDNAME_PAUSE_SUCCESS(HttpStatus.OK, 0, "Tạm dừng Brandname thành công!"),
    DELETE_SUCCESS(HttpStatus.OK, 0, "Xóa thành công!"),
    DOCUMENT_NOT_EXSIT(HttpStatus.OK, 20005, "Không tìm thấy tài liệu!"),
    FILE_NOT_CREATE(HttpStatus.BAD_REQUEST, 20006, "Không thể tạo file tạm thời"),
    FILE_NOT_CREATE_ZIP(HttpStatus.BAD_REQUEST, 20007, "Lỗi khi tạo file ZIP"),
    FILE_ACCESS_ZIP_ERROR(HttpStatus.BAD_REQUEST, 20008, "Lỗi khi truy cập file ZIP"),
    TELCO_NOT_IN_BRANDNAME(HttpStatus.BAD_REQUEST, 20009, "Nhà mạng không có trong brandname!"),
    BRANDNAME_INVALID_NAME(HttpStatus.BAD_REQUEST, 200010,
            "Brandname không được chứa ký tự đặc biệt “% # & [] {} \\ < @"),
    BRANDNAME_BLANK_LABEL_TYPE(HttpStatus.BAD_REQUEST, 20011, "Vui lòng chọn lĩnh vực %s."),
    BRANDNAME_REQUIRED_NETWORK_LABEL(HttpStatus.BAD_REQUEST, 20012, "Thông tin đăng ký mạng là bắt buộc"),
    BRANDNAME_INVALID_STATUS(HttpStatus.BAD_REQUEST, 20013, "Trạng thái của brandname không hợp lệ."),
    BRANDNAME_LIMIT_FIZE_UPLOAD(HttpStatus.BAD_REQUEST, 20014, "File tối đa 5MB"),
    BRANDNAME_INVALID_EXTENSION_FILE(HttpStatus.BAD_REQUEST, 20015, "Chỉ upload định dạng PDF"),
    BRANDNAME_NETWORK_NOT_EXISTS(HttpStatus.BAD_REQUEST, 20016, "Nhà mạng lựa chọn không tồn tại."),
    BRANDNAME_FILE_UPLOAD_NOT_FOUND(HttpStatus.BAD_REQUEST, 20017, "Không tìm thấy tệp đính kèm."),
    BRANDNAME_INVALID_TIME(HttpStatus.BAD_REQUEST, 20018, "Thời gian không hợp lệ."),
    BRANDNAME_EXPIRE_AT_REQUIRED(HttpStatus.BAD_REQUEST, 20019, "Vui lòng chọn thời gian hết hạn Brandname."),
    BRANDNAME_ACTIVE_NETWORK_REQUIRED(HttpStatus.BAD_REQUEST, 20020, "Tối thiểu 1 nhà mạng đã được duyệt."),

    FILE_NOT_FOUND(HttpStatus.NOT_FOUND, 404, "Không tìm thấy tệp tin tại đường dẫn đã chỉ định!"),
    FILE_NOT_READABLE(HttpStatus.BAD_REQUEST, 400, "Không thể đọc tệp tin, vui lòng kiểm tra quyền truy cập!"),
    FILE_ACCESS_ERROR(HttpStatus.INTERNAL_SERVER_ERROR, 500, "Lỗi khi truy cập tệp tin, URL không hợp lệ!"),

    // OA
    OA_NOT_EXIST(HttpStatus.BAD_REQUEST, 404, "Không tìm thấy OA với ID:!"),
    OA_IS_EXSIT(HttpStatus.OK, 100401, "OA ID đã tồn tại. Vui lòng thử lại"),
    OA_CONNECT_REQUEST_SUCCESS(HttpStatus.OK, 0, "Yêu cầu kết nối OA thành công"),
    OA_CONNECT_REQUEST_FAIL(HttpStatus.OK, 100402, "Yêu cầu kết nối OA thất bại"),
    OA_UPDATE_SUCCESS(HttpStatus.OK, 0, "Cập nhật OA thành công"),
    OA_UPDATE_FAIL(HttpStatus.OK, 1004003, "Cập nhật OA thất bại"),
    OA_DELETE_SUCCESS(HttpStatus.OK, 0, "Xoá OA thành công"),
    OA_DELETE_FAIL(HttpStatus.OK, 100404, "Xoá OA thất bại"),
    OA_DELETE_STATUS(HttpStatus.BAD_REQUEST, 404, "Không thể xoá vì OA đang hoạt động"),
    OA_PAUSE_SUCCESS(HttpStatus.OK, 0, "Tạm dừng OA thành công"),
    OA_CONNECT_SUCCESS(HttpStatus.OK, 0, "Kết nối OA thành công"),

    // Network Operator
    NO_NETWORK_OPERATOR_FOUND(HttpStatus.NOT_FOUND, 100000, "Không tìm thấy nhà mạng nào!"),

    // Policy
    UPDATE_MESSAGE_POLICY_SUCCESS(HttpStatus.OK, 0, "Cập nhật chính sách gửi tin thành công"),
    UPDATE_MESSAGE_POLICY_FAIL(HttpStatus.OK, 100000, "Cập nhật chính sách gửi tin thất bại"),
    NO_DAYS_OF_WEEK_FOUND(HttpStatus.NOT_FOUND, 100000, "Không tìm thấy ngày nào trong tuần!"),
    TIME_POLICY_OVERLAP(HttpStatus.BAD_REQUEST, 400,
            "Bạn đã cấu hình giới hạn cho khoảng thời gian với nhà mạng tương ứng."),
    TIME_START_END_NOT_SAME(HttpStatus.BAD_REQUEST, 400, "Thời gian bắt đầu và kết thúc không được trùng nhau!"),
    TIME_START_END_NOT_VALID(HttpStatus.BAD_REQUEST, 400, "Thời gian bắt đầu và kết thúc không hợp lệ"),
    MESSAGE_LIMIT_NOT_VALID(HttpStatus.BAD_REQUEST, 400, "Giới hạn số tin gửi không hợp lệ"),
    CHAR_LIMIT_NOT_VALID(HttpStatus.BAD_REQUEST, 400, "Giới hạn ký tự không hợp lệ"),
    BLOCKED_KEYWORD_DELETE_SUCCESS(HttpStatus.OK, 0, "Xóa từ khóa thành công"),
    BLOCKED_KEYWORD_DELETE_FAIL(HttpStatus.OK, 100000, "Xóa từ khóa thất bại"),
    BLOCKED_KEYWORD_ADD_SUCCESS(HttpStatus.OK, 0, "File %s đã được tải lên thành công"),
    BLOCKED_KEYWORD_ADD_FAIL(HttpStatus.OK, 100000, "Thêm mới từ khóa thất bại"),
    SMS_TYPE_NOT_VALID(HttpStatus.BAD_REQUEST, 400, "Loại SMS không hợp lệ"),
    FILE_NOT_EXCEL(HttpStatus.BAD_REQUEST, 400, "File không đúng định dạng, vui lòng chọn lại."),
    FILE_EXCEEDS_MAX_SIZE(HttpStatus.BAD_REQUEST, 400, "File vượt quá %s, vui lòng chọn lại."),
    DOWNLOAD_TEMPLATE_FAIL(HttpStatus.INTERNAL_SERVER_ERROR, 500, "Tải mẫu thất bại"),
    FILE_INVALID_DATA(HttpStatus.BAD_REQUEST, 400, "File chứa từ khoá sai định dạng. Vui lòng thử lại!"),

    // Template
    TEMPLATE_NOT_FOUND(HttpStatus.NOT_FOUND, 1000, "Không tìm thấy template nào!"),
    UPDATE_TEMPLATE_STATUS_FAIL(HttpStatus.BAD_REQUEST, 1001, "%s template thất bại"),
    REASON_NOT_BLANK(HttpStatus.BAD_REQUEST, 1002, "Lý do không được để trống"),
    CREATE_TEMPLATE_SUCCESS(HttpStatus.OK, 0, "Tạo template thành công"),
    UPDATE_TEMPLATE_SUCCESS(HttpStatus.OK, 0, "Cập nhật template thành công"),
    DELETE_TEMPLATE_SUCCESS(HttpStatus.OK, 0, "Xóa template thành công"),
    TEMPLATE_TYPE_NOT_MATCH(HttpStatus.BAD_REQUEST, 1003, "Loại template không hợp lệ"),

    ;

    public final HttpStatus status;
    public final Integer code;
    String message;

    MessageResponseDict(HttpStatus status, Integer code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

    public MessageResponseDict addParams(String... params) {
        this.message = String.format(this.message, (Object[]) params);

        return this;
    }
}
