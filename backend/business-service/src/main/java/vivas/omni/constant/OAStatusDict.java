package vivas.omni.constant;

import lombok.Getter;

@Getter
public enum OAStatusDict {
    ACTIVE(1, "Hoạt động"),
    PAUSE(2, "Tạm dừng"),
    PENDING(3, "Đang kết nối");

    public final Integer value;
    public final String description;

    OAStatusDict(Integer code, String description) {
        this.value = code;
        this.description = description;
    }

    public static String getNameById(int code) {
        for (OAStatusDict type : values()) {
            if (type.getValue() == code) {
                return type.getDescription();
            }
        }
        return null;
    }


}
