package vivas.omni.constant;

import lombok.Getter;

@Getter
public enum PolicySettingTypeDict {
    SMS_TIME_LIMIT(1),
    MESSAGE_COUNT(2),
    SMS_CHAR_LIMIT(3),
    ZSN_TIME_LIMIT(4),;

    private final Integer type;

    PolicySettingTypeDict(Integer type) {
        this.type = type;
    }

    public static PolicySettingTypeDict of(Integer type) {
        for (PolicySettingTypeDict policySettingTypeDict : PolicySettingTypeDict.values()) {
            if (policySettingTypeDict.type.equals(type)) {
                return policySettingTypeDict;
            }
        }
        return null;
    }
}
