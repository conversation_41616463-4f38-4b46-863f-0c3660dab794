package vivas.omni.constant;

import lombok.Getter;

@Getter
public enum ProfileLabelGroupDict {
    CSKH_PROFILE_LABEL_REGISTER_BUSINESS_NAME(1),
    CSKH_PROFILE_LABEL_DOMAIN(2),
    CSKH_PROFILE_LABEL_BRAND_CERTIFICATION(3),
    QC_PROFILE_LABEL_BRAND_CERTIFICATION(4),
    QC_PROFILE_LABEL_DOMAIN(5),
    QC_PROFILE_LABEL_FAMOUS_BRAND(6),
    ;


    private final int type;

    ProfileLabelGroupDict(int type) {
        this.type = type;
    }
}
