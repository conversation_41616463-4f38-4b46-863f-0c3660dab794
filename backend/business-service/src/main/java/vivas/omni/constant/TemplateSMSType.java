package vivas.omni.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum TemplateSMSType {
    // 4 loại: v<PERSON><PERSON> b<PERSON>, <PERSON><PERSON>, d<PERSON><PERSON> bảng, đ<PERSON><PERSON> gi<PERSON>, khảo sát
    CSKH(1),
    QC(2),
    ;

    Integer type;

    public static TemplateSMSType of(Integer id) {
        for (TemplateSMSType type : TemplateSMSType.values()) {
            if (type.getType().equals(id)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Loại template không hợp lệ: " + id);
    }


}
