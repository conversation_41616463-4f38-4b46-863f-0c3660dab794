package vivas.omni.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum TemplateStatus {
    // 1: <PERSON><PERSON><PERSON> động 2: Tạm dừng 3: <PERSON><PERSON> duyệt 4: Từ chối
    DRAFT(0, "Nháp"),
    ACTIVE(1, "Hoạt động"),
    PENDING(2, "Chờ duyệt"),
    PAUSE(3, "Tạm dừng"),
    REJECTED(4, "Từ chối"),
    ;

    Integer status;
    String name;

    public static TemplateStatus of(Integer status) {
        for (TemplateStatus templateStatus : TemplateStatus.values()) {
            if (templateStatus.getStatus().equals(status)) {
                return templateStatus;
            }
        }
        throw new IllegalArgumentException("Invalid status: " + status);
    }
}
