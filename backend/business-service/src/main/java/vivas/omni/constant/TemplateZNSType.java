package vivas.omni.constant;

import lombok.Getter;

@Getter
public enum TemplateZNSType {
    TEXT(1),
    OTP(2),
    TABLE(3),
    RATING(4),
    PAYMENT(5),
    VOUCHER(6);

    public final int value;

    TemplateZNSType(int value) {
        this.value = value;
    }

    public static TemplateZNSType of(Integer id) {
        for (TemplateZNSType type : TemplateZNSType.values()) {
            if (type.getValue() == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid TemplateType id: " + id);
    }
}
