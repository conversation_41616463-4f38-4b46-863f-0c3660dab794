package vivas.omni.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.request.AgentDetailRequest;
import vivas.omni.request.SearchAgentRequest;
import vivas.omni.response.AgentDetailResponse;
import vivas.omni.response.AgentListResponse;
import vivas.omni.response.AgentResponse;
import vivas.omni.response.PagingResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.service.AgentService;
import vivas.omni.utils.ResponseUtils;

import java.util.List;

@Tag(name = "API Admin quản lý đại lý")
@Slf4j
@RequestMapping("/v1/api/admin/agent")
@RestController
@RequiredArgsConstructor
public class AgentAdminController {

    private final AgentService agentService;

    @GetMapping("/list")
    @Operation(summary = "API Danh sách đại lý không phân trang")
    public ResponseEntity<ResponseCommon<List<AgentListResponse>>> getListAgent(@RequestParam(required = false) Integer status) {
        return ResponseUtils.ok(agentService.getListAgent(status));
    }

    @PostMapping("/list")
    @Operation(summary = "API Danh sách đại lý có phân trang")
    public ResponseEntity<ResponseCommon<PagingResponse<AgentResponse>>> getAllAgent(@RequestBody SearchAgentRequest request) {
        return ResponseUtils.ok(agentService.getAllAgent(request));
    }

    @PostMapping()
    @Operation(summary = "API Thêm mới đại lý")
    public ResponseEntity<ResponseCommon<AgentDetailResponse>> addAgent(@RequestBody @Valid AgentDetailRequest request) {
        log.info("addAgent: {}", request);
        return ResponseUtils.ok(MessageResponseDict.ADD_AGENT_SUCCESS, agentService.addAgent(request));
    }

    @PutMapping("/{agentId}")
    @Operation(summary = "API Sửa thông tin đại lý")
    public ResponseEntity<ResponseCommon<AgentDetailResponse>> updateAgent(@PathVariable Long agentId,
                                                                           @RequestBody @Valid AgentDetailRequest request) {
        log.info("updateAgent: {}", request);
        return ResponseUtils.ok(MessageResponseDict.UPDATE_AGENT_SUCCESS, agentService.updateAgent(agentId, request));
    }

    @GetMapping("/{agentId}")
    @Operation(summary = "API Xem chi tiết đại lý")
    public ResponseEntity<ResponseCommon<AgentDetailResponse>> getAgentById(@PathVariable Long agentId) {
        log.info("getAgentById: {}", agentId);
        return ResponseUtils.ok(agentService.getAgentById(agentId));
    }

    @DeleteMapping("/{agentId}")
    @Operation(summary = "API Xoá đại lý")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> deleteAgent(@PathVariable Long agentId) {
        log.info("deleteAgent: {}", agentId);
        return ResponseUtils.ok(agentService.deleteAgent(agentId));
    }
}
