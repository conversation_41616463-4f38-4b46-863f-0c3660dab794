package vivas.omni.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vivas.omni.constant.HeaderKey;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.dto.StatusUpdateRequest;
import vivas.omni.request.label.BrandNameSearchRequest;
import vivas.omni.request.label.BrandNameUpdateAdminRequest;
import vivas.omni.response.BrandNameDetailResponse;
import vivas.omni.response.BrandNameResponse;
import vivas.omni.response.PagingResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.response.label.BrandNameUpdateResponse;
import vivas.omni.service.BrandNameService;
import vivas.omni.utils.ResponseUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@RestController
@RequestMapping("/v1/api/admin/brandname")
@Tag(name = "API quản lý Brand Name Admin")
@RequiredArgsConstructor
@Slf4j
public class BrandNameAdminController {

    private final BrandNameService service;

    @Operation(summary = "API xem danh sách brand name")
    @PostMapping("/list")
    public ResponseEntity<ResponseCommon<PagingResponse<BrandNameResponse>>> searchBrandName(@RequestBody BrandNameSearchRequest brandNameRequest) {
        log.info("searchBrandName admin: {}", brandNameRequest);
        return ResponseUtils.ok(service.getBrandName(brandNameRequest));
    }

    @Operation(summary = "API xem chi tiết brand name")
    @GetMapping("/{id}")
    public ResponseEntity<ResponseCommon<BrandNameDetailResponse>> getBrandNameById(@PathVariable("id") Long id) {
        log.info("getBrandNameById admin: {}", id);
        BrandNameDetailResponse data = service.brandNameDetail(id);
        return ResponseUtils.ok(data);
    }

    @Operation(summary = "API xoá brand name")
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> deleteBrandName(@PathVariable("id") Long id) {
        log.info("deleteBrandName admin: {}", id);
        return ResponseUtils.ok(service.deleteBrandName(id));
    }


    @Operation(summary = "API duyệt/từ chối")
    @PutMapping("/{brandnameId}/status")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> updateBrandStatus(@PathVariable Long brandnameId, @Valid @RequestBody StatusUpdateRequest statusRequest) {

        log.info("updateBrandStatus admin: id: {}, {}", brandnameId, statusRequest);
        return ResponseUtils.ok(service.updateBrandStatus(brandnameId, statusRequest));
    }

    @Operation(summary = "API tải file hồ sơ")
    @GetMapping("/{brandnameId}/documents")
    public ResponseEntity<Resource> downloadDocument(@PathVariable Long brandnameId, @Valid @RequestParam Long serviceSmsType) {
        Resource fileResource = service.getDocumentFile(brandnameId, serviceSmsType);
        String filename = fileResource.getFilename();

        // Encode filename theo chuẩn RFC 5987
        String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8).replace("+", "%20");

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFilename)
                .header("X-Message", URLEncoder.encode("Success", StandardCharsets.UTF_8))
                .body(fileResource);
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<ResponseCommon<BrandNameUpdateResponse>> updateBrandNameApi(HttpServletRequest request, @PathVariable("id") Long labelId, @Valid @RequestBody BrandNameUpdateAdminRequest brandNameUpdateAdminRequest) throws IOException {
        log.info("updateBrandNameApi admin: {}", brandNameUpdateAdminRequest);
        String userId = request.getHeader(HeaderKey.USER_ID);

        return ResponseUtils.ok(MessageResponseDict.UPDATE_BRANDNAME_SUCCESS, service.updateBrandNameAdmin(labelId, brandNameUpdateAdminRequest, userId));
    }

}
