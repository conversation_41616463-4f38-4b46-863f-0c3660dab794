package vivas.omni.controller.admin;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.constant.UploadType;
import vivas.omni.dto.BusinessDto;
import vivas.omni.request.BusinessDetailRequest;
import vivas.omni.response.BusinessDetailResponse;
import vivas.omni.response.BusinessResponse;
import vivas.omni.response.PagingResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.service.BusinessService;
import vivas.omni.utils.ResponseUtils;

import java.util.List;
import java.util.UUID;

@Tag(name = "API Admin quản lý doanh nghiệp")
@Slf4j
@RequestMapping("/v1/api/admin/business")
@RestController
public class BusinessAdminController {

    private final BusinessService businessService;

    public BusinessAdminController(BusinessService businessService) {
        this.businessService = businessService;
    }

    @GetMapping("/oa")
    public ResponseEntity<ResponseCommon<List<BusinessDto>>> getListBusinessByLabel(
    ) {
        return ResponseUtils.ok(businessService.getListBusinessByLabel());
    }

    @GetMapping("/list")
    public ResponseEntity<ResponseCommon<List<BusinessDto>>> getListBusiness(@RequestParam(required = false) Integer status
    ) {
        return ResponseUtils.ok(businessService.getListBusiness(status));
    }

    @GetMapping()
    public ResponseEntity<ResponseCommon<PagingResponse<BusinessResponse>>> getAllBusiness(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String businessName,
            @RequestParam(required = false) Integer customerSource,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer pageIndex,
            @RequestParam(required = false) Integer pageSize
    ) {
        log.info("getAllBusiness - keyword: {}, businessName: {}, customerSource: {}, status: {}, pageIndex: {}, pageSize: {}",
                keyword, businessName, customerSource, status, pageIndex, pageSize);
        return ResponseUtils.ok(businessService.getAllBusiness(keyword, businessName, customerSource, status, pageIndex, pageSize));
    }

    @PostMapping()
    public ResponseEntity<ResponseCommon<Long>> addBusiness(@RequestBody @Valid BusinessDetailRequest businessDetail) {
        log.info("addBusiness: {}", businessDetail);
        return ResponseUtils.ok(MessageResponseDict.ADD_BUSINESS_SUCCESS, businessService.addBusiness(businessDetail));
    }

    @PutMapping("/{businessId}")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> updateBusiness(@PathVariable Long businessId,
                                                                              @RequestBody @Valid BusinessDetailRequest businessDetail) {
        log.info("updateBusiness: {}", businessDetail);
        return ResponseUtils.ok( businessService.updateBusiness(businessId, businessDetail));
    }

    @GetMapping("/{businessId}")
    public ResponseEntity<ResponseCommon<BusinessDetailResponse>> getBusinessById(@PathVariable Long businessId) {
        log.info("getBusinessById: {}", businessId);
        return ResponseUtils.ok(businessService.getBusinessById(businessId));
    }

    @DeleteMapping("/{businessId}")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> deleteBusiness(@PathVariable Long businessId) {
        log.info("deleteBusiness: {}", businessId);
        return ResponseUtils.okMsg(businessService.deleteBusiness(businessId), "doanh nghiệp");
    }
}
