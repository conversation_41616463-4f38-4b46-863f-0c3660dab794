package vivas.omni.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.request.UploadLogoRequest;
import vivas.omni.response.PagingResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.service.LogoService;

@RestController
@RequestMapping("/v1/api/admin/logo")
@Tag(name = "Admin - API quản lý Logo")
@RequiredArgsConstructor
public class LogoAdminController {

    private final LogoService service;

    @Operation(summary = "API xem danh sách logo")
    @GetMapping("/")
    public ResponseEntity<ResponseCommon<PagingResponse<?>>> getLogoList(@RequestParam(required = false) String name,
                                                                             @RequestParam Integer status,
                                                                             @RequestParam int pageSize,
                                                                             @RequestParam int pageIndex) {
        return service.searchLogo(name, status, pageSize, pageIndex);
    }

    @Operation(summary = "API xem chi tiết logo")
    @GetMapping("/{id}")
    public ResponseEntity<ResponseCommon<?>> getLogoById(@PathVariable("id") Long id) {
        return service.logoDetail(id);
    }

    @Operation(summary = "API thêm mới logo")
    @PostMapping("")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> addLogo(@RequestBody UploadLogoRequest request) {
        return service.addLogo(request);
    }

    @Operation(summary = "API xem chi tiết logo")
    @PutMapping("")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> updateLogo(@RequestBody UploadLogoRequest request) {
        return service.updateLogo(request);
    }

    @Operation(summary = "API xoá logo")
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> deleteLogo(@PathVariable("id") Long id) {
        return service.deleteLogo(id);
    }
}
