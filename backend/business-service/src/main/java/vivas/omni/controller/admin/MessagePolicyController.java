package vivas.omni.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vivas.omni.constant.HeaderKey;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.request.BlockedKeywordRequest;
import vivas.omni.request.MessagePolicyRequest;
import vivas.omni.response.*;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.service.MessagePolicyService;
import vivas.omni.utils.ResponseUtils;

import java.util.List;

@RestController
@RequestMapping("/v1/api/admin/message-policy")
@Tag(name = "Admin - API quản lý chính sách gửi tin")
public class MessagePolicyController {
    private final MessagePolicyService messagePolicyService;

    public MessagePolicyController(MessagePolicyService messagePolicyService) {
        this.messagePolicyService = messagePolicyService;
    }

    @GetMapping("")
    @Operation(summary = "Lấy danh sách chính sách gửi tin")
    public ResponseEntity<ResponseCommon<List<MessagePolicyResponse>>> getPolicyList() {
        return ResponseUtils.ok(messagePolicyService.getPolicyList());
    }

    @PostMapping("")
    @Operation(summary = "Cập nhật chính sách gửi tin", description = """
            - **network_operator_id**: ID của nhà mạng. Các giá trị:
              - `1`: Reddi
              - `2`: Itel
              - `3`: Sfone
              - `4`: Vietnamobile
              - `6`: Viettel
              - `7`: Mobifone
              - `8`: Vinaphone
            
            - **policy_setting_type**: Loại cài đặt chính sách. Các giá trị:
              - `1`: Giới hạn thời gian gửi SMS
              - `2`: Giới hạn số tin gửi SMS
              - `3`: Giới hạn ký tự SMS
              - `4`: Giới hạn thời gian gửi ZNS
            
            - **sms_type**: Loại SMS. Các giá trị:
              - `1`: CSKH
              - `2`: Quảng cáo

            """)
    public ResponseEntity<ResponseCommon<MessageResponseDict>> updatePolicies(
            @RequestBody List<MessagePolicyRequest> request) {
        return ResponseUtils.ok(messagePolicyService.updatePolicies(request));
    }

    // lấy danh sách nhà mạng
    @GetMapping("/network-operators")
    @Operation(summary = "Lấy danh sách nhà mạng")
    public ResponseEntity<ResponseCommon<List<NetworkOperatorResponse>>> getNetworkOperatorsList() {
        return ResponseUtils.ok(messagePolicyService.getNetworkOperatorList());
    }

    // lấy danh sách ngày trong tuần
    @GetMapping("/days-of-week")
    @Operation(summary = "Lấy danh sách ngày trong tuần")
    public ResponseEntity<ResponseCommon<List<DaysOfWeekResponse>>> getDaysOfWeekList() {
        return ResponseUtils.ok(messagePolicyService.getDaysOfWeekList());
    }

    @GetMapping("/blocked-keywords")
    @Operation(summary = "API xem danh sách từ khóa bị chặn", description = "Xem danh sách từ khóa bị chặn")
    public ResponseEntity<ResponseCommon<PagingResponse<BlockedKeywordResponse>>> getBlockedKeywords(
            @Parameter(description = "Từ khoá tìm kiếm") @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer pageIndex,
            @RequestParam(required = false) Integer pageSize) {
        return ResponseUtils.ok(messagePolicyService.getBlockedKeywords(keyword, pageIndex, pageSize));
    }

    @PostMapping("/blocked-keywords")
    @Operation(summary = "Thêm mới từ khóa bị chặn", description = """
            Thêm mới từ khóa bị chặn
            
            - **sms_type**: Loại SMS. Các giá trị:
              - `1`: CSKH
              - `2`: Quảng cáo
            
            """)
    public ResponseEntity<ResponseCommon<MessageResponseDict>> addBlockedKeyword(HttpServletRequest request,
                                                                                 @Valid @RequestBody BlockedKeywordRequest blockedKeywordRequest) {
        String userId = request.getHeader(HeaderKey.USER_ID);

        return ResponseUtils.ok(messagePolicyService.addBlockedKeyword(blockedKeywordRequest, userId));
    }

    @DeleteMapping("/blocked-keywords/{keywordId}")
    @Operation(summary = "Xóa từ khóa bị chặn", description = "API xóa từ khóa bị chặn")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> deleteBlockedKeyword(@PathVariable Long keywordId) {
        return ResponseUtils.ok(messagePolicyService.deleteBlockedKeyword(keywordId));
    }

    @PostMapping(value = "/blocked-keywords/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Upload file từ khóa bị chặn", description = "API upload file từ khóa bị chặn")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> uploadBlockedKeywordsTemplate(HttpServletRequest request,
                                                                                             @Parameter(description = "Excel file (.xlsx)", required = true) @RequestPart("file") MultipartFile file) {
        String userId = request.getHeader(HeaderKey.USER_ID);

        return ResponseUtils.ok(messagePolicyService.uploadBlockedKeywordsTemplate(file, userId));
    }

    @GetMapping("/blocked-keywords/template")
    @Operation(summary = "Download template file từ khóa bị chặn", description = "API download template file từ khóa bị chặn")
    public ResponseEntity<byte[]> downloadBlockedKeywordsTemplate() {
        return messagePolicyService.downloadBlockedKeywordsTemplate();
    }
}
