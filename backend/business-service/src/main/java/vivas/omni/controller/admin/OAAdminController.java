package vivas.omni.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.dto.OADto;
import vivas.omni.request.UpdateOARequest;
import vivas.omni.response.PagingResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.service.OAService;
import vivas.omni.utils.ResponseUtils;
import vivas.omni.utils.group.AdminGroup;
import vivas.omni.utils.group.ClientGroup;

@RestController
@RequestMapping("/v1/api/admin/oa")
@Tag(name = "Admin - API quản lý official account")
@AllArgsConstructor
public class OAAdminController {

    private final OAService service;

    @Operation(summary = "API xem danh sách official account")
    @GetMapping("")
    public ResponseEntity<ResponseCommon<PagingResponse<OADto>>> getOfficialAccountList(@RequestParam(required = false) String name,
                                                                                 @RequestParam(required = false) Integer status,
                                                                                 @RequestParam(required = false) Long packageId,
                                                                                 @RequestParam(required = false) Long businessId,
                                                                                 @RequestParam int pageSize,
                                                                                 @RequestParam int pageIndex) {
        return ResponseUtils.ok(service.searchOfficialAccount(name, status, packageId, businessId, pageSize, pageIndex));
    }

    @Operation(summary = "API xem chi tiết official account")
    @GetMapping("/{id}")
    public ResponseEntity<ResponseCommon<OADto>> getOfficialAccountById(@PathVariable("id") Long id) {
        return ResponseUtils.ok(service.officialAccountDetail(id));
    }

//    @Operation(summary = "API thêm mới official account")
//    @PostMapping("")
//    public ResponseEntity<ResponseCommon<MessageResponseDict>> addOfficialAccount(@RequestBody CreateOARequest request) {
//        return ResponseUtils.ok(service.addOfficialAccount(request));
//    }

    @Operation(summary = "API cập nhật official account")
    @PutMapping("")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> updateOfficialAccount(@RequestBody @Validated(AdminGroup.class) UpdateOARequest request) {
        return ResponseUtils.ok(service.updateOfficialAccount(request));
    }

    @Operation(summary = "API xoá official account")
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> deleteOfficialAccount(@PathVariable("id") Long id) {
        return ResponseUtils.ok(service.deleteOfficialAccount(id));
    }

    @Operation(summary = "API xác nhận - tạm dừng OA")
    @PutMapping("/approve_or_pause/{id}")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> approveOrPauseOA(@PathVariable("id") Long id) {
        return ResponseUtils.ok(service.approveOrPauseOA(id));
    }
}
