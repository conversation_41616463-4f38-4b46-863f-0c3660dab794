package vivas.omni.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vivas.omni.constant.ChannelType;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.repository.TemplateRepository;
import vivas.omni.repository.entity.Template;
import vivas.omni.request.template.sms.SMSTemplateSearchRequest;
import vivas.omni.request.template.sms.UpdateSMSTemplateRequest;
import vivas.omni.request.template.sms.UpdateSMSTemplateStatusRequest;
import vivas.omni.response.PagingResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.response.template.SMSTemplateResponse;
import vivas.omni.service.SMSTemplateService;
import vivas.omni.utils.ResponseUtils;

import java.util.List;

@RestController
@RequestMapping("v1/api/admin/sms-template")
@Tag(name = "API quản lý Template Admin")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class SMSTemplateAdminController {
    SMSTemplateService smsTemplateService;
    TemplateRepository templateRepository;

    @PostMapping("/search")
    @Operation(summary = "API tìm kiếm template SMS (POST)")
    public ResponseEntity<ResponseCommon<PagingResponse<SMSTemplateResponse>>> searchTemplates(@RequestBody @Valid SMSTemplateSearchRequest request) {
        log.info("searchTemplates with POST: {}", request);
        return ResponseUtils.ok(smsTemplateService.searchTemplates(request));
    }

//    @GetMapping("/search")
//    @Operation(summary = "API tìm kiếm template SMS (GET)")
//    public ResponseEntity<ResponseCommon<PagingResponse<SMSTemplateResponse>>> searchTemplatesGet(
//            @RequestParam(required = false) String keyword,
//            @RequestParam(required = false) Long labelId,
//            @RequestParam(required = false) Long typeId,
//            @RequestParam(required = false) Integer status,
//            @RequestParam(required = false) Boolean unicode,
//            @RequestParam(required = false) Integer pageIndex,
//            @RequestParam(required = false) Integer pageSize,
//            @RequestParam(required = false) String sortBy,
//            @RequestParam(required = false) Boolean sortAsc) {
//
//        SMSTemplateSearchRequest request = new SMSTemplateSearchRequest();
//        request.setKeyword(keyword);
//        request.setLabelId(labelId);
//        request.setTypeId(typeId);
//        request.setStatus(status);
//        request.setUnicode(unicode);
//        request.setPageIndex(pageIndex);
//        request.setPageSize(pageSize);
//        request.setSortBy(sortBy);
//        request.setSortAsc(sortAsc);
//
//        log.info("searchTemplates with GET: {}", request);
//        return ResponseUtils.ok(smsTemplateService.searchTemplates(request));
//    }

    @PutMapping
    @Operation(summary = "API cập nhật template SMS")
    public ResponseEntity<ResponseCommon<SMSTemplateResponse>> updateTemplate(@RequestBody @Valid UpdateSMSTemplateRequest request) {
        log.info("updateTemplate: {}", request);
        return ResponseUtils.ok(smsTemplateService.updateTemplate(request));
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "API đổi trạng thái template SMS")
    public ResponseEntity<ResponseCommon<SMSTemplateResponse>> updateTemplateStatus(@PathVariable Long id, @RequestBody @Valid UpdateSMSTemplateStatusRequest request) {
        log.info("updateTemplateStatus: {}", id);
        return ResponseUtils.ok(smsTemplateService.updateTemplateStatus(id, request));
    }


    @GetMapping("/all")
    @Operation(summary = "API lấy tất cả template SMS")
    public ResponseEntity<ResponseCommon<List<Template>>> getAllSMSTemplates() {
        log.info("Getting all SMS templates");

        return ResponseUtils.ok(templateRepository.findAllByChannelId(ChannelType.SMS.getValue()));
    }

    @GetMapping("/{id}")
    @Operation(summary = "API lấy chi tiết template SMS theo ID")
    public ResponseEntity<ResponseCommon<SMSTemplateResponse>> getTemplateById(@PathVariable Long id) {
        log.info("getTemplateById: {}", id);

        return ResponseUtils.ok(smsTemplateService.getTemplateById(id));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "API xóa template SMS theo ID")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> deleteTemplate(@PathVariable Long id) {
        log.info("deleteTemplate: {}", id);
        boolean deleted = smsTemplateService.deleteTemplate(id);
        if (!deleted) {
            return ResponseUtils.ok(MessageResponseDict.TEMPLATE_NOT_FOUND);
        }
        return ResponseUtils.ok(MessageResponseDict.DELETE_TEMPLATE_SUCCESS);
    }
}
