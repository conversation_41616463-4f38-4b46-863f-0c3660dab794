package vivas.omni.controller.advice;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.exception.AppException;
import vivas.omni.exception.BusinessException;
import vivas.omni.response.common.ResponseCommon;

import java.util.ArrayList;
import java.util.List;

@ControllerAdvice
@Slf4j
public class ExceptionController {

    @ExceptionHandler({ConstraintViolationException.class})
    public ResponseEntity<Object> handleConstraintViolation(ConstraintViolationException ex, WebRequest request) {
        log.error("Exception handleConstraintViolation: {}", ex.getMessage(), ex);
        List<String> errors = new ArrayList<>();
        for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
            errors.add(violation.getRootBeanClass().getName() + " " + violation.getPropertyPath() + ": " + violation.getMessage());
        }
        ResponseCommon<?> response = new ResponseCommon<>();
        response.setCode(HttpStatus.BAD_REQUEST.value());
        response.setMessage(!ObjectUtils.isEmpty(errors) ? errors.get(0) : "");
        return ResponseEntity.badRequest().body(response);
    }

    @ExceptionHandler({MethodArgumentNotValidException.class})
    public ResponseEntity<Object> handleConstraintViolation(MethodArgumentNotValidException ex, WebRequest request) {
        log.error("Exception handleConstraintViolation: {} - ", ex.getMessage(), ex);

        String errorMessage = ex.getBindingResult().getFieldErrors().get(0).getDefaultMessage();
        ResponseCommon<?> response = new ResponseCommon<>();
        response.setCode(HttpStatus.BAD_REQUEST.value());
        response.setMessage(!StringUtils.isEmpty(errorMessage) ? errorMessage : "");
        return ResponseEntity.badRequest().body(response);
    }

//    @ExceptionHandler({BadCredentialsException.class})
//    public ResponseEntity<Object> handleUnauthorized(BadCredentialsException ex, WebRequest request) {
//        log.error("Exception handleConstraintViolation: {} - ", ex.getMessage(), ex);
//
//        ResponseCommon<?> response = new ResponseCommon<>();
//        response.setCode(HttpStatus.UNAUTHORIZED.value());
//        response.setMessage(!StringUtils.isEmpty(ex.getMessage()) ? ex.getMessage() : "");
//        return ResponseEntity.badRequest().body(response);
//    }

    @ExceptionHandler({MethodArgumentTypeMismatchException.class})
    public ResponseEntity<Object> handleMethodArgumentTypeMismatch(MethodArgumentTypeMismatchException ex, WebRequest request) {
        log.error("Exception handleMethodArgumentTypeMismatch: {}", ex.getMessage(), ex);
        String error = ex.getName() + ": " + ex.getRequiredType().getName();

        ResponseCommon<?> response = new ResponseCommon<>();
        response.setCode(HttpStatus.BAD_REQUEST.value());
        response.setMessage(error);
        return ResponseEntity.badRequest().body(response);
    }

    @ExceptionHandler({BusinessException.class})
    public ResponseEntity<Object> handleBusinessException(BusinessException ex, WebRequest request) {
        log.error("Exception handleBusinessException: {}", ex.getMessage(), ex);
        ResponseCommon<?> response = new ResponseCommon<>();
        response.setCode(ex.getResponseDict().getCode());
        response.setMessage(buildMessage(ex.getResponseDict(), ex.getParams()));
        return ResponseEntity.status(ex.getResponseDict().getStatus()).body(response);
    }

    @ExceptionHandler({RuntimeException.class})
    public ResponseEntity<Object> handleRuntimeException(RuntimeException ex, WebRequest request) {
        log.error("Exception handleRuntimeException: {}", ex.getMessage(), ex);
        ResponseCommon<?> response = new ResponseCommon<>();
        response.setCode(MessageResponseDict.ERROR.getCode());
        response.setMessage(MessageResponseDict.ERROR.getMessage());
        return ResponseEntity.status(MessageResponseDict.ERROR.getStatus()).body(response);
    }

    @ExceptionHandler({AppException.class})
    public ResponseEntity<Object> handleAppException(AppException ex, WebRequest request) {
        log.error("Exception handleAppException: {}", ex.getMessage(), ex);
        ResponseCommon<?> response = new ResponseCommon<>();
        response.setCode(MessageResponseDict.ERROR.getCode());
        response.setMessage(MessageResponseDict.ERROR.getMessage());
        return ResponseEntity.status(MessageResponseDict.ERROR.getStatus()).body(response);
    }

    public String buildMessage(MessageResponseDict msg, List<String> params) {
        if (CollectionUtils.isEmpty(params)) {
            return msg.getMessage();
        }

        return String.format(msg.getMessage(), params.toArray());
    }
}
