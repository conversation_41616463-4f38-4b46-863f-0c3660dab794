package vivas.omni.controller.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import vivas.omni.constant.HeaderKey;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.request.label.BrandNameCreateRequest;
import vivas.omni.request.label.BrandNameSearchRequest;
import vivas.omni.request.label.BrandNameUpdateClientRequest;
import vivas.omni.response.label.BrandNameCreateResponse;
import vivas.omni.response.BrandNameDetailResponse;
import vivas.omni.response.BrandNameResponse;
import vivas.omni.response.PagingResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.response.label.BrandNameUpdateResponse;
import vivas.omni.service.BrandNameService;
import org.springframework.web.bind.annotation.*;
import vivas.omni.utils.ResponseUtils;

import java.io.IOException;

@RestController
@RequestMapping("/v1/api/client/brandname")
@Tag(name = "API quản lý Brand Name Client")
@RequiredArgsConstructor
@Slf4j
public class BrandNameController {

    private final BrandNameService brandNameService;

    @Operation(summary = "API xem danh sách brand name")
    @PostMapping("/list")
    public ResponseEntity<ResponseCommon<PagingResponse<BrandNameResponse>>> searchBrandName(@RequestBody BrandNameSearchRequest brandNameRequest) {
        log.info("searchBrandName client: {}", brandNameRequest);
        return ResponseUtils.ok(brandNameService.getBrandName(brandNameRequest));
    }

    @Operation(summary = "API xem chi tiết brand name")
    @GetMapping("/{id}")
    public ResponseEntity<ResponseCommon<BrandNameDetailResponse>> getBrandNameById(@PathVariable("id") Long id) {
        log.info("getBrandNameById admin: {}", id);
        BrandNameDetailResponse data = brandNameService.brandNameDetail(id);
        return ResponseUtils.ok(data);
    }

    @PostMapping("/create")
    public ResponseEntity<ResponseCommon<BrandNameCreateResponse>> createBrandNameApi(HttpServletRequest request, @Valid @RequestBody BrandNameCreateRequest brandNameCreateRequest) throws JsonProcessingException {
        log.info("createBrandNameApi client: {}", brandNameCreateRequest);
        String userId = request.getHeader(HeaderKey.USER_ID);

        if (brandNameCreateRequest.getTag() == 1) {
            return ResponseUtils.ok(MessageResponseDict.ADD_BRANDNAME_SUCCESS, brandNameService.createBrandName(brandNameCreateRequest, userId));
        } else {
            return ResponseUtils.ok(MessageResponseDict.REQUEST_APPROVE_BRANDNAME_SUCCESS, brandNameService.createBrandName(brandNameCreateRequest, userId));
        }
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<ResponseCommon<BrandNameUpdateResponse>> updateBrandNameApi(HttpServletRequest request, @PathVariable("id") Long labelId, @Valid @RequestBody BrandNameUpdateClientRequest brandNameUpdateClientRequest) throws IOException {
        log.info("updateBrandNameApi client: {}", brandNameUpdateClientRequest);
        String userId = request.getHeader(HeaderKey.USER_ID);

        if (brandNameUpdateClientRequest.getTag() == 1) {
            return ResponseUtils.ok(MessageResponseDict.UPDATE_BRANDNAME_SUCCESS, brandNameService.updateBrandName(labelId, brandNameUpdateClientRequest, userId));
        } else {
            return ResponseUtils.ok(MessageResponseDict.REQUEST_APPROVE_BRANDNAME_SUCCESS, brandNameService.updateBrandName(labelId, brandNameUpdateClientRequest, userId));
        }
    }

    @Operation(summary = "API xoá brand name")
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> deleteBrandName(@PathVariable("id") Long id) {
        log.info("deleteBrandName admin: {}", id);
        return ResponseUtils.ok(brandNameService.deleteBrandName(id));
    }

}
