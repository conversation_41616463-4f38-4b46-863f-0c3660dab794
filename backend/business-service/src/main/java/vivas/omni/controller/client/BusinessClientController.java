package vivas.omni.controller.client;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.request.BusinessDetailRequest;
import vivas.omni.response.BusinessDetailResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.service.BusinessService;
import vivas.omni.utils.ResponseUtils;

@Tag(name = "API Client quản lý doanh nghiệp")
@Slf4j
@RequestMapping("/v1/api/client/business")
@RestController
public class BusinessClientController {

    private final BusinessService businessService;

    public BusinessClientController(BusinessService businessService) {
        this.businessService = businessService;
    }

    @GetMapping("/{businessId}")
    public ResponseEntity<ResponseCommon<BusinessDetailResponse>> getBusinessById(@PathVariable Long businessId) {
        log.info("getBusinessById: {}", businessId);
        return ResponseUtils.ok(businessService.getBusinessById(businessId));
    }

    @PutMapping("/{businessId}")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> updateBusiness(@PathVariable Long businessId,
                                                                              @RequestBody @Valid BusinessDetailRequest businessDetail) {
        log.info("updateBusiness: {}", businessDetail);
        return ResponseUtils.ok( businessService.updateBusinessClient(businessId, businessDetail));
    }

}
