package vivas.omni.controller.client;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.controller.common.BaseController;
import vivas.omni.dto.OADto;
import vivas.omni.repository.AccountInfoRepository;
import vivas.omni.request.CreateOARequest;
import vivas.omni.request.UpdateOARequest;
import vivas.omni.response.PagingResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.service.OAService;
import vivas.omni.utils.ResponseUtils;
import vivas.omni.utils.group.ClientGroup;

import java.util.List;

@RestController
@RequestMapping("/v1/api/client/oa")
@Tag(name = "API quản lý official account")
@AllArgsConstructor
public class OAController extends BaseController {

    private final OAService service;
    private final AccountInfoRepository accountInfoRepository;

    @Operation(summary = "API xem danh sách official account")
    @GetMapping("")
    public ResponseEntity<ResponseCommon<PagingResponse<OADto>>> getOfficialAccountList(HttpServletRequest request,
                                                                                        @RequestParam Long userId,
                                                                                        @RequestParam(required = false) String name,
                                                                                        @RequestParam(required = false) Integer status,
                                                                                        @RequestParam(required = false) Long packageId,
                                                                                        @RequestParam int pageSize,
                                                                                        @RequestParam int pageIndex) {
        Long businessId = accountInfoRepository.getIdBusinessById(userId);
        return ResponseUtils.ok(service.searchOfficialAccount(name, status, packageId, businessId, pageSize, pageIndex));
    }

    @Operation(summary = "API xem chi tiết official account")
    @GetMapping("/{id}")
    public ResponseEntity<ResponseCommon<OADto>> getOfficialAccountById(@PathVariable("id") Long id) {
        return ResponseUtils.ok(service.officialAccountDetail(id));
    }

    @Operation(summary = "API thêm mới official account")
    @PostMapping("")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> addOfficialAccount(@RequestBody @Valid CreateOARequest request) {
        return ResponseUtils.ok(service.addOfficialAccount(request));
    }

    @Operation(summary = "API cập nhật official account")
    @PutMapping("")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> updateOfficialAccount(@RequestBody @Validated(ClientGroup.class) UpdateOARequest request) {
        return ResponseUtils.ok(service.updateOfficialAccount(request));
    }

    @Operation(summary = "API xoá official account")
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> deleteOfficialAccount(@PathVariable("id") Long id) {
        return ResponseUtils.ok(service.deleteOfficialAccount(id));
    }

    @Operation(summary = "API xem danh sách official account không paging")
    @GetMapping("/list")
    public ResponseEntity<ResponseCommon<List<OADto>>> getOfficialAccountList(@RequestParam Long businessId) {
        return ResponseUtils.ok(service.searchOANoPaging(businessId));
    }
}
