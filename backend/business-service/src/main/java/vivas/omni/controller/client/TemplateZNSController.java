package vivas.omni.controller.client;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.repository.AccountInfoRepository;
import vivas.omni.request.template.zns.CreateTemplateZNSRequest;
import vivas.omni.request.template.zns.UpdateTemplateZNSRequest;
import vivas.omni.response.PagingResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.response.template.ZNSTemplateListResponse;
import vivas.omni.service.TemplateZNSService;
import vivas.omni.utils.ResponseUtils;

import java.io.IOException;

@RestController
@RequestMapping("/v1/api/client/zns-template")
@Tag(name = "API quản lý template ZNS")
@RequiredArgsConstructor
public class TemplateZNSController {
    private final TemplateZNSService templateZNSService;
    private final AccountInfoRepository accountInfoRepository;

    @Operation(summary = "API thêm mới template zns")
    @PostMapping("")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> addTemplateZNS(@RequestBody @Valid CreateTemplateZNSRequest request) {
        return ResponseUtils.ok(templateZNSService.addTemplateZNS(request));
    }

    @Operation(summary = "API cập nhật template zns")
    @PutMapping("")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> updateTemplateZNS(@RequestBody @Valid UpdateTemplateZNSRequest request) throws IOException {
        return ResponseUtils.ok(templateZNSService.updateTemplateZNS(request));
    }

    @Operation(summary = "API xem danh sách template zns")
    @GetMapping("")
    public ResponseEntity<ResponseCommon<PagingResponse<ZNSTemplateListResponse>>> getTemplateZNSList(@RequestParam Long userId,
                                                                                                      @RequestParam(required = false) String word,
                                                                                                      @RequestParam(required = false) String oaId,
                                                                                                      @RequestParam(required = false) Integer status,
                                                                                                      @RequestParam(required = false) Integer templateType,
                                                                                                      @RequestParam int pageSize,
                                                                                                      @RequestParam int pageIndex) {
        Long businessId = accountInfoRepository.getIdBusinessById(userId);
        return ResponseUtils.ok(templateZNSService.searchTemplateZNS(word, oaId, status, templateType, businessId, pageSize, pageIndex));
    }

    @Operation(summary = "API xem chi tiết template zns")
    @GetMapping("/{id}")
    public ResponseEntity<ResponseCommon<ZNSTemplateListResponse>> getTemplateZNSById(@PathVariable("id") Long id) {
        return ResponseUtils.ok(templateZNSService.templateZNSDetail(id));
    }

    @Operation(summary = "API xoá template zns")
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseCommon<MessageResponseDict>> deleteTemplateZNS(@PathVariable("id") Long id) {
        return ResponseUtils.ok(templateZNSService.deleteTemplateZNS(id));
    }
}
