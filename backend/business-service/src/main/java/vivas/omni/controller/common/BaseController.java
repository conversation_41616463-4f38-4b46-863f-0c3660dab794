package vivas.omni.controller.common;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.ObjectUtils;

public class BaseController {
    protected Long getUserId(HttpServletRequest request) {
        Object object = request.getAttribute("userId");
        return ObjectUtils.isEmpty(object) ? 0 : (Long) object;
    }

    protected String getUsername(HttpServletRequest request) {
        Object object = request.getAttribute("username");
        return ObjectUtils.isEmpty(object) ? null : (String) object;
    }

    protected String getEmail(HttpServletRequest request) {
        Object object = request.getAttribute("email");
        return ObjectUtils.isEmpty(object) ? null : (String) object;
    }
}
