package vivas.omni.controller.common;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vivas.omni.response.DocumentResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.service.DocumentService;
import vivas.omni.utils.ResponseUtils;

import java.util.List;

@Tag(name = "API DS tài liệu import")
@RestController
@RequestMapping("/v1/api/document")
public class DocumentController {

    private final DocumentService documentService;

    public DocumentController(DocumentService documentService) {
        this.documentService = documentService;
    }

    @GetMapping
    public ResponseEntity<ResponseCommon<List<DocumentResponse>>> getDocumentBySmsTypeAndProfileGroup(
            @RequestParam("service_sms_type_id") Integer serviceSmsTypeId,
            @RequestParam(value = "profile_group_type", defaultValue = "1") Integer profileGroupType,
            @RequestParam(value = "label_type_id") Integer labelTypeId
    ) {
        return ResponseUtils.ok(documentService.getDocumentByServiceSmsType(serviceSmsTypeId, profileGroupType, labelTypeId));
    }

    @GetMapping("/other_type")
    public ResponseEntity<ResponseCommon<List<DocumentResponse>>> getDocumentBySmsTypeAndProfileGroupAndLabelType(
            @RequestParam("service_sms_type_id") Integer serviceSmsTypeId,
            @RequestParam(value = "profile_group_type", defaultValue = "1") Integer profileGroupType,
            @RequestParam(value = "label_type_id") List<Integer> labelTypeId
    ) {
        return ResponseUtils.ok(documentService.getDocumentByServiceSmsTypeLabelType(serviceSmsTypeId, profileGroupType, labelTypeId));
    }
}
