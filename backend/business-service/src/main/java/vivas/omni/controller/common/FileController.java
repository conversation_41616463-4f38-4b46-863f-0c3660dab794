package vivas.omni.controller.common;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vivas.omni.constant.MessageResponseDict;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.response.file.FileUploadResponse;
import vivas.omni.service.FileService;
import vivas.omni.utils.ResponseUtils;

import java.io.IOException;

@Tag(name = "API Common upload file")
@RequestMapping("/v1/api/common/file")
@RestController
@Slf4j
public class FileController {

    private final FileService fileService;

    @Autowired
    public FileController(FileService fileService) {
        this.fileService = fileService;
    }

    @PostMapping(value = "/upload", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<ResponseCommon<FileUploadResponse>> upload(@RequestPart("file") MultipartFile file,
//                                                                     @RequestParam("path_sub") String pathSub,
                                                                     @RequestParam("type") int uploadType) throws IOException {
        return ResponseUtils.ok(fileService.storeFile(file, uploadType));
    }

    @DeleteMapping(value = "/delete/{id}")
    public ResponseEntity<ResponseCommon<Long>> delete(@PathVariable("id") Long id) throws IOException {
        return ResponseUtils.ok(fileService.deleteFile(id));
    }

    @GetMapping("/event/file/{type}/{fileUploadId}")
    public  ResponseEntity<Resource> viewFile(@PathVariable(name = "fileUploadId") Long fileUploadId, @PathVariable(name = "type") String type) {
        return fileService.download(fileUploadId,type);
    }

}
