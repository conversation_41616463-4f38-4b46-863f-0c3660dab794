package vivas.omni.controller.common;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vivas.omni.response.LabelTypeResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.service.LabelTypeService;
import vivas.omni.utils.ResponseUtils;

import java.util.List;

@Tag(name = "API DS lĩnh vực")
@RestController
@RequestMapping("/v1/api/label_type")
public class LabelTypeController {

    private final LabelTypeService labelTypeService;

    public LabelTypeController(LabelTypeService labelTypeService) {
        this.labelTypeService = labelTypeService;
    }

    @GetMapping
    public ResponseEntity<ResponseCommon<List<LabelTypeResponse>>> getLabelTypesApi(
            @RequestParam(value = "service_sms_type_id") Integer serviceSmsTypeId,
            @RequestParam(value = "parent_id", required = false) Integer parentId) {
        return ResponseUtils.ok(labelTypeService.getLabelType(serviceSmsTypeId, parentId));
    }
}
