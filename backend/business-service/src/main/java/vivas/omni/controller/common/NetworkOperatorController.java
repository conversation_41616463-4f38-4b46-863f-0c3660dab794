package vivas.omni.controller.common;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vivas.omni.response.NetworkOperatorResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.service.NetworkOperatorService;
import vivas.omni.utils.ResponseUtils;

import java.util.List;

@Tag(name = "API DS nhà mạng")
@RestController
@RequestMapping("/v1/api/network")
public class NetworkOperatorController {

    private final NetworkOperatorService networkOperatorService;

    public NetworkOperatorController(NetworkOperatorService networkOperatorService) {
        this.networkOperatorService = networkOperatorService;
    }

    @GetMapping
    public ResponseEntity<ResponseCommon<List<NetworkOperatorResponse>>> getNetworkOperator() {
        return ResponseUtils.ok(networkOperatorService.getNetworkOperatorAll());
    }
}
