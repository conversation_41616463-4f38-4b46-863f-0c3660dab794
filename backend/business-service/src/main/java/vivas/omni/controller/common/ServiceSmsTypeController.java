package vivas.omni.controller.common;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vivas.omni.response.ServiceSmsTypeResponse;
import vivas.omni.response.common.ResponseCommon;
import vivas.omni.service.ServiceSmsTypeService;
import vivas.omni.utils.ResponseUtils;

import java.util.List;

@Tag(name = "API DS loại dịch vụ gửi tin (CSKH/QC)")
@RestController
@RequestMapping("/v1/api/service_sms")
public class ServiceSmsTypeController {

    private final ServiceSmsTypeService serviceSmsTypeService;

    public ServiceSmsTypeController(ServiceSmsTypeService serviceSmsTypeService) {
        this.serviceSmsTypeService = serviceSmsTypeService;
    }

    @GetMapping
    public ResponseEntity<ResponseCommon<List<ServiceSmsTypeResponse>>> getAll() {
        return ResponseUtils.ok(serviceSmsTypeService.getAll());
    }
}
