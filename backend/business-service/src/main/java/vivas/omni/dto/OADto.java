package vivas.omni.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OADto {
    private long id;

    private String oaName;

    private Integer status;

    @JsonProperty("status_name")
    private String statusName;

    @JsonProperty("oa_id")
    private String oaId;

    @JsonProperty("expired_at")
    private LocalDateTime expiredAt;

    @JsonProperty("package_id")
    private Long packageId;

    @JsonProperty("business_name")
    private String businessName;
}
