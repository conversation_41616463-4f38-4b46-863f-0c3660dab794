package vivas.omni.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class StatusUpdateRequest {

    @NotNull(message = "Hành động không được để trống")
    private Integer action; // 1: duyet, 2: tu choi

    private String reason;

    private Long expiredAt;

    private Long documentExpiredAt;

    private List<Integer> networkOperatorIds;
}
