package vivas.omni.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * DTO for {@link vivas.omni.repository.entity.TemplateParam}
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TemplateParamDTO {
//    Long id;

    @NotBlank(message = "Tên tham số không được để trống")
    String name;

    @JsonProperty("field_360_id")
    Long field360Id;

    Long fieldZaloId;

    @Size(max = 100, message = "Tên tham số không được quá 100 ký tự")
    @NotBlank(message = "Nội dung mẫu không được để trống")
    String sampleData;
}