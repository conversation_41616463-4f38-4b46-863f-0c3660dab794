package vivas.omni.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import vivas.omni.constant.MessageResponseDict;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class BusinessException extends RuntimeException {

    private MessageResponseDict responseDict;
    private List<String> params;

    public BusinessException(MessageResponseDict responseDict) {
        super();
        this.responseDict = responseDict;
    }

    public BusinessException(MessageResponseDict responseDict, List<String> params) {
        this.responseDict = responseDict;
        this.params = params;
    }

}
