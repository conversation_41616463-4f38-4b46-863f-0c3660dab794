package vivas.omni.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import vivas.omni.repository.entity.Template;
import vivas.omni.repository.projection.SMSTemplateProjection;
import vivas.omni.request.template.sms.CreateSMSTemplateRequest;
import vivas.omni.response.template.SMSTemplateResponse;

@Mapper(componentModel = "spring")
public interface SMSTemplateMapper {
    /**
     * Maps a CreateSMSTemplateRequest to Template entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "name", ignore = true)
    @Mapping(target = "title", ignore = true)
    @Mapping(target = "channelId", ignore = true)
    @Mapping(target = "businessId", ignore = true)
    @Mapping(target = "zaloId", ignore = true)
    @Mapping(target = "mainButton", ignore = true)
    @Mapping(target = "extraButton", ignore = true)
    Template toEntity(CreateSMSTemplateRequest createSMSTemplateRequest);

    /**
     * Maps a Template entity to SMSTemplateResponse
     */
    @Mapping(target = "labelName", ignore = true)
    @Mapping(target = "params", ignore = true)
    SMSTemplateResponse toResponse(Template template);

    /**
     * Maps an SMSTemplateProjection to SMSTemplateResponse
     */
    @Mapping(target = "params", ignore = true)
    SMSTemplateResponse toResponse(SMSTemplateProjection projection);
}
