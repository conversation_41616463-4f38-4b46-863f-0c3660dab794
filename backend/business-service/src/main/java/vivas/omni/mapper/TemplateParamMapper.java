package vivas.omni.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import vivas.omni.dto.TemplateParamDTO;
import vivas.omni.repository.entity.TemplateParam;

@Mapper(componentModel = "spring")
public interface TemplateParamMapper {
    /**
     * Maps a TemplateParamDTO to a TemplateParam entity
     * The templateId should be set separately after mapping
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "templateId", ignore = true)
    TemplateParam toEntity(TemplateParamDTO dto);

    /**
     * Maps a TemplateParam entity to a TemplateParamDTO
     */
    TemplateParamDTO toDTO(TemplateParam param);
}

