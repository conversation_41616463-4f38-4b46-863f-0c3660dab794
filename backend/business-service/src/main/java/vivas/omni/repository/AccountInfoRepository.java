package vivas.omni.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vivas.omni.repository.entity.AccountInfo;

import java.util.List;

public interface AccountInfoRepository extends JpaRepository<AccountInfo, Long> {

    @Query(value = "SELECT idBusiness FROM AccountInfo WHERE id =:userId")
    Long getIdBusinessById(Long userId);
    
    List<AccountInfo> findByIdBusinessAndStatus(Long idBusiness, Integer status);
}
