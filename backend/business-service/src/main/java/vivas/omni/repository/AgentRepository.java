package vivas.omni.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import vivas.omni.repository.entity.Agent;
import vivas.omni.repository.projection.AccountNumberProjection;
import vivas.omni.repository.projection.AgentDetailProjection;
import vivas.omni.repository.projection.AgentProjection;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface AgentRepository extends JpaRepository<Agent, Long>, JpaSpecificationExecutor<Agent> {

    Optional<Agent> findByIdAndStatus(Long id, Integer status);

    @Query("SELECT a.id AS agentId, a.agentName AS agentName, a.agentCode AS agentCode, a.agentEmail AS agentEmail, a.agentPhone AS agentPhone, " +
            "a.agentType AS agentType, a.contactEmail AS contactEmail, a.contactName AS contactName, a.contactPhone AS contactPhone, " +
            "a.taxCode AS taxCode, a.address AS address, a.status AS status, " +
            "( " +
            "                SELECT COUNT(ai.id)" +
            "                FROM Agent a" +
            "                LEFT JOIN AccountInfo ai ON ai.idAgent = a.id" +
            "                WHERE a.id = :agentId" +
            "                GROUP BY a.id" +
            "            ) AS accountNumber" +
            " FROM Agent a WHERE a.id = :agentId")
    AgentDetailProjection getAgentDetailById(Long agentId);

    @Query(value = """
                SELECT a.id AS id, COUNT(ai.id) AS accountNumber
                FROM Agent a
                LEFT JOIN AccountInfo ai ON ai.idAgent = a.id
                WHERE a.id IN (:agentIds)
                GROUP BY a.id
            """)
    List<AccountNumberProjection> getAccountNumbersByAgentIds(List<Long> agentIds);

    List<Agent> findByStatus(Integer status);

    @Query(value = """
        SELECT
            AG.ID AS id,
            AG.AGENT_NAME AS agentName,
            AG.AGENT_CODE AS agentCode,
            AG.AGENT_PHONE AS agentPhone,
            AG.ADDRESS AS address,
            AG.AGENT_TYPE AS agentType,
            AN.ACCOUNT_NUMBER AS accountNumber,
            AG.STATUS AS status
        FROM AGENT AG
        INNER JOIN (
            SELECT
                A.ID,
                COUNT(AI.ID_AGENT) AS ACCOUNT_NUMBER
            FROM AGENT A
            LEFT JOIN ACCOUNT_INFO AI ON AI.ID_AGENT = A.ID
            WHERE (:keyword IS NULL OR (
                      LOWER(A.AGENT_NAME) LIKE '%' || LOWER(:keyword) || '%' COLLATE BINARY_AI
                   OR LOWER(A.AGENT_CODE) LIKE '%' || LOWER(:keyword) || '%' COLLATE BINARY_AI
                   OR A.AGENT_PHONE LIKE :keyword || '%'
                   OR LOWER(A.ADDRESS) LIKE '%' || LOWER(:keyword) || '%'))
              AND (:name IS NULL OR LOWER(A.AGENT_NAME) LIKE '%' || LOWER(:name) || '%' COLLATE BINARY_AI)
              AND (:agentType IS NULL OR A.AGENT_TYPE = :agentType)
              AND (:status IS NULL OR A.STATUS = :status)
            GROUP BY A.ID
        ) AN ON AN.ID = AG.ID
        ORDER BY AG.UPDATED_AT DESC
    """, countQuery = """
            SELECT COUNT(*)
            FROM AGENT AG
            INNER JOIN (
                SELECT A.ID, COUNT(AI.ID_AGENT) AS ACCOUNT_NUMBER
                FROM AGENT A
                LEFT JOIN ACCOUNT_INFO AI ON AI.ID_AGENT = A.ID
                WHERE
                    (:keyword IS NULL
                        OR (
                            LOWER(A.AGENT_NAME) LIKE '%' || LOWER(:keyword) || '%' COLLATE BINARY_AI
                            OR LOWER(A.AGENT_CODE) LIKE '%' || LOWER(:keyword) || '%' COLLATE BINARY_AI
                            OR A.AGENT_PHONE LIKE :keyword || '%'
                            OR LOWER(A.ADDRESS) LIKE '%' || LOWER(:keyword) || '%'
                        )
                    )
                    AND (:name IS NULL OR LOWER(A.AGENT_NAME) LIKE '%' || LOWER(:name) || '%' COLLATE BINARY_AI)
                    AND (:agentType IS NULL OR A.AGENT_TYPE = :agentType)
                    AND (:status IS NULL OR A.STATUS = :status)
                GROUP BY A.ID
            ) AN ON AN.ID = AG.ID
            """,
    nativeQuery = true)
    Page<AgentProjection> searchAgentPagingByCondition(String keyword, String name, Integer agentType, Integer status, Pageable pageable);
}
