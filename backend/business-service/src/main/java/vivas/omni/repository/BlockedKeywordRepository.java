package vivas.omni.repository;

import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import vivas.omni.repository.entity.BlockedKeyword;

public interface BlockedKeywordRepository extends JpaRepository<BlockedKeyword, Long> {
    Optional<BlockedKeyword> findByKeywordIgnoreCase(String keyword);

    Page<BlockedKeyword> findByKeywordContainingIgnoreCase(String keywords, Pageable pageable);
}
