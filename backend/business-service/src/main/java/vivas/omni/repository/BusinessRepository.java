package vivas.omni.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import vivas.omni.repository.entity.Business;
import vivas.omni.repository.projection.AccountNumberProjection;
import vivas.omni.repository.projection.BusinessDetailProjection;
import vivas.omni.repository.projection.BusinessProjection;

import java.util.Collection;
import java.util.List;

public interface BusinessRepository extends JpaRepository<Business, Long>, JpaSpecificationExecutor<Business> {

    @Query(value = """
                SELECT ID, BUSINESS_NAME 
                FROM BUSINESS
                WHERE ID IN (SELECT DISTINCT BUSINESS_ID FROM LABEL WHERE CHANNEL_ID = 1)
            """, nativeQuery = true)
    List<BusinessDetailProjection> getListBusinessByLabel();

    @Query(value = """
                SELECT b.ID, COUNT(ai.ID) AS accountNumber
                FROM BUSINESS b
                LEFT JOIN ACCOUNT_INFO ai ON ai.ID_BUSINESS = b.ID
                WHERE b.ID IN (:businessIds)
                GROUP BY b.ID
            """, nativeQuery = true)
    List<AccountNumberProjection> getAccountNumbersByBusinessIds(List<Long> businessIds);

    @Query(value = "SELECT BUSINESS_NAME FROM BUSINESS WHERE ID = :id", nativeQuery = true)
    String findBusinessNameById(Long id);

    @Query(value = """
                SELECT
                    B.BUSINESS_NAME     AS businessName,
                    B.BUSINESS_CODE     AS businessCode,
                    B.ADDRESS           AS address,
                    B.TAX_CODE          AS taxCode,
                    B.BUSINESS_PHONE    AS businessPhone,
                    B.BUSINESS_EMAIL    AS businessEmail,
                    B.CONTACT_NAME      AS contactName,
                    B.CONTACT_PHONE     AS contactPhone,
                    B.CONTACT_EMAIL     AS contactEmail,
                    B.CUSTOMER_SOURCE   AS customerSource,
                    B.AGENT_ID          AS agentId,
                    A.AGENT_NAME        AS agentName,
                    B.STATUS         AS status,
                    (
                        SELECT COUNT(AC.ID)
                        FROM BUSINESS BU
                        LEFT JOIN ACCOUNT_INFO AC ON AC.ID_BUSINESS = BU.ID
                        WHERE BU.ID = :businessId
                        GROUP BY BU.ID
                    ) AS accountNumber
                FROM BUSINESS B
                LEFT JOIN AGENT A ON B.AGENT_ID = A.ID
                WHERE B.ID = :businessId
            
            """, nativeQuery = true)
    BusinessDetailProjection getBusinessDetailById(Long businessId);

    List<Business> findByStatus(Integer status);

    @Query(value = """
            SELECT BU.id AS id,
                   BU.business_name AS businessName,
                   BU.business_code AS businessCode,
                   BU.business_phone AS businessPhone,
                   BU.address AS address,
                   BU.customer_source AS customerSource,
                   AN.account_number AS accountNumber,
                   BU.status AS status
            FROM business BU
            INNER JOIN 
            (
                SELECT b.ID, COUNT(ai.ID_BUSINESS) AS account_number
                FROM BUSINESS b
                LEFT JOIN ACCOUNT_INFO ai ON ai.ID_BUSINESS = b.ID 
                WHERE 
                    (:keyword IS NULL
                       OR (LOWER(b.business_name) LIKE '%' || LOWER(:keyword) || '%' COLLATE BINARY_AI
                       OR LOWER(b.business_code) LIKE '%' || LOWER(:keyword) || '%' COLLATE BINARY_AI
                       OR b.business_phone LIKE :keyword || '%'
                       OR LOWER(b.address) LIKE '%' || LOWER(:keyword) || '%'))
                    AND (:name IS NULL
                         OR LOWER(b.business_name) LIKE '%' || LOWER(:name) || '%' COLLATE BINARY_AI)
                    AND (:customerSource IS NULL
                         OR b.customer_source = :customerSource)
                    AND (:status IS NULL
                         OR b.status = :status)
                GROUP BY b.ID
            ) AN ON AN.ID = BU.ID
            ORDER BY BU.updated_at DESC
        """,
            countQuery = """
            SELECT COUNT(*)
            FROM business BU
            INNER JOIN 
            (
                SELECT b.ID
                FROM BUSINESS b
                LEFT JOIN ACCOUNT_INFO ai ON ai.ID_BUSINESS = b.ID 
                WHERE 
                    (:keyword IS NULL
                       OR (LOWER(b.business_name) LIKE '%' || LOWER(:keyword) || '%' COLLATE BINARY_AI
                       OR LOWER(b.business_code) LIKE '%' || LOWER(:keyword) || '%' COLLATE BINARY_AI
                       OR b.business_phone LIKE :keyword || '%'
                       OR LOWER(b.address) LIKE '%' || LOWER(:keyword) || '%'))
                    AND (:name IS NULL
                         OR LOWER(b.business_name) LIKE '%' || LOWER(:name) || '%' COLLATE BINARY_AI)
                    AND (:customerSource IS NULL
                         OR b.customer_source = :customerSource)
                    AND (:status IS NULL
                         OR b.status = :status)
                GROUP BY b.ID
            ) AN ON AN.ID = BU.ID
        """,
            nativeQuery = true)
    Page<BusinessProjection> searchBusinessPagingByCondition(
            String keyword,
            String name,
            Integer customerSource,
            Integer status,
            Pageable pageable
    );


}
