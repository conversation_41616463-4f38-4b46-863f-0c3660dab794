package vivas.omni.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vivas.omni.repository.entity.Document;

import java.util.List;

public interface DocumentRepository extends JpaRepository<Document, Long> {

    @Query(value = """
        SELECT d 
        FROM Document d 
        where d.serviceSmsTypeId in (:serviceSmsTypeIds) 
            and d.profileLabelGroupType = :profileGroupType
            and (d.labelTypeId is null or d.labelTypeId = :labelTypeId)
        order by d.orderBy
    """)
    List<Document> getAllDocumentByServiceSmsTypeAndProfile(List<Integer> serviceSmsTypeIds, Integer profileGroupType, Integer labelTypeId);

    @Query(value = """
        SELECT d 
        FROM Document d 
        where d.serviceSmsTypeId in (:serviceSmsTypeIds) 
            and d.profileLabelGroupType = :profileGroupType
            and d.labelTypeId IN (:labelTypeId)
        order by d.orderBy
    """)
    List<Document> getDocumentByServiceSmsTypeIdAndLabelTypeId(List<Integer> serviceSmsTypeIds, Integer profileGroupType, List<Integer> labelTypeId);
}
