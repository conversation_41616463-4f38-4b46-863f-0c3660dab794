package vivas.omni.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vivas.omni.repository.entity.FileUpload;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface FileUploadRepository extends JpaRepository<FileUpload, Long> {
    Optional<FileUpload> findByLabelId(Long labelId);
    List<FileUpload> findAllByLabelIdAndServiceSmsTypeId(Long labelId, Long serviceSmsTypeId);

    List<FileUpload> findFileUploadByLabelId(Long labelId);

    List<FileUpload> findFileUploadByFileUploadIdIn(List<Long> fileUploadIds);

    List<FileUpload> findFileUploadByTemplateId(Long templateId);

    List<FileUpload> findFileUploadByTemplateIdIn(List<Long> templateIds);
}
