package vivas.omni.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vivas.omni.repository.entity.LabelNetwork;
import vivas.omni.repository.entity.NetworkOperator;
import vivas.omni.repository.projection.LabelNetworkDetailProjection;

import java.util.List;

public interface LabelNetworkRepository extends JpaRepository<LabelNetwork, Long> {

    @Query(value = """
        SELECT
        	l.LABLE_NETWORK_ID as labelNetworkId,
        	L.LABEL_ID as labelId,
        	L.NETWORK_OPERATOR_ID as networkOperatorId,
        	L.NETWORK_STATUS as networkStatus,
        	N.NAME as name
        FROM
        	label_network l
        INNER JOIN network_operator n ON
        	l.network_operator_id = n.network_operator_id
        WHERE
        	l.label_id = :labelId
    """, nativeQuery = true)
    List<LabelNetworkDetailProjection> getLabelNetworkDetail(Long labelId);

    List<LabelNetwork> getLabelNetworkByLabelId(Long labelId);

    @Query(value = "SELECT * FROM label_network l WHERE l.label_id = :labelId and l.network_operator_id IN (:networkOperatorId)", nativeQuery = true)
    List<LabelNetwork> getLabelNetworkByLabelAndNetwork(Long labelId, List<Integer> networkOperatorId);
}
