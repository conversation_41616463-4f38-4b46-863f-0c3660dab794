package vivas.omni.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vivas.omni.repository.entity.Label;
import vivas.omni.repository.projection.BrandNameProjection;
import vivas.omni.repository.projection.OAProjection;

import java.util.List;
import java.util.Optional;

public interface LabelRepository extends JpaRepository<Label, Integer>, JpaSpecificationExecutor<Label> {
    Optional<Label> findById(Long id);

    @Query(value = """
            SELECT o.ID as id,
                   o.OA_ID AS oaId,
                   o.NAME AS name,
                   b.BUSINESS_NAME AS businessName,
                   o.PACKAGE_ID AS packageId,
                   o.STATUS AS status,
                   o.EXPIRED_AT AS expiredAt
            FROM LABEL o
            LEFT JOIN BUSINESS b ON o.BUSINESS_ID = b.ID
            WHERE o.CHANNEL_ID = 1
                AND (:status is NULL OR o.STATUS = :status)
                AND (:packageId is NULL OR o.PACKAGE_ID = :packageId)
                AND (:businessId is NULL OR o.BUSINESS_ID = :businessId)
                AND (
                    (:oaName IS NULL OR LOWER(o.NAME) LIKE LOWER('%' || :oaName || '%') COLLATE BINARY_AI)
                    OR (:oaId IS NOT NULL AND o.OA_ID = :oaId)
                )
            ORDER BY
                CASE WHEN o.STATUS = 3 THEN 0 ELSE 1 END,
                o.UPDATED_AT DESC
            """,
            countQuery = """
                    SELECT COUNT(*)
                    FROM LABEL o
                    LEFT JOIN BUSINESS b ON o.BUSINESS_ID = b.ID
                    WHERE o.CHANNEL_ID = 1
                        AND (:status is NULL OR o.STATUS = :status)
                        AND (:packageId is NULL OR o.PACKAGE_ID = :packageId)
                        AND (:businessId is NULL OR o.BUSINESS_ID = :businessId)
                        AND (
                            (:oaName IS NULL OR LOWER(o.NAME) LIKE LOWER('%' || :oaName || '%') COLLATE BINARY_AI)
                            OR (:oaId IS NOT NULL AND o.OA_ID = :oaId)
                        )
                    """, nativeQuery = true
    )
    Page<OAProjection> oaSearchByFilter(
            @Param("status") Integer status,
            @Param("packageId") Long packageId,
            @Param("businessId") Long businessId,
            @Param("oaId") String oaId,
            @Param("oaName") String oaName,
            Pageable pageable
    );

    @Query(value = """
    SELECT o.ID as id,
           o.OA_ID AS oaId,
           o.NAME AS name,
           o.STATUS AS status
           FROM LABEL o
           WHERE o.CHANNEL_ID = 1 AND o.STATUS = 1 AND o.BUSINESS_ID = :businessId
    """, nativeQuery = true)
    List<OAProjection> oaSearchNoPaging(Long businessId);

    @Query(value = """
            SELECT o.id as id,
                   o.oaId AS oaId,
                   o.name AS name,
                   b.businessName AS businessName,
                   o.packageId AS packageId,
                   o.status AS status,
                   o.expiredAt AS expiredAt
            FROM Label o
            LEFT JOIN Business b ON o.businessId = b.id
            WHERE o.id = :id
            """)
    OAProjection getOAById(Long id);

    @Query(value = "SELECT COUNT(*) FROM Label l WHERE l.oaId = :oaId")
    int existsByOAId(String oaId);

    @Query(value = "SELECT COUNT(*) FROM Label l WHERE l.oaId = :oaId and l.id != :id")
    int existsByOAIdWhenUpdate(String oaId, Long id);

    @Query(value = """
                SELECT
                	L.ID,
                	L.NAME,
                	B.BUSINESS_NAME AS businessName,
                	S.NAME AS smsType,
                    S.ID AS smsTypeId,
                	L.CREATED_AT AS createdAt,
                	L.EXPIRED_AT AS expiredAt,
                	L.STATUS
                FROM LABEL L
                INNER JOIN BUSINESS B ON B.ID = L.BUSINESS_ID
                INNER JOIN SERVICE_SMS_TYPE S ON S.ID = L.SERVICE_SMS_TYPE_ID
                WHERE (:brandName IS NULL OR LOWER(L.NAME) LIKE '%' || LOWER(:brandName) || '%' COLLATE BINARY_AI)
                	AND (:businessId IS NULL OR L.BUSINESS_ID = :businessId)
                	AND (:smsTypeId IS NULL OR L.SERVICE_SMS_TYPE_ID = :smsTypeId)
                	AND (
                     (:status IS NOT NULL AND L.STATUS = :status)
                     OR (:status IS NULL AND :userId IS NULL AND L.STATUS IN (1, 2, 3))
                	 OR (:status IS NULL AND :userId IS NOT NULL AND L.STATUS IN (-1, 1, 2, 3, 4))
                    )
                	AND L.CHANNEL_ID = 2
            """,
            countQuery = """
                        SELECT COUNT(*)
                        FROM LABEL L
                        INNER JOIN BUSINESS B ON B.ID = L.BUSINESS_ID
                        INNER JOIN SERVICE_SMS_TYPE S ON S.ID = L.SERVICE_SMS_TYPE_ID
                        WHERE (:brandName IS NULL OR LOWER(L.NAME) LIKE '%' || LOWER(:brandName) || '%' COLLATE BINARY_AI)
                          AND (:businessId IS NULL OR L.BUSINESS_ID = :businessId)
                          AND (:smsTypeId IS NULL OR L.SERVICE_SMS_TYPE_ID = :smsTypeId)
                          AND (
                             (:status IS NOT NULL AND L.STATUS = :status)
                             OR (:status IS NULL AND :userId IS NULL AND L.STATUS IN (1, 2, 3))
                        	 OR (:status IS NULL AND :userId IS NOT NULL AND L.STATUS IN (-1, 1, 2, 3, 4))
                            )
                          AND L.CHANNEL_ID = 2
                    """, nativeQuery = true)
    Page<BrandNameProjection> searchBrandNameByFilter(String brandName, Long businessId, Integer smsTypeId, Integer status, Long userId, Pageable pageable);
}
