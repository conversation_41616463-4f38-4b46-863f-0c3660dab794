package vivas.omni.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vivas.omni.repository.entity.LabelType;

import java.util.List;

public interface LabelTypeRepository extends JpaRepository<LabelType, Long> {

    @Query(value = "SELECT lt from LabelType lt where lt.serviceSmsTypeId IN (:serviceSmsTypeId) and lt.parentId is null order by lt.id")
    List<LabelType> findAllLabelTypesParent(List<Integer> serviceSmsTypeId);

    @Query(value = "SELECT lt from LabelType lt where lt.serviceSmsTypeId in (:serviceSmsTypeId) and lt.parentId = :parentId order by lt.id")
    List<LabelType> findAllLabelTypesSub(List<Integer> serviceSmsTypeId, Integer parentId);
}
