package vivas.omni.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vivas.omni.repository.entity.NetworkOperator;

import java.util.List;

public interface NetworkOperatorRepository extends JpaRepository<NetworkOperator, Long> {

    @Query(value = "SELECT n FROM NetworkOperator n order by n.networkOperatorId")
    List<NetworkOperator> getAllNetworkOperator();

    List<NetworkOperator> findNetworkOperatorByNetworkOperatorIdIn(List<Long> networkOperatorIds);
}
