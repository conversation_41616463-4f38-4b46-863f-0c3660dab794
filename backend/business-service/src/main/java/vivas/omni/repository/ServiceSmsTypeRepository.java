package vivas.omni.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vivas.omni.repository.entity.ServiceSmsType;

public interface ServiceSmsTypeRepository extends JpaRepository<ServiceSmsType, Long> {

    @Query("SELECT c FROM ServiceSmsType c WHERE c.id = :id ")
    String findContractTypeName(@Param("id") Long id);
}
