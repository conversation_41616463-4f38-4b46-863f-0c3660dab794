package vivas.omni.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vivas.omni.repository.entity.TemplateParam;

import java.util.List;

public interface TemplateParamRepository extends JpaRepository<TemplateParam, Long> {

    @Query("SELECT tp FROM TemplateParam tp WHERE tp.templateId IN :templateIds")
    List<TemplateParam> findByTemplateIdIn(@Param("templateIds") List<Long> templateIds);

    /**
     * Tìm tất cả tham số theo ID template
     */
    List<TemplateParam> findAllByTemplateId(Long templateId);

    /**
     * Xóa tất cả tham số theo ID template
     */
    @Modifying
    @Query("DELETE FROM TemplateParam tp WHERE tp.templateId = :templateId")
    void deleteAllByTemplateId(@Param("templateId") Long templateId);
}
