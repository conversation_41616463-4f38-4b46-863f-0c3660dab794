package vivas.omni.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vivas.omni.repository.entity.Template;
import vivas.omni.repository.projection.SMSTemplateProjection;
import vivas.omni.repository.projection.TemplateZNSProjection;

import java.util.List;

public interface TemplateRepository extends JpaRepository<Template, Long> {

    @Query(value = """
            SELECT t.ID as id,
                   t.TYPE_ID AS typeId,
                   t.NAME AS name,
                   l.NAME AS labelName,            
                   t.STATUS AS status,
                   t.TITLE AS title,
                   TO_CHAR(t.CONTENT) AS content,
                   t.NOTE AS note,
                    t.MAIN_BUTTON AS mainButton,
                    t.EXTRA_BUTTON AS extraButton,
                    t.TABLE_DATA AS tableData,
                    tb.BANK_NAME AS bankName,
                    tb.ACCOUNT_NAME accountName,
                    tb.ACCOUNT_NUMBER accountNumber,
                    tb.PRICE AS price,
                    tb.CONTENT AS contentBank,
                    tv.PRICE_VOUCHER AS priceVoucher,
                    tv.CONDITION AS condition,
                    tv.START_DATE AS startDate,
                    tv.END_DATE AS endDate,
                    tv.VOUCHER_CODE AS voucherCode
            FROM TEMPLATE t 
            LEFT JOIN LABEL l ON t.LABEL_ID = l.ID
            LEFT JOIN TEMPLATE_BANK tb ON t.ID = tb.TEMPLATE_ID
            LEFT JOIN TEMPLATE_VOUCHER tv ON t.ID = tv.TEMPLATE_ID
            WHERE t.CHANNEL_ID = 1
                AND (:status is NULL OR t.STATUS = :status)
                AND (:templateType is NULL OR t.TYPE_ID = :templateType)
                AND (:businessId is NULL OR t.BUSINESS_ID = :businessId)
                AND (:oaId IS NULL OR l.OA_ID = :oaId)
                AND (
                    (:tempName IS NULL OR LOWER(t.NAME) LIKE LOWER('%' || :tempName || '%') COLLATE BINARY_AI)
                    OR (:zaloId IS NOT NULL AND t.ZALO_ID = :zaloId)
                ) 
            """, countQuery = """
                SELECT COUNT(*)
                FROM TEMPLATE t 
                LEFT JOIN LABEL l ON t.LABEL_ID = l.ID
                LEFT JOIN TEMPLATE_BANK tb ON t.ID = tb.TEMPLATE_ID
                LEFT JOIN TEMPLATE_VOUCHER tv ON t.ID = tv.TEMPLATE_ID
                WHERE t.CHANNEL_ID = 1
                    AND (:status is NULL OR t.STATUS = :status)
                    AND (:templateType is NULL OR t.TYPE_ID = :templateType)
                    AND (:businessId is NULL OR t.BUSINESS_ID = :businessId)
                    AND (:oaId IS NULL OR l.OA_ID = :oaId)
                    AND (
                        (:tempName IS NULL OR LOWER(t.NAME) LIKE LOWER('%' || :tempName || '%') COLLATE BINARY_AI)
                        OR (:zaloId IS NOT NULL AND t.ZALO_ID = :zaloId)
                    )
            """, nativeQuery = true)
    Page<TemplateZNSProjection> templateZNSByFilter(Integer status, Integer templateType, Long businessId, String oaId, String tempName, String zaloId, Pageable pageable);

    @Query(value = """
            SELECT t.ID as id,
                   t.TYPE_ID AS typeId,
                   t.NAME AS name,
                   l.NAME AS labelName,
                   b.BUSINESS_NAME AS businessName,
                   t.STATUS AS status,
                   t.TITLE AS title,
                   TO_CHAR(t.CONTENT) AS content,
                   t.NOTE AS note,
                    t.MAIN_BUTTON AS mainButton,
                    t.EXTRA_BUTTON AS extraButton,
                    t.TABLE_DATA AS tableData,
                    tb.BANK_NAME AS bankName,
                    tb.ACCOUNT_NAME accountName,
                    tb.ACCOUNT_NUMBER accountNumber,
                    tb.PRICE AS price,
                    tb.CONTENT AS contentBank,
                    tv.PRICE_VOUCHER AS priceVoucher,
                    tv.CONDITION AS condition,
                    tv.START_DATE AS startDate,
                    tv.END_DATE AS endDate,
                    tv.VOUCHER_CODE AS voucherCode
            FROM TEMPLATE t 
            LEFT JOIN BUSINESS b ON t.BUSINESS_ID = b.ID
            LEFT JOIN LABEL l ON t.LABEL_ID = l.ID
            LEFT JOIN TEMPLATE_BANK tb ON t.ID = tb.TEMPLATE_ID
            LEFT JOIN TEMPLATE_VOUCHER tv ON t.ID = tv.TEMPLATE_ID
            WHERE t.ID = :id
            """, nativeQuery = true)
    TemplateZNSProjection getTemplateZNSById(Long id);

    @Query(value = """
            SELECT t.LABEL_ID    AS labelId,
                   l.NAME        AS labelName,
                   t.TYPE_ID     AS typeId,
                   TO_CHAR(t.CONTENT) AS content,
                   t.STATUS      AS status,
                   t.UNICODE     AS unicode,
                   t.NOTE        AS note,
                   t.ID          AS id,
                   t.BUSINESS_ID AS businessId,
                   t.CREATED_AT  AS createdAt
            FROM TEMPLATE t
                     LEFT JOIN LABEL l ON t.LABEL_ID = l.ID
            WHERE t.CHANNEL_ID = :channelId
              AND (:keyword IS NULL OR :keyword = '' OR t.CONTENT LIKE '%' || :keyword || '%')
              AND (:labelId IS NULL OR t.LABEL_ID = :labelId)
              AND (:typeId IS NULL OR t.TYPE_ID = :typeId)
              AND (:status IS NULL OR t.STATUS = :status)
              AND (:unicode IS NULL OR t.UNICODE = :unicode)
              AND (:businessId IS NULL OR t.BUSINESS_ID = :businessId)
            """, nativeQuery = true)
    Page<SMSTemplateProjection> searchTemplates(
            @Param("channelId") Long channelId,
            @Param("keyword") String keyword,
            @Param("labelId") Long labelId,
            @Param("typeId") Long typeId,
            @Param("status") Integer status,
            @Param("unicode") Boolean unicode,
            @Param("businessId") Long businessId,
            Pageable pageable);

    /**
     * Find all templates by channel ID
     */
    @Query("SELECT t FROM Template t WHERE t.channelId = :channelId")
    List<Template> findAllByChannelId(@Param("channelId") Long channelId);
}

