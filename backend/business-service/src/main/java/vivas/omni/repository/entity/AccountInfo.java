package vivas.omni.repository.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Table(name = "ACCOUNT_INFO") // Oracle thường sử dụng tên viết hoa
public class AccountInfo extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ACCOUNT_INFO_SEQ")
    @SequenceGenerator(name = "ACCOUNT_INFO_SEQ", sequenceName = "ACCOUNT_INFO_ID_SEQ", allocationSize = 1)
    @Column(name = "ID")
    private Long id;

    @Column(name = "PASSWORD", nullable = false, length = 500)
    private String password;

    @Column(name = "ROLE")
    private Integer role;

    @Column(name = "EMAIL", length = 100)
    private String email;

    @Column(name = "GROUP_PERMISSION")
    private Long groupPermission;

    @Column(name = "STATUS")
    private Integer status;

    @Column(name = "FULL_NAME")
    private String fullName;

    @Column(name = "GENDER")
    private Integer gender;

    @Column(name = "AVATAR", length = 1000)
    private String avatar;

    @Column(name = "PHONE", length = 20)
    private String phone;

    @Column(name = "ADDRESS", length = 500)
    private String address;

    @Column(name = "ACCOUNT_TYPE")
    private Integer accountType;

    @Column(name = "ID_AGENT")
    private Long idAgent;

    @Column(name = "ID_BUSINESS")
    private Long idBusiness;

    @Column(name = "OTP_ACTIVE")
    private Integer otpActive;

    @Column(name = "IS_FIRST_LOGIN")
    private Integer isFirstLogin;

    @PrePersist
    protected void onCreate() {
        this.otpActive = 1;
        this.isFirstLogin = 1;
        this.status = 1;
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
}