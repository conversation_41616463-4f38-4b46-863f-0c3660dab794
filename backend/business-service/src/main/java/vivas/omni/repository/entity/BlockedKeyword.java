package vivas.omni.repository.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Getter
@Setter
@ToString(callSuper = true)
@Table(name = "blocked_keyword")
public class BlockedKeyword extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BLOCKED_KEYWORK_SEQ")
    @SequenceGenerator(name = "BLOCKED_KEYWORK_SEQ", sequenceName = "BLOCKED_KEYWORD_ID_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "keyword", nullable = false, length = 50)
    private String keyword;

    @Column(name = "description", length = 50)
    private String description;

    @Column(name = "sms_type", nullable = false)
    private Integer smsType; // 1: CSKH, 2: QC

}
