package vivas.omni.repository.entity;


import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Business {

      /*
    GeneratedValue.generator == SequenceGenerator.name
    allocationSize = n => mỗi lần call get nextVal sẽ call lấy n seq tiếp theo và lưu vào cache (tầng hibernate)
     => để các insert tiếp theo sẽ không cần call ly db => sử dụng khi update nhiều & batchsize
     drawback => khi tắt ứng dụng giá trị val này sẽ bị mất trên call => app call lại và lấy nextVal từ gias trị đã lưu trước đó
    */
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BUSINESS_SEQ")
    @SequenceGenerator(name = "BUSINESS_SEQ", sequenceName = "BUSINESS_ID_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "business_code")
    private String businessCode;

    @Column(name = "business_name")
    private String businessName;

    @Column(name = "address")
    private String address;

    @Column(name = "tax_code")
    private String taxCode;

    @Column(name = "business_phone")
    private String businessPhone;

    @Column(name = "business_email")
    private String businessEmail;

    @Column(name = "contact_name")
    private String contactName;

    @Column(name = "contact_phone")
    private String contactPhone;

    @Column(name = "contact_email")
    private String contactEmail;

    @Column(name = "customer_source")
    private Integer customerSource;

    @Column(name = "agent_id")
    private Long agentId;

    // 1: active, 2: inactive
    @Column(name = "status")
    private Integer status;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}