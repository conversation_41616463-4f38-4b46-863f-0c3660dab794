package vivas.omni.repository.entity;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Getter
@Setter
@ToString(callSuper = true)
@Table(name = "document")
public class Document extends AbstractEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "DOCUMENT_SEQ")
    @SequenceGenerator(name = "DOCUMENT_SEQ", sequenceName = "DOCUMENT_ID_SEQ", allocationSize = 1)
    private Long documentId;

    @Column(name = "name")
    private String name;

    @Column(name = "tooltip")
    private String tooltip;

    private String question;

    @Column(name = "link_sample")
    private String linkSample;

    @Column(name = "check_type")
    private Integer checkType; // 0: upload; 1: select yes, no; 2: checkbox (1 checkbox)

    @Column(name = "check_upload")
    private Integer checkUpload; // 0 là bắt buộc upload, 1 => chọn 'có' sẽ phải upload; 2 => chon 'không' sẽ phải upload

    @Column(name = "label_type_id")
    private Long labelTypeId;

    @Column(name = "service_sms_type_id")
    private Integer serviceSmsTypeId;

    @Column(name = "group_document_type")
    private Integer groupDocumentType; // 1: require, 2: optional

    @Column(name = "profile_label_group_type")
    private Integer profileLabelGroupType;

    @Column(name = "order_by")
    private Integer orderBy;
}
