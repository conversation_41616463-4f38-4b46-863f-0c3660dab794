package vivas.omni.repository.entity;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@ToString(callSuper = true)
@Table(name = "file_upload")
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FileUpload extends AbstractEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "FILE_UPLOAD_SEQ")
    @SequenceGenerator(name = "FILE_UPLOAD_SEQ", sequenceName = "FILE_UPLOAD_ID_SEQ", allocationSize = 1)
    private Long fileUploadId;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "file_system_name")
    private String fileSystemName;

    @Column(name = "extension")
    private String extension;

    @Column(name = "file_size")
    private Long fileSize;

    @Column(name = "path")
    private String path;

    @Column(name = "file_type")
    private Integer fileType;

    @Column(name = "business_id")
    private Long businessId;

    @Column(name = "agent_id")
    private Long agentId;

    @Column(name = "label_id")
    private Long labelId;

    @Column(name = "label_type_id")
    private Long labelTypeId;

    @Column(name = "document_id")
    private Long documentId;

    @Column(name = "service_sms_type_id")
    private Integer serviceSmsTypeId;

    @Column(name = "check_question")
    private Integer checkQuestion;

    @Column(name = "profile_label_group_type")
    private Integer profileLabelGroupType;

    @Column(name = "template_id")
    private Long templateId;

    @Column(name = "template_type_id")
    private Integer templateTypeId;
}
