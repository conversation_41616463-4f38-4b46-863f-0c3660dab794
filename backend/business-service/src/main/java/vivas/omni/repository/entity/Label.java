package vivas.omni.repository.entity;

import lombok.*;

import jakarta.persistence.*;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@ToString(callSuper = true)
@Table(name = "label")
@NoArgsConstructor
public class Label extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LABEL_SEQ")
    @SequenceGenerator(name = "LABEL_SEQ", sequenceName = "LABEL_ID_SEQ", allocationSize = 1)
    private Long id;

    private String name;

    private Integer status;

    @Column(name = "oa_id")
    private String oaId;

    @Column(name = "expired_at")
    private LocalDateTime expiredAt;

    @Column(name = "registered_at")
    private LocalDateTime registeredAt;

    @Column(name = "document_expired_at")
    private LocalDateTime documentExpiredAt;

    @Column(name = "channel_id")
    private Long channelId;

    @Column(name = "package_id")
    private Long packageId;

    @Column(name = "business_id")
    private Long businessId;

    @Column(name = "service_sms_type_id")
    private Long serviceSmsTypeId;

    @Column(name = "label_type_qc_id")
    private Long labelTypeQCId;

    @Column(name = "label_type_qc_other")
    private Long labelTypeQCOther;

    @Column(name = "label_type_cskh_id")
    private Long labelTypeCskhId;

    @Column(name = "label_type_cskh_other")
    private Long labelTypeCskhOther;

    @Column(name = "rejection_reason")
    private String rejectionReason;

}
