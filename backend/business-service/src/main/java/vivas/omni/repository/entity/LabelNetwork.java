package vivas.omni.repository.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Getter
@Setter
@ToString(callSuper = true)
@Table(name = "label_network")
public class LabelNetwork extends AbstractEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LABEL_NETWORK_SEQ")
    @SequenceGenerator(name = "LABEL_NETWORK_SEQ", sequenceName = "LABEL_NETWORK_ID_SEQ", allocationSize = 1)
    @Column(name = "lable_network_id")
    private Long labelNetworkId;

    @Column(name = "label_id")
    private Long labelId;

    @Column(name = "network_operator_id")
    private Long networkOperatorId;

    @Column(name = "network_status")
    private Integer networkStatus;
}

