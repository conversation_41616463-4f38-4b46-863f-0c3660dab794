package vivas.omni.repository.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Data
@Table(name = "label_type")
@NoArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class LabelType {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LABEL_TYPE_SEQ")
    @SequenceGenerator(name = "LABEL_TYPE_SEQ", sequenceName = "LABEL_TYPE_ID_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "label_type_name")
    private String labelTypeName;

    @Column(name = "type")
    private Integer type;

    @Column(name = "service_sms_type_id")
    private Integer serviceSmsTypeId;

    @Column(name = "parent_id")
    private Long parentId; // if type is other, parent_id

}
