package vivas.omni.repository.entity;

import lombok.Data;

import jakarta.persistence.*;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Data
@Table
@NoArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class Logo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private Long labelId;

    private String lightImage;

    private String darkImage;

    private String logoSize;
}
