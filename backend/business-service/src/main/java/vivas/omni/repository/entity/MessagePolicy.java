package vivas.omni.repository.entity;

import java.time.LocalTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Getter
@Setter
@ToString(callSuper = true)
@Table(name = "message_policy")
public class MessagePolicy extends AbstractEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MESSAGE_POLICY_SEQ")
    @SequenceGenerator(name = "MESSAGE_POLICY_SEQ", sequenceName = "MESSAGE_POLICY_ID_SEQ", allocationSize = 1)
    private Long Id;

    @Column(name = "network_operator_id")
    private Long networkOperatorId;

    @Column(name = "policy_type")
    private long policyType; // 1: ZALO, 2: SMS

    @Column(name = "policy_setting_type")
    private Integer policySettingType; // 1: Th<PERSON><PERSON> gian <PERSON>, 2: <PERSON><PERSON> tin SMS, 3: <PERSON><PERSON> tự <PERSON>, 4: Thời gian ZNS

    @Column(name = "start_days_of_week_id")
    private Integer startDaysOfWeekId;

    @Column(name = "end_days_of_week_id")
    private Integer endDaysOfWeekId;

    @Column(name = "start_time")
    private LocalTime startTime;

    @Column(name = "end_time")
    private LocalTime endTime;

    @Column(name = "char_limit")
    private Integer charLimit;

    @Column(name = "sms_type")
    private Integer smsType; // 1: CSKH, 2: Quảng cáo

    @Column(name = "is_unicode")
    private Boolean isUnicode = false; // true: Có Unicode, false: Không Unicode

    @Column(name = "limit_send_date")
    private Integer limitSendDate;

    @Column(name = "limit_send_month")
    private Integer limitSendMonth;

}
