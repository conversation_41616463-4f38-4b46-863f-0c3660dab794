package vivas.omni.repository.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Getter
@Setter
@ToString(callSuper = true)
@Table(name = "network_operator")
public class NetworkOperator {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "NETWORK_OPERATOR_SEQ")
    @SequenceGenerator(name = "NETWORK_OPERATOR_SEQ", sequenceName = "NETWORK_OPERATOR_ID_SEQ", allocationSize = 1)
    @Column(name = "network_operator_id")
    private Long networkOperatorId;

    @Column(name = "name")
    private String name;

    @Column(name = "prefix")
    private String prefix;

    @Column(name = "status")
    private Integer status;

}
