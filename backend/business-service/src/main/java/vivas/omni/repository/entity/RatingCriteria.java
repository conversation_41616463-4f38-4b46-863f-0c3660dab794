package vivas.omni.repository.entity;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "RATING_CRITERIA") // Oracle thường sử dụng tên viết hoa
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RatingCriteria {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CRITERIA_SEQ")
    @SequenceGenerator(name = "CRITERIA_SEQ", sequenceName = "CRITERIA_ID_SEQ", allocationSize = 1)
    private Long criteriaId;

    private String content;

    private Long ratingId;
}
