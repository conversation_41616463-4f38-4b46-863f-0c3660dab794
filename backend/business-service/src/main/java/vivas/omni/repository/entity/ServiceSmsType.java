package vivas.omni.repository.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@ToString
@Getter
@Setter
@Table(name = "service_sms_type")
@NoArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class ServiceSmsType {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SERVICE_SMS_TYPE_SEQ")
    @SequenceGenerator(name = "SERVICE_SMS_TYPE_SEQ", sequenceName = "SERVICE_SMS_TYPE_ID_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "des")
    private Integer des;
}
