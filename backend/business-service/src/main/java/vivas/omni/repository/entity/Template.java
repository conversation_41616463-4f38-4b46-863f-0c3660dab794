package vivas.omni.repository.entity;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class Template extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TEMPLATE_SEQ")
    @SequenceGenerator(name = "TEMPLATE_SEQ", sequenceName = "TEMPLATE_ID_SEQ", allocationSize = 1)
    Long id;

    Long labelId;

    Integer typeId;

    String name;

    String title;

    @Lob
    @Basic(fetch = FetchType.LAZY)
    String content;

    Integer status;

    Boolean unicode;

    String note;

    String reason;

    Long channelId;

    Long businessId;

    Long zaloId;

    @Column(name = "main_button", columnDefinition = "VARCHAR2(500)")
    private String mainButton;

    @Column(name = "extra_button", columnDefinition = "VARCHAR2(500)")
    String extraButton;

    @Column(name = "table_data", columnDefinition = "VARCHAR2(500)")
    String tableData;
}