package vivas.omni.repository.entity;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "TEMPLATE_BANK") // Oracle thường sử dụng tên viết hoa
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TemplateBank {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BANK_SEQ")
    @SequenceGenerator(name = "BANK_SEQ", sequenceName = "BANK_ID_SEQ", allocationSize = 1)
    private Long bankId;

    private String bankName;

    private String accountName;

    private String accountNumber;

    private String price;

    private String content;

    private int isBusinessAccount;

    private Long templateId;
}
