package vivas.omni.repository.entity;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "TEMPLATE_BUTTON") // Oracle thường sử dụng tên viết hoa
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TemplateButton {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BUTTON_SEQ")
    @SequenceGenerator(name = "BUTTON_SEQ", sequenceName = "BUTTON_ID_SEQ", allocationSize = 1)
    private Long buttonId;

    private String buttonName;

    private Integer buttonType;

    private String link;

    private Long templateId;
}
