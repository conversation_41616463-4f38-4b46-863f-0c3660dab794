package vivas.omni.repository.entity;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TemplateParam {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TEMPLATE_PARAM_SEQ")
    @SequenceGenerator(name = "TEMPLATE_PARAM_SEQ", sequenceName = "TEMPLATE_PARAM_ID_SEQ", allocationSize = 1)
    Long id;

    Long templateId;

    String name;

    Long fieldZaloId;

    @Column(name = "field_360_id")
    Long field360Id;

    String sampleData;
}
