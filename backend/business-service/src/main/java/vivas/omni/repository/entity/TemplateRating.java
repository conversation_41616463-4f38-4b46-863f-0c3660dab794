package vivas.omni.repository.entity;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "TEMPLATE_RATING") // Oracle thường sử dụng tên viết hoa
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TemplateRating {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RATING_SEQ")
    @SequenceGenerator(name = "RATING_SEQ", sequenceName = "RATING_SEQ_ID_SEQ", allocationSize = 1)
    private Long ratingId;

    private Integer ratingStar;

    private String ratingDescription;

    private String question;

    private String contentThanks;

    private String thankDescription;

    private Long templateId;
}
