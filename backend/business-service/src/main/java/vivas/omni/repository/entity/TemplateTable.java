package vivas.omni.repository.entity;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "TEMPLATE_TABLE") // Oracle thường sử dụng tên viết hoa
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TemplateTable {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TABLE_SEQ")
    @SequenceGenerator(name = "TABLE_SEQ", sequenceName = "TABLE_ID_SEQ", allocationSize = 1)
    private Long tableId;

    private String tableName;

    private String content;

    private Long templateId;
}
