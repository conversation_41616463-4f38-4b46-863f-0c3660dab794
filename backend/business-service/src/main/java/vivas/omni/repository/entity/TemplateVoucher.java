package vivas.omni.repository.entity;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "TEMPLATE_VOUCHER") // Oracle thường sử dụng tên viết hoa
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TemplateVoucher {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "VOUCHER_SEQ")
    @SequenceGenerator(name = "VOUCHER_SEQ", sequenceName = "VOUCHER_ID_SEQ", allocationSize = 1)
    private Long voucherId;

    private String priceVoucher;

    private String condition;

    private String startDate;

    private String endDate;

    private String voucherCode;

    private Integer showCode;

    private Long templateId;
}
