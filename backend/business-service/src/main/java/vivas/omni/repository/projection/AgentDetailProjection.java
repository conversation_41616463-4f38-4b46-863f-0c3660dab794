package vivas.omni.repository.projection;

public interface AgentDetailProjection {
    Long getAgentId();

    String getAgentName();

    String getAgentCode();

    String getAddress();

    String getTaxCode();

    String getAgentPhone();

    String getAgentEmail();

    Integer getAgentType();

    String getContactName();

    String getContactPhone();

    String getContactEmail();

    Integer getStatus();

    Integer getAccountNumber();
}
