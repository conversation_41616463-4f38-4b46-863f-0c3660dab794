package vivas.omni.repository.projection;


public interface BusinessDetailProjection {

    Long getId();

    String getBusinessName();

    String getBusinessCode();

    String getAddress();

    String getTaxCode();

    String getBusinessPhone();

    String getBusinessEmail();

    String getContactName();

    String getContactPhone();

    String getContactEmail();

    Integer getCustomerSource();

    Long getAgentId();

    String getAgentName();

    Integer getStatus();

    Integer getAccountNumber();
}
