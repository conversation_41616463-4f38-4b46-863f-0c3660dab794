package vivas.omni.repository.projection;

import java.time.LocalDateTime;

/**
 * Projection for {@link vivas.omni.repository.entity.Template}
 */
public interface SMSTemplateProjection {
    Long getId();

    Long getLabelId();

    String getLabelName();

    Integer getTypeId();

    String getContent();

    Integer getStatus();

    Boolean getUnicode();

    String getNote();

    String getReason();

    Long getBusinessId();

    LocalDateTime getCreatedAt();
}