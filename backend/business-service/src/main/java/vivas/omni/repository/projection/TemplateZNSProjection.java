package vivas.omni.repository.projection;

import org.springframework.expression.spel.ast.Projection;

public interface TemplateZNSProjection {

    Long getId();

    Integer getTypeId();

    String getName();

    String getLabelName();

    String getBusinessName();

    Integer getStatus();

    String getTitle();

    String getContent();

    String getNote();

    String getMainButton();

    String getExtraButton();

    String getTableData();

    String getBankName();

    String getAccountName();

    String getAccountNumber();

    String getPrice();

    String getContentBank();

    String getPriceVoucher();

    String getCondition();

    String getStartDate();

    String getEndDate();

    String getVoucherCode();
}
