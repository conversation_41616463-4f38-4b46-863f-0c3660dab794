package vivas.omni.request.label;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.persistence.Column;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BrandNameCreateRequest {

    @NotEmpty(message = "Brandname không được để trống.")
    @Length(max = 11, min = 3, message = "Brandname có độ dài tối thiểu 3 ký tự, tối đa 11 ký tự.")
    private String name;
    private Long labelTypeQcId;
    private Long labelTypeQcOther;
    private Long labelTypeCskhId;
    private Long labelTypeCskhOther;

    @NotNull(message = "Thông tin này là bắt buộc.")
    private List<NetworkOperatorCreate> networkOperatorCreateList;

    @NotNull(message = "Vui lòng chọn loại gửi tin.")
    private ServiceSmsTypeCreate serviceSmsTypeCreate;

    private ProfileDocumentLabelCreate documentCskh;

    private ProfileDocumentLabelCreate documentQc;

    // 1 - luu nhap, 2 - yeu cau duyet
    private Integer tag;
}
