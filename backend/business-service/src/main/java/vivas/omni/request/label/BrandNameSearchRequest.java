package vivas.omni.request.label;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Getter;


@Getter
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BrandNameSearchRequest {

    @JsonProperty("brand_name")
    private String brandName;

    @JsonProperty("business_id")
    private Long businessId;

    @JsonProperty("sms_type_id")
    private Integer smsTypeId;

    @JsonProperty("status")
    private Integer status;

    @JsonProperty("page_index")
    private Integer pageIndex;

    @JsonProperty("page_size")
    private Integer pageSize;

    @JsonProperty("user_id")
    private Long userId;
}
