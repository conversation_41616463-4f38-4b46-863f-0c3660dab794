package vivas.omni.request.label;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BrandNameUpdateClientRequest {

    @NotEmpty(message = "Brandname không được để trống.")
    @Length(max = 11, min = 3, message = "Brandname có độ dài tối thiểu 3 ký tự, tối đa 11 ký tự.")
    private String name;

    @NotNull(message = "Thông tin này là bắt buộc.")
    private List<NetworkOperatorUpdate> networkOperatorUpdateList;

    private ProfileDocumentLabelUpdate documentCskh;

    private ProfileDocumentLabelUpdate documentQc;

    // 1 - luu nhap, 2 - yeu cau duyet
    private Integer tag;
}