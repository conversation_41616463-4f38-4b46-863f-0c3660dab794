package vivas.omni.request.label;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FileUploadUpdate {

    private Long fileUploadId;
//    private String fileSystemName;
//    private String fileName;
//    private String extension;
//    private Long fileSize;
//    private String path;
//    private Integer fileType;
    private Long businessId;
    private Long agentId;
    private Long labelId;
    private Long labelTypeId;
    private Long documentId;
    private Integer serviceSmsTypeId;
    private Integer checkQuestion;
    private Integer profileLabelGroupType;
}
