package vivas.omni.request.label;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class NetworkOperatorUpdate {

    private Long networkOperatorId;

    private Long labelNetworkId;

    private Long labelId;

    private String name;

    private Integer networkStatus;
}
