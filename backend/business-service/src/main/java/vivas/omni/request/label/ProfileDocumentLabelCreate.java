package vivas.omni.request.label;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ProfileDocumentLabelCreate {

    private Integer profileLabelGroupType;

//    @Schema(description = "file upload không phải lĩnh vực")
//    private List<FileUploadCreate> fileUploadCreateList;
//
//    @Schema(description = "file upload lĩnh vực")
//    private List<FileUploadCreate>  labelTypeFileUploadCreateList;

    @Schema(description = "file upload")
    private List<FileUploadCreate> fileUploadCreateList;
}
