package vivas.omni.request.label;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ServiceSmsTypeUpdate {

    @NotNull(message = "<PERSON><PERSON><PERSON> tin không được để trống.")
    private Long id;

    private String name;

    private Integer des;

}
