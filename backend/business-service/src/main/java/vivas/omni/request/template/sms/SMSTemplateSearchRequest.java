package vivas.omni.request.template.sms;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import vivas.omni.request.PagingRequest;

@EqualsAndHashCode(callSuper = true)
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SMSTemplateSearchRequest extends PagingRequest {
    String keyword;
    Long labelId;
    Long typeId;
    Long businessId;
    Integer status;
    Boolean unicode;
}
