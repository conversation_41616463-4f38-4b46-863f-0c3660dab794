package vivas.omni.request.template.sms;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import vivas.omni.dto.TemplateParamDTO;

import java.util.List;

/**
 * DTO cho yêu cầu cập nhật SMS template
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UpdateSMSTemplateRequest {
    @NotNull
    Long id;

    @NotNull
    Integer typeId;

    Boolean unicode;

    @NotBlank
    @Size(max = 2000)
    String content;

    String note;

    List<@Valid TemplateParamDTO> params;

    @NotNull
    Boolean submit; // true: submit for approve, false: save as draft
}
