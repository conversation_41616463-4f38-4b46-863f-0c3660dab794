# Include any files or directories that you don't want to be copied to your
# container here (e.g., local build artifacts, temporary files, etc.).
#
# For more help, visit the .dockerignore file reference guide at
# https://docs.docker.com/go/build-context-dockerignore/

**/.idea
**/*.iml
**/.classpath
**/.project
**/.settings
**/.toolstarget
**/.vs
**/.vscode
**/.next
**/.cache
**/*.*proj.user
**/*.dbmdl
**/*.jfm
**/charts

**/.git
**/.gitignore
**/.gitattribute
**/*Dockerfile*
**/.dockerignore
**/*docker-compose*
**/*compose*
build-image-harbor.sh

**/target
**/.env*
**/tmp*/
**/logs*/
**/data*/
!src/**/data*

LICENSE
README.md
HELP.md

