FROM registry.vivas.vn/local/maven:3.9.9-amazoncorretto-21 AS build
WORKDIR /app

COPY pom.xml ./
RUN --mount=type=cache,target=/root/.m2 mvn dependency:go-offline

COPY . .
RUN --mount=type=cache,target=/root/.m2,rw mvn -B package -DskipTests

FROM registry.vivas.vn/local/amazoncorretto:21.0.6
WORKDIR /app
COPY --from=build /app/target/*.jar /app/app.jar
COPY --from=build /app/logback.xml /app/logback.xml
COPY --from=build /app/application.yml /app/application.yml

# Command to run the application
EXPOSE 8038
ENTRYPOINT ["java", "-jar", "app.jar"]

#FROM amazoncorretto:21-alpine3.21 AS dependencies
#
#WORKDIR /build
#
#COPY ./mvnw .mvn/ ./pom.xml ./
#RUN --mount=type=cache,target=/root/.m2 ./mvnw dependency:go-offline
#
#FROM dependencies AS build
#
#WORKDIR /build
#
#COPY ./src/ ./src/
#RUN --mount=type=cache,target=/root/.m2 \
#    mvn -B package -DskipTests && \
#    mv target/$(./mvnw help:evaluate -Dexpression=project.artifactId -q -DforceStdout)-$(./mvnw help:evaluate -Dexpression=project.version -q -DforceStdout).jar target/app.jar
#
#FROM build AS extract
#
#WORKDIR /build
#
#RUN java -Djarmode=tools -jar target/app.jar extract --destination extracted
#
#FROM amazoncorretto:21-alpine3.21 AS custom-runtime
#
#RUN $JAVA_HOME/bin/jlink \
#      --add-modules ALL-MODULE-PATH \
##      --add-modules $(cat ./jmodules.tmp) \
#      --strip-debug \
#      --no-man-pages \
#      --no-header-files \
#      --compress zip-3 \
#      --output /javaruntime/ \
#
#FROM alpine:3.21 AS production
#
#ENV JAVA_HOME=/opt/java/openjdk
#ENV PATH="$JAVA_HOME/bin:$PATH"
#COPY --from=custom-runtime /javaruntime/ $JAVA_HOME
#
#WORKDIR /opt/app
#
#COPY --from=extract /build/extracted/lib/ ./lib/
#COPY --from=extract /build/extracted/app.jar ./
#
#VOLUME /opt/app/logs/
#EXPOSE 8038/tcp
#
#ENTRYPOINT ["java", "-jar", "app.jar"]