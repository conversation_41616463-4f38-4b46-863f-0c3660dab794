server:
  port: 8038

spring:
  application:
    name: push_engine
  datasource:
    url: ${DATABASE_URL:***************************************}
    username: ${DATABASE_USERNAME:omni}
    password: ${DATABASE_PASSWORD:omni#2025}
    hikari:
      pool-name: Hikari
      auto-commit: false
      idle-timeout: 150000
      connection-timeout: 300000
      maximum-pool-size: 200
      minimum-idle: 10
      max-lifetime: 1800000
      validation-timeout: 3000
      leak-detection-threshold: 15000
      keepalive-time: 30000
    driver-class-name: oracle.jdbc.OracleDriver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    open-in-view: false
    properties:
      hibernate:
        format_sql: true
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
        jdbc:
          batch_size: 600
          fetch_size: 600
          time_zone: Asia/Ho_Chi_Minh

  rabbitmq:
    host: ${RABBITMQ_HOST:************}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:admin}
    password: ${RABBITMQ_PASSWORD:Vivas@123}
    virtual-host: ${RABBITMQ_VHOST:/}
    listener:
      simple:
        retry:
          enabled: true
          initial-interval: 2000s
          max-attempts: 5
          max-interval: 2000s
          multiplier: 1.5

app:
  policy-worker:
    # Number of messages parallelly loaded into memory to be validated
    messages-in-memory: ${POLICY_WORKER_MESSAGES_IN_MEM:1000}
    # Number of messages parallelly validated per second
    messages-per-second: ${POLICY_WORKER_MESSAGES_PER_SECOND:500}
    # Number of messages parallelly loaded into memory to be sent to Push Service
    push-requests-in-memory: ${POLICY_WORKER_PUSH_REQUESTS_IN_MEM:500}
    # Number of messages parallelly sent to Push Service per second
    push-requests-per-second: ${POLICY_WORKER_PUSH_REQUESTS_PER_SECOND:200}
  push-worker:
    max-retry-attempts: ${PUSH_WORKER_MAX_RETRY_ATTEMPTS:3}
  rabbitmq:
    node-id: ${NODE_ID:1} # required to be unique across all instances of this service
    topic:
      campaign-event: omni.topic.campaign-event
    queue:
      campaign-event: ${app.rabbitmq.topic.campaign-event}.queue.${app.rabbitmq.node-id}
      push-request: omni.queue.push-request
      failover-request: omni.queue.failover-request

message:
  scan:
    interval: 60000  # 1 phút
#  batch:
#    size: 1000      # Số lượng tin nhắn xử lý mỗi batch
#  processing:
#    max-retries: 3
#    retry-delay: 300000  # 5 phút
#  node:
#    id: ${NODE_ID:node-1}  # ID của node hiện tại
#  lock:
#    timeout: 300000  # 5 phút
#    retry-interval: 1000  # 1 giây

external-system:
  brandname:
    baseurl: ${BRANDNAME_BASEURL:http://10.84.5.76:9282/SMSBNAPINEW}
    username: ${BRANDNAME_USERNAME:omni}
    password: ${BRANDNAME_PASSWORD:123456}
    api:
      push-sms: ${BRANDNAME_API_PUSHSMS:/sendsms}
      push-zns: ${BRANDNAME_API_PUSHZNS:/sendzns}
      check-status: ${BRANDNAME_API_CHECK_STATUS:/verifyOmni}
    timeout: 30 # unit: second
