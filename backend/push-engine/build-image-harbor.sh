#!/bin/bash

# Định nghĩa các biến
DOCKER_IMAGE_NAME="be_omni_channel/push_engine"
# Lấy 8 ký tự đầu của hash (nếu có)
HASH_BASE=${1:-$(git rev-parse --short=8 HEAD 2>/dev/null || echo "latest")}

# Random 4 ký tự
RANDOM_SUFFIX=$(LC_ALL=C tr -dc 'A-Za-z0-9' </dev/urandom | head -c6 || echo XX)
#RANDOM_SUFFIX="ver_1.0.0"

# Kết hợp thành HASH mới
HASH="${HASH_BASE}-${RANDOM_SUFFIX}"

DOCKER_IMAGE_WITH_TAG="registry.vivas.vn/$DOCKER_IMAGE_NAME:$HASH"

# In ra thông tin
echo "Building Docker image: $DOCKER_IMAGE_WITH_TAG"

# Đăng nhập vào Harbor (nếu cần)
# Thay username và password bằng thông tin thực tế của bạn
echo "Logging in to Harbor registry"
docker login registry.vivas.vn -u "robot$be_omni_channel+business_service" -p "x8ahD7nA4G4PAixxCPlgrzYbPnBBl4Zs"

# Build + push Docker image
docker buildx build --platform linux/amd64,linux/arm64 -t $DOCKER_IMAGE_WITH_TAG --push .

# Kiểm tra nếu build thành công
if [ $? -eq 0 ]; then
    echo "Successfully pushed $DOCKER_IMAGE_WITH_TAG to Harbor"
else
    echo "Failed to build Docker image"
    exit 1
fi

echo "Done!"