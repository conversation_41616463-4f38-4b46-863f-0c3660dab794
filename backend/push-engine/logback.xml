<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <property name="DIR" value="logs"></property>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{30}\(%line\) - %X{traceId:-} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${DIR}/app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${DIR}/archive/app_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>30MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{30}\(%line\) - %X{traceId:-} %msg%n</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </root>

    <logger name="org.apache.kafka" level="INFO"/>
    <logger name="org.springframework" level="INFO"/>
<!--    <logger name="org.hibernate.SQL" level="debug">-->
<!--        <appender-ref ref="STDOUT"/>-->
<!--    </logger>-->
    <logger name="com.zaxxer.hikari" level="INFO"/>
    <logger name="org.springframework.data.repository" level="WARN"/>
    <logger name="vivas.omni.domain.service.PendingMessageFilteringService" level="DEBUG"/>
</configuration>