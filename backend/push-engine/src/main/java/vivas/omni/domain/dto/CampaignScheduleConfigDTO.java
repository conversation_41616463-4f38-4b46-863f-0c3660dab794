package vivas.omni.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vivas.omni.infrastructure.constant.ScheduleType;
import vivas.omni.infrastructure.util.LocalDateListDeserializer;
import vivas.omni.infrastructure.util.LocalDateTimeDeserializer;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CampaignScheduleConfigDTO {
    // Thời gian gửi tin
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime sendTime;

    // Cấu hình cho SEND_WEEKLY
    private List<Integer> weekDays; // 1=Monday, 2=Tuesday, ..., 7=Sunday

    // Cấu hình cho lịch trình hàng tháng (ngày cụ thể)
    private Boolean isMonthlyByDay; // true: theo ngày, false: theo thứ
//    @Size(min = 1, max = 31, message = "Ngày trong tháng phải từ 1 đến 31")
    private Integer monthDay; // 1-31, ngày trong tháng

    // Cấu hình cho lịch trình hàng tháng (thứ cụ thể)
//    @Size(min = 1, max = 7, message = "Ngày trong tuần phải từ 1 đến 7")
    private Integer weekDay; // 1-7 (1=Monday, 7=Sunday)
//    @Size(min = -1, max = 4, message = "Vị trí trong tuần hợp lệ là 1 (đầu tiên), 2 (thứ hai), 3 (thứ ba), 4 (thứ tư), -1 (cuối cùng)")
    private Integer weekPosition; // "1" (đầu tiên), "2" (thứ hai), "3" (thứ ba), "4" (thứ tư), "-1" (cuối cùng)

    // Cấu hình cho lịch trình lặp lại
    private Integer repeatInterval; // Số đơn vị thời gian lặp lại
    private ScheduleType repeatType; // Loại lịch trình lặp lại tùy chỉnh (SEND_CUSTOM, SEND_DAILY, SEND_WEEKLY,
                                     // SEND_MONTHLY)

    // Cấu hình cho lịch trình ngày cụ thể
    @JsonDeserialize(using = LocalDateListDeserializer.class)
    private List<LocalDate> specificDates; // Danh sách các ngày cụ thể
}
