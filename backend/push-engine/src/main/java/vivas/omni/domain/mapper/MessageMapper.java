package vivas.omni.domain.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import vivas.omni.domain.repository.entity.MessageLog;
import vivas.omni.domain.repository.entity.PendingMessage;

@Mapper(componentModel = "spring")
public interface MessageMapper {
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "pendingMessageId", source = "pendingMessage.id")
    MessageLog toMessageLog(PendingMessage pendingMessage);
}
