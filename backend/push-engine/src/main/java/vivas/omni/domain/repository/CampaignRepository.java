package vivas.omni.domain.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vivas.omni.domain.repository.entity.Campaign;

import java.util.Collection;
import java.util.List;

public interface CampaignRepository extends JpaRepository<Campaign, Long>, CustomCampaignRepository {
    @Query(value = """
        SELECT c.* FROM CAMPAIGN c
        WHERE c.ID NOT IN :ids AND c.SCHEDULE_TYPE = :scheduleType AND c.STATUS IN :status
        """, nativeQuery = true)
    List<Campaign> findByIdNotInAndScheduleTypeAndStatusIn(Collection<Long> ids, String scheduleType, Collection<String> status);
}
