package vivas.omni.domain.repository;

import vivas.omni.domain.repository.entity.PendingMessage;
import vivas.omni.infrastructure.constant.MessageStatus;

import java.util.Collection;
import java.util.List;

public interface CustomPendingMessageRepository {
    List<PendingMessage> findAndUpdateStatusByCampaignIdAndChannelInAndStatus(
            MessageStatus nextStatus, Long campaignId, Collection<String> channels, MessageStatus status, int size
    );

    int updateStatusByCampaignIdAndStatusAndUpdatedAt(MessageStatus nextStatus, Long campaignId, MessageStatus status, int updatedAtDiffMinute, int size);
}
