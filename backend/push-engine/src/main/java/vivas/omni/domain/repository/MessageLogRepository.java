package vivas.omni.domain.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vivas.omni.domain.repository.entity.MessageLog;
import vivas.omni.domain.repository.entity.PendingMessage;
import vivas.omni.infrastructure.constant.ChannelType;
import vivas.omni.infrastructure.constant.MessageStatus;

import java.util.Collection;
import java.util.Optional;

@Repository
public interface MessageLogRepository extends JpaRepository<MessageLog, Long> {
    Optional<MessageLog> findByPendingMessageId(long pendingMessageId);

    @Modifying
    @Query("UPDATE MessageLog m SET m.status = :status WHERE m.id in :ids AND m.status = :oldStatus")
    void updateStatusByIdInAndStatus(MessageStatus status, Collection<Long> ids, MessageStatus oldStatus);
}