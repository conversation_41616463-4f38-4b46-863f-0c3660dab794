package vivas.omni.domain.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vivas.omni.domain.repository.entity.PendingMessage;
import vivas.omni.infrastructure.constant.ChannelType;
import vivas.omni.infrastructure.constant.MessageStatus;

import java.util.Collection;

@Repository
public interface PendingMessageRepository extends JpaRepository<PendingMessage, Long>, CustomPendingMessageRepository {
    int countByCampaignIdAndChannelTypeInAndStatusIn(Long campaignId, Collection<ChannelType> channelTypes, Collection<MessageStatus> status);

    @Modifying
    @Query("UPDATE PendingMessage m SET m.status = :status WHERE m.id in :ids AND m.status = :oldStatus")
    void updateStatusByIdInAndStatus(MessageStatus status, Collection<Long> ids, MessageStatus oldStatus);
}