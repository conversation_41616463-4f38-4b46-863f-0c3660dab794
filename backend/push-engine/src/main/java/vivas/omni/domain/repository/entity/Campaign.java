package vivas.omni.domain.repository.entity;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import vivas.omni.infrastructure.constant.*;
import vivas.omni.infrastructure.util.GenericJsonConverter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "CAMPAIGN")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class Campaign extends AbstractEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE)
    @SequenceGenerator(name = "CAMPAIGN_SEQ", sequenceName = "CAMPAIGN_ID_SEQ", allocationSize = 1)
    long id;
    String name;
    @Enumerated(EnumType.STRING)
    CampaignType campaignType; // 1: SCHEDULED, 2: LBA, 3: EVENT
    @Enumerated(EnumType.STRING)
    CampaignStatus status; // 1: DRAFT, 2: NOT_STARTED, 3: ACTIVE, 4: OUT_OF_EPOINT, 5: PAUSED, 6: FINISHED
    @Embedded
    CampaignScheduleEmbedded schedule;
    @Lob
    String sendingTimeRanges;
    Boolean useFailover;
    @Enumerated(EnumType.STRING)
    ChannelType channelType; // 1: ZNS, 2: SMS
    @Enumerated(EnumType.STRING)
    ChannelType failoverChannelType; // 1: ZNS, 2: SMS
    @Lob
    String sendingTimeRangesFailover;
    // Thông tin nhóm đối tượng gắn với chiến dịch
    @Enumerated(EnumType.STRING)
    TargetGroupType targetGroupType; // 1: EXISTING_GROUP, 2: UPLOAD_LIST
    Long targetGroupId; // chỉ khi target_group_type = "TARGET_GROUP"

    String subscriberFilePath; // chỉ khi target_group_type = "UPLOAD_LIST"
    String errorFilePath;
    String subscriberFileViewName;
    String blacklistFilePath;
    String errorBlacklistFilePath;
    String blacklistFileViewName;
    // JSON chứa: validationStatus, totalRecords, validRecords, invalidRecords,
    // uploadDate
    @Lob
    String subscriberFileInfo;
    // JSON chứa: validationStatus, totalRecords, validRecords, invalidRecords,
    // uploadDate
    @Lob
    String blacklistFileInfo;

    Long labelId;
    String oa;
    String brandName;
    // Thông tin template gắn với chiến dịch
    Long templateId;
    @Column(length = 4000)
    String templateContent;
    Long labelIdFailover;
    Long templateIdFailover;
    @Column(length = 4000)
    String templateContentFailover;
    // Thông tin chi phí và thống kê của chiến dịch
    BigDecimal estimatedCost;
    BigDecimal remainingCost;
    Long businessId; // ID của business sở hữu chiến dịch
    BigDecimal allocatedCost; // Chi phí đã phân bổ từ ví
    BigDecimal spentCost; // Chi phí đã chi tiêu thực tế
    Long successCount;
    Long failureCount;
    Long totalSent;
    LocalDateTime finalizedAt; // Thời gian hoàn tất tạo chiến dịch (DRAFT -> NOT_STARTED)

    @Transient
    Set<ChannelType> workingChannels = new HashSet<>();
    // TODO: consider to set these status to CAMPAIGN or CAMPAIGN_LOG
    @Transient
    boolean doneWithFilteringMessages = false;
    @Transient
    boolean doneWithRequestingToSendAllMessages = false;
    @Transient
    boolean doneWithSendingAllMessages = false;

    @Embeddable
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class CampaignScheduleEmbedded {
        @Column(name = "schedule_type", nullable = true)
        @Enumerated(EnumType.STRING)
        ScheduleType type; // SEND_NOW, SEND_ONCE, SEND_DAILY, SEND_WEEKLY, SEND_MONTHLY, SEND_CUSTOM
        LocalDate startDate;
        LocalDate endDate;
        LocalDateTime sendTime;
        String cronExpression; // Cho SEND_CUSTOM hoặc generated từ config khác
        @Lob
        @Column(name = "schedule_config")
        @Convert(converter = GenericJsonConverter.class)
        Object config; // Lưu chi tiết cấu hình dạng JSON
    }
}
