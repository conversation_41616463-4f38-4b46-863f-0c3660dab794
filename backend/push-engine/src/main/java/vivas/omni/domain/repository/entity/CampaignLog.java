package vivas.omni.domain.repository.entity;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import vivas.omni.infrastructure.constant.ExecutionStatus;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CampaignLog {
    @CreationTimestamp
    LocalDateTime createdAt;
    @UpdateTimestamp
    LocalDateTime updatedAt;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE)
    @SequenceGenerator(name = "CAMPAIGN_LOG_SEQ", sequenceName = "CAMPAIGN_LOG_ID_SEQ", allocationSize = 1)
    Long id;

    Long campaignId; // ID của chiến dịch liên quan
    private Integer totalMessages;

    @Builder.Default
    private Integer processedMessages = 0;

    LocalDateTime scheduledTime; // Thời gian dự kiến xử lí

    @Enumerated(EnumType.STRING)
    @Builder.Default
    private ExecutionStatus status = ExecutionStatus.SCHEDULED;

    private LocalDateTime startTime;

    private LocalDateTime endTime;
}
