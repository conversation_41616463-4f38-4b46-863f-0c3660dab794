package vivas.omni.domain.repository.entity;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;
import vivas.omni.infrastructure.constant.ChannelType;
import vivas.omni.infrastructure.constant.SmsType;

import java.time.LocalTime;
import java.util.Optional;

@Entity
@Table(name = "MESSAGE_POLICY")
@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@ToString(callSuper = true)
public class MessagePolicy extends AbstractEntity {
    @Id
    long id;

    @Column(name = "NETWORK_OPERATOR_ID")
    Integer networkOperatorId;

    @Column(name = "POLICY_TYPE")
    @Convert(converter = ChannelType.Converter.class)
    ChannelType channelType; // 1: ZALO, 2: SMS

    @Column(name = "POLICY_SETTING_TYPE")
    @Convert(converter = PolicyEnum.Converter.class)
    PolicyEnum policySettingType; // 1: Th<PERSON><PERSON> gian <PERSON>, 2: <PERSON><PERSON> tin SMS, 3: <PERSON><PERSON> tự SMS, 4: Th<PERSON><PERSON> gian <PERSON>

    @Column(name = "START_DAYS_OF_WEEK_ID")
    Integer startWeekday;

    @Column(name = "END_DAYS_OF_WEEK_ID")
    Integer endWeekday;

    @Column(name = "START_TIME")
    LocalTime startTime;

    @Column(name = "END_TIME")
    LocalTime endTime;

    @Column(name = "CHAR_LIMIT")
    Long charLimit;

    @Column(name = "SMS_TYPE")
    @Convert(converter = SmsType.Converter.class)
    SmsType smsType;

    @Column(name = "IS_UNICODE")
    Boolean isUnicode = false; // true: Có Unicode, false: Không Unicode

    @Column(name = "LIMIT_SEND_DATE")
    Long limitSendDate;

    @Column(name = "LIMIT_SEND_MONTH")
    Long limitSendMonth;

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @AllArgsConstructor
    @Getter
    public enum PolicyEnum {
        SMS_SENDING_TIME_LIMIT(1),
        SMS_SENDING_LIMIT(2),
        SMS_CHARACTER_LIMIT(3),
        ZNS_SENDING_TIMME_LIMIT(4);

        int number;

        public static PolicyEnum of(@Nullable Integer number) {
            if (number == null) return null;

            for (PolicyEnum value : values()) {
                if (value.number == number) return value;
            }

            return null;
        }

        public static class Converter implements AttributeConverter<PolicyEnum, Integer>{
            @Override
            public Integer convertToDatabaseColumn(PolicyEnum policyEnum) {
                return Optional.ofNullable(policyEnum).map(PolicyEnum::getNumber).orElse(null);
            }

            @Override
            public PolicyEnum convertToEntityAttribute(Integer integer) {
                return Optional.ofNullable(integer).map(PolicyEnum::of).orElse(null);
            }
        }
    }
}
