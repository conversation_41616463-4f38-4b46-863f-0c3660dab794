package vivas.omni.domain.repository.entity;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import vivas.omni.infrastructure.constant.*;
import vivas.omni.infrastructure.util.GenericJsonConverter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "NETWORK_OPERATOR")
@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NetworkOperator {
    @Id
    int id;
    String name;
    Integer status;
}
