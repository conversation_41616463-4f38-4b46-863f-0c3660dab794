package vivas.omni.domain.repository.entity;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import vivas.omni.infrastructure.constant.ChannelType;
import vivas.omni.infrastructure.constant.MessageStatus;
import vivas.omni.infrastructure.constant.SmsType;

import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "PENDING_MESSAGE")
@EntityListeners(AuditingEntityListener.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PendingMessage {
    @Id
    @Column(name = "ID", nullable = false)
    long id;

    @Column(name = "CHANNEL_TYPE", nullable = false)
    @Enumerated(value = EnumType.STRING)
    ChannelType channelType;

    @Column(name = "SMS_TYPE")
    @Convert(converter = SmsType.Converter.class)
    SmsType smsType;

    @Column(name = "STATUS")
    @Enumerated(EnumType.STRING)
    MessageStatus status;

    @Column(name = "CAMPAIGN_ID", nullable = false)
    Long campaignId;

    @Column(name = "TEMPLATE_ID", nullable = false)
    Long templateId;

    @Column(name = "LABEL_ID", nullable = false)
    Long labelId;

    @Column(name = "AGENT_ID", nullable = false)
    Long agentId;

    @Column(name = "CONTENT", length = 4000)
    String content;

    @Column(name = "PARAMS", length = 4000)
    String params;

    @Column(name = "PRIORITY")
    Integer priority;

    @Column(name = "CONTRACT_TYPE_ID")
    Long contractTypeId;

    @Column(name = "SENT_IP", length = 30)
    String sentIp;

    @Column(name = "SENT_METHOD", length = 30)
    String sentMethod;

    /**
     * Store either MSISDN or phone number.
     */
    @Column(name = "RECIPIENT", length = 4000, nullable = false)
    String recipient;

//    @Column(name = "CONTRACT_ID")
//    Long contractId;

//    @Column(name = "USER_NAME", length = 30)
//    String userName;

    //    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "SCHEDULED_TIME")
    LocalDateTime scheduledTime;

    @Column(name = "LABEL_TYPE")
    Integer labelType;

    @Column(name = "ENCODING")
    Integer encoding = 0;

    @Column(name = "TELCO_ID")
    Integer telcoId;

    @Column(name = "MSG_ID", length = 50)
    String msgId;

    @Column(name = "MSG_LENGTH")
    Integer msgLength;

    @Column(name = "MSG_COUNT")
    Integer msgCount;

    @Column(name = "GATEWAY_ID")
    Integer gatewayId;

    @Column(name = "SOURCE", length = 11)
    String source;

    @Column(name = "CREATED_AT")
    @CreatedDate
    LocalDateTime createdAt;

    @Column(name = "UPDATED_AT")
    @LastModifiedDate
    LocalDateTime updatedAt;

    @Transient
    String brandname;

    /**
     * This attribute is set (= campaign.useFailover) before sending push request to Push Service,
     * by which reduces query time (for CAMPAIGN record) of that service.
     */
    @Transient
    boolean configuredFailover;
}
