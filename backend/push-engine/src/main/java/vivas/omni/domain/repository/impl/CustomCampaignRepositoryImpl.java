package vivas.omni.domain.repository.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.stereotype.Repository;
import vivas.omni.domain.repository.entity.Campaign;
import vivas.omni.domain.repository.CustomCampaignRepository;
import vivas.omni.infrastructure.constant.CampaignType;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Repository
public class CustomCampaignRepositoryImpl implements CustomCampaignRepository {
    @PersistenceContext
    EntityManager entityManager;

    /**
     * Retrieve all CAMPAIGNs records that meet one of the following conditions:
     * <br/>
     * (1) SEND_NOW
     * (2) SEND_ONCE && now() >= config.sendTime.date
     * - now() within config.startDate -> config.endDate && now() in 1 of time windows (this condition will be checked by code after fetching) &&:
     *  (3) SEND_DAILY: none.
     *  (4) SEND_WEEKLY && now().weekday in config.weekDays
     *  (5.1) SEND_MONTHLY && isMonthlyByDay && now().monthday == config.monthDay
     *  (5.2) SEND_MONTHLY && !isMonthlyByDay && now().weekday in config.weekDays && now().weekNumberInMonth = config.weekPosition
     *  (6.1) SEND_CUSTOM && config.repeatType == SEND_DAILY && (now().date - config.startDate) % config.repeatType == 0
     *  (6.2 - depend on ISO) SEND_CUSTOM && config.repeatType == SEND_WEEKLY && now().weekday in config.weekDays && (now().weekNumber - config.startDate.weekNumber) % config.repeatInterval == 0
     *  (6.3) SEND_CUSTOM && config.repeatType == SEND_MONTHLY && isMonthlyByDay && now().monthday == config.monthday && (now().month - config.startDate.month) % config.repeatInterval == 0
     *  (6.4) SEND_CUSTOM && config.repeatType == SEND_MONTHLY && !isMonthlyByDay && now().weekday in config.weekDays && (now().month - config.startDate.month) % config.repeatInterval == 0 && now().weekNumberInMonth == config.weekPosition
     *  (6.5) SEND_CUSTOM && config.repeatType == SEND_CUSTOM && now().date in config.specificDates
     * <br/>
     * Note that now().weekNumberInMonth is computed by now().weekNumber - now().dayInMonth(1).weekNumber + 1.
     */
    @Override
    public List<Campaign> findTodayScheduledCampaigns() {
//        :currentWeekday = MOD(TO_NUMBER(TO_CHAR(currentDate, 'D')) + 5, 7) + 1
//        :currentDate = TO_CHAR(currentDate, 'yyyy-mm-dd')
        String query = """
                WITH pre_computed AS (
                    select CURRENT_TIMESTAMP AT TIME ZONE '+07:00' currentTime,
                        TRUNC(CURRENT_TIMESTAMP AT TIME ZONE '+07:00') currentDate,
                        MOD(TO_NUMBER(TO_CHAR(TRUNC(CURRENT_TIMESTAMP AT TIME ZONE '+07:00'), 'D')) + 5, 7) + 1 currentWeekday, -- Mon->Sat
                        TO_DATE('01' || TO_CHAR(TRUNC(CURRENT_TIMESTAMP AT TIME ZONE '+07:00'), '/mm/yyyy'), 'dd/mm/yyyy') as firstDayInMonth,
                        MOD(TO_NUMBER(TO_CHAR(TO_DATE('01' || TO_CHAR(TRUNC(CURRENT_TIMESTAMP AT TIME ZONE '+07:00'), '/mm/yyyy'), 'dd/mm/yyyy'), 'D')) + 5, 7) + 1 as weekdayOfFirstDayInMonth -- convert Sun->Sat to Mon->Sun
                    from dual
                )
                SELECT c.*
                FROM CAMPAIGN c
                    JOIN pre_computed pc ON 1 = 1
                WHERE c.CAMPAIGN_TYPE = '""" + CampaignType.SCHEDULED.getValue() + "'" + """
                  AND (
                    -- Only extract one-time campaigns that are scheduled to run on the same day
                    (c.SCHEDULE_TYPE = 'SEND_ONCE' AND currentTime >= c.SEND_TIME AND (TO_CHAR(currentDate, 'dd/mm/yyyy') = TO_CHAR(c.SEND_TIME, 'dd/mm/yyyy') OR c.STATUS = 'ACTIVE'))
                    OR
                    (
                        currentDate >= c.START_DATE AND currentDate <= c.END_DATE
                        AND (
                            c.SCHEDULE_TYPE = 'SEND_DAILY'
                            OR
                            (c.SCHEDULE_TYPE = 'SEND_WEEKLY' AND JSON_EXISTS(c.SCHEDULE_CONFIG, '$.week_days[*] ? (@ == :currentWeekday)'))
                            OR
                            (c.SCHEDULE_TYPE = 'SEND_MONTHLY' AND JSON_EXISTS(c.SCHEDULE_CONFIG, '$.is_monthly_by_day ? (@ == true)') AND TO_NUMBER(TO_CHAR(currentDate, 'DD')) = JSON_VALUE(c.SCHEDULE_CONFIG, '$.month_day'))
                            OR
                            (c.SCHEDULE_TYPE = 'SEND_MONTHLY' AND JSON_EXISTS(c.SCHEDULE_CONFIG, '$.is_monthly_by_day ? (@ == false)')
                                 AND currentDate = firstDayInMonth + (JSON_VALUE(c.SCHEDULE_CONFIG, '$.week_days[0]') - weekdayOfFirstDayInMonth + CASE WHEN ((JSON_VALUE(c.SCHEDULE_CONFIG, '$.week_days[0]') - weekdayOfFirstDayInMonth) >= 0) THEN 0 ELSE 7 END) + (JSON_VALUE(c.SCHEDULE_CONFIG, '$.week_position') - 1) * 7)
                            OR
                            (c.SCHEDULE_TYPE = 'SEND_CUSTOM' AND JSON_VALUE(c.SCHEDULE_CONFIG, '$.repeat_type') = 'SEND_DAILY' AND MOD(currentDate - c.START_DATE, JSON_VALUE(c.SCHEDULE_CONFIG, '$.repeat_interval')) = 0)
                            OR
                            (c.SCHEDULE_TYPE = 'SEND_CUSTOM' AND JSON_VALUE(c.SCHEDULE_CONFIG, '$.repeat_type') = 'SEND_WEEKLY' AND JSON_EXISTS(c.SCHEDULE_CONFIG, '$.week_days[*] ? (@ == :currentWeekday)')
                                AND MOD(TO_NUMBER(TO_CHAR(currentDate, 'IW')) - TO_NUMBER(TO_CHAR(c.START_DATE, 'IW')), JSON_VALUE(c.SCHEDULE_CONFIG, '$.repeat_interval')) = 0)
                            OR
                            (c.SCHEDULE_TYPE = 'SEND_CUSTOM' AND JSON_VALUE(c.SCHEDULE_CONFIG, '$.repeat_type') = 'SEND_MONTHLY' AND JSON_VALUE(c.SCHEDULE_CONFIG, '$.is_monthly_by_day') = 'true'
                                AND TO_NUMBER(TO_CHAR(currentDate, 'DD')) = JSON_VALUE(c.SCHEDULE_CONFIG, '$.month_day')
                                AND MOD(TO_NUMBER(TO_CHAR(currentDate, 'mm')) - TO_NUMBER(TO_CHAR(c.START_DATE, 'mm')), JSON_VALUE(c.SCHEDULE_CONFIG, '$.repeat_interval')) = 0)
                            OR
                            (c.SCHEDULE_TYPE = 'SEND_CUSTOM' AND JSON_VALUE(c.SCHEDULE_CONFIG, '$.repeat_type') = 'SEND_MONTHLY' AND JSON_VALUE(c.SCHEDULE_CONFIG, '$.is_monthly_by_day') = 'false'
                                AND JSON_EXISTS(c.SCHEDULE_CONFIG, '$.week_days[*] ? (@ == 2)')
                                AND JSON_EXISTS(c.SCHEDULE_CONFIG, '$.week_days[*] ? (@ == :currentWeekday)')
                                AND MOD(TO_NUMBER(TO_CHAR(currentDate, 'mm')) - TO_NUMBER(TO_CHAR(c.START_DATE, 'mm')), JSON_VALUE(c.SCHEDULE_CONFIG, '$.repeat_interval')) = 0
                                AND currentDate = firstDayInMonth + (currentWeekday - weekdayOfFirstDayInMonth + CASE WHEN (currentWeekday - weekdayOfFirstDayInMonth) >= 0 THEN 0 ELSE 7 END) + (JSON_VALUE(c.SCHEDULE_CONFIG, '$.week_position') - 1) * 7)
                            OR
                            (c.SCHEDULE_TYPE = 'SEND_CUSTOM' AND JSON_VALUE(c.SCHEDULE_CONFIG, '$.repeat_type') IS NULL AND JSON_EXISTS(c.SCHEDULE_CONFIG, '$.specific_dates[*] ? (@ == ":currentDate")'))
                        )
                    )
                  )
                ORDER BY c.UPDATED_AT DESC, c.ID DESC
            """.replace(":currentWeekday", String.valueOf(LocalDate.now().getDayOfWeek().getValue()))
                .replace(":currentDate", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        return entityManager.createNativeQuery(query, Campaign.class).getResultList();
    }
}
