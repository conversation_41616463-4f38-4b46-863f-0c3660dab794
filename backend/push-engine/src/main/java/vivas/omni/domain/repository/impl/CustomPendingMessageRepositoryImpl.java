package vivas.omni.domain.repository.impl;

import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.EntityTransaction;
import jakarta.persistence.Query;
import vivas.omni.domain.repository.entity.PendingMessage;
import vivas.omni.domain.repository.CustomPendingMessageRepository;
import vivas.omni.infrastructure.constant.MessageStatus;

import java.util.Collection;
import java.util.List;

public class CustomPendingMessageRepositoryImpl implements CustomPendingMessageRepository {
    EntityManagerFactory entityManagerFactory;

    public CustomPendingMessageRepositoryImpl(EntityManagerFactory entityManagerFactory) {
        this.entityManagerFactory = entityManagerFactory;
    }

    /**
     * Temporarily lock queried records and immediately transition status from SUFFICIENT -> VALIDATING before unlocking.
     */
    @Override
    public List<PendingMessage> findAndUpdateStatusByCampaignIdAndChannelInAndStatus(
            MessageStatus nextStatus, Long campaignId, Collection<String> channels, MessageStatus status, int size
    ) {
        try (var entityManager = this.entityManagerFactory.createEntityManager()) {
            Query query = entityManager.createNativeQuery("""
                            SELECT m.* FROM PENDING_MESSAGE m
                            WHERE m.ID IN (
                                SELECT ID FROM PENDING_MESSAGE
                                WHERE CAMPAIGN_ID = :campaignId AND CHANNEL_TYPE IN :channels AND STATUS = :status
                                ORDER BY m.PRIORITY DESC, m.ID OFFSET 0 ROWS FETCH FIRST :size ROWS ONLY
                                )
                            FOR UPDATE SKIP LOCKED
                            """,
                    PendingMessage.class
            );

            query.setParameter("campaignId", campaignId);
            query.setParameter("channels", channels);
            query.setParameter("status", status.name());
            query.setParameter("size", size);

            entityManager.getTransaction().begin();

            List<PendingMessage> resultList = (List<PendingMessage>) query.getResultList();

            resultList.forEach((pendingMessage) -> pendingMessage.setStatus(nextStatus));

//            if (!resultList.isEmpty()) {
//                Query statusUpdatingCommand = entityManager
//                        .createQuery("UPDATE PendingMessage m SET m.status = :status WHERE m.id IN :ids")
//                        .setParameter("status", nextStatus)
//                        .setParameter("ids", resultList.stream().map(PendingMessage::getId).toList());
//
//                statusUpdatingCommand.executeUpdate();
//            }

            entityManager.getTransaction().commit();

            return resultList;
        }
    }

    @Override
    public int updateStatusByCampaignIdAndStatusAndUpdatedAt(MessageStatus nextStatus, Long campaignId, MessageStatus status, int updatedAtDiffMinute, int size) {
        try (var entityManager = this.entityManagerFactory.createEntityManager()) {
            Query query = entityManager.createNativeQuery("""
                    UPDATE PENDING_MESSAGE m SET m.STATUS = :nextStatus
                    WHERE m.ID IN (SELECT ID FROM PENDING_MESSAGE
                                   WHERE CAMPAIGN_ID = :campaignId AND STATUS = :status
                                     AND (SYSDATE - UPDATED_AT) >= INTERVAL '""" + updatedAtDiffMinute + "' MINUTE" + """
                                   ORDER BY PRIORITY DESC, ID OFFSET 0 ROWS FETCH FIRST :size ROWS ONLY)
                    """);

            query.setParameter("nextStatus", nextStatus.name());
            query.setParameter("campaignId", campaignId);
            query.setParameter("status", status.name());
            query.setParameter("size", size);

            entityManager.getTransaction().begin();

            var numOfUpdatedRows = query.executeUpdate();

            entityManager.getTransaction().commit();
            entityManager.close();

            return numOfUpdatedRows;
        }
    }
}
