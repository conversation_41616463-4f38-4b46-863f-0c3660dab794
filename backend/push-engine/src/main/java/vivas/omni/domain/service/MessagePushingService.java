package vivas.omni.domain.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import vivas.omni.domain.mapper.MessageMapper;
import vivas.omni.domain.repository.MessageLogRepository;
import vivas.omni.domain.repository.PendingMessageRepository;
import vivas.omni.domain.repository.entity.MessageLog;
import vivas.omni.domain.repository.entity.PendingMessage;
import vivas.omni.infrastructure.client.ExternalSystemConfiguration;
import vivas.omni.infrastructure.constant.ChannelType;
import vivas.omni.infrastructure.constant.MessageStatus;
import vivas.omni.infrastructure.messaging.broker.MessageBroker;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.Executor;

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MessagePushingService {
    private static final DateTimeFormatter brandnameDateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final TypeReference<LinkedHashMap<String, String>> mapType = new TypeReference<>() {
    };

    ExternalSystemConfiguration externalSystemConfiguration;
    WebClient webClientToBrandname;
    MessageBroker messageBroker;
    ObjectMapper objectMapper;
    MessageMapper messageMapper;
    TransactionTemplate transactionTemplate;
    PendingMessageRepository pendingMessageRepository;
    MessageLogRepository messageLogRepository;

    Executor messagePushingExecutor;

    String pushRequestQueueName;
    String failoverRequestQueueName;
    int maximumPushAttempts;

    public MessagePushingService(
            ExternalSystemConfiguration externalSystemConfiguration,
            @Qualifier("webClientToBrandname") WebClient webClientToBrandname,
            MessageBroker messageBroker,
            @Qualifier("messageQueueObjectMapper") ObjectMapper objectMapper,
            MessageMapper messageMapper,
            PlatformTransactionManager platformTransactionManager,
            PendingMessageRepository pendingMessageRepository,
            MessageLogRepository messageLogRepository,
            @Value("${app.rabbitmq.queue.push-request}") String pushRequestQueueName,
            @Value("${app.rabbitmq.queue.failover-request}") String failoverRequestQueueName,
            @Value("${app.push-worker.max-retry-attempts:3}") int maximumPushAttempts
    ) {
        this.externalSystemConfiguration = externalSystemConfiguration;
        this.webClientToBrandname = webClientToBrandname;
        this.messageBroker = messageBroker;
        this.objectMapper = objectMapper;
        this.messageMapper = messageMapper;
        this.transactionTemplate = new TransactionTemplate(platformTransactionManager);
        this.pendingMessageRepository = pendingMessageRepository;
        this.messageLogRepository = messageLogRepository;

        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        this.messagePushingExecutor = taskExecutor;

        taskExecutor.setThreadNamePrefix("cpg-push-");
        taskExecutor.setCorePoolSize(10);
        taskExecutor.setMaxPoolSize(50);
        taskExecutor.afterPropertiesSet();
        taskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        taskExecutor.setAwaitTerminationSeconds(15);

        this.pushRequestQueueName = pushRequestQueueName;
        this.failoverRequestQueueName = failoverRequestQueueName;
        this.maximumPushAttempts = maximumPushAttempts;
    }

    @RabbitListener(queues = "${app.rabbitmq.queue.push-request}", concurrency = "5-10")
    @SneakyThrows
    public void pushMessage(PendingMessage receivedMessage) {
        String apiPath;
        Object requestBody;

        if (receivedMessage.getChannelType() == ChannelType.SMS) {
            apiPath = externalSystemConfiguration.getBrandname().getApi().getPushSms();
            requestBody = createBrandnameSendSmsApiBody(receivedMessage);
        } else if (receivedMessage.getChannelType() == ChannelType.ZNS) {
            apiPath = externalSystemConfiguration.getBrandname().getApi().getPushZns();
            requestBody = createBrandnameSendZnsApiBody(receivedMessage);
        } else {
            log.error("[CPG-{}] MSG-{} has invalid channel type: {}",
                    receivedMessage.getCampaignId(), receivedMessage.getId(), receivedMessage.getChannelType());
            throw new IllegalStateException("Invalid channel type of message with ID=%d".formatted(receivedMessage.getId()));
        }

        webClientToBrandname.post()
                .uri(apiPath)
                .bodyValue(requestBody)
                // Call API
                .retrieve()
                .bodyToMono(BrandnameSendMessageApiResponse.class)
                .doOnNext((response) -> transactionTemplate.execute((status) -> {
                    // Read again in PENDING_MESSAGE
                    PendingMessage pendingMessage = pendingMessageRepository.findById(receivedMessage.getId())
                            .orElseThrow(() -> {
                                log.error("[CPG-{}] {}-MSG-{} has been sent to recipient but cannot be retrieved again in database.",
                                        receivedMessage.getCampaignId(), receivedMessage.getChannelType().getName(), receivedMessage.getId());
                                return new IllegalStateException("PendingMessage with ID=%s not found".formatted(receivedMessage.getId()));
                            });

                    // Retrieve in MESSAGE_LOG
                    MessageLog messageLog = messageLogRepository.findByPendingMessageId(receivedMessage.getId())
                            .orElse(messageMapper.toMessageLog(pendingMessage));

                    messageLog.setTransactionId(response.transactionid);

                    if (response.code() == 0) {
                        // If the push/sending request (to a 3rd party) is determined as accepted
                        // (with this response, we cannot know yet if sending to recipient gets failed or succeeded)
                        messageLog.setStatus(MessageStatus.SENT);
                    } else {
                        messageLog.setStatus(MessageStatus.FAILED);
                        messageLog.setAdditionalInfo(response.message());

                        try {
                            log.error("[CPG-{}] {}-MSG-{} could not be sent via Brandname, no retry attempt is conducted. Reason: {} (code: {}). Original request body: '{}'.",
                                    receivedMessage.getCampaignId(), receivedMessage.getChannelType().getName(), receivedMessage.getId(), response.message(), response.code(), objectMapper.writeValueAsString(requestBody));
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }

                        // Request Gen Service asynchronously to generate failover message (if configured)
                        if (receivedMessage.isConfiguredFailover()) {
                            messageBroker.publish(failoverRequestQueueName, receivedMessage);
                        }
                    }

                    // If not found in the MESSAGE_LOG table, INSERT INTO it with returned transactionId & status = SENT
                    if (messageLog.getId() == 0) messageLogRepository.save(messageLog);

                    // Remove from PENDING_MESSAGE IF EXISTS
                    pendingMessageRepository.deleteById(receivedMessage.getId());

                    return true;
                }))
                // Only retry when falling into 4xx or 5xx error
                .onErrorResume(WebClientResponseException.class, (exception) -> {
                    log.error("[CPG-{}] {}-MSG-{} cannot be sent via Brandname. HTTP status code: {}.",
                            receivedMessage.getCampaignId(), receivedMessage.getChannelType().getName(), receivedMessage.getId(), exception.getStatusCode().value());

                    transactionTemplate.execute((status) -> {
                        // Read again in PENDING_MESSAGE
                        PendingMessage pendingMessage = pendingMessageRepository.findById(receivedMessage.getId())
                                .orElseThrow(() -> new RuntimeException("[CPG-{}] ZNS-MSG-{} could not be retrieved again in database."));
                        MessageLog messageLog = messageLogRepository.findByPendingMessageId(receivedMessage.getId())
                                .orElse(messageMapper.toMessageLog(pendingMessage));
                        boolean messageLogNotPersisted = messageLog.getId() == 0;
                        boolean reachedMaximumAttempts = messageLog.getRetryCount() == maximumPushAttempts;
                        boolean allowsToRetry = messageLogNotPersisted || !reachedMaximumAttempts;

                        // If not found in the MESSAGE_LOG table, INSERT INTO it with returned transactionId & status = SENT
                        if (messageLogNotPersisted && messageLog.getStatus() != MessageStatus.FAILED) {
                            log.error("[CPG-{}] ZNS-MSG-{} should be in {} status, instead found being in {} status. Do not retry for this message.",
                                    receivedMessage.getCampaignId(), receivedMessage.getId(), MessageStatus.FAILED.name(), messageLog.getStatus().name());

                            throw new IllegalStateException("Illegal message status");
                        }

                        if (messageLogNotPersisted) { // If this is the first time message pushing get failed
                            messageLog.setStatus(MessageStatus.FAILED);
                            messageLogRepository.save(messageLog);
                        } else if (reachedMaximumAttempts) {
                            // If reach maximum attempts, delete message from PENDING_MESSAGE table
                            pendingMessageRepository.deleteById(receivedMessage.getId());

                            // Then request Gen Service asynchronously to generate failover message (if configured)
                            if (receivedMessage.isConfiguredFailover()) {
                                messageBroker.publish(failoverRequestQueueName, receivedMessage);
                            }
                        }

                        if (allowsToRetry) {
                            // Increase number of attempts by 1 and publish the message to broker to retry later.
                            messageLog.setRetryCount(messageLog.getRetryCount() + 1);
                            messageBroker.publish(pushRequestQueueName, receivedMessage);
                        }

                        return true;
                    });

                    return Mono.error(exception);
                })
                .subscribeOn(Schedulers.fromExecutor(messagePushingExecutor))
                .block();
    }

    private BrandnameSendSmsApiRequest createBrandnameSendSmsApiBody(PendingMessage pendingMessage) {
        ExternalSystemConfiguration.Brandname brandnameConfig = externalSystemConfiguration.getBrandname();

        return new BrandnameSendSmsApiRequest(
                brandnameConfig.getUsername(), brandnameConfig.getPassword(),
                pendingMessage.getBrandname(), pendingMessage.getContent(),
                LocalDateTime.now().format(brandnameDateTimeFormatter), 8, pendingMessage.getRecipient()
        );
    }

    private BrandnameSendZnsApiRequest createBrandnameSendZnsApiBody(PendingMessage pendingMessage) {
        try {
            ExternalSystemConfiguration.Brandname brandnameConfig = externalSystemConfiguration.getBrandname();
            Map<String, String> params = objectMapper.readValue(pendingMessage.getParams(), mapType);

            return new BrandnameSendZnsApiRequest(
                    brandnameConfig.getUsername(), brandnameConfig.getPassword(),
                    pendingMessage.getBrandname(), pendingMessage.getContent(),
                    LocalDateTime.now().format(brandnameDateTimeFormatter), params, pendingMessage.getRecipient()
            );
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private record BrandnameSendSmsApiRequest(String username, String password, String brandname, String textmsg,
                                              String sendtime /*yyyyMMddHHmmss*/,
                                              int isunicode /*0: khong dau; 8: unicode*/,
                                              String listmsisdn /*84391222xxx;84351222xxx*/) {
    }

    private record BrandnameSendZnsApiRequest(String username, String password, String brandname, String templateid,
                                              String sendtime /*yyyyMMddHHmmss*/,
                                              Map<String, String> data,
                                              String msisdn /*84391222xxx;84351222xxx*/) {
    }

    /**
     * Applied for both SMS & ZNS message.
     */
//    @Getter
    private record BrandnameSendMessageApiResponse(int code, String message, String transactionid) {
    }
}
