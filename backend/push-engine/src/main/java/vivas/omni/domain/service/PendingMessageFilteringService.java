package vivas.omni.domain.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityManagerFactory;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import vivas.omni.domain.dto.SendingTimeRangeDTO;
import vivas.omni.domain.repository.CampaignRepository;
import vivas.omni.domain.repository.PendingMessageRepository;
import vivas.omni.domain.repository.entity.Campaign;
import vivas.omni.domain.repository.entity.PendingMessage;
import vivas.omni.infrastructure.constant.CampaignStatus;
import vivas.omni.infrastructure.constant.ChannelType;
import vivas.omni.infrastructure.constant.MessageStatus;
import vivas.omni.infrastructure.constant.ScheduleType;
import vivas.omni.infrastructure.messaging.event.CampaignEvent;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ScheduledFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Filter candidate messages using system-level policies, by which determine which ones meet the condition
 * and request another worker to send them to recipients.
 */
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@Slf4j
public class PendingMessageFilteringService {
    final ObjectMapper objectMapper;
    final PolicyBasedMessageValidator policyBasedMessageValidator;
    final TransactionTemplate transactionTemplate;
    final EntityManagerFactory entityManagerFactory;
    final CampaignRepository campaignRepository;
    final PendingMessageRepository pendingMessageRepository;

    final TaskScheduler taskScheduler;
    final TaskExecutor messageValidatingTaskExecutor;
    final ConcurrentMap<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    /**
     * Store references to (1) SEND_NOW (immediately-launched) | SEND_ONCE (one-time) campaigns and (2) today campaigns.
     * Be modified only by the first step of the periodic task {@link this#loadTodayCampaignsAndScheduleRelatedTasks()},
     * therefore an already killed (cancelled & removed) or finished campaign will not be disappeared except when moving to the next day.
     */
    final ConcurrentMap<Long, Campaign> todayCampaigns = new ConcurrentHashMap<>();

    /**
     * Store references to working campaigns. A working campaign defined as "working",
     * which is loaded into, maintained in & removed from this Map, is under the following conditions:
     * <br/>
     * 1. SEND_NOW (immediately-launched) or SEND_ONCE (one-time) campaign:
     * 1.1. Maintained in the Map when: the campaign hasn't finished sending all pending messages,
     * or it has finished but the next day hasn't come, by which triggering the start-of-day task
     * ({@link this#loadTodayCampaignsAndScheduleRelatedTasks()}) to clean up previous day's tasks.
     * <br/>
     * 1.2. For SEND_NOW campaign:<br/>
     * 1.2.1. Be loaded by:<br/>
     * (1) Executing {@link this#loadImmediatelyLaunchedCampaigns()} at service startup.<br/>
     * (2) Handling the campaign creating event in {@link this#handleCampaignEvent(CampaignEvent)}.<br/>
     * 1.2.2. Be killed (removed from this Map) at start of a day by executing the periodic job {@link this#loadTodayCampaignsAndScheduleRelatedTasks()}.
     * <br/>
     * 1.3. For SEND_ONCE campaign:<br/>
     * 1.3.1. Be loaded by executing {@link this#loadTodayCampaignsAndScheduleRelatedTasks()} at service startup and at start of a day.<br/>
     * 1.3.2. Be updated when handling the campaign updating event in {@link this#handleCampaignEvent(CampaignEvent)}.<br/>
     * 1.3.2. Be killed (removed from this Map) at start of a day by executing the periodic job {@link this#loadTodayCampaignsAndScheduleRelatedTasks()}.
     * <br/>
     * 2. For all other schedule types: the campaign is within or between its time ranges.
     * 2.1. Be loaded by executing {@link this#loadTodayCampaignsAndScheduleRelatedTasks()} at service startup and at start of a day.<br/>
     * 2.2. Be killed (removed from this Map) at start of a day by executing the periodic job {@link this#loadTodayCampaignsAndScheduleRelatedTasks()}.
     */
    final ConcurrentMap<Long, Campaign> workingCampaigns = new ConcurrentHashMap<>();

    final int maxMessagesInMemory;
    final int sequentialBatchSize;

    final int maxProcessedMessagesPerSecond;
    /**
     * Store number of messages available to be processed within a second. This variable is used to limit processing of message within a second.
     * <br/>
     * The value of this variable will only be decreased whenever a message is accepted to be processed.
     * <br/>
     * Constraint: 0 <= value <= {@link this#maxProcessedMessagesPerSecond} (initial value).
     * <br/>
     * Every time transitioning to next second,
     * this variable is re-assigned with: {@link this#maxProcessedMessagesPerSecond} - {@link this#currentlyBeingProcessedMessages}.
     */
    int availableMessagesWithinSecond;
    /**
     * Store number of being-processed messages.
     * <br/>
     * The value of this variable will be both decreased (when a message is allowed to be processed) & increased (when a message is done with processing).
     * <br/>
     * Constraint: 0 (initial value) <= value <= {@link this#maxProcessedMessagesPerSecond}.
     * <br/>
     * Every time transitioning to next second,
     * this variable is read to subtract {@link this#availableMessagesWithinSecond} by this value.
     * <br/>
     */
    int currentlyBeingProcessedMessages = 0;
    /**
     * The birth of this variable is to ensure that
     * if {@link this#currentlyBeingProcessedMessages} > 0 every time transitioning to next second,
     * the number of messages handled within that next second can be increased by which it should not be limited to
     * `{@link this#maxProcessedMessagesPerSecond} - {@link this#currentlyBeingProcessedMessages}`.
     */
    int heldMessagesOfPrevSecond = 0;

    public PendingMessageFilteringService(
            ObjectMapper objectMapper,
            PolicyBasedMessageValidator policyBasedMessageValidator,
            PlatformTransactionManager transactionManager,
            EntityManagerFactory entityManagerFactory,
            CampaignRepository campaignRepository,
            PendingMessageRepository pendingMessageRepository,
            @Value("${app.policy-worker.messages-in-memory:1000}") int maxMessagesInMemory,
            @Value("${app.policy-worker.messages-per-second:500}") int maxProcessedMessagesPerSecond
    ) {
        this.objectMapper = objectMapper;
        this.policyBasedMessageValidator = policyBasedMessageValidator;
        this.transactionTemplate = new TransactionTemplate(transactionManager);
        this.entityManagerFactory = entityManagerFactory;
        this.campaignRepository = campaignRepository;
        this.pendingMessageRepository = pendingMessageRepository;

        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        this.taskScheduler = scheduler;

        scheduler.setThreadNamePrefix("cpg-lifecycle-task-pool-");
        scheduler.setPoolSize(10);
        scheduler.afterPropertiesSet();
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(15);

        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        this.messageValidatingTaskExecutor = taskExecutor;

        taskExecutor.setThreadNamePrefix("cpg-policy-filter-pool-");
        taskExecutor.setCorePoolSize(10);
        taskExecutor.setMaxPoolSize(50);
        taskExecutor.afterPropertiesSet();
        taskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        taskExecutor.setAwaitTerminationSeconds(15);

        this.maxMessagesInMemory = maxMessagesInMemory;
        this.sequentialBatchSize = 10;

        this.maxProcessedMessagesPerSecond = maxProcessedMessagesPerSecond;
        this.availableMessagesWithinSecond = maxProcessedMessagesPerSecond;
    }

    @PostConstruct
    protected void loadImmediatelyLaunchedCampaigns() {
        // Extract loaded immediately-launched campaign IDs
        List<Long> excludedIds = todayCampaigns.values().stream()
                .filter((c) -> c.getSchedule().getType() == ScheduleType.SEND_NOW)
                .map(Campaign::getId).toList();

        campaignRepository.findByIdNotInAndScheduleTypeAndStatusIn(excludedIds, ScheduleType.SEND_NOW.getValue(),
                        List.of(CampaignStatus.NOT_STARTED.getValue(), CampaignStatus.ACTIVE.getValue()))
                .forEach((c) -> {
                    // TODO: (do this here or in the validating task) If campaign.status == SENT_ALL && passed sending time, update status to FINISHED and return. Do the same for campaigns of the rest scheduling types
                    todayCampaigns.computeIfAbsent(c.getId(), (k) -> {
                        campaignInitiatorTask(c, null).run();

                        return c;
                    });
                });
    }

    @PostConstruct
    @Scheduled(cron = "0 0 0 * * *")
    protected void loadTodayCampaignsAndScheduleRelatedTasks() {
        // 0. Cancel related tasks & remove yesterday campaigns
        for (var it = todayCampaigns.entrySet().iterator(); it.hasNext(); ) {
            var campaign = it.next().getValue();

            if (campaign.getSchedule().getType() == ScheduleType.SEND_NOW || campaign.getSchedule().getType() == ScheduleType.SEND_ONCE) {
                if (campaign.isDoneWithRequestingToSendAllMessages()) {
                    campaignKillerTask(campaign).run();
                    it.remove();
                } else {
                    continue;
                }
            }

            // According to the requirements, all campaigns with set time ranges
            // should have their tasks cancelled and be removed from `workingCampaigns` also by running this statement once.
            // Conducting this step again just to make sure they cannot be in `workingCampaigns` anymore.
            campaignKillerTask(campaign).run();
            it.remove();
        }

        // 1. Fetch today campaigns
        List<Campaign> todayCampaignList = campaignRepository.findTodayScheduledCampaigns();

        this.todayCampaigns.putAll(todayCampaignList.stream().collect(Collectors.toUnmodifiableMap(Campaign::getId, c -> c)));

        LocalDateTime now = LocalDateTime.now();
        List<CompletableFuture<?>> schedulingTasks = new ArrayList<>();
        final int CAMPAIGN_SCHEDULE_COMPUTING_BATCH_SIZE = 10;

        // 2. Schedule related tasks for all fetched campaigns
        for (int b = 0; b < todayCampaignList.size(); b += CAMPAIGN_SCHEDULE_COMPUTING_BATCH_SIZE) {
            // Divide the campaign list into batches and process them parallelly.
            int actualBatchSize = Math.min(CAMPAIGN_SCHEDULE_COMPUTING_BATCH_SIZE, todayCampaignList.size() - b);
            int bFinal = b;

            schedulingTasks.add(CompletableFuture.runAsync(() -> {
                for (int i = bFinal; i < bFinal + actualBatchSize; i++) {
                    Campaign campaign = todayCampaignList.get(i);

                    if (campaign.getSchedule().getType() == ScheduleType.SEND_ONCE) {
                        if (campaign.getSchedule().getSendTime().isEqual(now)
                                || campaign.getSchedule().getSendTime().isAfter(now)) {
                            // If at or passed campaign.sendTime, launch the campaign immediately
                            campaignInitiatorTask(campaign, null).run();
                        } else {
                            String taskId = getCampaignInitiatorTaskName(campaign.getId(), campaign.getSchedule().getSendTime().format(DateTimeFormatter.ofPattern("HH:mm")));

                            registerOneTimeTask(
                                    taskId,
                                    campaignInitiatorTask(campaign, taskId),
                                    campaign.getSchedule().getSendTime().toInstant(ZoneOffset.ofHours(7))
                            );
                        }

                        continue;
                    }

                    // Handle the rest scheduling types
                    try {
                        // 2.1. Merge time ranges
                        TypeReference<List<SendingTimeRangeDTO>> timeRangesType = new TypeReference<>() {
                        };

                        // Sending time ranges via main channel
                        List<SendingTimeRangeDTO> timeRanges = mergeAndExtractCurrentAndFutureTimeRanges(
                                objectMapper.readValue(campaign.getSendingTimeRanges(), timeRangesType).toArray(SendingTimeRangeDTO[]::new)
                        );
                        // Sending time ranges via failover channel
                        List<SendingTimeRangeDTO> failoverTimeRanges = (campaign.getSendingTimeRangesFailover() != null) ?
                                mergeAndExtractCurrentAndFutureTimeRanges(
                                        objectMapper.readValue(campaign.getSendingTimeRangesFailover(), timeRangesType).toArray(SendingTimeRangeDTO[]::new)
                                ) : null;
                        List<SendingTimeRangeDTO> allTimeRanges = new LinkedList<>(timeRanges);

                        Optional.ofNullable(failoverTimeRanges).ifPresent(allTimeRanges::addAll);

                        List<SendingTimeRangeDTO> resultTimeRanges = mergeAndExtractCurrentAndFutureTimeRanges(allTimeRanges.toArray(SendingTimeRangeDTO[]::new));

                        // 2.2. Schedule campaign-lifecycle controlling tasks
                        // For each result time range:
                        // - if now() is after end time, ignore it and continue.
                        // - else if in that range, launch immediately by adding to workingCampaigns.
                        // - else (now() is before start time), schedule an initiator & a killer job.
                        for (int j = 0; j < resultTimeRanges.size(); j++) {
                            SendingTimeRangeDTO resultTimeRange = resultTimeRanges.get(j);
                            LocalDateTime startTime = LocalTime.parse(resultTimeRange.getStartTime()).atDate(now.toLocalDate());
                            LocalDateTime endTime = LocalTime.parse(resultTimeRange.getEndTime()).atDate(now.toLocalDate());

                            // If passed a time range, continue reading the next one
                            if (endTime.isEqual(now) || endTime.isBefore(now)) continue;

                            if (startTime.isEqual(now) || startTime.isBefore(now)) {
                                // Run immediately if in a time range
                                campaignInitiatorTask(campaign, null).run();
                            } else if (j == 0) {
                                // Schedule an initiator if not in time range
                                String taskId = getCampaignInitiatorTaskName(campaign.getId(), resultTimeRange.getStartTime());

                                registerOneTimeTask(
                                        taskId,
                                        campaignInitiatorTask(campaign, taskId),
                                        startTime.toInstant(ZoneOffset.ofHours(7))
                                );

                                // If running a node alone & that node gets outage suddenly, a campaign may still be in ACTIVE status.
                                // When a node's started up after that, check the campaign's status and switch to other appropriate value
                                // TODO: consider to make a job to periodically scan the campaigns currently with ACTIVE status
                                if (campaign.getStatus() == CampaignStatus.ACTIVE) {
                                    campaign.setStatus(CampaignStatus.NOT_STARTED);
                                    campaignRepository.save(campaign);
                                }
                            } else {
                                // If not the first time range of the day, just resume working campaigns
                                String taskId = getCampaignResumingTaskName(campaign.getId(), resultTimeRange.getStartTime());

                                registerOneTimeTask(
                                        getCampaignResumingTaskName(campaign.getId(), resultTimeRange.getStartTime()),
                                        campaignResumingTask(campaign, taskId),
                                        startTime.toInstant(ZoneOffset.ofHours(7))
                                );
                            }

                            // If not the last time range of the day, only pause working campaigns when out of time range.
                            // Schedule to kill campaigns otherwise.
                            if (j < resultTimeRanges.size() - 1) {
                                String taskId = getCampaignPausingTaskName(campaign.getId(), resultTimeRange.getEndTime());

                                registerOneTimeTask(
                                        taskId,
                                        campaignPausingTask(campaign, taskId),
                                        endTime.toInstant(ZoneOffset.ofHours(7))
                                );
                            } else {
                                registerOneTimeTask(
                                        getCampaignKillerTaskName(campaign.getId(), resultTimeRange.getEndTime()),
                                        campaignKillerTask(campaign),
                                        endTime.toInstant(ZoneOffset.ofHours(7))
                                );
                            }
                        }

                        // 2.3. Schedule tasks to update the `campaign.workingChannels` list
                        for (int j = 0; j < timeRanges.size(); j++) {
                            var timeRange = timeRanges.get(j);
                            ChannelType channelType = campaign.getChannelType();

                            if (now.toLocalTime().isBefore(LocalTime.parse(timeRange.getStartTime()))) {
                                registerOneTimeTask(
                                        getChannelListUpdatingTask(campaign.getId(), true, channelType, timeRange.getStartTime()),
                                        () -> campaign.getWorkingChannels().add(channelType),
                                        LocalTime.parse(timeRange.getStartTime()).atDate(now.toLocalDate()).toInstant(ZoneOffset.ofHours(7))
                                );
                            } else {
                                campaign.getWorkingChannels().add(channelType);
                            }

                            registerOneTimeTask(
                                    getChannelListUpdatingTask(campaign.getId(), false, channelType, timeRange.getEndTime()),
                                    () -> campaign.getWorkingChannels().remove(channelType),
                                    LocalTime.parse(timeRange.getEndTime()).atDate(now.toLocalDate()).toInstant(ZoneOffset.ofHours(7))
                            );
                        }

                        Optional.ofNullable(failoverTimeRanges).ifPresent((l) -> {
                            for (int j = 0; j < l.size(); j++) {
                                var timeRange = l.get(j);
                                ChannelType channelType = campaign.getFailoverChannelType();

                                if (now.toLocalTime().isBefore(LocalTime.parse(timeRange.getStartTime()))) {
                                    registerOneTimeTask(
                                            getChannelListUpdatingTask(campaign.getId(), true, channelType, timeRange.getStartTime()),
                                            () -> campaign.getWorkingChannels().add(channelType),
                                            LocalTime.parse(timeRange.getStartTime()).atDate(now.toLocalDate()).toInstant(ZoneOffset.ofHours(7))
                                    );
                                } else {
                                    campaign.getWorkingChannels().add(channelType);
                                }

                                registerOneTimeTask(
                                        getChannelListUpdatingTask(campaign.getId(), false, channelType, timeRange.getEndTime()),
                                        () -> campaign.getWorkingChannels().remove(channelType),
                                        LocalTime.parse(timeRange.getEndTime()).atDate(now.toLocalDate()).toInstant(ZoneOffset.ofHours(7))
                                );
                            }
                        });
                    } catch (JsonProcessingException e) {
                        log.error("Error deserializing sendingTimeRanges '{}'", campaign.getSendingTimeRanges());
                        throw new RuntimeException(e);
                    }
                }
            }));
        }

        CompletableFuture.allOf(schedulingTasks.toArray(CompletableFuture[]::new)).join();
    }

    private List<SendingTimeRangeDTO> mergeAndExtractCurrentAndFutureTimeRanges(SendingTimeRangeDTO... timeRanges) {
        SortedMap<LocalTime, Boolean> timeBorders = new TreeMap<>(); // value=true if is startTime
        LocalTime now = LocalTime.now();

        for (SendingTimeRangeDTO timeRange : timeRanges) {
            var startTime = LocalTime.parse(timeRange.getStartTime());
            var endTime = LocalTime.parse(timeRange.getEndTime());

            // Continue only if now is before end time at least 3s (hard coded)
            if (now.until(endTime, ChronoUnit.SECONDS) < 3) continue;

            // If stored a time of opposite type, remove it from the Map and do not insert the new one
            if (timeBorders.containsKey(startTime) && !timeBorders.get(startTime)/*isEndTime*/) {
                timeBorders.remove(startTime);
            } else if (!timeBorders.containsKey(startTime)) {
                timeBorders.put(startTime, true);
            }

            if (timeBorders.containsKey(endTime) && timeBorders.get(endTime)/*isStartTime*/) {
                timeBorders.remove(endTime);
            } else if (!timeBorders.containsKey(endTime)) {
                timeBorders.put(endTime, false);
            }
        }

        // Merge the time ranges using a stack
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("HH:mm");
        Stack<Map.Entry<LocalTime, Boolean>> tempTimeBorderStack = new Stack<>();
        List<SendingTimeRangeDTO> resultTimeRanges = new ArrayList<>();

        timeBorders.entrySet().forEach((entry) -> {
            if (entry.getValue()/*isStartTime*/) {
                tempTimeBorderStack.push(entry);
                return;
            }

            /*isEndTime*/
            if (tempTimeBorderStack.size() == 1) {
                LocalTime startTime = tempTimeBorderStack.getFirst().getKey();
                LocalTime endTime = entry.getKey();

                if (now.isBefore(endTime)) {
                    resultTimeRanges.add(new SendingTimeRangeDTO(startTime.format(dtf), endTime.format(dtf)));
                }
            }

            tempTimeBorderStack.pop();
        });

        return resultTimeRanges;
    }

    @Scheduled(fixedDelay = 1000L)
    protected synchronized void countBySecond() {
        availableMessagesWithinSecond = maxProcessedMessagesPerSecond - currentlyBeingProcessedMessages;
    }

    @Scheduled(fixedDelay = 2000L, initialDelay = 5000L)
    protected void filterMessagesByPolicies() {
        // Try to set an appropriate fetching size per campaign after every x seconds.
        int fetchingSizePerCampaign = workingCampaigns.isEmpty() ? 0 : (maxMessagesInMemory / workingCampaigns.size());

        CompletableFuture<?>[] messageValidatingTasks = workingCampaigns.values().stream().map((campaign) -> CompletableFuture.runAsync(() -> {
            if (campaign.getStatus() != CampaignStatus.ACTIVE) return;

            if (campaign.isDoneWithFilteringMessages()) return;

            // Temporarily lock queried records and immediately transition status from SUFFICIENT -> VALIDATING before unlocking
            // TODO: filtered by SCHEDULED_TIME <= now when fetching to send, eventually get all (including SCHEDULED_TIME > now) if UPDATED_AT - now >= 10min)
            List<PendingMessage> pendingMessages = pendingMessageRepository.findAndUpdateStatusByCampaignIdAndChannelInAndStatus(
                    MessageStatus.VALIDATING, campaign.getId(), campaign.getWorkingChannels().stream().map(ChannelType::getName).toList(),
                    MessageStatus.SUFFICIENT, fetchingSizePerCampaign
            );

            if (pendingMessages.isEmpty()) {
                boolean messageGeneratingHasCompleted = true; // TODO: [integration phase] define a field to store & read whether all of the messages of a campaign has been generated.
                boolean outOfPendingMessages = pendingMessageRepository.countByCampaignIdAndChannelTypeInAndStatusIn(
                        campaign.getId(), campaign.getWorkingChannels(), List.of(MessageStatus.SUFFICIENT, MessageStatus.VALIDATING)
                ) == 0;

                if (messageGeneratingHasCompleted && outOfPendingMessages) {
                    campaign.setDoneWithFilteringMessages(true);
                    // TODO: may need to update MESSAGE_LOG.STATUS, by which Push Service can read this status and transition to OUT_OF_EPOINTS status if needed.
                    log.debug("[CPG-{}] Messages stored in PENDING_MESSAGE has run out. Stop validating message of this campaign.", campaign.getId());
                } else {
                    log.debug("[CPG-{}] Messages stored in PENDING_MESSAGE temporarily run out.", campaign.getId());
                }

                return;
            }

            log.debug("[CPG-{}] Updated status of pending messages: [{}] to {}",
                    campaign.getId(),
                    pendingMessages.stream().map(String::valueOf).collect(Collectors.joining(",")),
                    MessageStatus.VALIDATING
            );

            List<List<PendingMessage>> currentMessageBatches = new LinkedList<>();

            for (int i = 0; i < pendingMessages.size(); i += sequentialBatchSize) {
                currentMessageBatches.add(pendingMessages.subList(i, i + Math.min(pendingMessages.size() - i, sequentialBatchSize)));
            }

            CompletableFuture.allOf(
                    currentMessageBatches.stream().map((batch) -> CompletableFuture.runAsync(() -> {
                        batch.forEach((message) -> {
                            // Require availableMessagesWithinSecond > 0 before conducting the main instructions
                            do {
                                synchronized (this) {
                                    if (availableMessagesWithinSecond == 0) {
                                        log.debug("[CPG-{}] Waiting for idle slots; {} messages in queue.", campaign.getId(), pendingMessages.size());
                                        continue;
                                    }

                                    updateAvailableMessagesPerSecond(1);
                                }

                                if (passesPolicies(message)) {
                                    message.setStatus(MessageStatus.VALID);
                                    log.trace("[CPG-{}] Message {\"id\":{},\"content\":\"{}...\"} matches the policies.",
                                            campaign.getId(), message.getId(), message.getContent().substring(0, Math.min(10, message.getContent().length())));
                                } else {
                                    // TODO: for checking telecom provider-specific time ranges, find way to determine which msgs should be re-validated more than 1 time (until the end of campaign's last time range)
                                    message.setStatus(MessageStatus.INVALID);
                                    log.trace("[CPG-{}] Message {\"id\":{},\"content\":\"{}...\"} doesn't match the policies.",
                                            campaign.getId(), message.getId(), message.getContent().substring(0, Math.min(10, message.getContent().length())));
                                }

                                break;
                            } while (true);

                            updateAvailableMessagesPerSecond(-1);
                        });

                        List<Long> validMessageIds = batch.stream().filter((m) -> m.getStatus() == MessageStatus.VALID).map(PendingMessage::getId).toList();
                        List<Long> invalidMessageIds = batch.stream().filter((m) -> m.getStatus() == MessageStatus.INVALID).map(PendingMessage::getId).toList();

                        // TODO: update SCHEDULED_TIME by using EntityManager & merge/attach PendingMessage entities
                        try (var entityManager = entityManagerFactory.createEntityManager()) {
                            entityManager.getTransaction().begin();
                            batch.stream().filter((m) -> m.getStatus() == MessageStatus.VALID).forEach(entityManager::merge);
                            batch.stream().filter((m) -> m.getStatus() == MessageStatus.INVALID).forEach(entityManager::merge);
                            entityManager.getTransaction().commit();
//                        transactionTemplate.execute((status) -> {
//                            pendingMessageRepository.updateStatusByIdInAndStatus(MessageStatus.VALID, validMessageIds, MessageStatus.VALIDATING);
//                            pendingMessageRepository.updateStatusByIdInAndStatus(MessageStatus.INVALID, invalidMessageIds, MessageStatus.VALIDATING);

//                            return true;
//                        });
                        }

                        log.debug("[CPG-{}] Updated status of pending messages: [{}] to {}; [{}] to {}",
                                campaign.getId(),
                                validMessageIds.stream().map(String::valueOf).collect(Collectors.joining(",")),
                                MessageStatus.VALID,
                                invalidMessageIds.stream().map(String::valueOf).collect(Collectors.joining(",")),
                                MessageStatus.INVALID
                        );
                    }, messageValidatingTaskExecutor)).toArray(CompletableFuture[]::new)
            ).join();
        }, messageValidatingTaskExecutor)).toArray(CompletableFuture[]::new);

        CompletableFuture.allOf(messageValidatingTasks).join();
    }

    /**
     * @param subtracted can be either positive (subtract) or negative (plus).
     */
    private synchronized void updateAvailableMessagesPerSecond(int subtracted) {
        if (subtracted == 0) return;

        if (subtracted > 0) availableMessagesWithinSecond -= subtracted;

        if (subtracted < 0 && heldMessagesOfPrevSecond > 0) {
            int minOfTwo = Math.min(-subtracted, heldMessagesOfPrevSecond);
            availableMessagesWithinSecond += minOfTwo;
            heldMessagesOfPrevSecond -= minOfTwo;
        }

        currentlyBeingProcessedMessages += subtracted;
    }

    private boolean passesPolicies(PendingMessage pendingMessage) {
        return true;
//        if (!policyBasedMessageValidator.isWithinAllowedSMSSendingTimeRange(pendingMessage)) {
//            policyBasedMessageValidator.setScheduledTime(pendingMessage);
//
//            return false;
//        }
//
//        return true;
    }

    private boolean isSendingWithin(PendingMessage pendingMessage) {
        return true;
    }

    /**
     * Find {@link PendingMessage} entities with the status {@link MessageStatus#VALIDATING} that haven't been
     * switched to new status for too long (by checking if {@link PendingMessage#getUpdatedAt()} - now >= 5 minutes),
     * then update them back to {@link MessageStatus#SUFFICIENT}
     * to let {@link this#filterMessagesByPolicies()} fetch & validate them.
     * <br/>
     * One of the main causes of this problem is one of this service's nodes get outage unexpectedly
     * while dealing with a set of messages in memory.
     */
    @Scheduled(fixedDelay = 60000L, initialDelay = 1000L)
    protected void recoverHungPendingMessageStatus() {
        int updateSizePerCampaign = workingCampaigns.isEmpty() ? 0 : (maxMessagesInMemory / workingCampaigns.size());

        workingCampaigns.values().forEach((campaign) -> {
            while (true) {
                int numOfUpdatedRows = pendingMessageRepository.updateStatusByCampaignIdAndStatusAndUpdatedAt(
                        MessageStatus.SUFFICIENT, campaign.getId(),
                        MessageStatus.VALIDATING, 5, updateSizePerCampaign
                );

                if (numOfUpdatedRows < updateSizePerCampaign) break;
            }
        });
    }

    /**
     * Handle user input to a campaign.
     */
    @RabbitListener(queues = "${app.rabbitmq.queue.campaign-event}")
    protected void handleCampaignEvent(CampaignEvent campaignEvent) {
        switch (campaignEvent.getType()) {
            case CREATED -> {
                Campaign campaign = campaignRepository.findById(campaignEvent.getId()).orElseThrow(() -> new RuntimeException("No campaign with ID=%d is found".formatted(campaignEvent.getId())));

                // According to requirements, only SEND_NOW campaign is allowed to be launched right after created.
                // Campaigns of the rest types must be launched on next day.
                if (campaign.getSchedule().getType() == ScheduleType.SEND_NOW) {
                    campaignKillerTask(campaign).run();
                    todayCampaigns.put(campaign.getId(), campaign);
                    campaignInitiatorTask(campaign, null).run();
                    log.debug("Loaded & launched SEND_NOW campaign with ID=%d".formatted(campaignEvent.getId()));
                }
            }
            case CHANGED_STATUS -> {
                Campaign campaign = Optional.ofNullable(todayCampaigns.get(campaignEvent.getId()))
                        .orElseThrow(() -> new RuntimeException("Received status update event of the campaign with ID={} but the campaign is not found in `todayCampaigns` task."));
                CampaignStatus oldStatus = campaign.getStatus();
                CampaignStatus newStatus = campaignEvent.getStatus();


                if (newStatus == CampaignStatus.FINISHED) { // Precondition: campaign.status == ACTIVE or PAUSED
                    campaignKillerTask(campaign).run();
                    log.debug("Switched status from {} to {} for campaign with ID={}, thus killed the campaign immediately.", oldStatus.getValue(), newStatus.getValue(), campaign.getId());
                } else { // ACTIVE => PAUSED or PAUSED => ACTIVE
                    campaign.setStatus(newStatus);
                    log.debug("Switched status from {} to {} for campaign with ID={}", oldStatus.getValue(), newStatus.getValue(), campaign.getId());
                }
            }
            // Precondition: campaign.status == NOT_STARTED
            case DELETED -> {
                Optional.ofNullable(workingCampaigns.get(campaignEvent.getId()))
                        .ifPresent((campaign) -> {
                            campaignKillerTask(campaign).run();
                            log.debug("Killed campaign with ID={}", campaign.getId());
                        });
            }
        }
    }

    private ScheduledFuture<?> registerOneTimeTask(String identifier, Runnable task, Instant instant) {
        ScheduledFuture<?> scheduledTask = taskScheduler.schedule(task, instant);

        scheduledTasks.put(identifier, scheduledTask);

        return scheduledTask;
    }

    private void cancelAndRemoveRelatedTasks(long campaignId) {
        scheduledTasks.keySet().stream()
                .filter((taskId) -> Pattern.compile("^CPG-" + campaignId + "(-[\\w:]+)+$").asMatchPredicate().test(taskId))
                .forEach((taskId) -> {
                    scheduledTasks.remove(taskId).cancel(false);
                    log.info("Cancelled task with ID '{}'", taskId);
                });
    }

    /**
     * Load campaign into {@link this#workingCampaigns} and transition its status to {@link CampaignStatus#ACTIVE}.
     */
    private Runnable campaignInitiatorTask(Campaign campaign, @Nullable String taskId) {
        return () -> {
            workingCampaigns.put(campaign.getId(), campaign);
            campaign.setStatus(CampaignStatus.ACTIVE);
            campaignRepository.save(campaign);

            // These scheduling types don't require to define separate time ranges for each channel,
            // so load all declared channels while the campaign is being initiated.
            if (campaign.getSchedule().getType() == ScheduleType.SEND_NOW || campaign.getSchedule().getType() == ScheduleType.SEND_ONCE) {
                Set<ChannelType> workingChannels = new HashSet<>();

                workingChannels.add(campaign.getChannelType());
                Optional.ofNullable(campaign.getFailoverChannelType()).ifPresent(workingChannels::add);

                campaign.setWorkingChannels(Set.copyOf(workingChannels));
            }

            Optional.ofNullable(taskId).ifPresent(scheduledTasks::remove); // remove reference to this task from the Map right after having done
        };
    }

    /**
     * Update specified campaign's status to {@link CampaignStatus#ACTIVE}.
     */
    private Runnable campaignResumingTask(Campaign campaign, @Nullable String taskId) {
        return () -> {
            campaign.setStatus(CampaignStatus.ACTIVE);
            campaignRepository.save(campaign);
            Optional.ofNullable(taskId).ifPresent(scheduledTasks::remove); // remove from the Map right after having done with this task
        };
    }

    /**
     * Update specified campaign's status to {@link CampaignStatus#PAUSED}.
     */
    private Runnable campaignPausingTask(Campaign campaign, @Nullable String taskId) {
        return () -> {
            campaign.setStatus(CampaignStatus.PAUSED);
            campaignRepository.save(campaign);
            Optional.ofNullable(taskId).ifPresent(scheduledTasks::remove); // remove from the Map right after having done with this task
        };
    }

    /**
     * Remove reference to specified campaign from {@link this#todayCampaigns} and {@link this#todayCampaigns},
     * then update the campaign's status to {@link CampaignStatus#FINISHED}.
     */
    private Runnable campaignKillerTask(Campaign campaign) {
        return () -> {
            todayCampaigns.remove(campaign.getId());
            workingCampaigns.remove(campaign.getId());
            cancelAndRemoveRelatedTasks(campaign.getId());

            if (!campaignRepository.existsById(campaign.getId())) return;

//            if (campaign.getSchedule().getType() == ScheduleType.SEND_NOW
//                    || campaign.getSchedule().getType() == ScheduleType.SEND_ONCE) {
//                // may check something before switching campaign's status to FINISHED, like requiring that there's no record found in PENDING_MESSAGE.
            campaign.setStatus(CampaignStatus.FINISHED);
//            } else {
//                if (campaign.getSchedule().getEndDate().isEqual(LocalDate.now())
//                        || campaign.getSchedule().getEndDate().isBefore(LocalDate.now())) {
//                    campaign.setStatus(CampaignStatus.FINISHED);
//                } else {
//                    campaign.setStatus(CampaignStatus.NOT_STARTED);
//                }
//            }

            campaignRepository.save(campaign);
        };
    }

// These task ID generator methods must follow the regex in the `cancelAndRemoveRelatedTasks` method.

    private static String getCampaignInitiatorTaskName(long id, String time) {
        return "CPG-%d-INIT-%s".formatted(id, time);
    }

    private static String getCampaignResumingTaskName(long id, String time) {
        return "CPG-%d-RESUME-%s".formatted(id, time);
    }

    private static String getCampaignPausingTaskName(long id, String time) {
        return "CPG-%d-PAUSE-%s".formatted(id, time);
    }

    private static String getCampaignKillerTaskName(long id, String time) {
        return "CPG-%d-KILL-%s".formatted(id, time);
    }

    private static String getChannelListUpdatingTask(long id, boolean isAdding, ChannelType channelType, String time) {
        return "CPG-%d-%s-%s-%s".formatted(id, isAdding ? "ADD" : "RM", channelType.getName(), time);
    }
}
