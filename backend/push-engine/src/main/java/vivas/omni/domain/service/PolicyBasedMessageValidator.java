package vivas.omni.domain.service;

import jakarta.annotation.PostConstruct;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import vivas.omni.domain.repository.MessagePolicyRepository;
import vivas.omni.domain.repository.entity.MessagePolicy;
import vivas.omni.domain.repository.entity.PendingMessage;
import vivas.omni.infrastructure.constant.SmsType;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.IntStream;

@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PolicyBasedMessageValidator {
    MessagePolicyRepository messagePolicyRepository;

    /**
     * Query order: SmsType -> Telecom provider -> expected {@link List}<{@link SmsSendingTimeRanges}>
     */
    Map<SmsType, Map<Integer, SmsSendingTimeRanges>> smsSendingTimeRanges = Map.of(
            SmsType.CSKH, new HashMap<>(),
            SmsType.QC, new HashMap<>()
    );

    @PostConstruct
    @Scheduled(fixedDelay = 300000)
    protected void fetchTelecomProviders() {
        var allPolicies = messagePolicyRepository.findAll();

        allPolicies.stream()
                .filter((rule) -> rule.getPolicySettingType() == MessagePolicy.PolicyEnum.SMS_SENDING_TIME_LIMIT)
                .forEach((rule) -> Optional.of(smsSendingTimeRanges.get(rule.getSmsType()))
                        .ifPresent((m) -> m.computeIfAbsent(
                                                rule.getNetworkOperatorId(),
                                                (k) -> new SmsSendingTimeRanges(rule.getSmsType(), rule.getNetworkOperatorId(), new LinkedList<>())
                                        )
                                        .timeRangeList().addAll(
                                                IntStream.range(rule.getStartWeekday(), rule.getEndWeekday() + 1).boxed()
                                                        .map((weekday) -> new WeekBasedTimeRange(weekday, rule.getStartTime(), rule.getEndTime()))
                                                        .toList()
                                        )
                        )
                );
    }

    /**
     * Determine if specified SMS message is allowed to be sent at current time
     * by reading the (telecom provider-specific) sending time limit policy.
     *
     * @return true if one of the following conditions is met:<br/>
     * 1. No policy for recipient's telecom provider is found.<br/>
     * 2. Any policy for today without start/end time is found.<br/>
     * 3. any policy covering current time is found.
     */
    public boolean isWithinAllowedSMSSendingTimeRange(PendingMessage pendingMessage) {
        var now = LocalDateTime.now();
        int currentDayOfWeek = now.getDayOfWeek().getValue();
        LocalTime currentTime = now.toLocalTime();
//        List<WeekBasedTimeRange> timeRanges =
        return Optional.ofNullable(smsSendingTimeRanges.get(pendingMessage.getSmsType()).get(pendingMessage.getTelcoId()))
                .map((o) -> o.timeRangeList().stream()
                        .anyMatch((r) -> (r.weekday() == currentDayOfWeek)
                                        && (
                                        (r.fromTime() == null && r.toTime() == null)
                                                || (
                                                (r.fromTime() != null && r.toTime() != null)
                                                        && (r.fromTime().isBefore(currentTime) || r.fromTime().equals(currentTime))
                                                        && (currentTime.isBefore(r.toTime()) || currentTime.equals(r.toTime()))
                                        )
                                )

                        )
                )
                .orElse(true);
    }

    /**
     * Determine assigned value by the following rules (read in order from top to bottom):<br/>
     * If no policy for recipient's telecom provider is found, assign now - 2.<br/>
     * If no policy for today is found, assign next future date - 3.<br/>
     * If any policy for today without start/end time is found, assign now - 2.<br/>
     * If any policy covering current time is found, assign now - 2.<br/>
     * If any policy specifying future time range is found, assign next future date - 3.<br/>
     * If any policy specifying a time range in the past is found, assign next future date - 3.<br/>
     * <p>
     * There are 4 possible cases of the <code>scheduledTime</code> value which is assigned to {@param pendingMessage}:
     * 1. not today, depicted by SCHEDULED_TIME = next_date (then just filter by DATE(SCHEDULED_TIME) = DATE(now))
     * 2. now, depicted by SCHEDULED_TIME = now and continue exec
     * 3. future, depicted by SCHEDULED_TIME = next_fromTime_of_next_timeRange and step over
     * 4. past (should not read again within today), depicted by SCHEDULED_TIME = latest_date_time and then filter them by .
     */
    public void setScheduledTime(PendingMessage pendingMessage) {
        var now = LocalDateTime.now();
        var currentDate = now.toLocalDate();
        int currentDayOfWeekInt = now.getDayOfWeek().getValue();
        LocalTime currentTime = now.toLocalTime();
        List<WeekBasedTimeRange> telecomSpecificTimeRanges = Optional
                .ofNullable(smsSendingTimeRanges.get(pendingMessage.getSmsType()).get(pendingMessage.getTelcoId()))
                .map(SmsSendingTimeRanges::timeRangeList)
                .orElse(null);

        // If no policy of recipient's telecom provider is found, return now
        if (CollectionUtils.isEmpty(telecomSpecificTimeRanges)) {
            pendingMessage.setScheduledTime(now);

            return;
        }

        boolean cannotBeSentToday = telecomSpecificTimeRanges.stream().noneMatch((r) -> r.weekday() == currentDayOfWeekInt);

        // If no policy for today is found, assign date time of nearest future timeRange.fromTime
        if (cannotBeSentToday) {
            LocalDateTime closestDateTime = telecomSpecificTimeRanges.stream().reduce(now.plusDays(7), (possiblyClosestDateTimeInFuture, weekBasedTimeRange) -> {
                // Compute diff based on day of week
                int diffToNextDateTime = weekBasedTimeRange.weekday() - currentDayOfWeekInt + ((weekBasedTimeRange.weekday() > currentDayOfWeekInt) ? 0 : 7);
                LocalDateTime nextDateTime = currentDate.plusDays(diffToNextDateTime).atTime(
                        Optional.ofNullable(weekBasedTimeRange.fromTime()).orElse(LocalTime.of(0, 0))
                );

                return (nextDateTime.isBefore(possiblyClosestDateTimeInFuture)) ? nextDateTime : possiblyClosestDateTimeInFuture;
            }, ((dateTime1, dateTime2) -> dateTime1.isBefore(dateTime2) ? dateTime1 : dateTime2));

            pendingMessage.setScheduledTime(closestDateTime);

            return;
        }

        List<WeekBasedTimeRange> todayTimeRanges = telecomSpecificTimeRanges.stream()
                .filter((r) -> r.weekday() == currentDayOfWeekInt)
                .toList();

        boolean canBeSentNow = todayTimeRanges.stream()
                .anyMatch(r -> (r.fromTime() == null && r.toTime() == null)
                                || (
                                (r.fromTime() != null && r.toTime() != null)
                                        && (r.fromTime().isBefore(currentTime) || r.fromTime().equals(currentTime))
                                        && (currentTime.isBefore(r.toTime()) || currentTime.equals(r.toTime()))
                        )
                );

        if (canBeSentNow) {
            pendingMessage.setScheduledTime(now);

            return;
        }

        // Assign nearest future time within today
        LocalDateTime scheduledDateTime = todayTimeRanges.stream()
                .filter(r -> r.fromTime() != null && currentTime.isBefore(r.fromTime()))
                .map(WeekBasedTimeRange::fromTime)
                .reduce(
                        now.plusDays(1),
                        (oldNextDateTime, fromTime) -> currentDate.atTime(fromTime).isBefore(oldNextDateTime) ? currentDate.atTime(fromTime) : oldNextDateTime,
                        (dateTime1, dateTime2) -> dateTime1.isBefore(dateTime2) ? dateTime1 : dateTime2
                );

        // If found at least 1 future time range within today
        if (!scheduledDateTime.isEqual(now)) {
            pendingMessage.setScheduledTime(scheduledDateTime);

            return;
        }

        // Assign nearest time in the past within today
        LocalDateTime scheduledTimeInPast = todayTimeRanges.stream()
                .filter(r -> r.toTime() != null && currentTime.isAfter(r.toTime()))
                .map(WeekBasedTimeRange::toTime)
                .reduce(
                        now.minusDays(1),
                        (possiblyClosestDateTimeInPast, toTime) -> currentDate.atTime(toTime).isAfter(possiblyClosestDateTimeInPast) ? currentDate.atTime(toTime) : possiblyClosestDateTimeInPast,
                        (dateTime1, dateTime2) -> dateTime1.isAfter(dateTime2) ? dateTime1 : dateTime2
                );

        pendingMessage.setScheduledTime(scheduledTimeInPast);
    }

    public record SmsSendingTimeRanges(SmsType smsType, int telecomProvider, List<WeekBasedTimeRange> timeRangeList) {
    }

    public record WeekBasedTimeRange(int weekday, @Nullable LocalTime fromTime, @Nullable LocalTime toTime) {
    }
}
