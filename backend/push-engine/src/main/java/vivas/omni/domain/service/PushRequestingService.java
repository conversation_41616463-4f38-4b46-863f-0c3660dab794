package vivas.omni.domain.service;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import vivas.omni.domain.repository.entity.Campaign;
import vivas.omni.domain.repository.entity.PendingMessage;
import vivas.omni.domain.repository.CampaignRepository;
import vivas.omni.domain.repository.PendingMessageRepository;
import vivas.omni.infrastructure.constant.ChannelType;
import vivas.omni.infrastructure.constant.MessageStatus;
import vivas.omni.infrastructure.messaging.broker.MessageBroker;

import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

/**
 * Periodically query for valid messages & request Push Service to send them to recipients.
 * Call method: async via broker.
 */
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class PushRequestingService {
    final TransactionTemplate transactionTemplate;
    final MessageBroker messageBroker;
    final PendingMessageRepository pendingMessageRepository;

//    final TaskScheduler taskScheduler;
    final TaskExecutor pushRequestingTaskExecutor;
//    final ConcurrentMap<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    final ConcurrentMap<Long, Campaign> workingCampaigns;

    final String pushRequestQueueName;

    final int maxMessagesInMemory;
    final int sequentialBatchSize;

    final int maxPushRequestsPerSecond;
    /**
     * Store number of messages available to be processed within a second. This variable is used to limit processing of message within a second.
     * <br/>
     * The value of this variable will only be decreased whenever a message is accepted to be processed.
     * <br/>
     * Constraint: 0 <= value <= {@link PushRequestingService#maxPushRequestsPerSecond} (initial value).
     * <br/>
     * Every time transitioning to next second,
     * this variable is re-assigned with: {@link PushRequestingService#maxPushRequestsPerSecond} - {@link PushRequestingService#currentlyBeingSentPushRequests}.
     */
    int availablePushRequestsWithinSecond;
    /**
     * Store number of being-processed messages.
     * <br/>
     * The value of this variable will be both decreased (when a message is allowed to be processed) & increased (when a message is done with processing).
     * <br/>
     * Constraint: 0 (initial value) <= value <= {@link PushRequestingService#maxPushRequestsPerSecond}.
     * <br/>
     * Every time transitioning to next second,
     * this variable is read to subtract {@link PushRequestingService#availablePushRequestsWithinSecond} by this value.
     * <br/>
     */
    int currentlyBeingSentPushRequests = 0;
    /**
     * The birth of this variable is to ensure that
     * if {@link PushRequestingService#currentlyBeingSentPushRequests} > 0 every time transitioning to next second,
     * the number of messages handled within that next second can be increased by which it should not be limited to
     * `{@link PushRequestingService#maxPushRequestsPerSecond} - {@link PushRequestingService#currentlyBeingSentPushRequests}`.
     */
    int heldSlotsOfPrevSecond = 0;

    public PushRequestingService(
            PlatformTransactionManager transactionManager,
            MessageBroker messageBroker,
            PendingMessageFilteringService pendingMessageFilteringService,
            PendingMessageRepository pendingMessageRepository,
            @Value("${app.rabbitmq.queue.push-request}") String pushRequestQueueName,
            @Value("${app.policy-worker.push-requests-in-memory:500}") int maxMessagesInMemory,
            @Value("${app.policy-worker.push-requests-per-second:200}") int maxPushRequestsPerSecond
    ) {
        this.transactionTemplate = new TransactionTemplate(transactionManager);
        this.messageBroker = messageBroker;
        this.pendingMessageRepository = pendingMessageRepository;

//        this.taskScheduler = pendingMessageFilteringService.getTaskScheduler();

        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        this.pushRequestingTaskExecutor = taskExecutor;

        taskExecutor.setThreadNamePrefix("cpg-policy-push-request-pool-");
        taskExecutor.setCorePoolSize(10);
        taskExecutor.setMaxPoolSize(50);
        taskExecutor.afterPropertiesSet();
        taskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        taskExecutor.setAwaitTerminationSeconds(15);

        this.workingCampaigns = pendingMessageFilteringService.getWorkingCampaigns();

        this.pushRequestQueueName = pushRequestQueueName;

        this.maxMessagesInMemory = maxMessagesInMemory;
        this.sequentialBatchSize = 10;

        this.maxPushRequestsPerSecond = maxPushRequestsPerSecond;
        this.availablePushRequestsWithinSecond = maxPushRequestsPerSecond;
    }

    @Scheduled(fixedDelay = 1000L)
    protected synchronized void countBySecond() {
        availablePushRequestsWithinSecond = maxPushRequestsPerSecond - currentlyBeingSentPushRequests;
    }

    @Scheduled(fixedDelay = 2000L, initialDelay = 5000L)
    protected void sendPushRequests() {
        int fetchingSizePerCampaign = workingCampaigns.isEmpty() ? 0 : (maxMessagesInMemory / workingCampaigns.size());

        CompletableFuture<?>[] messageValidatingTasks = workingCampaigns.values().stream().map((campaign) -> CompletableFuture.runAsync(() -> {
            if (campaign.isDoneWithRequestingToSendAllMessages()) return;

            List<PendingMessage> pendingMessages = pendingMessageRepository.findAndUpdateStatusByCampaignIdAndChannelInAndStatus(
                    MessageStatus.REQUESTING, campaign.getId(), campaign.getWorkingChannels().stream().map(ChannelType::getName).toList(),
                    MessageStatus.VALID, fetchingSizePerCampaign
            );

            if (pendingMessages.isEmpty()) {
                return;
            }

            log.debug("[CPG-{}] Updated status of pending messages: [{}] to {}",
                    campaign.getId(),
                    pendingMessages.stream().map(String::valueOf).collect(Collectors.joining(",")),
                    MessageStatus.REQUESTING
            );

            List<List<PendingMessage>> currentMessageBatches = new LinkedList<>();

            for (int i = 0; i < pendingMessages.size(); i += sequentialBatchSize) {
                currentMessageBatches.add(pendingMessages.subList(i, i + Math.min(pendingMessages.size() - i, sequentialBatchSize)));
            }

            CompletableFuture.allOf(
                    currentMessageBatches.stream().map((batch) -> CompletableFuture.runAsync(() -> {
                        batch.forEach((message) -> {
                            // Require availablePushRequestsWithinSecond > 0 before conducting the main instructions
                            do {
                                synchronized (this) {
                                    if (availablePushRequestsWithinSecond == 0) {
                                        log.debug("[CPG-{}] Waiting for idle slots; {} messages in queue.", campaign.getId(), pendingMessages.size());
                                        continue;
                                    }

                                    updateAvailableMessagesPerSecond(1);
                                }

                                PendingMessage sentMessage = PendingMessage.builder()
                                        .id(message.getId())
                                        .channelType(message.getChannelType())
                                        .recipient(message.getRecipient())
                                        .brandname(campaign.getBrandName())
                                        .content(message.getContent())
                                        .configuredFailover(campaign.getUseFailover())
                                        .build();

                                messageBroker.publish(pushRequestQueueName, sentMessage);
                                message.setStatus(MessageStatus.REQUESTED);

                                log.trace("[CPG-{}] Message {\"id\":{},\"content\":\"{}...\"} has been sent.",
                                        campaign.getId(), message.getId(), message.getContent().substring(0, Math.min(10, message.getContent().length())));

                                break;
                            } while (true);

                            updateAvailableMessagesPerSecond(-1);
                        });

                        List<Long> sentMessageIds = batch.stream().filter((m) -> m.getStatus() == MessageStatus.REQUESTED).map(PendingMessage::getId).toList();

                        transactionTemplate.execute((status) -> {
                            pendingMessageRepository.updateStatusByIdInAndStatus(MessageStatus.REQUESTED, sentMessageIds, MessageStatus.REQUESTING);

                            return true;
                        });

                        log.debug("[CPG-{}] Updated status of following pending messages to {}: [{}]",
                                campaign.getId(),
                                MessageStatus.REQUESTED,
                                sentMessageIds.stream().map(String::valueOf).collect(Collectors.joining(","))
                        );
                    }, pushRequestingTaskExecutor)).toArray(CompletableFuture[]::new)
            ).join();
        }, pushRequestingTaskExecutor)).toArray(CompletableFuture[]::new);

        CompletableFuture.allOf(messageValidatingTasks).join();
    }

    /**
     * @param subtracted can be either positive (subtract) or negative (plus).
     */
    private synchronized void updateAvailableMessagesPerSecond(int subtracted) {
        if (subtracted == 0) return;

        if (subtracted > 0) availablePushRequestsWithinSecond -= subtracted;

        if (subtracted < 0 && heldSlotsOfPrevSecond > 0) {
            int minOfTwo = Math.min(-subtracted, heldSlotsOfPrevSecond);
            availablePushRequestsWithinSecond += minOfTwo;
            heldSlotsOfPrevSecond -= minOfTwo;
        }

        currentlyBeingSentPushRequests += subtracted;
    }

    /**
     * Find {@link PendingMessage} entities with the status {@link MessageStatus#REQUESTING} or {@link MessageStatus#REQUESTED} that haven't been
     * switched to new status for too long (by checking if {@link PendingMessage#getUpdatedAt()} - SYSDATE >= 5 minutes) (TODO: this should be configurable),
     * then revert them back to {@link MessageStatus#VALID}
     * to let {@link this#sendPushRequests()} fetch & validate them.
     * <br/>
     * One of the main causes of this problem is one of consumer get outage unexpectedly
     * while dealing with a set of messages in memory.
     */
    @Scheduled(fixedDelay = 60000L, initialDelay = 10000L)
    protected void scanHungPendingMessages() {
        int updateSizePerCampaign = workingCampaigns.isEmpty() ? 0 : (maxMessagesInMemory / workingCampaigns.size());

        workingCampaigns.values().forEach((campaign) -> {
            while (true) {
                int numOfUpdatedRows = pendingMessageRepository.updateStatusByCampaignIdAndStatusAndUpdatedAt(
                        MessageStatus.VALID, campaign.getId(),
                        MessageStatus.REQUESTING, 5, updateSizePerCampaign
                );

                numOfUpdatedRows += pendingMessageRepository.updateStatusByCampaignIdAndStatusAndUpdatedAt(
                        MessageStatus.VALID, campaign.getId(),
                        MessageStatus.REQUESTED, 5, updateSizePerCampaign
                );

                if (numOfUpdatedRows == 0) break;
            }
        });
    }
}
