package vivas.omni.infrastructure.client;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "external-system")
@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@Setter
public class ExternalSystemConfiguration {
    Brandname brandname;

    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @Getter
    public static class Brandname {
        String baseUrl;
        String username;
        String password;
        Integer timeout;
        ApiList api;

        @AllArgsConstructor
        @FieldDefaults(level = AccessLevel.PRIVATE)
        @Getter
        public static class ApiList {
            String pushSms;
            String pushZns;
        }
    }
}
