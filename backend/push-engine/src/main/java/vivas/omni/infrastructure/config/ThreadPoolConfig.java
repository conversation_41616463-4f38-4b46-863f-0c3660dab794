package vivas.omni.infrastructure.config;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.task.TaskExecutionProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
//@DependsOn("constant")
//@EnableAsync
@RequiredArgsConstructor
public class ThreadPoolConfig {
    private final TaskExecutionProperties taskExecutionProperties;

    @Bean(destroyMethod = "shutdown")
    public Executor pushWorker() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        executor.setCorePoolSize(taskExecutionProperties.getPool().getCoreSize());
//        executor.setMaxPoolSize(taskExecutionProperties.getPool().getMaxSize());
//        executor.setQueueCapacity(taskExecutionProperties.getPool().getQueueCapacity());
//        executor.setThreadNamePrefix(taskExecutionProperties.getThreadNamePrefix() + "push-");
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        executor.setWaitForTasksToCompleteOnShutdown(taskExecutionProperties.getShutdown().isAwaitTermination());
//        executor.setAwaitTerminationSeconds((int) taskExecutionProperties.getShutdown().getAwaitTerminationPeriod().toSeconds());
//        executor.initialize();
        return executor;
    }

    @Bean(destroyMethod = "shutdown")
    public Executor policyWorker() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        executor.setCorePoolSize(taskExecutionProperties.getPool().getCoreSize());
//        executor.setMaxPoolSize(taskExecutionProperties.getPool().getMaxSize());
//        executor.setQueueCapacity(taskExecutionProperties.getPool().getQueueCapacity());
//        executor.setThreadNamePrefix(taskExecutionProperties.getThreadNamePrefix() + "policy-");
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        executor.setWaitForTasksToCompleteOnShutdown(taskExecutionProperties.getShutdown().isAwaitTermination());
//        executor.setAwaitTerminationSeconds((int) taskExecutionProperties.getShutdown().getAwaitTerminationPeriod().toSeconds());
//        executor.initialize();
        return executor;
    }
}
