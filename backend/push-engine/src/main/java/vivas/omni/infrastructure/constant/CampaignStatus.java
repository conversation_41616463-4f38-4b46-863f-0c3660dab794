package vivas.omni.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CampaignStatus {
    DRAFT(1, "DRAFT", "<PERSON>ản nháp"),
    NOT_STARTED(2, "NOT_STARTED", "Chưa bắt đầu"),
    ACTIVE(3, "ACTIVE", "Đang hoạt động"),
    OUT_OF_EPOINT(4, "OUT_OF_EPOINT", "Hết điểm"),
    PAUSED(5, "PAUSED", "Tạm dừng"),
    FINISHED(6, "FINISHED", "<PERSON><PERSON> kết thúc"),

    /*The following status can be found only in CAMPAIGN_LOG*/

    /**
     * A campaign is marked with this status when all of its messages have been generated.
     */
    GENERATED_ALL(7, "GENERATED_ALL", ""),
    /**
     * A campaign is marked with this status when all of its generated messages have been validated and its prior status is {@link this#GENERATED_ALL}.
     */
    FILTERED_ALL(8, "FILTERED_ALL", ""),
    /**
     * A campaign is marked with this status when its prior status is {@link this#FILTERED_ALL}
     * and all or a portion of its generated messages, which were initially set as SUFFICIENT status,
     * have been pushed/sent to recipients (including failed sendings).
     */
    SENT_ALL(9, "SENT_ALL", ""),

    /**/
    ;

    private final Integer code;
    private final String value;
    private final String description;

    /**
     * Tìm CampaignStatus từ code
     */
    public static CampaignStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CampaignStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown campaign status code: " + code);
    }

    /**
     * Tìm CampaignStatus từ value string
     */
    public static CampaignStatus fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (CampaignStatus status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown campaign status value: " + value);
    }

    /**
     * Kiểm tra xem code có hợp lệ không
     */
    public static boolean isValidCode(Integer code) {
        if (code == null) {
            return false;
        }
        for (CampaignStatus status : values()) {
            if (status.code.equals(code)) {
                return true;
            }
        }
        return false;
    }
}
