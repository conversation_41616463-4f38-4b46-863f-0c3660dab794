package vivas.omni.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CampaignType {
    SCHEDULED(1, "SCHEDULED", "Chiến dịch theo lịch trình"),
    LBA(2, "<PERSON><PERSON>", "Chiến dịch Location Based Advertising"),
    EVENT(3, "EVENT", "Chiến dịch theo sự kiện");

    private final Integer code;
    private final String value;
    private final String description;

    /**
     * Tìm CampaignType từ code
     */
    public static CampaignType fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CampaignType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown campaign type code: " + code);
    }

    /**
     * Tìm CampaignType từ value string
     */
    public static CampaignType fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (CampaignType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown campaign type value: " + value);
    }

    /**
     * Kiểm tra xem code c<PERSON> hợp lệ không
     */
    public static boolean isValidCode(Integer code) {
        if (code == null) {
            return false;
        }
        for (CampaignType type : values()) {
            if (type.code.equals(code)) {
                return true;
            }
        }
        return false;
    }
}
