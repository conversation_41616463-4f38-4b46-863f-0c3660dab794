package vivas.omni.infrastructure.constant;

import jakarta.persistence.AttributeConverter;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Optional;

@Getter
public enum ChannelType {
    ZNS(1, "ZNS"),
    SMS(2, "SMS");

    private final int number;
    private final String name;

    ChannelType(int number, String name) {
        this.number = number;
        this.name = name;
    }

    @Nullable
    public static ChannelType of(@Nullable Integer number) {
        if (number == null) return null;

        for (ChannelType channelType : values()) {
            if (channelType.number == number) {
                return channelType;
            }
        }

        return null;
    }

    public static class Converter implements AttributeConverter<ChannelType, Integer> {
        @Override
        public Integer convertToDatabaseColumn(ChannelType channelType) {
            return Optional.ofNullable(channelType).map(ChannelType::getNumber).orElse(null);
        }

        @Override
        public ChannelType convertToEntityAttribute(Integer number) {
            return Optional.ofNullable(number).map(ChannelType::of).orElse(null);
        }
    }
}
