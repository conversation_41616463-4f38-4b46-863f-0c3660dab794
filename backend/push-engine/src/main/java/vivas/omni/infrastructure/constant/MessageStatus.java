package vivas.omni.infrastructure.constant;

import vivas.omni.domain.repository.entity.PendingMessage;

public enum MessageStatus {
    /* The following status exist only in PENDING_MESSAGE */
    CREATED,
    /**
     * Previous status: {@link this#CREATED}.<br/>
     * Messages transitioning to this status (by Gen Service) if:<br/>
     * 1. Remaining balance is enough to send the messages (most of the cases).<br/>
     * 2. Having been in {@link this#VALIDATING} for a long-enough period (this period should be configurable).
     */
    SUFFICIENT,
    /**
     * Previous status: {@link this#SUFFICIENT}.<br/>
     * Messages transitioning to this status only after being read by one of Policy Service nodes,
     * by which conduct validating them based on system-wide policies.
     */
    VALIDATING,
    /**
     * Previous status: {@link this#VALIDATING}.<br/>
     * Messages transitioning to this status if:<br/>
     * 1. Passed the system-wide policies (most of the cases).<br/>
     * 2. Having been in {@link this#REQUESTING} or {@link this#REQUESTED} for a long-enough period (this period should be configurable).
     */
    VALID,
    /**
     * Previous status: {@link this#VALID}.<br/>
     * Messages transitioning to this status only after being read by one of Policy Service nodes,
     * by which prepare to send corresponding push requests to Push Service.
     */
    REQUESTING,
    /**
     * Previous status: {@link this#REQUESTING}.<br/>
     * Messages transitioning to this status only after successfully their push requests have been sent to Push Service.
     */
    REQUESTED,
    /**/

    /* The following status exist only in MESSAGE_LOG */
    /**
     * Previous status: {@link this#REQUESTED}.<br/>
     * Messages transitioning to this status only after successfully sent to recipients.<br/>
     */
    SENT,
    /**
     * Messages transitioning to this status only after getting confirmed by 3rd party service.<br/>
     */
    SUCCEED,
    /**
     * Messages transitioning to this status after achieving 1 of the following 2 conditions:<br/>
     * 1. Getting confirmed by 3rd party service.<br/>
     * 2. Not yet received confirmation from 3rd party service for a long-enough period (this period should be configurable).
     */
    FAILED,
    /**/

    /**
     * In the PENDING_MESSAGE table, a message is in this status if:<br/>
     * 1. Not passed the policy of telecom provider-specific sending time ranges.
     * 2. Not passed the policy of telecom provider-specific number of messages allowed to be sent.
     * <br/>
     * In the MESSAGE_LOG table, a message is in this status if:<br/>
     * 1. Not passed the policy of limit message content's length.<br/>
     * 2. Not passed the policy of forbidden keywords in message content.
     */
    INVALID, // exists in both of the tables
}
