package vivas.omni.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ScheduleType {
    SEND_NOW(1, "SEND_NOW", "<PERSON><PERSON><PERSON> ngay lập tức"),
    SEND_ONCE(2, "SEND_ONCE", "G<PERSON>i một lần tại thời điểm cụ thể"),
    SEND_DAILY(3, "SEND_DAILY", "Gửi hàng ngày"),
    SEND_WEEKLY(4, "SEND_WEEKLY", "Gửi hàng tuần"),
    SEND_MONTHLY(5, "SEND_MONTHLY", "<PERSON><PERSON><PERSON> hàng tháng"),
    SEND_CUSTOM(6, "SEND_CUSTOM", "Gửi theo cron expression tùy chỉnh");

    private final Integer code;
    private final String value;
    private final String description;

    public static ScheduleType fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ScheduleType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown schedule type code: " + code);
    }

    /**
     * Tìm ScheduleType từ value string
     */
    public static ScheduleType fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (ScheduleType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown schedule type value: " + value);
    }

    /**
     * Kiểm tra xem code có hợp lệ không
     */
    public static boolean isValidCode(Integer code) {
        if (code == null) {
            return false;
        }
        for (ScheduleType type : values()) {
            if (type.code.equals(code)) {
                return true;
            }
        }
        return false;
    }

    public Integer getCode() {
        return code;
    }
}