package vivas.omni.infrastructure.constant;

import jakarta.persistence.AttributeConverter;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
@Getter
public enum SmsType {
    CSKH(1, "Chăm sóc khách hàng"),
    QC(2, "Quảng cáo");

    int number;
    String name;

    @Nullable
    public static SmsType of(@Nullable Integer number) {
        if (number == null) return null;

        for (SmsType value : values()) {
            if (value.number == number) return value;
        }

        return null;
    }

    public static class Converter implements AttributeConverter<SmsType, Integer> {
        @Override
        public Integer convertToDatabaseColumn(SmsType smsType) {
            return Optional.ofNullable(smsType).map(SmsType::getNumber).orElse(null);
        }

        @Override
        public SmsType convertToEntityAttribute(Integer integer) {
            return Optional.ofNullable(integer).map(SmsType::of).orElse(null);
        }
    }
}
