package vivas.omni.infrastructure.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TargetGroupType {
    EXISTING_GROUP(1),
    UPLOAD_LIST(2);

    private final Integer type;

    @JsonValue
    public Integer getType() {
        return type;
    }

    @JsonCreator
    public static TargetGroupType fromType(Integer type) {
        if (type == null) {
            return null;
        }
        for (TargetGroupType targetGroupType : values()) {
            if (targetGroupType.type.equals(type)) {
                return targetGroupType;
            }
        }
        throw new IllegalArgumentException("Unknown target group type: " + type);
    }
}