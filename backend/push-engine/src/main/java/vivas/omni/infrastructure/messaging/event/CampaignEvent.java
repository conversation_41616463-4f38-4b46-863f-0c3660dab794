package vivas.omni.infrastructure.messaging.event;

import lombok.Getter;
import org.springframework.lang.Nullable;
import vivas.omni.domain.repository.entity.Campaign;
import vivas.omni.infrastructure.constant.CampaignStatus;

@Getter
public class CampaignEvent {
    EventType type;
    long id;
    @Nullable
    CampaignStatus status; // not null only when event type == CHANGED_STATUS

    public enum EventType {
        CREATED,
        DELETED,
        CHANGED_STATUS;
    }
}
