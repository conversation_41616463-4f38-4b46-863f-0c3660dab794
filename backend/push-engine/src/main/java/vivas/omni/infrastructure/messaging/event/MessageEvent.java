package vivas.omni.infrastructure.messaging.event;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class MessageEvent {

    private String name;

    private String reqId;

    private String labelId;

    private String contractTypeId;

    private String contractId;

    private String message;

    private String scheduleTime;

    private String mobileList;

    private String isTelcoSub;

    private String agentId;

    private String apiUser;

    private String apiPass;

    private String userName;

    private String dataCoding;

}
