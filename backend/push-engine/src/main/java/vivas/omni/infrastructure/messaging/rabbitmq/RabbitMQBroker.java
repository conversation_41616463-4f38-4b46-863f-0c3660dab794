package vivas.omni.infrastructure.messaging.rabbitmq;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.stereotype.Component;
import vivas.omni.infrastructure.messaging.broker.MessageBroker;
import vivas.omni.infrastructure.messaging.broker.MessageHandler;

import java.util.HashMap;
import java.util.Map;

@Component
public class RabbitMQBroker implements MessageBroker {
    private static final Logger logger = LoggerFactory.getLogger(RabbitMQBroker.class);
    
    private final RabbitTemplate rabbitTemplate;
    private final ConnectionFactory connectionFactory;
    private final Map<String, SimpleMessageListenerContainer> listeners = new HashMap<>();

    public RabbitMQBroker(RabbitTemplate rabbitTemplate, ConnectionFactory connectionFactory) {
        this.rabbitTemplate = rabbitTemplate;
        this.connectionFactory = connectionFactory;
    }

    @Override
    public void publish(String topic, Object message) {
        try {
            rabbitTemplate.convertAndSend(topic, message);
            logger.debug("Published message to topic: {}", topic);
        } catch (AmqpException e) {
            logger.error("Error publishing message to topic: {}", topic, e);
            throw new RuntimeException("Failed to publish message", e);
        }
    }

    @Override
    public void subscribe(String topic, MessageHandler handler) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(topic);
        container.setMessageListener(message -> {
            try {
                String messageBody = new String(message.getBody());
                handler.handle(messageBody);
            } catch (Exception e) {
                logger.error("Error handling message from topic: {}", topic, e);
            }
        });
        container.start();
        listeners.put(topic, container);
        logger.info("Subscribed to topic: {}", topic);
    }

    @Override
    public void initialize() {
        // Initialize any required exchanges, queues, and bindings
        logger.info("Initializing RabbitMQ broker");
    }

    @Override
    public void shutdown() {
        listeners.values().forEach(SimpleMessageListenerContainer::stop);
        listeners.clear();
        logger.info("RabbitMQ broker shutdown completed");
    }
} 