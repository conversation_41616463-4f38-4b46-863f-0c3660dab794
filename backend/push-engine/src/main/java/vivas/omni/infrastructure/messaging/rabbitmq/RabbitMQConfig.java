package vivas.omni.infrastructure.messaging.rabbitmq;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RabbitMQConfig {
    public static final String COMMON_EXCHANGE_NAME = "omni.exchange";
    public static final String DEFAULT_DEAD_LETTER_EXCHANGE_NAME = COMMON_EXCHANGE_NAME + ".dlx";
    public static final String DEFAULT_DEAD_LETTER_QUEUE_NAME = "omni.queue.dlq";

//    @Bean
//    public DirectExchange exchange() {
//        return new DirectExchange(COMMON_EXCHANGE_NAME);
//    }

    @Bean
    public DirectExchange deadLetterExchange() {
        return new DirectExchange(DEFAULT_DEAD_LETTER_EXCHANGE_NAME);
    }

    @Bean
    public Queue nodeOwnedCampaignEventQueue(@Value("${app.rabbitmq.queue.campaign-event}") String nodeOwnedCampaignEventQueue) {
        return createQueueWithDefaultDLQ(nodeOwnedCampaignEventQueue);
    }

    @Bean
    public Queue pushRequestQueue(@Value("${app.rabbitmq.queue.push-request}") String pushRequestQueueName) {
        return createQueueWithDefaultDLQ(pushRequestQueueName);
    }

    @Bean
    public Queue failoverRequestQueue(@Value("${app.rabbitmq.queue.failover-request}") String failoverRequestQueueName) {
        return createQueueWithDefaultDLQ(failoverRequestQueueName);
    }

    private Queue createQueueWithDefaultDLQ(String queueName) {
        return QueueBuilder.durable(queueName)
                .withArgument("x-message-ttl", 30000) // 30 seconds
                .withArgument("x-dead-letter-exchange", DEFAULT_DEAD_LETTER_EXCHANGE_NAME)
                .withArgument("x-dead-letter-routing-key", DEFAULT_DEAD_LETTER_QUEUE_NAME)
                .build();
    }

    @Bean
    public Queue deadLetterQueue() {
        return QueueBuilder.durable(DEFAULT_DEAD_LETTER_QUEUE_NAME)
                .withArgument("x-message-ttl", ********) // 1 day
                .build();
    }

    /**
     * Declare all bindings here.
     */
    @Bean
    public Declarables bindings() {
        return new Declarables(
                BindingBuilder.bind(deadLetterQueue()).to(deadLetterExchange()).with("omni.routing-key.dlq")
        );
    }

    @Bean
    public SimpleMessageListenerContainer simpleMessageListenerContainer(SimpleRabbitListenerContainerFactory factory) {
        factory.setMessageConverter(messageQueueJsonMessageConverter());
        factory.setConcurrentConsumers(5);
        factory.setMaxConcurrentConsumers(10);

        return factory.createListenerContainer();
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(messageQueueJsonMessageConverter());
        return rabbitTemplate;
    }

    @Bean
    public Jackson2JsonMessageConverter messageQueueJsonMessageConverter() {
        return new Jackson2JsonMessageConverter(messageQueueObjectMapper());
    }

    @Bean
    public ObjectMapper messageQueueObjectMapper() {
        return JsonMapper.builder()
                .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
                .serializationInclusion(JsonInclude.Include.NON_NULL)
                .configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .addModule(new JavaTimeModule())
                .build();
    }
} 