package vivas.omni.infrastructure.util;

import lombok.experimental.UtilityClass;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;

@UtilityClass
public class CommonUtil {

    public LocalDateTime convertToLocalDateTime(Long dateTime) {
        if (dateTime == null || dateTime == 0) {
            return null;
        }
        return Instant.ofEpochSecond(dateTime).atZone(ZoneId.of("Asia/Ho_Chi_Minh")).toLocalDateTime();
    }

    public LocalDate convertToLocalDate(Long dateTime) {
        if (dateTime == null || dateTime == 0) {
            return null;
        }
        return Instant.ofEpochSecond(dateTime).atZone(ZoneId.of("Asia/Ho_Chi_Minh")).toLocalDate();
    }
}
