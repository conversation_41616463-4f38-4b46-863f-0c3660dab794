package vivas.omni.infrastructure.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import lombok.RequiredArgsConstructor;

import java.io.IOException;

@Converter(autoApply = false)
@RequiredArgsConstructor
public class GenericJsonConverter implements AttributeConverter<Object, String> {

    private final ObjectMapper objectMapper;

    @Override
    public String convertToDatabaseColumn(Object attribute) {
        try {
            if (attribute == null)
                return null;
            return objectMapper.writeValueAsString(attribute);
        } catch (Exception e) {
            throw new IllegalArgumentException("Could not serialize object to JSON", e);
        }
    }

    @Override
    public Object convertToEntityAttribute(String dbData) {
        try {
            return dbData == null ? null : objectMapper.readValue(dbData, Object.class);
        } catch (IOException e) {
            throw new RuntimeException("Could not convert JSON to Object", e);
        }
    }
}
