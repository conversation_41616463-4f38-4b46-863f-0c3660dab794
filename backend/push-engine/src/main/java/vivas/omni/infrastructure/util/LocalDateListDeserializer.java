package vivas.omni.infrastructure.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class LocalDateListDeserializer extends JsonDeserializer<List<LocalDate>> {

    @Override
    public List<LocalDate> deserialize(JsonParser jsonParser,
            DeserializationContext deserializationContext)
            throws IOException {

        JsonToken token = jsonParser.getCurrentToken();

        if (token == JsonToken.START_ARRAY) {
            List<LocalDate> dates = new ArrayList<>();
            
            while (jsonParser.nextToken() != JsonToken.END_ARRAY) {
                JsonToken currentToken = jsonParser.getCurrentToken();
                
                if (currentToken == JsonToken.VALUE_NUMBER_INT) {
                    // Handle timestamp (Long) - convert using CommonUtil
                    Long timestamp = jsonParser.getValueAsLong();
                    LocalDate date = CommonUtil.convertToLocalDate(timestamp);
                    if (date != null) {
                        dates.add(date);
                    }
                } else if (currentToken == JsonToken.VALUE_STRING) {
                    // Handle ISO date string
                    String dateString = jsonParser.getValueAsString();
                    if (dateString != null && !dateString.trim().isEmpty()) {
                        dates.add(LocalDate.parse(dateString));
                    }
                }
                // Skip null values or other types
            }
            
            return dates;
        } else {
            // Handle null or other types
            return null;
        }
    }
}
