package vivas.omni.infrastructure.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalDateTime;

public class LocalDateTimeDeserializer extends
        JsonDeserializer<LocalDateTime> {

    @Override
    public LocalDateTime deserialize(JsonParser jsonParser,
            DeserializationContext deserializationContext)
            throws IOException {

        JsonToken token = jsonParser.getCurrentToken();

        if (token == JsonToken.VALUE_NUMBER_INT) {
            // Handle timestamp (Long) - convert using CommonUtil
            Long timestamp = jsonParser.getValueAsLong();
            return CommonUtil.convertToLocalDateTime(timestamp);
        } else if (token == JsonToken.VALUE_STRING) {
            // Handle ISO datetime string
            String dateTimeString = jsonParser.getValueAsString();
            if (dateTimeString == null || dateTimeString.trim().isEmpty()) {
                return null;
            }
            return LocalDateTime.parse(dateTimeString);
        } else {
            // Handle null or other types
            return null;
        }
    }
}
