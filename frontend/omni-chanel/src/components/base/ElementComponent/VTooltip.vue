<template>
  <el-tooltip :placement="placement" :content="props.content" :disabled="props.disabled">
    <template #content>
      <div v-if="!props.content" class="max-w-[320px]">
        <slot name="contentTemplate"> </slot>
      </div>
    </template>
    <slot name="trigger">
      <Icon icon="mingcute:information-fill" class="text-sm text-[#5a5e65] ml-[6px]" />
    </slot>
  </el-tooltip>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue';

const props = withDefaults(
  defineProps<{
    placement?: string;
    content?: string;
    disabled?: boolean;
  }>(),
  {
    placement: 'right',
    disabled: false,
  },
);
</script>

<style scoped></style>
