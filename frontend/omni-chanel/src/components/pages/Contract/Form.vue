<template style="position: relative">
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:file-text" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          v-for="(item, index) in breadcrumbItems"
          :key="index"
          :to="item.path ? { path: item.path } : undefined"
        >
          {{ item.label }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
  <ContractFormFields
    v-if="dataConfig && Object.keys(dataConfig).length > 0"
    :type="type"
    :dataConfig="dataConfig"
    :id="id"
    :initialData="contractData"
    :contractStatus="contractStatus"
    :contractTypeId="contractTypeId"
    :contractEntityType="contractEntityType"
    ref="contractFormRef"
  />
  <div
    class="w-full flex justify-center px-[15px] py-[9px] border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] absolute bottom-0 rounded-b-[14px]"
  >
    <VElementButton
      v-if="type !== PageType.Details"
      styleButton="s"
      label="Hủy"
      id="contract-cancel-button"
      :bgColor="color.closeButton"
      @click="handleCancel"
    />
    <VElementButton
      v-if="type !== PageType.Details"
      label="Lưu"
      styleButton="s"
      id="contract-save-button"
      :bgColor="color.main"
      @click="contractFormRef?.onSubmit"
    />
    <VElementButton
      v-else
      label="Cập nhật"
      styleButton="s"
      id="contract-update-button"
      :bgColor="color.main"
      @click="openUpdate"
    />
  </div>
  <PopupCancelConfirm
    v-model:popupVisible="isShowConfirmPopup"
    @onClose="isShowConfirmPopup = false"
    @onConfirm="back"
  />
</template>

<script setup lang="ts">
import { onMounted, ref, computed, inject } from 'vue';
import { useRouter } from 'vue-router';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useContractStore } from '@/store/contract';
import { color } from '@/constants/statusColor';
import { TEXT } from '@/shared';
import PopupCancelConfirm from '@/components/base/common/PopupCancelConfirm.vue';
import ContractFormFields from './components/ContractFormFields.vue';
import { ROUTE_NAME } from '@/shared';
import { ContractEntityType, ContractPayloadType } from '@/enums/contract';
import { PageType, AccountType } from '@/enums/common';
import adminEnterpriseConfig from './adminEnterprise.constant';
import adminAgentConfig from './adminAgent.constant';
import agentEnterpriseConfig from './agentEnterprise.constant';
import agentAgentConfig from './agentAgent.constant';
import { areEqualObjects } from '@/utils/object';

const props = defineProps<{
  id?: number;
  type: PageType;
  accountType?: number;
  contractEntityType?: string;
}>();

const router = useRouter();
const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const contractStore = useContractStore();

const dataConfig = ref<any>({});
const contractData = ref({});
const contractFormRef = ref();
const contractStatus = ref<number>();
const contractTypeId = ref<number>();
const originalFormData = ref<any>({});

const breadcrumbItems = computed(() => {
  if (!dataConfig.value?.breadcrumbConfig) return [];
  if (props.type === PageType.Add) {
    return dataConfig.value.breadcrumbConfig.add || [];
  }
  if (props.type === PageType.Details) {
    return dataConfig.value.breadcrumbConfig.view || [];
  }
  return dataConfig.value.breadcrumbConfig.update || [];
});

const back = () => {
  const savedPath = contractStore.listContractPath;

  if (savedPath) {
    contractStore.clearListContractPath();
    router.push(savedPath);
    return;
  }

  if (props.type === PageType.Add) {
    if (props.contractEntityType === ContractEntityType.Enterprise) {
      router.push({
        name: ROUTE_NAME.CONTRACT_ENTERPRISE,
      });
    } else {
      router.push({
        name: ROUTE_NAME.CONTRACT_AGENT,
      });
    }
  } else if (props.type === PageType.Update) {
    if (props.contractEntityType === ContractEntityType.Enterprise) {
      router.push({
        name: ROUTE_NAME.CONTRACT_ENTERPRISE_DETAIL,
        params: {
          id: props.id,
        },
      });
    } else {
      router.push({
        name: ROUTE_NAME.CONTRACT_AGENT_DETAIL,
        params: {
          id: props.id,
        },
      });
    }
  }
};

const openUpdate = () => {
  const routeName =
    props.contractEntityType === ContractEntityType.Enterprise
      ? ROUTE_NAME.CONTRACT_ENTERPRISE_UPDATE
      : ROUTE_NAME.CONTRACT_AGENT_UPDATE;

  router.push({
    name: routeName,
    params: {
      id: props.id,
    },
  });
};

//#region Confirm
const isShowConfirmPopup = ref(false);

const isFormChanged = () => {
  if (props.type === PageType.Add) {
    const currentFormData = contractFormRef.value?.getCurrentFormData?.();
    if (!currentFormData) return false;

    const fieldsToCheck = Object.entries(currentFormData).filter(
      ([key]) => key !== 'contract_type',
    );

    const hasData = fieldsToCheck.some(([, value]) => {
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      return value !== null && value !== undefined && value !== '';
    });
    return hasData;
  } else if (props.type === PageType.Update) {
    const currentFormData = contractFormRef.value?.getCurrentFormData?.();
    if (!currentFormData || !originalFormData.value) return false;

    return !areEqualObjects(originalFormData.value, currentFormData, [
      'id',
      'created_at',
      'updated_at',
      'contractStatusId',
    ]);
  }
  return false;
};

const handleCancel = () => {
  if (isFormChanged()) {
    isShowConfirmPopup.value = true;
  } else {
    back();
  }
};
//#endregion

// API URL helper
const getApiUrl = (action: 'detail') => {
  const baseUrl = dataConfig.value?.urlApi;
  return `${baseUrl}/${props.id}`;
};

// Get contract detail for UPDATE mode
const getDetail = async () => {
  if (!props.id) return;

  try {
    overlayLoading.toggleLoading(true);
    const response = await api.get(getApiUrl('detail'), {
      params: { contractTypeId: contractTypeId.value },
    });
    if (response.data.code === 0) {
      contractData.value = response.data.data;
      contractStatus.value = response.data.data?.contractStatusId;

      originalFormData.value = {
        enterprise_id: response.data.data?.partyBId,
        agent_id: response.data.data?.partyBId,
        contract_number: response.data.data?.contractNumber,
        contract_name: response.data.data?.contractName,
        service: response.data.data?.contractChannelIds,
        contract_type: response.data.data?.isTimeLimited,
        effective_date: response.data.data?.contractStartDate,
        expiry_date: response.data.data?.contractEndDate,
        note: response.data.data?.note,
        contract_files: response.data.data?.contractFile ? 'true' : '',
        contract_file_list: response.data.data?.contractFile || [],
      };
    } else {
      toast('error', response.data.message);
    }
  } catch (error) {
    console.error('getDetail error:', error);
    toast('error', TEXT.ERROR_OCCURRED);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

onMounted(async () => {
  if (props.accountType === AccountType.Admin) {
    if (props.contractEntityType === ContractEntityType.Agent) {
      dataConfig.value = adminAgentConfig;
      contractTypeId.value = ContractPayloadType.AdminAgent;
    } else if (props.contractEntityType === ContractEntityType.Enterprise) {
      dataConfig.value = adminEnterpriseConfig;
      contractTypeId.value = ContractPayloadType.AdminEnterprise;
    }
  } else if (props.accountType === AccountType.Agent) {
    if (props.contractEntityType === ContractEntityType.Agent) {
      dataConfig.value = agentAgentConfig;
      contractTypeId.value = ContractPayloadType.AgentAgent;
    } else if (props.contractEntityType === ContractEntityType.Enterprise) {
      dataConfig.value = agentEnterpriseConfig;
      contractTypeId.value = ContractPayloadType.AgentEnterprise;
    }
  }

  // Fetch contract data for UPDATE mode
  if (props.type === PageType.Update) {
    await getDetail();
  }
});
</script>

<style scoped>
:deep(.p-tag) {
  width: 120px;
}
.view-height {
  height: calc(100vh - 265px);
}
</style>
