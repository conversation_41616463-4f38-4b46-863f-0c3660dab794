// Constants cho hợp đồng đại lý (Admin)
import { ALIGN, PLACEHOLDER, ROUTE_PATH } from '@/shared';
import { formatDateTime } from '@/utils/getter.utils';
import { baseDataFilter } from './index.constant';
import { DATE_FORMAT } from '@/constants/common';
import {
  ContractActionsType,
  ContractAppendixActionsType,
  ContractDurationType,
  ContractStatusType,
} from '@/enums/contract';
import {
  getContractStatusLabel,
  getContractTypeLabel,
  getServiceNamesFromIds,
} from './index.utils';

const configAdminAgent = {
  urlApi: '/business/v1/api/admin/contract',
  headers: [
    {
      key: 'contract_number',
      name: '<PERSON><PERSON> hợp đồng',
      visible: true,
      align: ALIGN.START,
    },
    {
      key: 'contract_name',
      name: '<PERSON><PERSON><PERSON> hợ<PERSON> đồng',
      visible: true,
      align: ALIGN.START,
    },
    {
      key: 'agent_name',
      name: '<PERSON><PERSON><PERSON> đạ<PERSON> lý',
      visible: true,
      align: ALIGN.START,
    },
    {
      key: 'service',
      name: '<PERSON><PERSON><PERSON> vụ',
      visible: true,
      align: ALIGN.START,
    },
    {
      key: 'contract_type',
      name: 'Loại hợp đồng',
      visible: true,
      align: ALIGN.CENTER,
    },
    {
      key: 'effective_date',
      name: 'Ngày hiệu lực',
      visible: true,
      align: ALIGN.CENTER,
    },
    {
      key: 'expiry_date',
      name: 'Ngày hết hạn',
      visible: true,
      align: ALIGN.CENTER,
    },
    {
      key: 'status',
      name: 'Trạng thái',
      visible: true,
      align: ALIGN.CENTER,
    },
  ],
  styleHeaders: (index: number): string => {
    const styles = [
      'w-[10%]', // Số hợp đồng
      'w-[20%]', // Tên hợp đồng
      'w-[20%]', // Tên đại lý
      'w-[10%]', // Dịch vụ
      'w-[12%]', // Loại hợp đồng
      'w-[10%]', // Ngày hiệu lực
      'w-[10%]', // Ngày hết hạn
      'w-[8%]', // Trạng thái
    ];
    return styles[index] || 'w-[5%] min-w-[50px]';
  },
  sortTableKeys: [],
  dataFilter: [
    {
      label: 'Đại lý',
      valueName: 'agent_id',
      type: 'dropdown',
      placeholder: PLACEHOLDER.SELECT,
      filterable: true,
      defaultValue: '',
      dropdownConfig: {
        option: [],
      },
    },
    ...baseDataFilter,
  ],
  searchConfig: {
    placeholder: 'Tìm kiếm theo số hợp đồng, tên hợp đồng',
  },
  renderCell: {
    contract_number: (row: any) => row?.contractNumber || '',
    contract_name: (row: any) => row?.contractName || '',
    agent_name: (row: any) => row?.partyBName || '',
    service: (row: any) => getServiceNamesFromIds(row?.contractChannelIds),
    contract_type: (row: any) => getContractTypeLabel(row?.isTimeLimited),
    effective_date: (row: any) => {
      return row?.contractStartDate ? formatDateTime(row?.contractStartDate, DATE_FORMAT.DATE) : '';
    },
    expiry_date: (row: any) => {
      return row?.contractEndDate ? formatDateTime(row?.contractEndDate, DATE_FORMAT.DATE) : '';
    },
    status: (row: any) => getContractStatusLabel(row?.contractStatusId),
  },
  actionRules: {
    [ContractStatusType.Active]: [
      ContractActionsType.VIEW,
      ContractActionsType.UPDATE,
      ContractActionsType.ADD_APPENDIX,
      ContractActionsType.LIQUIDATE,
    ],
    [ContractStatusType.Expired]: [
      ContractActionsType.VIEW,
      ContractActionsType.UPDATE,
      ContractActionsType.ADD_APPENDIX,
      ContractActionsType.LIQUIDATE,
    ],
    [ContractStatusType.Liquidated]: [ContractActionsType.VIEW],
  },
  breadcrumbConfig: {
    base: [
      { label: 'Hợp đồng', path: ROUTE_PATH.CONTRACT_AGENT },
      { label: 'Đại lý', path: null },
    ],
    add: [
      { label: 'Hợp đồng', path: ROUTE_PATH.CONTRACT_AGENT },
      { label: 'Đại lý', path: ROUTE_PATH.CONTRACT_AGENT },
      { label: 'Thêm mới', path: null },
    ],
    update: [
      { label: 'Hợp đồng', path: ROUTE_PATH.CONTRACT_AGENT },
      { label: 'Đại lý', path: ROUTE_PATH.CONTRACT_AGENT },
      { label: 'Chỉnh sửa', path: null },
    ],
    detail: [
      { label: 'Hợp đồng', path: ROUTE_PATH.CONTRACT_AGENT },
      { label: 'Đại lý', path: ROUTE_PATH.CONTRACT_AGENT },
      { label: 'Xem chi tiết', path: null },
    ],
  },
  actionButton: {
    add: true,
    // Detail screen
    detail: {
      [ContractStatusType.Active]: [
        ContractActionsType.UPDATE,
        ContractActionsType.ADD_APPENDIX,
        ContractActionsType.LIQUIDATE,
      ],
      [ContractStatusType.Expired]: [
        ContractActionsType.UPDATE,
        ContractActionsType.ADD_APPENDIX,
        ContractActionsType.LIQUIDATE,
      ],
      [ContractStatusType.Liquidated]: [],
    },
  },
  formFields: {
    agent_id: true,
  },
  appendix: {
    headers: [
      {
        key: 'appendix_number',
        name: 'Số phụ lục',
        visible: true,
        align: ALIGN.START,
      },
      {
        key: 'appendix_name',
        name: 'Tên phụ lục',
        visible: true,
        align: ALIGN.START,
      },
      {
        key: 'note',
        name: 'Ghi chú',
        visible: true,
        align: ALIGN.START,
      },
      {
        key: 'effective_date',
        name: 'Ngày hiệu lực PL',
        visible: true,
        align: ALIGN.CENTER,
      },
    ],
    styleHeaders: (index: number): string => {
      const styles = [
        'w-[20%]', // Số phụ lục
        'w-[25%]', // Tên phụ lục
        'w-[30%]', // Ghi chú
        'w-[15%]', // Ngày hiệu lực PL
      ];
      return styles[index] || 'w-[10%] min-w-[50px]';
    },
    sortTableKeys: [],
    searchConfig: {
      placeholder: 'Tìm kiếm theo số phụ lục, tên phụ lục',
    },
    renderCell: {
      appendix_number: (row: any) => row?.addendumNumber || '',
      appendix_name: (row: any) => row?.addendumName || '',
      note: (row: any) => row?.note || '',
      effective_date: (row: any) => {
        return row?.addendumStartDate
          ? formatDateTime(row?.addendumStartDate, DATE_FORMAT.DATE)
          : '';
      },
    },
    actionRules: {
      [ContractStatusType.Active]: [
        ContractAppendixActionsType.VIEW,
        ContractAppendixActionsType.DELETE,
      ],
      [ContractStatusType.Expired]: [
        ContractAppendixActionsType.VIEW,
        ContractAppendixActionsType.DELETE,
      ],
      [ContractStatusType.Liquidated]: [ContractAppendixActionsType.VIEW],
    },
    actionButton: {
      add: true,
    },
  },
};

export default configAdminAgent;
