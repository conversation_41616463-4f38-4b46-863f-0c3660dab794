<template>
  <div class="mx-[10px] mt-[40px] flex justify-center view-height mb-[40px] overflow-auto">
    <el-form label-position="top" class="w-[70%] flex justify-center gap-[75px] pb-[52px]">
      <div class="flex flex-col gap-[15px] w-[50%]">
        <VElementDropdown
          v-if="dataConfig?.formFields?.enterprise_id"
          name="enterprise_id"
          label="Doanh nghiệp"
          :filterable="true"
          id="contract-enterprise_id-dropdown"
          :placeholder="getPlaceholder(PLACEHOLDER.SELECT_ENTERPRISE, type)"
          :disabled="props.type === PageType.Details || props.type === PageType.Update"
          :option="listEnterprise"
          :style="'w-full'"
          :required="true"
        />
        <VElementDropdown
          v-if="dataConfig?.formFields?.agent_id"
          name="agent_id"
          label="<PERSON><PERSON><PERSON> lý"
          :filterable="true"
          id="contract-agent_id-dropdown"
          :placeholder="getPlaceholder(PLACEHOLDER.SELECT_AGENT, type)"
          :disabled="props.type === PageType.Details || props.type === PageType.Update"
          :option="listAgent"
          :style="'w-full'"
          :required="true"
        />
        <VElementInput
          name="contract_number"
          size="default"
          label="Số hợp đồng"
          id="contract-contract_number-input"
          :placeholder="getPlaceholder('Nhập số hợp đồng', type)"
          :disabled="props.type === PageType.Details"
          :maxlength="25"
          :showLimit="false"
          :required="true"
        />
        <VElementInput
          name="contract_name"
          size="default"
          label="Tên hợp đồng"
          id="contract-contract_name-input"
          :placeholder="getPlaceholder('Nhập tên hợp đồng', type)"
          :disabled="props.type === PageType.Details"
          :maxlength="50"
          :showLimit="false"
        />
        <VElementDropdown
          name="service"
          label="Dịch vụ"
          :filterable="false"
          :multiple="true"
          id="contract-service-dropdown"
          :placeholder="getPlaceholder('Chọn dịch vụ', type)"
          :disabled="props.type === PageType.Details"
          :option="SERVICE_OPTIONS"
          :style="'w-full'"
          :required="true"
          :maxCollapseTags="MAX_COLLAPSE_SERVICE"
        />
        <VUploadFileServerCommon
          ref="fileUploadServerRef"
          file="contract_files"
          listFile="contract_file_list"
          :formType="type"
          label="File hợp đồng"
          id="contract-files"
          :required="true"
          :maxFile="5"
          :fileTypes="['pdf', 'doc', 'docx', 'png']"
          :sizeToValidate="5 * 1024 * 1024"
          msgErrType="Chỉ upload định dạng PDF, Doc, PNG"
          msgErrSize="File tối đa 5MB"
          :typeApi="FileUploadApiType.ImageFile"
        />
      </div>

      <div class="flex flex-col gap-[15px] w-[50%]">
        <VElementDropdown
          name="contract_type"
          label="Loại hợp đồng"
          :filterable="false"
          id="contract-contract_type-dropdown"
          :placeholder="getPlaceholder(PLACEHOLDER.SELECT, type)"
          :disabled="props.type === PageType.Details"
          :option="CONTRACT_TYPE_OPTIONS"
          :style="'w-full'"
        />
        <VElementDateTimePicker
          name="effective_date"
          type="date"
          format="DD/MM/YYYY"
          label="Ngày hiệu lực"
          :placeholder="getPlaceholder('Chọn ngày hiệu lực', type)"
          :disabled="props.type === PageType.Details"
          :required="true"
          :style="'!w-full'"
          @change="handleEffectiveDateChange"
        />
        <VElementDateTimePicker
          v-if="values.contract_type === ContractDurationType.Limited"
          name="expiry_date"
          type="date"
          format="DD/MM/YYYY"
          label="Ngày hết hạn"
          :placeholder="getPlaceholder('Chọn ngày hết hạn', type)"
          :disabled="props.type === PageType.Details"
          :disabledDates="disableExpiryDates"
          :style="'!w-full'"
        />
        <VElementInput
          size="default"
          name="note"
          type="textarea"
          label="Ghi chú"
          :placeholder="getPlaceholder('Nhập ghi chú', type)"
          :maxlength="250"
          :showLimit="false"
          :disabled="props.type === PageType.Details"
        />

        <div v-if="type !== PageType.Add">
          <div class="text-[#6b7280] text-[14px] font-semibold mb-[7px]">Trạng thái</div>
          <Tag
            :class="getContractStatusColor(contractStatus)"
            :value="getContractStatusLabel(contractStatus)"
          />
        </div>
        <VElementDateTimePicker
          v-if="hasLiquidationData"
          name="liquidation_date"
          type="date"
          format="DD/MM/YYYY"
          label="Ngày thanh lý"
          :placeholder="getPlaceholder('Chọn Ngày thanh lý', type)"
          :disabled="true"
          :disabledDates="disableExpiryDates"
          :style="'!w-full'"
        />
        <VUploadFileServerCommon
          v-if="hasLiquidationData"
          ref="fileUploadLiquidationServerRef"
          file="liquidation_files"
          listFile="liquidation_file_list"
          :formType="type"
          label="File Biên bản thanh lý"
          id="contract-files"
          :required="true"
          :maxFile="5"
          :fileTypes="['pdf', 'doc', 'docx', 'png']"
          :sizeToValidate="5 * 1024 * 1024"
          msgErrType="Chỉ upload định dạng PDF, Doc, PNG"
          msgErrSize="File tối đa 5MB"
          :typeApi="FileUploadApiType.ImageFile"
          :disabled="true"
        />
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, inject } from 'vue';
import { useForm } from 'vee-validate';
import * as yup from 'yup';
import { useRouter } from 'vue-router';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useContractStore } from '@/store/contract';
import { TEXT, PLACEHOLDER, type TDropdownItem, ROUTE_NAME } from '@/shared';
import { ERROR_MESSAGE } from '@/constants/templateText';
import { getNullableString, getPlaceholder } from '@/utils';
import { toISOStringWithoutTimezone } from '@/utils/formatDate';
import { FileUploadApiType, PageType, AccountType } from '@/enums/common';
import { getApiUrl, getContractStatusColor, getContractStatusLabel } from '../index.utils';
import { SERVICE_OPTIONS, CONTRACT_TYPE_OPTIONS, MAX_COLLAPSE_SERVICE } from '../index.constant';
import { ContractStatusType, ContractDurationType, ContractEntityType } from '@/enums/contract';
import VUploadFileServerCommon from '@/components/base/common/VUploadFileServerCommon.vue';
import { handleApiError } from '@/utils/useErrorHandler';
import { useUserSession } from '@/store/userSession';

// Props
interface Props {
  type: PageType;
  dataConfig: any;
  id?: number;
  contractStatus?: number;
  initialData?: any;
  contractTypeId: number;
  contractEntityType: string;
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
});

// Services
const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const router = useRouter();
const contractStore = useContractStore();
const user: any = useUserSession();
const accountType = computed<number>(() => user.accountType);
// Data
const listEnterprise = ref<TDropdownItem[]>([]);
const listAgent = ref<TDropdownItem[]>([]);
const fileUploadServerRef = ref();
const fileUploadLiquidationServerRef = ref();
const maxFileCount = ref(0);
const maxFileLiquidationCount = ref(0);

const hasLiquidationData = computed(() => {
  return !!(
    values.liquidation_date ||
    (values.liquidation_file_list && values.liquidation_file_list.length > 0) ||
    maxFileLiquidationCount.value > 0
  );
});

const validationSchema = computed(() => {
  const baseSchema = {
    contract_number: yup.string().required(ERROR_MESSAGE.REQUIRED_FIELD('Số hợp đồng')).trim(),
    contract_name: yup.string().nullable(),
    service: yup
      .array()
      .min(1, ERROR_MESSAGE.REQUIRED_SELECT('Dịch vụ'))
      .required(ERROR_MESSAGE.REQUIRED_SELECT('Dịch vụ')),
    contract_files: yup.mixed().required(ERROR_MESSAGE.CONTRACT.REQUIRED_FILE),
    contract_type: yup.number().nullable(),
    effective_date: yup.string().required(ERROR_MESSAGE.REQUIRED_SELECT('Ngày hiệu lực')),
    note: yup.string().nullable(),
  };

  const dynamicSchema: Record<string, any> = { ...baseSchema };

  if (props.dataConfig?.formFields?.enterprise_id) {
    dynamicSchema.enterprise_id = yup
      .number()
      .required(ERROR_MESSAGE.REQUIRED_SELECT('Doanh nghiệp'));
  }

  if (props.dataConfig?.formFields?.agent_id) {
    dynamicSchema.agent_id = yup.number().required(ERROR_MESSAGE.REQUIRED_SELECT('Đại lý'));
  }

  return yup.object(dynamicSchema);
});

// Form setup
const { values, handleSubmit, setFieldValue } = useForm({
  validationSchema,
});

// Computed
const disableExpiryDates = computed(() => {
  return (date: Date) => {
    if (!values.effective_date) return false;
    return date < new Date(values.effective_date);
  };
});

// Methods
const handleNavigateToDetail = (id: number) => {
  router.push({
    name:
      props.contractEntityType === ContractEntityType.Agent
        ? ROUTE_NAME.CONTRACT_AGENT_DETAIL
        : ROUTE_NAME.CONTRACT_ENTERPRISE_DETAIL,
    params: {
      id: id,
    },
  });
};

const getEnterprises = async () => {
  try {
    const response = await api.get(`/business/v1/api/admin/business/list`, {
      params: {
        status: 1,
        customerSource: accountType.value === AccountType.Admin ? 2 : undefined,
      },
    });
    if (response.data.code === 0 && response.data.data) {
      listEnterprise.value = response.data.data.map(
        (item: { id: number; business_name: string }) => {
          return {
            value: item.id,
            label: item.business_name,
          };
        },
      );
    } else {
      toast('error', response.data.message);
    }
  } catch (error) {
    console.error('getEnterprises error:', error);
    toast('error', TEXT.ERROR_OCCURRED);
  }
};

const getAgents = async () => {
  try {
    const response = await api.get(`/business/v1/api/admin/agent/list`, {
      params: { status: 1 },
    });
    if (response.data.code === 0 && response.data.data) {
      listAgent.value = response.data.data.map((item: { id: number; agent_name: string }) => {
        return {
          value: item.id,
          label: item.agent_name,
        };
      });
    } else {
      toast('error', response.data.message);
    }
  } catch (error) {
    console.error('getAgents error:', error);
    toast('error', TEXT.ERROR_OCCURRED);
  }
};

const onSubmit = handleSubmit(async () => {
  overlayLoading.toggleLoading(true);
  try {
    const fileIds = fileUploadServerRef.value?.listFileUpload.map(
      (item: any) => item.file_upload_id,
    );

    const baseParams = {
      contractNumber: getNullableString(values.contract_number),
      contractName: getNullableString(values.contract_name),
      channels: values.service,
      fileIds: fileIds,
      isTimeLimited: values.contract_type,
      contractStartDate: toISOStringWithoutTimezone(values.effective_date),
      contractEndDate: toISOStringWithoutTimezone(values.expiry_date),
      note: getNullableString(values.note),
      contractStatusId: ContractStatusType.Active,
      contractTypeId: props.contractTypeId,
    };

    const params = {
      ...baseParams,
      ...(props.dataConfig?.formFields?.enterprise_id && { partyBId: values.enterprise_id }),
      ...(props.dataConfig?.formFields?.agent_id && { partyBId: values.agent_id }),
    };

    let response;
    if (props.type === PageType.Add) {
      response = await api.post(getApiUrl('create', props.dataConfig.urlApi), params);
    } else {
      response = await api.put(getApiUrl('update', props.dataConfig.urlApi, props.id), params);
    }

    if (response.data.code === 0) {
      toast('success', response.data.message);
      contractStore.clearPreSelectedId();

      if (props.type === PageType.Add) {
        handleNavigateToDetail(response.data.data);
      } else if (props.id) {
        handleNavigateToDetail(props.id);
      }
    } else {
      toast('error', response.data.message);
    }
  } catch (error: any) {
    console.error('onSubmit error:', error);
    handleApiError(error);
  }

  overlayLoading.toggleLoading(false);
});

const setFormValues = (data: any) => {
  if (!data || Object.keys(data).length === 0) return;

  setFieldValue('enterprise_id', data?.partyBId);
  setFieldValue('agent_id', data?.partyBId);
  setFieldValue('contract_number', data?.contractNumber);
  setFieldValue('contract_name', data?.contractName);
  setFieldValue('service', data?.contractChannelIds);

  // Handle contract files
  if (data?.contractFile && Array.isArray(data.contractFile)) {
    maxFileCount.value = data?.contractFile?.length || 0;

    const transformedFiles = data.contractFile.map((file: any) => ({
      file_upload_id: file.file_upload_id,
      file_name: file.file_name,
      link_url: file.link_url,
    }));

    setFieldValue('contract_file_list', transformedFiles);

    setTimeout(() => {
      if (fileUploadServerRef.value && transformedFiles.length > 0) {
        fileUploadServerRef.value.listFileUpload = transformedFiles;
        setFieldValue('contract_files', 'true');
      } else {
        console.log('ContractFormFields - fileUploadServerRef not available or no files');
      }
    }, 100);
  } else {
    setFieldValue('contract_file_list', data?.contract_files || []);
  }

  // Handle liquidation files
  if (data?.liquidationFile && Array.isArray(data.liquidationFile)) {
    maxFileLiquidationCount.value = data?.liquidationFile?.length || 0;
    const transformedFiles = data.liquidationFile.map((file: any) => ({
      file_upload_id: file.file_upload_id,
      file_name: file.file_name,
      link_url: file.link_url,
    }));

    setFieldValue('liquidation_file_list', transformedFiles);

    setTimeout(() => {
      if (fileUploadLiquidationServerRef.value && transformedFiles.length > 0) {
        fileUploadLiquidationServerRef.value.listFileUpload = transformedFiles;
        setFieldValue('liquidation_files', 'true');
      } else {
        console.log(
          'ContractFormFields - fileUploadLiquidationServerRef not available or no files',
        );
      }
    }, 100);
  } else {
    setFieldValue('liquidation_file_list', data?.liquidation_files || []);
  }

  setFieldValue('contract_type', data?.isTimeLimited);
  setFieldValue('effective_date', data?.contractStartDate);
  setFieldValue('expiry_date', data?.contractEndDate);
  setFieldValue('liquidation_date', data?.liquidationDate);
  setFieldValue('note', data?.note);
};

const handleEffectiveDateChange = () => {
  if (values.contract_type === ContractDurationType.Limited) {
    setFieldValue('expiry_date', null);
  }
};

// Watchers
watch(
  () => values.contract_type,
  (value) => {
    if (value === ContractDurationType.Unlimited) {
      setFieldValue('expiry_date', null);
    }
  },
);

// Watch for initialData changes
watch(
  () => props.initialData,
  (newData) => {
    setFormValues(newData);
  },
  { immediate: true, deep: true },
);

onMounted(async () => {
  overlayLoading.toggleLoading(true);
  const preSelectedId = contractStore.getPreSelectedId();

  if (props.dataConfig?.formFields?.enterprise_id) {
    await getEnterprises();

    if (preSelectedId && props.type === PageType.Add) {
      if (props.contractEntityType === ContractEntityType.Enterprise) {
        setFieldValue('enterprise_id', preSelectedId);
      }
    }
  }

  if (props.dataConfig?.formFields?.agent_id) {
    await getAgents();

    if (preSelectedId && props.type === PageType.Add) {
      if (props.contractEntityType === ContractEntityType.Agent) {
        setFieldValue('agent_id', preSelectedId);
      }
    }
  }

  if (props.type === PageType.Add) {
    setFieldValue('contract_type', ContractDurationType.Limited);
  }

  overlayLoading.toggleLoading(false);
});

const getCurrentFormData = () => {
  return {
    enterprise_id: values.enterprise_id,
    agent_id: values.agent_id,
    contract_number: values.contract_number,
    contract_name: values.contract_name,
    service: values.service,
    contract_type: values.contract_type,
    effective_date: values.effective_date,
    expiry_date: values.expiry_date,
    note: values.note,
    contract_files: values.contract_files,
    contract_file_list: values.contract_file_list,
  };
};

onBeforeUnmount(() => {
  contractStore.clearPreSelectedId();
});

// Expose methods
defineExpose({
  onSubmit,
  setFieldValue,
  getCurrentFormData,
});
</script>
<style scoped>
:deep(.p-tag) {
  max-width: 120px;
  width: 100%;
}
</style>
