<template>
  <VOldTable
    idTable="contract-table"
    ref="tableRef"
    :rows="dataContract?.items ?? []"
    :classTableContainer="classTableContainer"
    :styleHeaders="styleHeaders"
    :headers="headers"
    :pagination="{
      totalPage: dataContract?.paging?.total_pages ?? 0,
      total: dataContract?.paging?.total_records ?? 0,
      perPage: params.page_size ?? 0,
    }"
    :actionLength="3"
    @pageChanged="onPageChange"
    @perPageChange="onPerPageChange"
    @setWidth="onSetWidth"
    @rowClick="handleRowClick"
  >
    <template v-slot:items="{ row }">
      <td
        v-for="header in headers"
        :key="header.key"
        class="text-primaryText px-2"
        :class="header.align === ALIGN.CENTER ? 'text-center' : ''"
      >
        <div
          class="column-container"
          :class="header.align === ALIGN.CENTER ? 'text-center' : ''"
          :title="getCellValue(header.key, row)"
        >
          <template v-if="header.key === 'status'">
            <Tag
              :class="getContractStatusColor(row.contractStatusId)"
              :value="getCellValue(header.key, row)"
            />
          </template>
          <template v-else>
            {{ getCellValue(header.key, row) }}
          </template>
        </div>
      </td>
    </template>
    <template v-slot:actions="{ row }">
      <li
        v-for="action in getRowActions(row)"
        :key="action.key"
        class="item min-w-[100px]"
        @click="handleAction(action.key, row)"
        @keydown="handleAction(action.key, row)"
      >
        <i :class="action.icon + ' text-xs'"></i>
        <span>{{ action.label === 'Thanh lý' ? 'Thanh lý hợp đồng' : action.label }}</span>
      </li>
    </template>
  </VOldTable>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { ALIGN } from '@/shared';
import { getContractStatusColor } from '../index.utils';
import { ACTIONS } from '../index.constant';
import VOldTable from '@/components/base/VOldTable.vue';

interface Props {
  dataContract: any;
  dataConfig: any;
  params: any;
  classTableContainer?: any;
}
const props = withDefaults(defineProps<Props>(), {});
const emit = defineEmits(['pageChanged', 'perPageChange', 'setWidth', 'rowClick', 'action']);

const tableRef = ref();
const tableWidth = ref(0);

const headers = computed(() => {
  if (!props.dataConfig?.headers) return [];

  return props.dataConfig.headers;
});

const styleHeaders = computed(() => {
  if (!props.dataConfig?.styleHeaders || !props.dataConfig?.headers) return [];

  return props.dataConfig.headers.map((header: any, index: number) => ({
    idx: index,
    class: props.dataConfig.styleHeaders(index),
  }));
});

// Methods
const getCellValue = (key: string, row: any) => {
  if (props.dataConfig?.renderCell && props.dataConfig.renderCell[key]) {
    return props.dataConfig.renderCell[key](row);
  }
  return row[key] || '';
};

const getRowActions = (row: any) => {
  const allowedActions = props.dataConfig?.actionRules?.[row.contractStatusId];
  if (!allowedActions) return [];
  return allowedActions.map((key: string) => ({
    key,
    ...ACTIONS[key as keyof typeof ACTIONS],
  }));
};

// Event handlers
const onPageChange = (value: number) => {
  emit('pageChanged', value);
};

const onPerPageChange = (value: number) => {
  emit('perPageChange', value);
};

const onSetWidth = (value: number) => {
  tableWidth.value = value;
  emit('setWidth', value);
};

const handleRowClick = (row: any) => {
  emit('rowClick', row);
};

const handleAction = (actionKey: string, row: any) => {
  emit('action', { actionKey, row });
};

// Expose methods
defineExpose({
  filterData: () => tableRef.value?.filterData?.(),
});
</script>

<style lang="scss" scoped>
.item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 131px;
  font-size: 14px;
  font-weight: 400;
  &:hover {
    color: var(--main-color);
  }
}

:deep(.p-tag) {
  width: 120px;
  text-align: center;
  white-space: nowrap;
}
</style>
