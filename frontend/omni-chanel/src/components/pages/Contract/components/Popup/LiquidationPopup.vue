<template>
  <Dialog
    modal
    header="<PERSON><PERSON> lý hợp đồng"
    v-model:visible="isVisible"
    :draggable="false"
    :pt="{
      content: { class: 'mb-[60px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: '!pl-[184px] !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
      },
    }"
    :style="{
      width: '550px',
      height: 'fit-content',
      backgroundColor: '#fff',
      maxHeight: '100%',
    }"
  >
    <div class="pt-[20px]">
      <div class="flex flex-col flex-1 gap-y-[20px]">
        <div class="max-w-96 text-[#6b7280] mx-auto mb-[8px] text-sm font-semibold text-center">
          Hợp đồng sau khi Thanh lý sẽ không được phé<PERSON><PERSON> nhật, bạn c<PERSON> muốn tiếp tục?
        </div>

        <div class="flex flex-col">
          <div class="flex items-center gap-x-[4px] mb-[6px]">
            <div class="text-[#6b7280] text-[14px] font-semibold">Ngày thanh lý</div>
            <p class="text-[14px] text-[red]">*</p>
          </div>
          <VElementDateTimePicker
            name="liquidation_date"
            placeholder="Chọn ngày thanh lý"
            :style="'!w-full'"
            :required="true"
            format="DD/MM/YYYY"
            valueFormat="YYYY-MM-DD"
          />
        </div>

        <div class="flex flex-col">
          <div class="flex items-center gap-x-[4px] mb-[6px]">
            <div class="text-[#6b7280] text-[14px] font-semibold">File Biên bản thanh lý</div>
            <p class="text-[14px] text-[red]">*</p>
          </div>
          <VUploadFileServerCommon
            ref="fileUploadRef"
            file="liquidation_files"
            listFile="liquidation_file_list"
            :formType="FORM_TYPE.ADD"
            label=""
            :required="true"
            :maxFile="5"
            :fileTypes="['pdf', 'doc', 'docx', 'png']"
            :sizeToValidate="5 * 1024 * 1024"
            msgErrType="Chỉ upload định dạng PDF, Doc, PNG"
            msgErrSize="File tối đa 5MB"
            :typeApi="FileUploadApiType.ImageFile"
          />
        </div>
      </div>
    </div>
    <div class="save-container flex justify-center items-center mt-5">
      <VElementButton :bgColor="color.closeButton" @click="handleClose" label="Đóng" />
      <VElementButton :bgColor="color.main" @click="clickSubmit" label="Xác nhận" />
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch, inject } from 'vue';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import Dialog from 'primevue/dialog';
import * as yup from 'yup';
import { useForm } from 'vee-validate';
import VUploadFileServerCommon from '@/components/base/common/VUploadFileServerCommon.vue';
import { FORM_TYPE } from '@/shared';
import { toISOStringWithoutTimezone } from '@/utils/formatDate';
import { FileUploadApiType } from '@/enums/common';
import { handleApiError } from '@/utils/useErrorHandler';

interface Props {
  id: number;
  contract?: any;
}

const props = defineProps<Props>();
const emit = defineEmits(['onClose', 'onConfirm']);

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const fileUploadRef = ref();

const isVisible = defineModel<boolean>('visible');

// Validation schema
const validationSchema = yup.object({
  liquidation_date: yup.string().required('Vui lòng chọn ngày thanh lý'),
  liquidation_files: yup.mixed().required('Vui lòng bổ sung file Biên bản thanh lý'),
  liquidation_file_list: yup
    .array()
    .min(1, 'Vui lòng bổ sung file Biên bản thanh lý')
    .required('Vui lòng bổ sung file Biên bản thanh lý'),
});

const { values, handleSubmit, setFieldValue } = useForm({
  validationSchema,
});

const handleClose = () => {
  emit('onClose');
  resetForm();
};

const resetForm = () => {
  setFieldValue('liquidation_date', '');
  setFieldValue('liquidation_file_list', []);
  if (fileUploadRef.value) {
    fileUploadRef.value.listFileUpload = [];
  }
};

const clickSubmit = () => {
  onSubmit();
};

const onSubmit = handleSubmit(async () => {
  try {
    overlayLoading.toggleLoading(true);

    const fileIds = values.liquidation_file_list.map((item: any) => item.file_upload_id);
    const requestData = {
      liquidationDate: toISOStringWithoutTimezone(values.liquidation_date),
      liquidationFileIds: fileIds,
    };

    const response = await api.post(
      `/business/v1/api/admin/contract/liquidate/${props.id}`,
      requestData,
    );

    if (response.data.code === 0) {
      toast('success', response.data.message || 'Thanh lý hợp đồng thành công');
      emit('onClose');
      emit('onConfirm');
      resetForm();
    } else {
      toast('error', response.data.message || 'Có lỗi xảy ra khi thanh lý hợp đồng');
    }
  } catch (error: any) {
    console.error('Liquidation error:', error);
    if (!error?.response?.status) {
      handleApiError(error);
    }
  } finally {
    emit('onClose');
    overlayLoading.toggleLoading(false);
  }
});

// Watch for popup visibility
watch(isVisible, (newValue) => {
  if (newValue && props.id) {
    resetForm();
  }
});
</script>

<style scoped>
:deep(.p-dialog) {
  border-radius: 12px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

:deep(.p-dialog-header) {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
}

:deep(.p-dialog-content) {
  padding: 0 1.5rem 1rem 1.5rem;
}
</style>
