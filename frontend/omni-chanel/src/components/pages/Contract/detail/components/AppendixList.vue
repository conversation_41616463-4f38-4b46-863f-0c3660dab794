<script setup lang="ts">
import { ref, computed, inject } from 'vue';
import * as yup from 'yup';
import { useForm } from 'vee-validate';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import VOldTable from '@/components/base/VOldTable.vue';
import { ALIGN } from '@/shared';
import { getContractStatusColor, getRowActions as getRowActionsUtil } from '../../index.utils';
import { APPENDIX_ACTIONS } from '../../index.constant';
import AddAppendixPopup from './popup/AddAppendixPopup.vue';
import { PopupDelete } from '@/components/base/new';
import { handleApiError } from '@/utils/useErrorHandler';

export interface ParamsSearch {
  page_index: number;
  page_size: number;
  searchValue?: string;
}

// <PERSON><PERSON>nh nghĩa props cho component
interface Props {
  id: number;
  type: any;
  dataConfig: any;
  dataTable: any;
  contractStatus?: number;
  contractTypeId: number;
}

// Khai báo props với giá trị mặc định nếu cần
const props = defineProps<Props>();
const overlayLoading = useOverLayLoadingStore();
const api = useApi();
const toast = inject('toast') as any;

const tableRef = ref();
const visibleModalDelete = ref(false);
const deleteId = ref();
const deleteName = ref();
const tableWidth = ref();
const params = ref<ParamsSearch>({
  page_index: 1,
  page_size: 20,
});
const dataAppendix = ref([]) as any;

const appendixConfig = computed(() => {
  return props.dataConfig?.appendix || {};
});

const headers = computed(() => {
  if (!appendixConfig.value?.headers) return [];
  return appendixConfig.value.headers;
});

const styleHeaders = computed(() => {
  if (!appendixConfig.value?.styleHeaders || !appendixConfig.value?.headers) return [];

  return appendixConfig.value.headers.map((header: any, index: number) => ({
    idx: index,
    class: appendixConfig.value.styleHeaders(index),
  }));
});

const { values, handleSubmit } = useForm({
  validationSchema: yup.object({
    org_id: yup.mixed(),
  }),
});

const getList = async () => {
  overlayLoading.toggleLoading(true);

  try {
    const requestData = {
      page_index: params.value?.page_index,
      page_size: params.value?.page_size,
      searchValue: values.keyword ? values.keyword.trim() : undefined,
    };

    if (!requestData.searchValue && requestData.searchValue !== '') {
      delete requestData.searchValue;
    }

    const requestEncoded = new URLSearchParams(requestData);

    const res = await api.get(`/business/v1/api/admin/contract/${props.id}/addendum`, {
      params: requestEncoded,
    });

    if (res.data.code === 0) {
      dataAppendix.value = {
        items: res.data.data.items || [],
        paging: {
          total_pages: res.data.data.paging?.total_pages,
          total_records: res.data.data.paging?.total_records,
        },
      };

      if (res.data.data?.items?.length === 0 && params.value.page_index !== 1) {
        tableRef.value.onPageChange(1);
        tableRef.value.filterData();
        return;
      }
    } else {
      toast('error', res.data.message);
    }
  } catch (error: any) {
    console.error('getList error:', error);
    handleApiError(error);
  }

  overlayLoading.toggleLoading(false);
};

const onPageChange = (value: number) => {
  params.value.page_index = value;
  getList();
};

const onPerPageChange = (value: number) => {
  params.value.page_size = value;
};

const handleNavigateToAdd = () => {
  typeAppendixPopup.value = 'add';
  showAppendixPopup.value = true;
};

const handleNavigateToDetail = (row: any) => {
  typeAppendixPopup.value = 'view';
  selectedAddendumId.value = row.contractAddendumId;
  showAppendixPopup.value = true;
};

const onSubmit = handleSubmit(() => {
  tableRef.value.filterData();
});

const onSetWidth = (value: number) => {
  tableWidth.value = value;
};

const getCellValue = (key: string, row: any) => {
  if (appendixConfig.value?.renderCell && appendixConfig.value.renderCell[key]) {
    return appendixConfig.value.renderCell[key](row);
  }
  return row[key] || '';
};

const showAddButton = computed(() => {
  return appendixConfig.value.actionButton?.add && props.contractStatus !== 3;
});

const showAppendixPopup = ref(false);
const selectedAddendumId = ref<number | null>(null);
const typeAppendixPopup = ref<'add' | 'view'>('add');

const getRowActionsForAppendix = (row: any) => {
  return getRowActionsUtil(
    appendixConfig.value?.actionRules,
    props.contractStatus,
    APPENDIX_ACTIONS,
  );
};

const handleAction = (actionKey: string, row: any) => {
  const actions = {
    view: () => {
      handleNavigateToDetail(row);
    },
    delete: () => {
      handleDelete(row);
    },
  };

  const actionFn = actions[actionKey as keyof typeof actions];
  if (actionFn) {
    actionFn();
  } else {
    console.warn('Unknown action:', actionKey);
  }
};

const handleDelete = (row: any) => {
  deleteId.value = row.contractAddendumId;
  deleteName.value = row.addendumName;
  visibleModalDelete.value = true;
};

const handleDeleteFromPopup = (appendixData: any) => {
  deleteId.value = appendixData.contractAddendumId;
  deleteName.value = appendixData.addendumName;
  visibleModalDelete.value = true;
};


defineExpose({
  getList,
});
</script>

<template>
  <div>
    <div v-if="appendixConfig.searchConfig" class="mb-4">
      <div class="relative filter-container flex justify-between">
        <div class="flex gap-[10px]">
          <VElementInput
            size="default"
            name="keyword"
            prefix="search"
            :placeholder="appendixConfig.searchConfig?.placeholder"
            :style="'!w-[480px]'"
            @keyup.enter="onSubmit"
          />
          <VElementButton
            :bgColor="color.main"
            label="Tìm kiếm"
            styleButton="s"
            @click="onSubmit"
          />
        </div>

        <VElementButton
          v-if="showAddButton"
          styleIcon="text-[20px] text-[#fff] mr-[7px]"
          icon="plus"
          styleButton="s"
          label="Thêm phụ lục"
          :bgColor="color.main"
          @click="handleNavigateToAdd"
        />
      </div>
    </div>

    <VOldTable
      classTableContainer="!max-h-[calc(100vh-430px)]"
      idTable="appendix-table"
      ref="tableRef"
      :rows="dataAppendix?.items ?? []"
      :styleHeaders="styleHeaders"
      :headers="headers"
      :pagination="{
        totalPage: dataAppendix?.paging?.total_pages ?? 0,
        total: dataAppendix?.paging?.total_records ?? 0,
        perPage: params.page_size ?? 0,
      }"
      :actionLength="3"
      @pageChanged="onPageChange"
      @perPageChange="onPerPageChange"
      @setWidth="onSetWidth"
      @rowClick="handleNavigateToDetail"
    >
      <template v-slot:items="{ row }">
        <td
          v-for="header in headers"
          :key="header.key"
          class="text-primaryText px-2"
          :class="header.align === ALIGN.CENTER ? 'text-center' : ''"
        >
          <div
            class="column-container"
            :class="header.align === ALIGN.CENTER ? 'text-center' : ''"
            :title="getCellValue(header.key, row)"
          >
            <template v-if="header.key === 'status'">
              <Tag
                :class="getContractStatusColor(row.status)"
                :value="getCellValue(header.key, row)"
              />
            </template>
            <template v-else>
              {{ getCellValue(header.key, row) }}
            </template>
          </div>
        </td>
      </template>
      <template v-slot:actions="{ row }">
        <li
          v-for="action in getRowActionsForAppendix(row)"
          :key="action.key"
          class="item min-w-[100px]"
          @click="handleAction(action.key, row)"
          @keydown="handleAction(action.key, row)"
        >
          <i :class="action.icon + ' text-xs'"></i>
          <span>{{ action.label }}</span>
        </li>
      </template>
    </VOldTable>

    <PopupDelete
      v-if="visibleModalDelete"
      v-model:visible="visibleModalDelete"
      :id="deleteId"
      :name="'phụ lục hợp đồng'"
      path="/business/v1/api/admin/contract/addendum/"
      @onConfirm="getList"
    />

    <AddAppendixPopup
      v-if="showAppendixPopup"
      v-model:visible="showAppendixPopup"
      :contractId="props.id"
      :addendumId="selectedAddendumId"
      :contractStatus="contractStatus"
      :contractTypeId="contractTypeId"
      :type="typeAppendixPopup"
      @success="getList"
      @close="showAppendixPopup = false"
      @delete="handleDeleteFromPopup"
    />
  </div>
</template>

<style lang="scss" scoped>
.item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 131px;
  font-size: 14px;
  font-weight: 400;
  &:hover {
    color: var(--main-color);
  }
}
:deep(.p-tag) {
  width: 120px;
}
</style>
