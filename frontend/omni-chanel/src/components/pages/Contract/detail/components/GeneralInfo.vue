<template>
  <div class="max-h-[calc(100vh-288px)] overflow-y-scroll">
    <ContractFormFields
      :type="type"
      :dataConfig="dataConfig"
      :id="id"
      :contractStatus="contractStatus"
      :initialData="initialData"
      :contractTypeId="contractTypeId"
    />
  </div>
</template>

<script setup lang="ts">
import ContractFormFields from '../../components/ContractFormFields.vue';
import { PageType } from '@/enums/common';

interface Props {
  dataConfig: any;
  id: number;
  contractStatus: number;
  type: PageType;
  initialData?: any;
  contractTypeId: number;
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
});
</script>
