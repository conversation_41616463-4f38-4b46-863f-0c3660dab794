<template>
  <Dialog
    modal
    v-model:visible="isVisible"
    :draggable="false"
    :closable="true"
    :pt="{
      content: { class: 'mt-[10px] mb-[50px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: 'text-center !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
        style: {
          justifyContent: 'center',
        },
      },
    }"
    :header="props.type === 'view' ? 'Xem chi tiết phụ lục' : 'Thêm mới phụ lục'"
    :style="{
      width: '600px',
      height: 'auto',
      backgroundColor: '#fff',
      maxHeight: '90vh',
    }"
  >
    <el-form label-position="top" class="flex justify-center">
      <div class="flex flex-col gap-4 w-full">
        <VElementInput
          size="default"
          name="appendix_number"
          :label="'Số phụ lục'"
          :placeholder="props.type === 'view' ? '' : 'Nhập số phụ lục'"
          :style="'!w-[100%]'"
          :required="true"
          :maxlength="25"
          :disabled="props.type === 'view'"
        />

        <VElementInput
          size="default"
          name="appendix_name"
          :label="'Tên phụ lục'"
          :placeholder="props.type === 'view' ? '' : 'Nhập tên phụ lục'"
          :style="'!w-[100%]'"
          :maxlength="50"
          :disabled="props.type === 'view'"
        />

        <VElementDateTimePicker
          size="default"
          name="effective_date"
          type="date"
          format="DD/MM/YYYY"
          :label="'Ngày hiệu lực PL'"
          :placeholder="props.type === 'view' ? '' : 'Chọn ngày hiệu lực PL'"
          :style="'!w-[100%]'"
          :required="true"
          :disabled="props.type === 'view'"
        />

        <VElementInput
          type="textarea"
          name="note"
          :label="'Ghi chú'"
          :placeholder="props.type === 'view' ? '' : 'Nhập ghi chú'"
          :style="'!w-[100%]'"
          :maxlength="250"
          :disabled="props.type === 'view'"
        />

        <VUploadFileServerCommon
          file="file"
          listFile="listFile"
          :formType="props.type === 'view' ? PageType.Details : PageType.Add"
          :label="'File phụ lục'"
          :required="true"
          :maxFile="props.type === 'view' ? maxFileCount : 5"
          :fileTypes="['pdf', 'doc', 'docx', 'png']"
          :sizeToValidate="FileSize._5Mb"
          :msgErrType="'Chỉ upload định dạng PDF, Doc, PNG'"
          :msgErrSize="'File tối đa 5MB'"
          :typeApi="FileUploadApiType.ImageFile"
        />
      </div>
    </el-form>

    <!-- Buttons -->
    <div class="save-container flex justify-center items-center gap-5 mt-5">
      <div>
        <VElementButton
          v-if="
            props.type === 'view' &&
            props?.contractStatus !== ContractStatusType.Liquidated &&
            contractTypeId !== ContractPayloadType.AgentAgent
          "
          styleButton="s"
          label="Xóa"
          :bgColor="color.main"
          @click="handleDelete"
        />
        <VElementButton
          styleButton="s"
          label="Đóng"
          :bgColor="color.closeButton"
          @click="closePopup"
        />
        <VElementButton
          v-if="props.type !== 'view'"
          styleButton="s"
          label="Lưu"
          :bgColor="color.main"
          :loading="isSubmitting"
          @click="onSubmit"
        />
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, inject, watch } from 'vue';
import { useForm } from 'vee-validate';
import * as yup from 'yup';
import Dialog from 'primevue/dialog';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { color } from '@/constants/statusColor';
import { ERROR_MESSAGE } from '@/constants/templateText';
import { PLACEHOLDER } from '@/shared';
import { FileSize, FileUploadApiType, PageType } from '@/enums/common';
import { getPlaceholder } from '@/utils';
import { toISOStringWithoutTimezone } from '@/utils/formatDate';
import VUploadFileServerCommon from '@/components/base/common/VUploadFileServerCommon.vue';
import { onMounted } from 'vue';
import { ContractPayloadType, ContractStatusType } from '@/enums/contract';
import { handleApiError } from '@/utils/useErrorHandler';

interface Props {
  contractId: number;
  contractStatus: number;
  type?: 'add' | 'view';
  addendumId?: number | null;
  contractTypeId?: number | null;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  addendumId: null,
  contractTypeId: null,
});
const emit = defineEmits(['close', 'success', 'delete']);

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const isVisible = defineModel<boolean>('visible');
const isSubmitting = ref(false);

const appendixData = ref<any>(null);
const maxFileCount = ref(5);

const validationSchema = yup.object({
  appendix_number: yup
    .string()
    .required(ERROR_MESSAGE.REQUIRED_FIELD('Số phụ lục'))
    .max(25, ERROR_MESSAGE.MAX_LENGTH_FIELD('Số phụ lục', 25))
    .trim(),
  effective_date: yup.date().required(ERROR_MESSAGE.REQUIRED_SELECT('Ngày hiệu lực PL')).nullable(),
  file: yup.mixed().required('Vui lòng bổ sung file phụ lục'),
  listFile: yup
    .array()
    .min(1, 'Vui lòng bổ sung file phụ lục')
    .required('Vui lòng bổ sung file phụ lục'),
});

const { handleSubmit, resetForm, setFieldValue } = useForm({
  validationSchema,
});

const getDetail = async () => {
  if (!props.addendumId) return;

  overlayLoading.toggleLoading(true);
  try {
    const response = await api.get(`/business/v1/api/admin/contract/addendum/${props.addendumId}`);

    if (response.data.code === 0) {
      const data = response.data.data;

      appendixData.value = {
        contractAddendumId: props.addendumId,
        addendumName: data?.addendumName,
      };

      maxFileCount.value = data.addendumFiles?.length || 0;

      setFieldValue('appendix_number', data.addendumNumber);
      setFieldValue('appendix_name', data.addendumName);
      setFieldValue('effective_date', data.addendumStartDate);
      setFieldValue('note', data.note);
      setFieldValue('listFile', data.addendumFiles || []);
    } else {
      toast('error', response.data.message || 'Có lỗi xảy ra khi tải dữ liệu');
    }
  } catch (error: any) {
    console.error('Error getting appendix detail:', error);
    if (!error?.response?.status) {
      handleApiError(error);
    }
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

const closePopup = () => {
  resetForm();
  isVisible.value = false;
  emit('close');
};

const handleDelete = () => {
  if (appendixData.value) {
    emit('delete', appendixData.value);
    closePopup();
  }
};

const onSubmit = handleSubmit(async (values) => {
  isSubmitting.value = true;
  overlayLoading.toggleLoading(true);

  try {
    const fileIds = values.listFile?.map((file: any) => file.file_upload_id);
    const payload = {
      addendumNumber: values.appendix_number,
      addendumName: values.appendix_name || '',
      addendumStartDate: toISOStringWithoutTimezone(values.effective_date),
      note: values.note || '',
      fileIds: fileIds,
    };

    const response = await api.post(
      `/business/v1/api/admin/contract/addendum/${props.contractId}`,
      payload,
    );

    if (response.data.code === 0) {
      toast('success', 'Thêm mới thành công');
      emit('success');
    } else {
      toast('error', response.data.message || 'Có lỗi xảy ra');
    }
  } catch (error: any) {
    console.error('Error adding appendix:', error);
    if (!error?.response?.status) {
      handleApiError(error);
    }
  } finally {
    closePopup();
    isSubmitting.value = false;
    overlayLoading.toggleLoading(false);
  }
});

onMounted(async () => {
  if (props.type === 'view') {
    await getDetail();
  }
});
</script>

<style scoped>
:deep(.p-dialog-mask.p-component-overlay) {
  z-index: 1000 !important;
}

:deep(.p-dialog-header) {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
}

:deep(.p-dialog-content) {
  padding: 0 1.5rem 1.5rem 1.5rem;
}
</style>
