<script setup lang="ts">
import { ref, onMounted, inject, computed, watch, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ROUTE_NAME, TEXT, TabValue } from '@/shared';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useContractStore } from '@/store/contract';
import GeneralInfo from './components/GeneralInfo.vue';
import { ContractEntityType, ContractPayloadType } from '@/enums/contract';
import { getApiUrl } from '../index.utils';
import { PageType, AccountType } from '@/enums/common';
import adminEnterpriseConfig from '../adminEnterprise.constant';
import adminAgentConfig from '../adminAgent.constant';
import agentEnterpriseConfig from '../agentEnterprise.constant';
import agentAgentConfig from '../agentAgent.constant';
import AppendixList from './components/AppendixList.vue';
import { ContractActionsType } from '@/enums/contract';
import { color } from '@/constants/statusColor';
import LiquidationPopup from '../components/Popup/LiquidationPopup.vue';
import AddAppendixPopup from './components/popup/AddAppendixPopup.vue';
import { type TTab } from '@/views/pages/app/admin/cai-dat/bang-gia/index.type';
import BaseTabs from '@/views/pages/app/admin/cai-dat/bang-gia/components/BaseTabs.vue';
import { handleApiError } from '@/utils/useErrorHandler';

const props = defineProps<{
  id: number;
  type: PageType;
  accountType: AccountType;
  contractEntityType: ContractEntityType;
}>();

const router = useRouter();
const route = useRoute();
const contractStore = useContractStore();
const dataConfig = ref<any>();
const dataAppendix = ref<any>();
const contractStatus = ref<number>();
const contractTypeId = ref<number>();
const contractData = ref({});
const appendixListRef = ref();
const showAppendixPopup = ref(false);
const visibleLiquidationPopup = ref(false);
const activeTab = ref<TabValue>(TabValue.Tab1);

const tabs: TTab[] = [
  {
    label: 'Thông tin chung',
    value: TabValue.Tab1,
  },
  {
    label: 'Phụ lục hợp đồng',
    value: TabValue.Tab2,
  },
];

const handleTabChanged = async (tabValue: TabValue) => {
  activeTab.value = tabValue;
  await triggerTabApi(tabValue);
};

const triggerTabApi = async (tabValue: TabValue) => {
  await nextTick();
  if (tabValue === TabValue.Tab2 && appendixListRef.value) {
    appendixListRef.value.getList();
  }
};

const availableActions = computed(() => {
  if (!dataConfig.value?.actionButton?.detail || !contractStatus.value) {
    return [];
  }

  return dataConfig.value.actionButton.detail[contractStatus.value] || [];
});

// Initialize dataConfig immediately based on props
const initializeDataConfig = () => {
  if (props.accountType === AccountType.Admin) {
    if (props.contractEntityType === ContractEntityType.Agent) {
      dataConfig.value = adminAgentConfig;
      contractTypeId.value = ContractPayloadType.AdminAgent;
    } else if (props.contractEntityType === ContractEntityType.Enterprise) {
      dataConfig.value = adminEnterpriseConfig;
      contractTypeId.value = ContractPayloadType.AdminEnterprise;
    }
  } else if (props.accountType === AccountType.Agent) {
    if (props.contractEntityType === ContractEntityType.Agent) {
      dataConfig.value = agentAgentConfig;
      contractTypeId.value = ContractPayloadType.AgentAgent;
    } else if (props.contractEntityType === ContractEntityType.Enterprise) {
      dataConfig.value = agentEnterpriseConfig;
      contractTypeId.value = ContractPayloadType.AgentEnterprise;
    }
  }
};

// Initialize immediately
initializeDataConfig();

const handleNavigateToList = () => {
  const savedPath = contractStore.getListContractPath();
  const activeTab = contractStore.getActiveTab();

  if (savedPath) {
    if (activeTab) {
      router.push({
        path: savedPath,
        query: { tab: activeTab },
      });
    } else {
      router.push(savedPath);
    }
  } else {
    if (props.contractEntityType === ContractEntityType.Agent) {
      router.push({
        name: ROUTE_NAME.CONTRACT_AGENT,
      });
    } else {
      router.push({
        name: ROUTE_NAME.CONTRACT_ENTERPRISE,
      });
    }
  }
};

const handleNavigateToUpdate = () => {
  const routeName =
    props.contractEntityType === ContractEntityType.Agent
      ? ROUTE_NAME.CONTRACT_AGENT_UPDATE
      : ROUTE_NAME.CONTRACT_ENTERPRISE_UPDATE;

  router.push({
    name: routeName,
    params: {
      id: props.id,
    },
  });
};

const handleAddAppendix = () => {
  showAppendixPopup.value = true;
};

const handleLiquidate = () => {
  visibleLiquidationPopup.value = true;
};

const handleLiquidationClose = () => {
  visibleLiquidationPopup.value = false;
};

const handleLiquidationConfirm = () => {
  visibleLiquidationPopup.value = false;
  getDetail();
};

const handleAppendixSuccess = () => {
  showAppendixPopup.value = false;
  appendixListRef.value?.getList();
};

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();

const breadcrumbItems = computed(() => {
  if (!dataConfig.value?.breadcrumbConfig) return [];
  if (props.type === PageType.Add) {
    return dataConfig.value.breadcrumbConfig.add || [];
  }
  if (props.type === PageType.Details) {
    return dataConfig.value.breadcrumbConfig.detail || [];
  }
  return dataConfig.value.breadcrumbConfig.update || [];
});

const getDetail = async () => {
  overlayLoading.toggleLoading(true);

  try {
    const response = await api.get(getApiUrl('detail', dataConfig.value?.urlApi, props.id), {
      params: { contractTypeId: route.query.contractTypeId ?? contractTypeId.value },
    });
    if (response.data.code === 0) {
      const res = response.data.data;
      contractData.value = res;
      contractStatus.value = res.contractStatusId;
      dataAppendix.value = res?.appendix;
    } else {
      toast('error', response.data.message);
    }
  } catch (error: any) {
    console.error('getDetail error:', error);
    if (!error?.response?.status) {
      handleApiError(error);
    }
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

watch(
  () => route.query.tab,
  async (newTab) => {
    if (newTab && typeof newTab === 'string') {
      const tabValue = +newTab as TabValue;
      activeTab.value = tabValue;
      await triggerTabApi(tabValue);
      router.replace({
        path: route.path,
        query: { ...route.query, tab: undefined },
      });
    }
  },
  { immediate: true },
);

onMounted(async () => {
  overlayLoading.toggleLoading(true);
  await getDetail();
  overlayLoading.toggleLoading(false);

  if (!route.query.tab) {
    await triggerTabApi(activeTab.value);
  }
});
</script>

<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:file-text" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          v-for="(item, index) in breadcrumbItems"
          :key="index"
          :to="item.path ? { path: item.path } : undefined"
        >
          {{ item.label }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>

  <BaseTabs :tabs :active-tab="activeTab" tabClass="w-[94%]" @click-tab="handleTabChanged">
    <template #tab-1>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <GeneralInfo
          v-if="dataConfig && Object.keys(dataConfig).length > 0"
          :id="props.id"
          :type="type"
          :initialData="contractData"
          :contractTypeId="contractTypeId"
          :dataConfig="dataConfig"
          :contractStatus="contractStatus"
        />
        <div v-else class="flex items-center justify-center h-full">
          <el-loading-spinner />
        </div>
      </div>
    </template>
    <template #tab-2>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <AppendixList
          ref="appendixListRef"
          v-if="dataConfig && Object.keys(dataConfig).length > 0"
          :id="props.id"
          :type="type"
          :dataConfig="dataConfig"
          :dataTable="dataAppendix"
          :contractStatus="contractStatus"
          :contractTypeId="contractTypeId"
        />
      </div>
    </template>
  </BaseTabs>

  <div
    class="flex z-[999] py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke items-center justify-between"
  >
    <VElementButton
      icon="arrow-narrow-left"
      text="black"
      bgColor="#EBEBEB"
      label="Quay lại"
      @click="handleNavigateToList"
    />

    <div v-if="activeTab === TabValue.Tab1" class="flex">
      <VElementButton
        v-if="availableActions.includes(ContractActionsType.LIQUIDATE)"
        label="Thanh lý hợp đồng"
        styleButton="s"
        :bgColor="color.secondary"
        @click="handleLiquidate"
      />
      <VElementButton
        v-if="availableActions.includes(ContractActionsType.ADD_APPENDIX)"
        label="Thêm phụ lục"
        styleButton="s"
        :bgColor="color.tertiary"
        @click="handleAddAppendix"
      />
      <VElementButton
        v-if="availableActions.includes(ContractActionsType.UPDATE)"
        label="Cập nhật"
        styleButton="s"
        :bgColor="color.main"
        @click="handleNavigateToUpdate"
      />
    </div>
  </div>

  <LiquidationPopup
    v-model:visible="visibleLiquidationPopup"
    :id="props.id"
    :contract="contractData"
    @onClose="handleLiquidationClose"
    @onConfirm="handleLiquidationConfirm"
  />

  <AddAppendixPopup
    v-if="showAppendixPopup"
    v-model:visible="showAppendixPopup"
    :contractId="props.id"
    type="add"
    @success="handleAppendixSuccess"
  />
</template>

<style lang="scss" scoped>
.view-height {
  height: calc(100vh - 172px);
  overflow: auto;
}

.item {
  padding: 6px 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  border-radius: 6px;
  &:hover {
    background-color: #f2f2f7;
  }
}
</style>
