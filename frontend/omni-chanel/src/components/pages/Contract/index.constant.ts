import { ServiceType, ServiceTypeLabel } from '@/enums/common';
import {
  ContractActionsType,
  ContractAppendixActionsType,
  ContractDurationLabel,
  ContractDurationType,
} from '@/enums/contract';
import { PLACEHOLDER, type TDropdownItem } from '@/shared';

export enum CONTRACT_STATUS {
  ACTIVE = 1,
  EXPIRED = 2,
  LIQUIDATED = 3,
}

export enum CONTRACT_TYPE {
  LIMITED = 1,
  UNLIMITED = 2,
}

export enum SERVICE_TYPE {
  SMS = 'SMS',
  ZNS = 'ZNS',
  ALL = 'ALL',
}

export const MAX_COLLAPSE_SERVICE = 3;

export const ACTIONS = {
  [ContractActionsType.VIEW]: { icon: 'pi pi-eye', label: 'Xem' },
  [ContractActionsType.UPDATE]: { icon: 'pi pi-pencil', label: 'Cập nhật' },
  [ContractActionsType.ADD_APPENDIX]: { icon: 'pi pi-plus-circle', label: 'Thêm phụ lục' },
  [ContractActionsType.LIQUIDATE]: { icon: 'pi pi-file-check', label: '<PERSON><PERSON> lý' },
};

export const APPENDIX_ACTIONS = {
  [ContractAppendixActionsType.VIEW]: { icon: 'pi pi-eye', label: 'Xem' },
  [ContractAppendixActionsType.DELETE]: { icon: 'pi pi-trash', label: 'Xóa' },
};

export const baseDataFilter = [
  {
    label: 'Dịch vụ',
    valueName: 'service',
    type: 'dropdown',
    placeholder: PLACEHOLDER.SELECT,
    defaultValue: '',
    dropdownConfig: {
      option: [
        { value: ServiceType.SMS, label: ServiceTypeLabel.SMS },
        { value: ServiceType.ZNS, label: ServiceTypeLabel.ZNS },
      ],
    },
  },
  {
    label: 'Trạng thái',
    valueName: 'status',
    type: 'dropdown',
    placeholder: PLACEHOLDER.SELECT,
    defaultValue: '',
    dropdownConfig: {
      option: [
        { value: 1, label: 'Hoạt động' },
        { value: 2, label: 'Hết hiệu lực' },
        { value: 3, label: 'Thanh lý' },
      ],
    },
  },
];

export const SERVICE_OPTIONS: TDropdownItem[] = [
  { value: ServiceType.SMS, label: ServiceTypeLabel.SMS },
  { value: ServiceType.ZNS, label: ServiceTypeLabel.ZNS },
];

export const CONTRACT_TYPE_OPTIONS: TDropdownItem[] = [
  { value: ContractDurationType.Limited, label: ContractDurationLabel.Limited },
  { value: ContractDurationType.Unlimited, label: ContractDurationLabel.Unlimited },
];

// ------------------------------
// export const baseHeader = [
//   {
//     key: 'contract_number',
//     label: 'Số hợp đồng',
//     visible: true,
//     sortable: true,
//     align: ALIGN.CENTER,
//   },
//   {
//     key: 'contract_name',
//     label: 'Tên hợp đồng',
//     visible: true,
//     sortable: true,
//   },
//   {
//     key: 'service',
//     label: 'Dịch vụ',
//     visible: true,
//     sortable: true,
//   },
//   {
//     key: 'contract_type',
//     label: 'Loại hợp đồng',
//     visible: true,
//     sortable: true,
//   },
//   {
//     key: 'effective_date',
//     label: 'Ngày hiệu lực',
//     visible: true,
//     sortable: true,
//     align: ALIGN.CENTER,
//   },
//   {
//     key: 'expiry_date',
//     label: 'Ngày hết hạn',
//     visible: true,
//     sortable: true,
//     align: ALIGN.CENTER,
//   },
//   {
//     key: 'status',
//     label: 'Trạng thái',
//     visible: true,
//     sortable: true,
//     align: ALIGN.CENTER,
//   },
// ];
