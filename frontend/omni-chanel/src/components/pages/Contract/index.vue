<script setup lang="ts">
import { onMounted, ref, reactive, watch, computed, inject } from 'vue';
import * as yup from 'yup';
import { useForm } from 'vee-validate';
import { useRoute, useRouter } from 'vue-router';
import { vOnClickOutside } from '@vueuse/components';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useContractStore } from '@/store/contract';
import FilterCard from '@/components/base/container/FilterCard.vue';
import { ROUTE_NAME } from '@/shared';
import { findByKeyvalue } from '@/utils/object';
import { ContractEntityType, ContractPayloadType } from '@/enums/contract';
import adminEnterpriseConfig from './adminEnterprise.constant';
import adminAgentConfig from './adminAgent.constant';
import agentEnterpriseConfig from './agentEnterprise.constant';
import agentAgentConfig from './agentAgent.constant';
import { AccountType } from '@/enums/common';
import LiquidationPopup from './components/Popup/LiquidationPopup.vue';
import AddAppendixPopup from './detail/components/popup/AddAppendixPopup.vue';
import ContractTable from './components/ContractTable.vue';
import { handleApiError } from '@/utils/useErrorHandler';

export interface ParamsSearch {
  page_index: number;
  page_size: number;
  email?: string;
  keyword?: string;
  searchValue?: string;
  id_common?: number;
  status?: number;
  enterprise_id?: number;
  agent_id?: number;
  service?: number;
  contractTypeId?: number;
}

const props = defineProps({
  accountType: {
    type: Number,
    required: true,
  },
  contractEntityType: {
    type: String,
    required: true,
  },
});

const router = useRouter();
const route = useRoute();
const overlayLoading = useOverLayLoadingStore();
const api = useApi();
const contractStore = useContractStore();
const toast = inject('toast') as any;
const dataConfig = ref({}) as any;
const showFilterCard = ref(false);
const tableRef = ref();
const filterRef = ref<InstanceType<typeof FilterCard>>();
const filterCount = ref('');
const visibleModalDelete = ref(false);
const deleteId = ref();
const visibleLiquidationPopup = ref(false);
const liquidationContract = ref();

const showAppendixPopup = ref(false);
const selectedContractId = ref<number | null>(null);
const params = ref<ParamsSearch>({
  page_index: 1,
  page_size: 20,
});
const dataContract = ref([]) as any;
const contractTypeId = ref<number>();

const filterValues = reactive({});

const dataFilter = computed(() => {
  let baseFilter = [];

  if (dataConfig.value?.dataFilter) {
    baseFilter = dataConfig.value.dataFilter;
  }

  return baseFilter.map((item: any) => ({
    ...item,
    defaultValue: filterValues[item.valueName] || item.defaultValue || '',
  }));
});

const breadcrumbItems = computed(() => {
  if (!dataConfig.value?.breadcrumbConfig) return [];

  return dataConfig.value.breadcrumbConfig.base || [];
});

const { values, handleSubmit } = useForm({
  validationSchema: yup.object({
    org_id: yup.mixed(),
  }),
});

const getList = async () => {
  overlayLoading.toggleLoading(true);

  try {
    const apiUrl = dataConfig.value?.urlApi || '';

    const requestData = {
      page_index: params.value?.page_index,
      page_size: params.value?.page_size,
      businessId: params.value?.enterprise_id,
      agentId: params.value?.agent_id,
      channelId: params.value?.service,
      contractStatusId: params.value?.status,
      contractTypeId: contractTypeId.value,
      searchValue: values.keyword ? values.keyword.trim() : undefined,
    };

    if (!requestData.searchValue && requestData.searchValue !== '') {
      delete requestData.searchValue;
    }
    if (!requestData.businessId) {
      delete requestData.businessId;
    }
    if (!requestData.agentId) {
      delete requestData.agentId;
    }
    if (!requestData.channelId) {
      delete requestData.channelId;
    }
    if (!requestData.contractStatusId) {
      delete requestData.contractStatusId;
    }

    const requestEncoded = new URLSearchParams(requestData);

    const res = await api.get(apiUrl, { params: requestEncoded });
    if (res.data.code === 0) {
      dataContract.value = {
        items: res.data.data.items,
        paging: {
          total_pages: res.data.data.paging?.total_pages,
          total_records: res.data.data.paging?.total_records,
        },
      };

      if (res.data.data?.items?.length === 0 && params.value.page_index !== 1) {
        tableRef.value.onPageChange(1);
        tableRef.value.filterData();
        return;
      }
    } else {
      toast('error', res.data.message);
    }
  } catch (error: any) {
    console.error('getList error:', error);
    handleApiError(error);
  }

  overlayLoading.toggleLoading(false);
};

const getAgents = async () => {
  try {
    const res = await api.get('/business/v1/api/admin/agent/list');
    if (res.data.code === 0) {
      const options = res.data.data.map((item: any) => ({
        label: item.agent_name,
        value: item.id,
      }));
      findByKeyvalue(dataConfig.value.dataFilter, 'agent_id').dropdownConfig.option = options;
    }
  } catch (error) {
    console.error('getAgents error:', error);
  }
};

const getEnterprises = async () => {
  try {
    const res = await api.get('/business/v1/api/admin/business/list', {
      params: { status: 1, customerSource: props.accountType === AccountType.Admin ? 2 : undefined },
    });
    if (res.data.code === 0) {
      const options = res.data.data.map((item: any) => ({
        label: item.business_name,
        value: item.id,
      }));
      findByKeyvalue(dataConfig.value.dataFilter, 'enterprise_id').dropdownConfig.option = options;
    }
  } catch (error) {
    console.error('getEnterprises error:', error);
  }
};

const filterData = (value: any) => {
  showFilterCard.value = false;

  // Lưu filter values để giữ lại khi mở popup
  Object.keys(value).forEach((key) => {
    (filterValues as any)[key] = value[key];
  });

  // Dynamic update params theo valueName từ dataFilter
  dataFilter.value.forEach((filterItem: any) => {
    if (value.hasOwnProperty(filterItem.valueName)) {
      (params.value as any)[filterItem.valueName] = value[filterItem.valueName];
    }
  });

  if (filterRef.value) {
    if (filterRef.value.count && filterRef.value.count > 0) {
      filterCount.value = `(${filterRef.value.count})`;
    } else {
      filterCount.value = '';
    }
  }
  tableRef.value.filterData();
};

const onPageChange = (value: number) => {
  params.value.page_index = value;
  getList();
};

const onPerPageChange = (value: number) => {
  params.value.page_size = value;
};

const handleNavigateToAdd = () => {
  router.push({
    name:
      props.contractEntityType === ContractEntityType.Agent
        ? ROUTE_NAME.CONTRACT_AGENT_ADD
        : ROUTE_NAME.CONTRACT_ENTERPRISE_ADD,
  });
};

const handleNavigateToDetail = (row: any) => {
  contractStore.setListContractPath(route.fullPath);

  router.push({
    name:
      props.contractEntityType === ContractEntityType.Agent
        ? ROUTE_NAME.CONTRACT_AGENT_DETAIL
        : ROUTE_NAME.CONTRACT_ENTERPRISE_DETAIL,
    params: {
      id: row.id,
    },
  });
};

const handleNavigateToUpdate = (row: any) => {
  // Lưu current path để có thể quay lại contract list
  contractStore.setListContractPath(route.fullPath);

  router.push({
    name:
      props.contractEntityType === ContractEntityType.Agent
        ? ROUTE_NAME.CONTRACT_AGENT_UPDATE
        : ROUTE_NAME.CONTRACT_ENTERPRISE_UPDATE,
    params: {
      id: row.id,
    },
  });
};

const handleAddAppendix = (row: any) => {
  selectedContractId.value = row.id;
  showAppendixPopup.value = true;
};

const handleLiquidate = (row: any) => {
  liquidationContract.value = row;
  visibleLiquidationPopup.value = true;
};

const handleLiquidationClose = () => {
  visibleLiquidationPopup.value = false;
  liquidationContract.value = null;
};

const handleLiquidationConfirm = () => {
  visibleLiquidationPopup.value = false;
  liquidationContract.value = null;
  getList();
};

const handleAppendixSuccess = () => {
  showAppendixPopup.value = false;
  selectedContractId.value = null;
  getList();
};

const handleTableAction = ({ actionKey, row }: { actionKey: string; row: any }) => {
  const actions = {
    view: () => handleNavigateToDetail(row),
    update: () => handleNavigateToUpdate(row),
    addAppendix: () => {
      handleAddAppendix(row);
    },
    liquidate: () => {
      handleLiquidate(row);
    },
  };

  const actionFn = actions[actionKey as keyof typeof actions];
  if (actionFn) {
    actionFn();
  } else {
    console.warn('Unknown action:', actionKey);
  }
};

const onSubmit = handleSubmit(() => {
  if (filterRef.value) {
    filterRef.value.onSubmit();
    if (filterRef.value.count && filterRef.value.count > 0) {
      filterCount.value = `(${filterRef.value.count})`;
    } else {
      filterCount.value = '';
    }
    return;
  }
  tableRef.value.filterData();
});

const onClickOutsideHandler = (ev: Event) => {
  const target = ev.target as Element;
  if (target.classList.contains('el-select-dropdown__option-item')) return;
  if (target.closest('.el-select-dropdown__option-item')) return;
  if (target.closest('.el-picker-panel__body')) return;
  if (target.closest('.filter')) return;
  showFilterCard.value = false;
};

onMounted(async () => {
  overlayLoading.toggleLoading(true);

  if (props.accountType === AccountType.Admin) {
    if (props.contractEntityType === ContractEntityType.Agent) {
      contractTypeId.value = ContractPayloadType.AdminAgent;
      dataConfig.value = adminAgentConfig;
      await getAgents();
    } else if (props.contractEntityType === ContractEntityType.Enterprise) {
      contractTypeId.value = ContractPayloadType.AdminEnterprise;
      dataConfig.value = adminEnterpriseConfig;
      await getEnterprises();
    }
  } else if (props.accountType === AccountType.Agent) {
    if (props.contractEntityType === ContractEntityType.Agent) {
      contractTypeId.value = ContractPayloadType.AgentAgent;
      dataConfig.value = agentAgentConfig;
      await getAgents();
    } else if (props.contractEntityType === ContractEntityType.Enterprise) {
      contractTypeId.value = ContractPayloadType.AgentEnterprise;
      dataConfig.value = agentEnterpriseConfig;
      await getEnterprises();
    }
  }

  await getList();
  overlayLoading.toggleLoading(false);
});
</script>

<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:file-text" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          v-for="(item, index) in breadcrumbItems"
          :key="index"
          :to="item.path ? { path: item.path } : undefined"
        >
          {{ item.label }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="flex gap-3 items-center">
      <VElementButton
        v-if="dataConfig.actionButton?.add"
        styleIcon="text-[20px] text-[#fff] mr-[7px]"
        icon="plus"
        styleButton="s"
        label="Thêm hợp đồng"
        :bgColor="color.main"
        @click="handleNavigateToAdd"
      />
    </div>
  </div>
  <div v-if="dataConfig.searchConfig" class="mx-[10px] mt-[20px]">
    <div class="relative filter-container">
      <div class="flex gap-[10px]">
        <VElementButton
          styleIcon="text-[20px] text-[#fff] mr-[7px]"
          styleButton="s"
          icon="filter-plus"
          class="filter"
          :bgColor="color.tertiary"
          :label="`Bộ lọc ${filterCount}`"
          @click="showFilterCard = !showFilterCard"
        />
        <VElementInput
          size="default"
          name="keyword"
          prefix="search"
          :placeholder="dataConfig.searchConfig?.placeholder"
          :style="'!w-[480px]'"
          @keyup.enter="onSubmit"
        />
        <VElementButton :bgColor="color.main" label="Tìm kiếm" styleButton="s" @click="onSubmit" />
        <Transition>
          <FilterCard
            v-if="showFilterCard"
            ref="filterRef"
            v-on-click-outside="onClickOutsideHandler"
            filterHeight="h-[160px]"
            :dataFilter="dataFilter"
            @filter="filterData"
          />
        </Transition>
      </div>
    </div>
    <ContractTable
      ref="tableRef"
      :dataContract="dataContract"
      :dataConfig="dataConfig"
      :params="params"
      @pageChanged="onPageChange"
      @perPageChange="onPerPageChange"
      @rowClick="handleNavigateToDetail"
      @action="handleTableAction"
    />
  </div>
  <VDialog
    ref="refDialog"
    v-model:visible="visibleModalDelete"
    :draggable="false"
    :pt="{
      content: { class: 'mb-[60px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: '!pl-[230px] !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
      },
    }"
    modal
    header="Xác nhận
    "
    :style="{
      width: '550px',
      height: '285px',
      backgroundColor: '#fff',
      maxHeight: '100%',
    }"
  >
    <PopupDelete @closeForm="visibleModalDelete = false" @getList="getList" :id="deleteId">
    </PopupDelete>
  </VDialog>

  <LiquidationPopup
    v-model:visible="visibleLiquidationPopup"
    :id="liquidationContract?.id"
    :contract="liquidationContract"
    @onClose="handleLiquidationClose"
    @onConfirm="handleLiquidationConfirm"
  />

  <AddAppendixPopup
    v-if="showAppendixPopup"
    v-model:visible="showAppendixPopup"
    :contractId="selectedContractId"
    type="add"
    @success="handleAppendixSuccess"
  />
</template>

<style lang="scss" scoped></style>
