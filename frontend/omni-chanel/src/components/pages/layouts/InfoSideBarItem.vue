<template>
  <li v-for="(i, index) in item.items" :key="index + i" :id="i.id">
    <a
      href="#"
      tabindex="0"
      class="nav-item"
      :class="{
        'item-container-select': checkRoute(i),
      }"
      @click="handleClickRoute(i)"
    >
      <div class="flex items-center gap-x-3 justify-start ml-1">
        <div class="w-[25px] flex" :class="i.icon === 'layout-grid' ? 'rotate-45' : ''">
          <Icon
            class="text-[25px] font-medium"
            :icon="`tabler:${i.icon}`"
            :class="{
              'text-main': checkRoute(i),
              'text-[#292d32]': !checkRoute(i),
            }"
          />
        </div>
        <span
          class="font-medium text-base truncate"
          :class="checkRoute(i) ? 'text-main' : 'text-[#292d32]'"
          >{{ i.label }}</span
        >
      </div>
    </a>
  </li>
</template>

<script setup lang="ts">
import { ROUTE_NAME } from '@/shared';
import { ROUTE_ID, ROUTE_SHORT_PATH } from '@/shared/sidebar.shared';
import { computed } from 'vue';
import { useRouter } from 'vue-router';

defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
  navChild: {
    type: Boolean,
    default: false,
  },
});

const router = useRouter();
const getCurrentPath = computed(() => router.currentRoute.value.path);

const checkRoute = function (i: any) {
  // Check cho menu "Thông tin đại lý" - active khi ở route chính và không có tab query
  if (i.id === ROUTE_ID.INFOR_SIDEBAR &&
      getCurrentPath.value.includes(ROUTE_SHORT_PATH.USER_INFO) &&
      !router.currentRoute.value.query.tab) {
    return true;
  }
  // Check cho menu "Hợp đồng" - active khi có tab=contracts
  if (i.id === 'menu-hop-dong-dai-ly-info' &&
      getCurrentPath.value.includes(ROUTE_SHORT_PATH.USER_INFO) &&
      router.currentRoute.value.query.tab === 'contracts') {
    return true;
  }
  // Check exact route match
  if (i.to && getCurrentPath.value === i.to && !i.tab) {
    return true;
  }
  return false;
};

const handleClickRoute = (i: any) => {
  if (i.id === ROUTE_ID.INFOR_SIDEBAR) {
    router.push({ name: ROUTE_NAME.USER_INFO_AGENT });
    return;
  }
  if (i.id === 'menu-hop-dong-dai-ly-info') {
    router.push({
      name: ROUTE_NAME.USER_INFO_AGENT,
      query: { tab: 'contracts' }
    });
    return;
  }
  if (i.to) {
    if (i.tab) {
      router.push({ path: i.to, query: { tab: i.tab } });
    } else {
      router.push({ path: i.to });
    }
  }
};
</script>

<style lang="scss" scoped>
.nav-item {
  height: 45px;
  display: flex;
  align-items: center;
  li {
    list-style-type: none;
  }
  &:hover .pi-sidebar {
    color: var(--main-color);
  }
}
a {
  padding: 6px 8px 6px 0;
  text-decoration: none;
  font-size: 16px;
  color: #54667a;
  display: flex;
  align-items: center;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  outline: none;
  justify-content: space-between;
  &:hover,
  &.active {
    color: var(--main-color);
    border-radius: 3px;
  }
}
.item-container-select {
  border-radius: 3px !important;
}
.main {
  margin-left: 200px;
  font-size: 20px;
  padding: 0 10px;
}
@media screen and (max-height: 450px) {
  .sidenav {
    padding-top: 15px;
  }
  .sidenav a {
    font-size: 18px;
  }
}
:deep(g) {
  stroke-width: 1.5px !important;
}
:deep(path) {
  stroke-width: 1.5px !important;
}
</style>
