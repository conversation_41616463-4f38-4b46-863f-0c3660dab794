export enum ContractEntityType {
  Enterprise = 'enterprise',
  Agent = 'agent',
}

export enum ContractStatusType {
  Active = 1,
  Expired = 2,
  Liquidated = 3,
}

export enum ContractStatusTypeLabel {
  Active = 'Hoạt động',
  Expired = 'Hết hiệu lực',
  Liquidated = '<PERSON><PERSON> lý',
}

export enum ContractDurationType {
  Limited = 1,
  Unlimited = 0,
}

export enum ContractDurationLabel {
  Limited = 'Có thời hạn',
  Unlimited = 'Vô thời hạn',
}

export enum ContractActionsType {
  VIEW = 'view',
  UPDATE = 'update',
  ADD_APPENDIX = 'addAppendix',
  LIQUIDATE = 'liquidate',
}

export enum ContractAppendixActionsType {
  VIEW = 'view',
  DELETE = 'delete',
}

// Payload create contract
export enum ContractPayloadType {
  AdminEnterprise = 1,
  AdminAgent = 2,
  AgentEnterprise = 3,
  AgentAgent = 4,
}

export enum ContractListPayloadType {
  AgentAgent = 2,
}