// For feature onboarding
export enum EnterpriseCategoryType {
  Approved = 1,
  Pending = 2,
}

export enum PendingEnterpriseStatus {
  Pending = 2,
  Reject = 3,
}

export enum PendingEnterpriseStatusLabel {
  Reject = 'Từ chối',
  Pending = 'Ch<PERSON> duyệt',
}

export enum PendingEnterprisePaymentMethod {
  Prepaid = 1,
  Postpaid = 2,
}

export enum PendingEnterprisePaymentMethodLabel {
  Prepaid = 'Trả trước',
  Postpaid = 'Trả sau',
}

export enum PendingCustomerSourceType {
  Agent = 1,
  WalkIn = 2,
}

export enum PendingCustomerSourceTypeLabel {
  Agent = "Đại lý",
  WalkIn = "Vãng lai",
}