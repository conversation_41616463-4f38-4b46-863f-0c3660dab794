<template>
  <div
    class="sidenav shadow px-[16px]"
    :class="sideBar.isShowSideBar ? 'slideInLeft' : 'slideInRight'"
  >
    <div class="scroll-sidebar" style="width: auto; height: 100%">
      <div class="overflow-y-auto overflow-x-hidden h-full">
        <div class="flex items-center gap-x-[10px] mb-[10px]">
          <img
            class="cursor-pointer size-[60px] select-none"
            src="@/assets/icons/Avatar.svg"
            alt="avatar"
          />
          <div class="flex items-end">
            <div
              class="text-[#292d32] text-[18px] font-medium truncate overflow-hidden whitespace-nowrap max-w-[180px]"
              :title="user.fullName || user.sub"
            >
              {{ user.fullName || user.sub }}
            </div>
          </div>
        </div>

        <!-- Nút Menu chính -->
        <div class="mb-[10px]">
          <div
            class="cursor-pointer h-[45px] flex items-center gap-[14px] rounded-md transition-colors px-[8px]"
            @click="handleBackToMainMenu"
          >
            <div class="w-[25px]">
              <Icon icon="tabler:arrow-left" class="text-[25px]" />
            </div>
            <Transition name="fade">
              <div v-show="sideBar.isShowSideBar" class="truncate font-medium text-base">
                Menu chính
              </div>
            </Transition>
          </div>
        </div>

        <ul class="space-y-[2px]">
          <template v-for="(item, i) in mainSidebar" :key="item + i">
            <InfoSideBarItem :item="item" />
          </template>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useSideBar } from '@/store/sidebar';
import { useUserSession, type UserData } from '@/store/userSession';
import InfoSideBarItem from '@/components/pages/layouts/InfoSideBarItem.vue';
import { ROUTE_ID } from '@/shared/sidebar.shared';
import { ROUTE_PATH } from '@/shared';
import { AccountType } from '@/enums/common';

const router = useRouter();
const user = useUserSession().user as UserData;
const sideBar = useSideBar();

const mainSidebar = ref([
  {
    items: [
      {
        label: 'Thông tin đại lý',
        icon: 'building-skyscraper',
        id: ROUTE_ID.INFOR_SIDEBAR,
        to: ROUTE_PATH.USER_INFO_AGENT,
      },
    ],
  },
]);

const handleBackToMainMenu = () => {
  sideBar.setIsShowInforSideBar(false);

  const accountType = Number(localStorage.getItem('accountType'));

  if (accountType === AccountType.Admin) {
    router.push({ path: ROUTE_PATH.SMS_BRANDNAME });
  } else if (accountType === AccountType.Agent) {
    router.push({ path: ROUTE_PATH.CONTRACT_ENTERPRISE });
  } else {
    router.push({ path: '/' });
  }
};
</script>

<style lang="scss" scoped>
.sidenav {
  background-color: #f7f9fd;
  overflow-x: hidden;
  position: absolute;
  animation: width 0.3s;
  height: 100%;
  top: 0px;
  padding-top: 93px;

  .scroll-sidebar {
    position: relative;
    overflow-y: hidden;
    width: auto;
    height: 100%;
    padding-bottom: 60px;
  }
}

:deep(g) {
  stroke-width: 1.5px !important;
}

:deep(path) {
  stroke-width: 1.5px !important;
}

.arrow-tranform-right {
  transform: rotate(-180deg);
  transition: transform 0.2s ease;
}

.arrow-tranform-left {
  transform: rotate(0deg);
  transition: transform 0.1s ease;
}

@keyframes slideInLeft {
  from {
    width: 80px;
  }

  to {
    width: 280px;
  }
}

@keyframes slideInRight {
  from {
    width: 280px;
  }

  to {
    width: 80px;
  }
}

.slideInLeft {
  width: 280px;
  animation: slideInLeft 0.1s ease-in;
}

.slideInRight {
  width: 80px;
  animation: slideInRight 0.1s ease-in;
}
</style>
