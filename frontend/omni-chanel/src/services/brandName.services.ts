import { useApi } from '@/store/useApi';

export const brandNameClientServices = () => {
  const api = useApi();

  return {
    getList() {
      return api.post('business/v1/api/client/brandname/list');
    },
    getDetail(id: number) {
      return api.get(`business/v1/api/client/brandname/${id}`);
    },
    create(data: any) {
      return api.post('business/v1/api/client/brandname/create', data);
    },
    update(id: number, data: any) {
      return api.put(`business/v1/api/client/brandname/update/${id}`, data);
    },
    delete(id: number) {
      return api.delete(`business/v1/api/client/brandname/${id}`);
    },
  };
};
