import { useApi } from '@/store/useApi';

export const templateSmsServices = () => {
  const api = useApi();
  return {
    getList(data: any) {
      return api.post('business/v1/api/client/sms-template/search', data);
    },
    create(data: any) {
      return api.post('business/v1/api/client/sms-template/create', data);
    },
    update(id: number, data: any) {
      return api.put(`business/v1/api/client/sms-template/update/${id}`, data);
    },
    delete(id: number) {
      return api.delete(`business/v1/api/client/sms-template/${id}`);
    },
    updateStatus(id: number, data: any) {
      return api.put(`business/v1/api/admin/sms-template/update-status/${id}`, data);
    },
  };
};
