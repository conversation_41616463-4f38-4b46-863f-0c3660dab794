import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useContractStore = defineStore('contract', () => {
  const listContractPath = ref<string | null>(null);
  const activeTab = ref<string | number | null>(null);
  const preSelectedId = ref<number | null>(null);

  const setListContractPath = (path: string, tab?: string | number) => {
    listContractPath.value = path;
    activeTab.value = tab || null;
  };

  const setPreSelectedId = (id: number) => {
    preSelectedId.value = id;
  };

  const clearPreSelectedId = () => {
    preSelectedId.value = null;
  };

  const clearListContractPath = () => {
    listContractPath.value = null;
    activeTab.value = null;
  };

  const getListContractPath = () => {
    return listContractPath.value;
  };

  const getActiveTab = () => {
    return activeTab.value;
  };

  const getPreSelectedId = () => {
    return preSelectedId.value;
  };

  return {
    listContractPath,
    activeTab,
    preSelectedId,
    setListContractPath,
    setPreSelectedId,
    clearPreSelectedId,
    clearListContractPath,
    getListContractPath,
    getActiveTab,
    getPreSelectedId,
  };
});
