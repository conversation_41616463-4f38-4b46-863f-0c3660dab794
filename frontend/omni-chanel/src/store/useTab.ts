import { TabValue } from '@/shared';
import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useTab = defineStore('tab', () => {
  const costAndListPriceTab = ref<TabValue>(
    localStorage.getItem('costAndListPriceTab') !== null
      ? +(localStorage.getItem('costAndListPriceTab') as string)
      : TabValue.Tab1,
  );
  const agentActiveTab = ref<TabValue>(
    localStorage.getItem('agentActiveTab') !== null
      ? +(localStorage.getItem('agentActiveTab') as string)
      : TabValue.Tab1,
  );
  const enterpriseActiveTab = ref(
    localStorage.getItem('enterpriseActiveTab') !== null
      ? +(localStorage.getItem('enterpriseActiveTab') as string)
      : TabValue.Tab1,
  );
  const childAgentActiveTab = ref<TabValue>(
    localStorage.getItem('childAgentActiveTab') !== null
      ? +(localStorage.getItem('childAgentActiveTab') as string)
      : TabValue.Tab1,
  );
  const childEnterpriseActiveTab = ref<TabValue>(
    localStorage.getItem('childEnterpriseActiveTab') !== null
      ? +(localStorage.getItem('childEnterpriseActiveTab') as string)
      : TabValue.Tab1,
  );

  const setCostAndListPriceTab = (tab: TabValue) => {
    costAndListPriceTab.value = tab;
    localStorage.setItem('costAndListPriceTab', tab.toString());
  };

  const setChildAgentActiveTab = (tab: TabValue) => {
    childAgentActiveTab.value = tab;
    localStorage.setItem('childAgentActiveTab', tab.toString());
  };

  const setChildEnterpriseActiveTab = (tab: TabValue) => {
    childEnterpriseActiveTab.value = tab;
    localStorage.setItem('childEnterpriseActiveTab', tab.toString());
  };

  const setAgentActiveTab = (tab: TabValue) => {
    agentActiveTab.value = tab;
    localStorage.setItem('agentActiveTab', tab.toString());
  };

  const setEnterpriseActiveTab = (tab: TabValue) => {
    enterpriseActiveTab.value = tab;
    localStorage.setItem('enterpriseActiveTab', tab.toString());
  };

  const clearCostAndListPriceTab = () => {
    costAndListPriceTab.value = TabValue.Tab1;
    localStorage.removeItem('costAndListPriceTab');
  };

  const clear = () => {
    agentActiveTab.value = TabValue.Tab1;
    enterpriseActiveTab.value = TabValue.Tab1;
    childAgentActiveTab.value = TabValue.Tab1;
    childEnterpriseActiveTab.value = TabValue.Tab1;
    costAndListPriceTab.value = TabValue.Tab1;
    localStorage.removeItem('childAgentActiveTab');
    localStorage.removeItem('childEnterpriseActiveTab');
    localStorage.removeItem('agentActiveTab');
    localStorage.removeItem('enterpriseActiveTab');
    localStorage.removeItem('costAndListPriceTab');
  };

  return {
    agentActiveTab,
    enterpriseActiveTab,
    childAgentActiveTab,
    childEnterpriseActiveTab,
    costAndListPriceTab,
    setCostAndListPriceTab,
    setChildAgentActiveTab,
    setChildEnterpriseActiveTab,
    setAgentActiveTab,
    setEnterpriseActiveTab,
    clear,
    clearCostAndListPriceTab,
  };
});
