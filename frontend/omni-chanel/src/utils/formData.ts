export const appendObjectToFormData = (formData: FormData, obj: any, parentKey?: string) => {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      const formKey = parentKey ? `${parentKey}[${key}]` : key;

      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        appendObjectToFormData(formData, value, formKey);
      } else {
        formData.append(formKey, value);
      }
    }
  }
};

export const viewFormData = (formData: FormData) => {
  formData.forEach((value, key) => {
    console.log(`Key: ${key}, Value: ${value}`);
  });
};
