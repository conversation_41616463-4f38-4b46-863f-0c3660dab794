/**
 * Utility functions for fuzzy search
 */

/**
 * Remove Vietnamese diacritics from a string
 * @param str - Input string
 * @returns String without diacritics
 */
export function removeDiacritics(str: string): string {
  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/đ/g, 'd')
    .replace(/Đ/g, 'D');
}

/**
 * Fuzzy search function that matches partial strings
 * @param searchTerm - The search term
 * @param targetString - The string to search in
 * @returns Boolean indicating if there's a match
 */
export function fuzzyMatch(searchTerm: string, targetString: string): boolean {
  if (!searchTerm || !targetString) return true;

  const normalizedSearch = removeDiacritics(searchTerm.toLowerCase());
  const normalizedTarget = removeDiacritics(targetString.toLowerCase());

  // Split search term into individual characters
  const searchChars = normalizedSearch.split('');
  let targetIndex = 0;

  // Check if all characters in search term can be found in order in target string
  for (const char of searchChars) {
    const foundIndex = normalizedTarget.indexOf(char, targetIndex);
    if (foundIndex === -1) {
      return false;
    }
    targetIndex = foundIndex + 1;
  }

  return true;
}

/**
 * Enhanced search that works like SQL LIKE '%searchTerm%' but case-insensitive and diacritic-insensitive
 * @param searchTerm - The search term
 * @param targetString - The string to search in
 * @returns Boolean indicating if there's a match
 */
export function enhancedFuzzyMatch(searchTerm: string, targetString: string): boolean {
  if (!searchTerm || !targetString) return true;

  const normalizedSearch = removeDiacritics(searchTerm.toLowerCase());
  const normalizedTarget = removeDiacritics(targetString.toLowerCase());

  // Simple substring match - like SQL LIKE '%searchTerm%'
  // Case-insensitive and diacritic-insensitive
  return normalizedTarget.includes(normalizedSearch);
}

/**
 * Filter function for dropdown options with fuzzy search
 * @param options - Array of dropdown options
 * @param searchTerm - The search term
 * @param labelKey - The key to search in (default: 'label')
 * @returns Filtered array of options
 */
export function fuzzyFilterOptions<T extends Record<string, any>>(
  options: T[],
  searchTerm: string,
  labelKey: string = 'label',
): T[] {
  if (!searchTerm) return options;

  return options.filter((option) => enhancedFuzzyMatch(searchTerm, option[labelKey] || ''));
}
