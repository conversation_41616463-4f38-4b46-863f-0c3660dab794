const normalizeValue = (value: any) => {
  if (value === null || value === undefined || value === '') {
    return null;
  }
  return value;
};

export const normalizeFormData = (data: any) => {
  const normalized: any = {};
  for (const [key, value] of Object.entries(data)) {
    normalized[key] = normalizeValue(value);
  }
  return normalized;
};

export function isEmptyObject(obj: any, excludes: string[]) {
  for (const prop in obj) {
    if (
      !excludes.includes(prop) &&
      obj[prop] !== undefined &&
      obj[prop] !== null &&
      obj[prop] !== ''
    ) {
      return false;
    }
  }
  return true;
}

export function areEqualObjects(obj1: any, obj2: any, excludes: string[]) {
  if (obj1 === null && obj2 === null) return true;
  if (obj1 === undefined && obj2 === undefined) return true;

  if (obj1 === null || obj1 === undefined || obj2 === null || obj2 === undefined) return false;

  const keys1 = Object.keys(obj1);

  for (const key of keys1) {
    if (!excludes.includes(key) && Object.prototype.toString.call(obj1[key]) == '[object Array]') {
      const theLength =
        obj1[key]?.length > obj2[key]?.length ? obj1[key]?.length : obj2[key]?.length;
      for (let i = 0; i < theLength; i++) {
        if (JSON.stringify(obj1[key][i]) !== JSON.stringify(obj2[key][i])) {
          return false;
        }
      }
    } else {
      if (!excludes.includes(key) && obj1[key] != obj2[key]) {
        return false;
      }
    }
  }

  return true;
}

export function areEqualArraysOfObjects(arr1: any, arr2: any, excludes: string[]) {
  if (arr1 === null && arr2 === null) return true;
  if (arr1 === undefined && arr2 === undefined) return true;

  if (arr1 === null || arr1 === undefined || arr2 === null || arr2 === undefined) return false;

  if (arr1.length !== arr2.length) return false;

  for (let i = 0; i < arr1.length; i++) {
    if (!areEqualObjects(arr1[i], arr2[i], excludes)) {
      return false;
    }
  }

  return true;
}

export function areEqualArrays(arr1: any, arr2: any) {
  if (arr1 === null && arr2 === null) return true;
  if (arr1 === undefined && arr2 === undefined) return true;

  if (arr1 === null || arr1 === undefined || arr2 === null || arr2 === undefined) return false;

  if (arr1.length !== arr2.length) return false;

  for (let i = 0; i < arr1.length; i++) {
    if (arr1[i] != arr2[i]) {
      return false;
    }
  }

  return true;
}

export const isObject = (x: any) =>
  typeof x === 'object' && !Array.isArray(x) && x !== null && x !== undefined;

export function compareSpecialFields(obj1: any, obj2: any, field: string) {
  if (!obj1 && !obj2) return true;
  if (!obj1 || !obj2) return false;

  const value1 = obj1[field];
  const value2 = obj2[field];

  if (value1 === value2) return true;

  // Check if either value exists but they're different
  return !(value1 || value2);
}

export function compareSpecialContents(oldData: any, newData: any) {
  // Compare bank_content
  const bankContentChanged =
    !areEqualObjects(oldData?.bank_content, newData?.bank_content, ['content']) ||
    !compareSpecialFields(oldData?.bank_content, newData?.bank_content, 'content');

  // Compare voucher_content
  const voucherContentChanged =
    !areEqualObjects(oldData?.voucher_content, newData?.voucher_content, ['start_date']) ||
    !compareSpecialFields(oldData?.voucher_content, newData?.voucher_content, 'start_date');

  return bankContentChanged || voucherContentChanged;
}

// Data filter
export const findByKeyvalue = (arr: any[], key: string) => {
  return arr.find((i) => i.valueName === key);
};
