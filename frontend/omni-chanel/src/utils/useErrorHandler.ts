import { toast } from '@/utils/useToast';
import { TEXT } from '@/shared';

interface ApiError {
  response?: {
    status?: number;
    data?: {
      message?: string;
    };
  };
  message?: string;
}

export const handleApiError = (error: ApiError) => {
  if (!navigator.onLine) {
    toast('error', TEXT.ERROR_OCCURRED);
    return;
  }

  const status = error?.response?.status;

  // Let useApi handle these status codes
  if ([401, 403, 422, 404].includes(status)) {
    throw error;
  }

  // Handle other errors
  const errorMessage = error?.response?.data?.message || error?.message || 'Đã có lỗi xảy ra. Vui lòng thử lại!';
  toast('error', errorMessage);
};

export const isApiError = (error: any): error is ApiError => {
  return error && typeof error === 'object' && 'response' in error;
};

export const getErrorMessage = (error: ApiError): string => {
  if (!navigator.onLine) {
    return TEXT.ERROR_OCCURRED;
  }

  const status = error?.response?.status;
  if (status === 404) {
    return 'Không tìm thấy dữ liệu';
  }
  if (status === 401) {
    return 'Phiên đăng nhập đã hết hạn';
  }
  if (status === 403) {
    return 'Bạn không có quyền thực hiện thao tác này';
  }
  if (status === 422) {
    return 'Dữ liệu không hợp lệ';
  }

  return error?.response?.data?.message || error?.message || 'Đã có lỗi xảy ra. Vui lòng thử lại!';
};