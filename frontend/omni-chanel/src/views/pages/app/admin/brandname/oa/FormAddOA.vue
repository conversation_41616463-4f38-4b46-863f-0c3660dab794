<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <ZnsIcon />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item @click="handleClickCancel" to="#">ZNS OA</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/customer/zns-oa/them' }">
          {{
            props.type === FORM_TYPE.ADD
              ? 'Thêm mới OA'
              : props.type === FORM_TYPE.UPDATE
                ? 'Cập nhật OA'
                : 'Chi tiết OA'
          }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
  <div class="mx-[10px] mt-[20px] flex justify-center view-height mb-[50px] overflow-auto">
    <el-form label-position="top" class="w-[70%] flex-col flex pb-[52px] gap-[15px]">
      <div class="flex flex-col gap-[15px] w-full">
        <div class="text-[16px] flex justify-center text-black mb-3">
          <div class="w-[60%] text-center">
            Vui lòng cung cấp thông tin chính xác để việc sử dụng dịch vụ không bị gián đoạn
          </div>
        </div>
        <VElementInput
          required
          id="oa-id"
          size="default"
          name="oa_id"
          :label="'OA ID'"
          :maxlength="25"
          :showLimit="false"
          :placeholder="PLACEHOLDER.TYPE"
          :disabled="props.type === FORM_TYPE.DETAILS"
        />
        <VElementInput
          required
          id="oa-name"
          size="default"
          name="oa_name"
          :maxlength="40"
          :showLimit="false"
          :label="'OA'"
          :placeholder="PLACEHOLDER.TYPE"
          :disabled="props.type === FORM_TYPE.DETAILS"
        />
        <VElementDropdown
          required
          id="label-type-oa"
          name="label_type_oa"
          :filterable="false"
          :label="'Lĩnh vực'"
          :option="listLabelTypeOA"
          :style="'w-[100%]'"
          :disabled="props.type === FORM_TYPE.DETAILS"
          :placeholder="PLACEHOLDER.SELECT"
        />
        <div>
          <el-form-item label="Hình thức giao dịch gói duy trì OA">
            <el-radio-group
              v-model="type_pay"
              class="flex flex-col justify-start items-start"
              disabled
            >
              <el-radio class="w-[100%] !mr-0" :label="LIST_TYPE_PAY[0].value" size="large">
                {{ LIST_TYPE_PAY[0].label }}
              </el-radio>
              <el-radio class="w-[100%]" :label="LIST_TYPE_PAY[1].value" size="large">
                {{ LIST_TYPE_PAY[1].label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <VElementDropdown
          v-show="type_pay === LIST_TYPE_PAY[0].value"
          required
          id="oa-package"
          name="oa_package"
          :filterable="false"
          :label="'Gói OA'"
          :option="listOAPackage"
          :style="'w-[100%]'"
          disabled
          :placeholder="PLACEHOLDER.SELECT"
        />
        <VElementDateTimePicker
          v-show="
            (type === FORM_TYPE.ADD && type_pay === LIST_TYPE_PAY[0].value) ||
            (type !== FORM_TYPE.ADD &&
              (status === OA_STATUS_VALUE.ACTIVE || status === OA_STATUS_VALUE.INACTIVE))
          "
          disabled
          required
          id="expired-package-at"
          size="default"
          name="expired_package_at"
          type="date"
          format="DD/MM/YYYY"
          :label="'Thời gian hết hạn'"
          :style="'!w-[100%]'"
          :disabledDates="disabledDatesStart"
          :placeholder="PLACEHOLDER.SELECT"
        />
        <VElementDropdown
          v-show="type_pay === LIST_TYPE_PAY[1].value"
          required
          id="package_vnpt_id"
          name="package_vnpt_id"
          :filterable="false"
          :label="'Gói OA muốn mua'"
          :option="OA_PACKAGE_OPTIONS_CONNECT"
          :style="'w-[100%]'"
          disabled
          :placeholder="PLACEHOLDER.SELECT"
        />
        <VElementInput
          id="tenant-name"
          disabled
          size="default"
          name="tenant_name"
          :label="'Tên doanh nghiệp'"
          :placeholder="PLACEHOLDER.TYPE"
        />
        <div v-if="props.type !== FORM_TYPE.ADD">
          <div class="font-semibold text-[#6B7280] text-[14px]">Trạng thái</div>
          <div class="flex gap-3 items-start mt-2">
            <Tag :class="getOAColorStatus(status)" :value="getOALabelStatus(status)" />
            <el-tooltip
              v-if="status === OA_STATUS_VALUE.REJECT || status === OA_STATUS_VALUE.INACTIVE"
              class="box-item"
              effect="dark"
              placement="top-start"
              :content="reason || ''"
              :raw-content="true"
            >
              <InfoCircle class="cursor-pointer" />
            </el-tooltip>
          </div>
        </div>
      </div>
    </el-form>
  </div>
  <div
    class="flex z-[999] py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke items-center"
    :class="type === FORM_TYPE.DETAILS ? 'justify-between' : 'justify-end'"
  >
    <VElementButton
      v-if="type === FORM_TYPE.DETAILS"
      id="back-btn"
      styleButton="s"
      label="Quay lại"
      :bgColor="color.closeButton"
      @click="handleClickCancel"
    />
    <div>
      <VElementButton
        v-if="type !== FORM_TYPE.DETAILS"
        id="close-btn"
        styleButton="s"
        label="Huỷ"
        :bgColor="color.closeButton"
        @click="handleClickCancel"
      />
      <VElementButton
        v-if="
          type === FORM_TYPE.DETAILS &&
          status !== OA_STATUS_VALUE.ACTIVE &&
          status !== OA_STATUS_VALUE.CONNECTING
        "
        id="delete-btn"
        styleButton="s"
        label="Xoá"
        :bgColor="color.secondary"
        @click="deleteItem"
      />
      <VElementButton
        v-if="type === FORM_TYPE.DETAILS && status === OA_STATUS_VALUE.ACTIVE"
        id="pause-btn"
        styleButton="s"
        label="Tạm dừng"
        :bgColor="color.secondary"
        @click="pauseItem"
      />
      <VElementButton
        v-if="type === FORM_TYPE.DETAILS && status === OA_STATUS_VALUE.CONNECTING"
        id="reject-btn"
        styleButton="s"
        label="Từ chối"
        :bgColor="color.secondary"
        @click="
          () => {
            rejectItem(false);
          }
        "
      />
      <VElementButton
        v-if="type === FORM_TYPE.DETAILS && status === OA_STATUS_VALUE.CONNECTING"
        id="reject-refund-btn"
        styleButton="s"
        label="Từ chối và hoàn tiền"
        :bgColor="color.secondary"
        @click="
          () => {
            rejectItem(true);
          }
        "
      />
      <VElementButton
        v-if="
          type === FORM_TYPE.DETAILS &&
          (status === OA_STATUS_VALUE.REJECT || status === OA_STATUS_VALUE.CONNECTING)
        "
        id="update-btn"
        styleButton="s"
        :bgColor="color.main"
        label="Cập nhật"
        @click="redirectUpdate"
      />
      <VElementButton
        v-if="type !== FORM_TYPE.DETAILS"
        id="submit-btn"
        styleButton="s"
        :bgColor="color.main"
        :label="'Lưu'"
        @click="onSubmit"
      />
      <VElementButton
        v-if="
          type === FORM_TYPE.DETAILS &&
          (status === OA_STATUS_VALUE.CONNECTING || status === OA_STATUS_VALUE.INACTIVE)
        "
        id="connect-btn"
        styleButton="s"
        :bgColor="color.tertiary"
        :label="status === OA_STATUS_VALUE.CONNECTING ? 'Xác nhận' : 'Kết nối'"
        @click="connectOA"
      />
    </div>
  </div>
  <PopupCancelConfirm
    v-model:popupVisible="isShowConfirmPopup"
    @onClose="isShowConfirmPopup = false"
    @onConfirm="backToZnsOA"
  />
  <Dialog
    modal
    ref="refDialog2"
    header="Xác nhận"
    v-model:visible="visibleModalDelete"
    :draggable="false"
    :pt="{
      content: { class: 'mb-[60px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: '!pl-[230px] !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
      },
    }"
    :style="{
      width: '550px',
      height: 'fit-content',
      backgroundColor: '#fff',
      maxHeight: '100%',
    }"
  >
    <PopupDelete
      v-if="typePopup === 'delete'"
      :id="deleteId"
      :name="deleteName"
      @onClose="visibleModalDelete = false"
      @onConfirm="handleDelete"
    />
    <PopupPause
      v-if="typePopup === 'pause'"
      :id="deleteId"
      @onClose="visibleModalDelete = false"
      @onConfirm="getDetail"
    />
    <PopupReject
      v-if="typePopup === 'reject'"
      :id="deleteId"
      :is_refund="isRefund"
      @onClose="visibleModalDelete = false"
      @onConfirm="getDetail"
    />
  </Dialog>
  <Dialog
    ref="refDialog"
    modal
    v-model:visible="isVisibleConnectPopup"
    :draggable="false"
    :closable="true"
    :pt="{
      content: { class: 'mt-[10px] mb-[50px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: 'flex !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
        style: {
          justifyContent: 'center',
        },
      },
    }"
    :header="`Xác nhận kết nối`"
    :style="{
      width: '600px',
      height: '350px',
      backgroundColor: '#fff',
      maxHeight: '100%',
    }"
  >
    <PopupConnect
      v-if="isVisibleConnectPopup"
      :id="oaId"
      :package_vnpt_id="values.package_vnpt_id"
      @onClose="isVisibleConnectPopup = false"
      @onConfirm="getDetail"
    />
  </Dialog>
</template>

<script setup lang="ts">
import { onMounted, ref, inject, watch } from 'vue';
import * as yup from 'yup';
import { useForm } from 'vee-validate';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useUserSession } from '@/store/userSession';
import { FORM_TYPE, isDateBefore, PLACEHOLDER, REGEX } from '@/shared';
import { timestampGetter } from '@/utils';
import { ROUTE_NAME } from '@/shared/route.shared';
import { useRouter } from 'vue-router';
import PopupCancelConfirm from '@/components/base/common/PopupCancelConfirm.vue';
import { LIST_TYPE_PAY, OA_PACKAGE_OPTIONS_CONNECT } from './index.constant';
import InfoCircle from '@/components/icon/InfoCircle.vue';
import { OA_STATUS_VALUE } from '@/shared/dropdown.shared';
import { getOAColorStatus, getOALabelStatus } from '@/utils/getter.utils';
import PopupPause from './popup/PopupPause.vue';
import PopupDelete from '@/components/base/common/PopupDelete.vue';
import PopupReject from './popup/PopupReject.vue';
import Dialog from 'primevue/dialog';
import ZnsIcon from '@/components/icon/ZnsIcon.vue';
import PopupConnect from './popup/popupConnect.vue';

interface IParam {
  id?: number;
  oa_id: number;
  oa_name: string;
  package_id: number;
  expired_at?: number;
  user_id?: number;
  type?: number;
  label_type_oa?: number;
}

const props = defineProps<{
  id: number;
  type?: string;
}>();

const api = useApi();
const toast = inject('toast') as any;
const user = useUserSession() as any;
const overlayLoading = useOverLayLoadingStore();
const isShowConfirmPopup = ref(false);
const router = useRouter();
const listLabelTypeOA = ref<any[]>([]);
const listOAPackage = ref([
  { value: 'maintainAdvanceOA', label: 'Nâng cao' },
  { value: 'maintainPremiumOA', label: 'Premium' },
]);
const type_pay = ref(1);
const status = ref(OA_STATUS_VALUE.ACTIVE);
const reason = ref('');
const visibleModalDelete = ref(false);
const deleteId = ref();
const deleteName = ref();
const typePopup = ref();
const isVisibleConnectPopup = ref(false);
const oaId = ref();
const isRefund = ref(false);
// Store original values for comparison in UPDATE mode
const originalValues = ref<any>({});

const validationSchema = yup.object({
  oa_id: yup
    .string()
    .trim()
    .required('OA ID không được để trống')
    .matches(/^[0-9]+$/, 'Chỉ nhập số'),
  oa_name: yup
    .string()
    .trim()
    .required('OA không được để trống')
    .matches(REGEX.SPECIAL_CHARACTER, 'OA không được chứa ký tự đặc biệt'),
  label_type_oa: yup.string().required('Vui lòng chọn lĩnh vực'),
  oa_package: yup.string().nullable(),
  expired_package_at: yup.string().nullable(),
  package_vnpt_id: yup.string().nullable(),
});

const { values, handleSubmit, setFieldValue } = useForm({
  validationSchema,
});

const disabledDatesStart = (date: Date) => {
  const now = new Date();
  return isDateBefore(date, now);
};

const getListLabelTypePay = async () => {
  try {
    const res = await api.get('/business/v1/api/client/oa/label_type_OA');
    if (res.data.code === 0) {
      listLabelTypeOA.value = res.data.data.map((item: any) => ({
        value: item.id,
        label: item.label_type_name,
      }));
    } else {
      toast('error', res.data.message);
    }
  } catch (error) {
    console.error(error);
  }
};

const getDetail = function () {
  overlayLoading.toggleLoading(true);
  api
    .get(`/business/v1/api/admin/oa/${props?.id}`)
    .then((response) => {
      if (response.data.code === 0) {
        const res = response.data.data;
        setFieldValue('oa_id', res.oa_id);
        setFieldValue('oa_name', res.oa_name);
        setFieldValue('tenant_name', res.business_name);
        setFieldValue('expired_package_at', new Date(res.expired_at));
        setFieldValue('oa_package', res.package_id);
        setFieldValue('package_vnpt_id', res.package_vnpt_id);
        setFieldValue('label_type_oa', res.label_type_oa);
        status.value = res.status;
        type_pay.value = res.type_pay;
        reason.value = res.rejection_reason;
        // Store original values for comparison
        originalValues.value = {
          oa_id: res.oa_id,
          oa_name: res.oa_name,
          expired_package_at: new Date(res.expired_at),
          oa_package: res.package_id,
          label_type_oa: res.label_type_oa,
          package_vnpt_id: res.package_vnpt_id,
          type_pay: res.type_pay,
        };
        overlayLoading.toggleLoading(false);
      } else {
        overlayLoading.toggleLoading(false);
        toast('error', response.data.message);
      }
    })
    .catch(() => {
      overlayLoading.toggleLoading(false);
    });
};
const backToZnsOA = () => {
  router.push({
    name: ROUTE_NAME.OA,
  });
};
const handleClickCancel = () => {
  let hasChanges = false;

  if (props.type === FORM_TYPE.ADD) {
    // Check if form has been modified for ADD type
    hasChanges =
      values.oa_id ||
      values.oa_name ||
      values.label_type_oa ||
      values.oa_package ||
      values.expired_package_at ||
      values.package_vnpt_id;
  } else if (props.type === FORM_TYPE.UPDATE) {
    // Check if form has been modified for UPDATE type by comparing with original values
    const compareDate = (date1: any, date2: any) => {
      if (!date1 && !date2) return true;
      if (!date1 || !date2) return false;
      return new Date(date1).getTime() === new Date(date2).getTime();
    };

    hasChanges =
      values.oa_id !== originalValues.value.oa_id ||
      values.oa_name !== originalValues.value.oa_name ||
      values.label_type_oa !== originalValues.value.label_type_oa ||
      values.oa_package !== originalValues.value.oa_package ||
      !compareDate(values.expired_package_at, originalValues.value.expired_package_at) ||
      values.package_vnpt_id !== originalValues.value.package_vnpt_id ||
      type_pay.value !== originalValues.value.type_pay;
  }

  if (hasChanges && props.type !== FORM_TYPE.DETAILS) {
    isShowConfirmPopup.value = true;
  } else {
    backToZnsOA();
  }
};
const redirectUpdate = () => {
  router.push({
    name: ROUTE_NAME.UPDATE_OA,
    params: {
      id: props.id,
      type: FORM_TYPE.UPDATE,
    },
  });
};

const redirectDetail = () => {
  router.push({
    name: ROUTE_NAME.DETAILS_OA,
    params: {
      id: props.id,
      type: FORM_TYPE.DETAILS,
    },
  });
};

const deleteItem = () => {
  typePopup.value = 'delete';
  deleteId.value = props.id;
  deleteName.value = values.oa_name;
  visibleModalDelete.value = true;
};

const handleDelete = async () => {
  try {
    overlayLoading.toggleLoading(true);
    await api
      .delete(`/business/v1/api/admin/oa/${deleteId.value}`)
      .then((response) => {
        if (response.data.code === 0) {
          overlayLoading.toggleLoading(false);
          visibleModalDelete.value = false;
          backToZnsOA();
          toast('success', response.data.message);
        } else {
          overlayLoading.toggleLoading(false);
          visibleModalDelete.value = false;
          toast('error', response.data.message);
        }
      })
      .catch(() => {
        visibleModalDelete.value = false;
        overlayLoading.toggleLoading(false);
      });
  } catch {
    visibleModalDelete.value = false;
    overlayLoading.toggleLoading(false);
  }
};

const pauseItem = () => {
  typePopup.value = 'pause';
  deleteId.value = props.id;
  visibleModalDelete.value = true;
};

const rejectItem = (refund: boolean) => {
  typePopup.value = 'reject';
  deleteId.value = props.id;
  isRefund.value = refund;
  visibleModalDelete.value = true;
};

const connectOA = async () => {
  if (type_pay.value === LIST_TYPE_PAY[1].value) {
    isVisibleConnectPopup.value = true;
    oaId.value = props.id;
  } else {
    try {
      overlayLoading.toggleLoading(true);
      await api
        .put(`/business/v1/api/admin/oa/approve_or_pause`, {
          id: props.id,
          reason: '',
        })
        .then((response) => {
          if (response.data.code === 0) {
            overlayLoading.toggleLoading(false);
            toast('success', response.data.message);
            getDetail();
          } else {
            overlayLoading.toggleLoading(false);
            toast('error', response.data.message);
          }
        })
        .catch(() => {
          overlayLoading.toggleLoading(false);
        });
    } catch {
      overlayLoading.toggleLoading(false);
    }
  }
};

const onSubmit = handleSubmit(async () => {
  overlayLoading.toggleLoading(true);
  const params: IParam = {
    id: props.id,
    oa_id: values.oa_id,
    oa_name: values.oa_name,
    package_id: values.oa_package,
    expired_at: timestampGetter(values.expired_package_at),
    label_type_oa: values.label_type_oa,
    user_id: user.user.userId,
    type: props.type === FORM_TYPE.UPDATE ? 1 : 2,
  };

  await api
    .put('/business/v1/api/admin/oa', params)
    .then((response) => {
      if (response.data.code === 0) {
        overlayLoading.toggleLoading(false);
        toast('success', response.data.message);
        redirectDetail();
      } else {
        overlayLoading.toggleLoading(false);
        toast('error', response.data.message);
      }
    })
    .catch((error) => {
      console.error(error);
      overlayLoading.toggleLoading(false);
    });
});

onMounted(async () => {
  await getListLabelTypePay();
  getDetail();
});

watch(
  () => props.type,
  (newVal) => {
    if (newVal === FORM_TYPE.DETAILS) {
      getDetail();
    }
  },
);
</script>

<style scoped>
:deep(.so-hop-dong .el-input__wrapper) {
  height: 30px !important;
}

:deep(.p-tag) {
  width: 120px;
}

.el-tag {
  aspect-ratio: 1;
}

:deep(.el-select-v2 .el-select-v2__selection .el-tag) {
  background-color: #1a34b5 !important;
  color: #fff !important;
}

:deep(.el-tag .el-icon) {
  background-color: red !important;
}

:deep(.el-tag .el-tag__close) {
  color: #fff !important;
}
.view-height {
  height: calc(100vh - 245px);
}
</style>
