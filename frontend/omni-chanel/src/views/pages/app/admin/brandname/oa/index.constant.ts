export const OA_PACKAGE_OPTIONS = [
  { value: 'maintainAdvanceOA', label: 'Nâng cao' },
  { value: 'maintainPremiumOA', label: 'Premium' },
];
export const OA_PACKAGE_OPTIONS_CONNECT = [
  { value: 'maintainAdvanceOA6month', label: 'Nâng cao 6 tháng' },
  { value: 'maintainAdvanceOA1year', label: 'Nâng cao 1 năm (tiết kiệm 10%)' },
  { value: 'maintainPremiumOA6month', label: 'Premium 6 tháng' },
  { value: 'maintainPremiumOA1year', label: 'Premium 1 năm (tiết kiệm 10%)' },
];
export const OA_STATUS_OPTIONS = [
  { value: 1, label: 'Hoạt động' },
  { value: 2, label: 'Tạm dừng' },
  { value: 3, label: 'Đang kết nối' },
  { value: 4, label: 'Từ chối' },
];

export const getOAPackage = (id: string): string => {
  switch (id) {
    case 'maintainAdvanceOA':
      return 'Nâng cao';
    case 'maintainPremiumOA':
      return 'Premium';
    default:
      return '';
  }
};

export const LIST_TYPE_PAY = [
  {
    value: 1,
    label: 'Mua gói tại Zalo',
  },
  {
    value: 2,
    label: 'Mua gói tại VNPT Omnichannel',
  },
];