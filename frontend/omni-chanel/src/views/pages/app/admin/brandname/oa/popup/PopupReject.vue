<template>
  <div class="pt-[15px]">
    <div class="flex flex-col mt-[20px] items-center" id="changePassword-old-password-group">
      <div class="w-[100%]">
        <el-form label-position="top">
          <VElementInput
            required
            name="reason"
            type="textarea"
            label="Lý do từ chối"
            :rows="3"
            :maxlength="250"
            :showCount="true"
            placeholder="Nhập lý do từ chối"
          />
        </el-form>
      </div>
    </div>
  </div>
  <div class="save-container flex justify-center items-center gap-5 mt-5">
    <VElementButton :bgColor="color.closeButton" @click="() => emit('onClose')" label="Hủy" />
    <VElementButton :bgColor="color.main" @click="handleReject" label="Đồng ý" />
  </div>
</template>

<script setup lang="ts">
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { inject } from 'vue';
import { useForm } from 'vee-validate';
import * as yup from 'yup';

const props = defineProps<{
  id: number;
  is_refund?: boolean;
}>();

const emit = defineEmits(['onClose', 'onConfirm']);

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();

const validationSchema = yup.object({
  reason: yup.string().trim().required('Lý do từ chối không được để trống'),
});

const { values, handleSubmit } = useForm({
  validationSchema,
});
const handleReject = handleSubmit(async () => {
  try {
    overlayLoading.toggleLoading(true);
    await api
      .put(`/business/v1/api/admin/oa/refuse`, {
        id: props.id,
        reason: values.reason,
        is_refund: props.is_refund,
      })
      .then((response) => {
        if (response.data.code === 0) {
          overlayLoading.toggleLoading(false);
          emit('onConfirm');
          emit('onClose');
          toast('success', response.data.message);
        } else {
          overlayLoading.toggleLoading(false);
          toast('error', response.data.message);
        }
      })
      .catch(() => {
        overlayLoading.toggleLoading(false);
      });
  } catch {
    overlayLoading.toggleLoading(false);
  }
});
</script>
