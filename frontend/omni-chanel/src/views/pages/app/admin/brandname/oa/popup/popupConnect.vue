<template>
  <div class="pt-[15px]">
    <div class="flex flex-col items-center" id="changePassword-old-password-group">
      <div class="font-normal text-[19px] text-center">Vui lòng nhập thông tin cho gói OA</div>
      <div class="w-[100%] mt-5">
        <el-form label-position="top" class="flex flex-col gap-3">
          <VElementDropdown
            required
            id="package_vnpt_id"
            name="package_vnpt_id"
            :filterable="false"
            :label="'Gói OA muốn mua'"
            :option="OA_PACKAGE_OPTIONS_CONNECT"
            :style="'w-[100%]'"
            placeholder="Chọn gói OA"
          />
          <VElementDateTimePicker
            required
            id="expired-package-at"
            size="default"
            name="expired_package_at"
            type="date"
            format="DD/MM/YYYY"
            :label="'Thời gian hết hạn'"
            :style="'!w-[100%]'"
            :disabledDates="disabledDatesStart"
            placeholder="Chọn thời gian hết hạn"
          />
        </el-form>
      </div>
    </div>
  </div>
  <div class="save-container flex justify-center items-center gap-5 mt-5">
    <VElementButton :bgColor="color.closeButton" @click="() => emit('onClose')" label="Hủy" />
    <VElementButton :bgColor="color.main" @click="handleConnect" label="Đồng ý" />
  </div>
</template>

<script setup lang="ts">
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { inject, onMounted } from 'vue';
import { useForm } from 'vee-validate';
import * as yup from 'yup';
import { OA_PACKAGE_OPTIONS_CONNECT } from '../index.constant';
import { isDateBefore } from '@/shared';
import moment from 'moment';

const props = defineProps<{
  id: number;
  package_vnpt_id?: any;
}>();

const emit = defineEmits(['onClose', 'onConfirm']);

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();

const validationSchema = yup.object({
  package_vnpt_id: yup.string().trim().required('Gói OA không được để trống'),
  expired_package_at: yup.string().trim().required('Thời gian hết hạn không được để trống'),
});

const { values, handleSubmit, setFieldValue } = useForm({
  validationSchema,
});

const disabledDatesStart = (date: Date) => {
  const now = new Date();
  return isDateBefore(date, now);
};

onMounted(() => {
  if (props.package_vnpt_id) {
    setFieldValue('package_vnpt_id', props.package_vnpt_id);
  }
});

const handleConnect = handleSubmit(async () => {
  try {
    overlayLoading.toggleLoading(true);
    await api
      .put(`/business/v1/api/admin/oa/approve_or_pause`, {
        id: props.id,
        reason: '',
        package_vnpt_id: values.package_vnpt_id,
        expired_at: moment(values.expired_package_at).unix(),
      })
      .then((response) => {
        if (response.data.code === 0) {
          overlayLoading.toggleLoading(false);
          toast('success', response.data.message);
          emit('onConfirm');
          emit('onClose');
        } else {
          overlayLoading.toggleLoading(false);
          toast('error', response.data.message);
        }
      })
      .catch(() => {
        overlayLoading.toggleLoading(false);
      });
  } catch {
    overlayLoading.toggleLoading(false);
  }
});
</script>
