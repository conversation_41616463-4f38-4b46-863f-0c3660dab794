<script setup lang="ts">
import { inject, onMounted, ref, watch } from 'vue';
import { useApi } from '@/store/useApi';
import { useRouter } from 'vue-router';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import PopupCancelConfirm from '@/components/base/common/PopupCancelConfirm.vue';
import { ROUTE_NAME, TabValue, TEXT } from '@/shared';
import { initSellingPriceValueSMS, initSellingPriceValueZNS } from './index.constants';
import SMSBoard from '../components/SMSBoard.vue';
import ZNSBoard from '../components/ZNSBoard.vue';
import {
  ChannelValue,
  CustomerType,
  type TSMSWrapper,
  type TTab,
  type TZNSWrapper,
} from '../index.type';
import { cloneDeep } from 'lodash';
import { getSellingPriceSMSValue, getSellingPriceZNSValue } from './index.utils';
import { PriceType } from '../index.constants';
import { initCostPriceValueSMS, initCostPriceValueZNS } from '../bang-gia-von/index.constants';
import { getCostPriceSMSValue, getCostPriceZNSValue } from '../bang-gia-von/index.utils';
import BaseChildTabs from '../components/BaseChildTabs.vue';
import { PageType } from '@/enums/common';
import { initListPriceValueSMS, initListPriceValueZNS } from '../bang-gia-niem-yet/index.constants';
import { getListPriceSMSValue, getListPriceZNSValue } from '../bang-gia-niem-yet/index.utils';
import { generateImage, getPriceBoardFileName } from '../index.utils';
import { useTab } from '@/store/useTab';

const props = defineProps<{
  type: PageType;
  customerId: number;
  routerDetails?: string;
}>();

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();

//#region Datasource
const oldSmsDataSource = ref<TSMSWrapper>(cloneDeep(initSellingPriceValueSMS));
const smsDataSource = ref<TSMSWrapper>(cloneDeep(initSellingPriceValueSMS));
const smsCapitalDataSource = ref<TSMSWrapper>(cloneDeep(initCostPriceValueSMS));
const smsFixedDataSource = ref<TSMSWrapper>(cloneDeep(initListPriceValueSMS));
const oldZnsDataSource = ref<TZNSWrapper>(cloneDeep(initSellingPriceValueZNS));
const znsDataSource = ref<TZNSWrapper>(cloneDeep(initSellingPriceValueZNS));
const znsCapitalDataSource = ref<TZNSWrapper>(cloneDeep(initCostPriceValueZNS));
const znsFixedDataSource = ref<TZNSWrapper>(cloneDeep(initListPriceValueZNS));

const fillCustomSMSData = () => {
  if (smsDataSource.value.platformCost === null || smsDataSource.value.platformCost === undefined) {
    smsDataSource.value.platformCost = smsFixedDataSource.value.platformCost;
  }

  smsDataSource.value.data.forEach((item, indexData) => {
    item.rows.forEach((row, indexRow) => {
      row.fields.forEach((field, indexField) => {
        if (!field.field && (field.value === null || field.value === undefined)) {
          field.value =
            smsFixedDataSource.value.data[indexData].rows[indexRow].fields[indexField].value;
        }
      });
    });
  });

  oldSmsDataSource.value = cloneDeep(smsDataSource.value);
};

const fillCustomZNSData = () => {
  if (znsDataSource.value.platformCost === null || znsDataSource.value.platformCost === undefined) {
    znsDataSource.value.platformCost = znsFixedDataSource.value.platformCost;
  }

  znsDataSource.value.data.forEach((item, indexData) => {
    item.rows.forEach((row, indexRow) => {
      row.fields.forEach((field, indexField) => {
        if (!field.field && (field.value === null || field.value === undefined)) {
          field.value =
            znsFixedDataSource.value.data[indexData].rows[indexRow].fields[indexField].value;
        }
      });
    });
  });

  oldZnsDataSource.value = cloneDeep(znsDataSource.value);
};

const mapDataSMS = (responseData: any) => {
  smsDataSource.value = cloneDeep(getSellingPriceSMSValue(responseData.custom));
  smsCapitalDataSource.value = cloneDeep(getCostPriceSMSValue(responseData.capital));
  smsFixedDataSource.value = cloneDeep(getListPriceSMSValue(responseData.fixed));
  fillCustomSMSData();
  setTimeout(() => {
    smsBoardRef.value?.resetFields();
  }, 100);
};

const mapDataZNS = (responseData: any) => {
  znsDataSource.value = cloneDeep(getSellingPriceZNSValue(responseData.custom));
  znsCapitalDataSource.value = cloneDeep(getCostPriceZNSValue(responseData.capital));
  znsFixedDataSource.value = cloneDeep(getListPriceZNSValue(responseData.fixed));
  fillCustomZNSData();
  setTimeout(() => {
    znsBoardRef.value?.resetFields();
  }, 100);
};

const mapDataDetails = (tab: TabValue, responseData: any) => {
  if (tab === TabValue.Tab1) {
    mapDataSMS(responseData);
  } else if (tab === TabValue.Tab2) {
    mapDataZNS(responseData);
  }
};

const getDetail = async function (tab: TabValue = activeTab.value) {
  if (tab === TabValue.Tab3 || tab === TabValue.Tab4) return;

  overlayLoading.toggleLoading(true);
  let url = `business/v1/api/price-list/sms?customerType=${CustomerType.Agent}&customerId=${props.customerId}`;
  if (tab === TabValue.Tab2) {
    url = `business/v1/api/price-list/zns?customerType=${CustomerType.Agent}&customerId=${props.customerId}`;
  }
  const { data } = await api.get(url);
  if (data.code === 0) {
    mapDataDetails(tab, data.data);
  } else {
    toast('error', data.message);
  }
  overlayLoading.toggleLoading(false);
};
//#endregion

//#region Form
const smsBoardRef = ref();
const znsBoardRef = ref();

const handleUploadFile = async (datetime: any) => {
  try {
    overlayLoading.toggleLoading(true);
    const fileName = getPriceBoardFileName(
      PriceType.SellingPrice,
      activeTab.value === TabValue.Tab1 ? ChannelValue.SMS : ChannelValue.ZNS,
    );
    const fileImage = await generateImage(
      activeTab.value === TabValue.Tab1 ? smsBoardRef.value?.element : znsBoardRef.value?.element,
      fileName,
      datetime,
    );
    if (fileImage) {
      const formData = new FormData();
      formData.append('file', fileImage);
      const res = await api.post('business/v1/api/common/file/upload?type=40', formData, {
        skipToastErr: true,
      });
      if (res.data.code === 0) {
        return res.data.data?.file_upload_id;
      } else {
        toast('error', res.data.message);
        return false;
      }
    }
    overlayLoading.toggleLoading(false);
  } catch (err: any) {
    console.error(err);
    toast('error', TEXT.ERROR_OCCURRED);
    overlayLoading.toggleLoading(false);
  }
};

const handleSubmitFormSMS = async (payload: any) => {
  const now = new Date();
  overlayLoading.toggleLoading(true);
  const fileUploadId = await handleUploadFile(now);
  if (!fileUploadId) {
    overlayLoading.toggleLoading(false);
    return;
  }
  const { data } = await api.put(
    `business/v1/api/price-list/sms?customerType=${CustomerType.Agent}&customerId=${props.customerId}&logFileId=${fileUploadId}&createdAt=${now.getTime()}`,
    payload,
  );
  if (data.code === 0) {
    toast('success', data.message);
  } else {
    toast('error', data.message);
  }
  overlayLoading.toggleLoading(false);
  emit('onSaved');
};

const handleSubmitFormZNS = async (payload: any) => {
  const now = new Date();
  overlayLoading.toggleLoading(true);
  const fileUploadId = await handleUploadFile(now);
  if (!fileUploadId) {
    overlayLoading.toggleLoading(false);
    return;
  }
  const { data } = await api.put(
    `business/v1/api/price-list/zns?customerType=${CustomerType.Agent}&customerId=${props.customerId}&logFileId=${fileUploadId}&createdAt=${now.getTime()}`,
    payload,
  );
  if (data.code === 0) {
    toast('success', data.message);
  } else {
    toast('error', data.message);
  }
  overlayLoading.toggleLoading(false);
  emit('onSaved');
};

const onSubmit = () => {
  overlayLoading.toggleLoading(true);
  setTimeout(async () => {
    if (activeTab.value === TabValue.Tab1) {
      await smsBoardRef.value.onSubmit();
    } else if (activeTab.value === TabValue.Tab2) {
      await znsBoardRef.value.onSubmit();
    }
  }, 100);
};
//#endregion

//#region Cancel validation
const emit = defineEmits(['onConfirmCancel', 'onSaved']);
const isVisibleCancelPopup = ref(false);

const handleCloseCancel = () => {
  isVisibleCancelPopup.value = false;
};

const handleConfirmCancel = () => {
  isVisibleCancelPopup.value = false;
  if (selectedTab.value !== activeTab.value) {
    activeTab.value = selectedTab.value;
    oldTab.value = selectedTab.value;
    moveToDetail();
    tabStore.setChildAgentActiveTab(+selectedTab.value);
  } else {
    emit('onConfirmCancel');
  }
};

const handleConfirmCancelParent = (isChangedTab: boolean) => {
  if (isChangedTab && activeTab.value === TabValue.Tab1) {
    smsDataSource.value = cloneDeep(initSellingPriceValueSMS);
    setTimeout(() => {
      smsBoardRef.value?.resetFields();
    }, 100);
  } else if (isChangedTab && activeTab.value === TabValue.Tab2) {
    znsDataSource.value = cloneDeep(initSellingPriceValueZNS);
    setTimeout(() => {
      znsBoardRef.value?.resetFields();
    }, 100);
  }
  if (isChangedTab || selectedTab.value !== activeTab.value) {
    activeTab.value = selectedTab.value;
  } else {
    emit('onConfirmCancel');
  }
};
//#endregion

//#region Router
const router = useRouter();

const moveToDetail = () => {
  router.push({
    name: props.routerDetails ? props.routerDetails : ROUTE_NAME.DETAILS_AGENT,
  });
};
//#endregion

//#region Tab
const oldTab = ref<TabValue | undefined>(TabValue.Tab1);
const activeTab = ref<TabValue>(TabValue.Tab1);
const selectedTab = ref<TabValue>(TabValue.Tab1);
const tabStore = useTab();

const tabs: TTab[] = [
  {
    label: 'SMS',
    value: TabValue.Tab1,
  },
  {
    label: 'ZNS',
    value: TabValue.Tab2,
  },
];

const isChangedValue = async () => {
  if (activeTab.value === TabValue.Tab1 && smsBoardRef.value) {
    await smsBoardRef.value?.updateDatasource();
    return JSON.stringify(oldSmsDataSource.value) !== JSON.stringify(smsDataSource.value);
  }
  if (znsBoardRef.value) {
    await znsBoardRef.value?.updateDatasource();
    return JSON.stringify(oldZnsDataSource.value) !== JSON.stringify(znsDataSource.value);
  }
  return true;
};

const handleTabChanged = async (tabValue: TabValue) => {
  if (tabValue === oldTab.value) return;
  if (props.type !== PageType.Details && (await isChangedValue())) {
    isVisibleCancelPopup.value = true;
    selectedTab.value = tabValue;
  } else {
    activeTab.value = tabValue;
    oldTab.value = tabValue;
    if (props.type === PageType.Details) {
      getDetail(tabValue);
    } else {
      moveToDetail();
    }
    tabStore.setChildAgentActiveTab(tabValue);
  }
};

const resetOldValue = () => {
  oldTab.value = TabValue.Tab1;
};

const handleClickCancel = async () => {
  if (await isChangedValue()) {
    isVisibleCancelPopup.value = true;
  } else moveToDetail();
};

const isValidAll = () => {
  if (activeTab.value === TabValue.Tab1 && smsBoardRef.value) {
    return smsBoardRef.value.isValidAll();
  }
  if (activeTab.value === TabValue.Tab2 && znsBoardRef.value) {
    return znsBoardRef.value.isValidAll();
  }
  return true;
};

const updateDatasource = () => {
  if (activeTab.value === TabValue.Tab1 && smsBoardRef.value) {
    return smsBoardRef.value.updateDatasource();
  }
  if (activeTab.value === TabValue.Tab2 && znsBoardRef.value) {
    return znsBoardRef.value.updateDatasource();
  }
  return true;
};

const resetErrors = () => {
  if (activeTab.value === TabValue.Tab1 && smsBoardRef.value) {
    smsBoardRef.value.resetErrors();
  }
  if (activeTab.value === TabValue.Tab2 && znsBoardRef.value) {
    znsBoardRef.value.resetErrors();
  }
};

const clearOldTab = () => {
  oldTab.value = undefined;
};
//#endregion

//#region Watcher
watch(
  () => props.type,
  (value) => {
    if (value === PageType.Details) {
      getDetail();
    }
  },
);
//#endregion

onMounted(() => {
  oldTab.value = tabStore.childAgentActiveTab;
  activeTab.value = tabStore.childAgentActiveTab;
  getDetail(activeTab.value);
});

defineExpose({
  activeTab,
  handleClickCancel,
  onSubmit,
  isChangedValue,
  getDetail,
  handleConfirmCancel,
  handleConfirmCancelParent,
  isValidAll,
  updateDatasource,
  resetErrors,
  resetOldValue,
  clearOldTab,
});
</script>

<template>
  <div class="overflow-y-auto w-full h-[calc(100%-40px)]">
    <BaseChildTabs :tabs :active-tab="activeTab" tabClass="w-[90%]" @click-tab="handleTabChanged">
      <template #tab-1>
        <SMSBoard
          v-model:datasource="smsDataSource"
          ref="smsBoardRef"
          note="Lưu ý: Giá khai báo phải bao gồm VAT"
          :type
          :activeTab
          :price-type="PriceType.SellingPrice"
          :capital-data="smsCapitalDataSource"
          :fixed-data="smsFixedDataSource"
          @onSubmitForm="handleSubmitFormSMS"
        />
      </template>
      <template #tab-2>
        <ZNSBoard
          v-model:datasource="znsDataSource"
          ref="znsBoardRef"
          note="Lưu ý: Giá khai báo phải bao gồm VAT"
          :type
          :activeTab
          :price-type="PriceType.SellingPrice"
          :capital-data="znsCapitalDataSource"
          :fixed-data="znsFixedDataSource"
          @onSubmitForm="handleSubmitFormZNS"
        />
      </template>
    </BaseChildTabs>
  </div>
  <PopupCancelConfirm
    v-model:popupVisible="isVisibleCancelPopup"
    @onClose="handleCloseCancel"
    @onConfirm="handleConfirmCancel"
  />
</template>
