import { PLACEHOLDER } from '@/shared';
import type { TSMSWrapper, TZNSWrapper } from '../index.type';

export const initSellingPriceValueSMS: TSMSWrapper = {
  platformCost: null,
  data: [
    {
      label: '<PERSON><PERSON> khai báo Brandname',
      unit: '(E-Point/ brandname)',
      hasField: false,
      rows: [
        {
          isHeader: true,
          fields: [
            { value: 'Vinaphone' },
            { value: 'Vietnamobile' },
            { value: 'Mobifone' },
            { value: 'Viettel' },
            { value: 'Itel' },
            { value: 'Reddi' },
            { value: 'Gtel' },
          ],
        },
        {
          fields: [
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
      ],
    },
    {
      label: 'Phí duy trì Brandname',
      unit: '(E-Point/ brandname/ tháng)',
      hasField: false,
      rows: [
        {
          isHeader: true,
          fields: [
            { value: 'Vinaphone' },
            { value: 'Vietnamobile' },
            { value: 'Mobifone' },
            { value: 'Viettel' },
            { value: 'Itel' },
            { value: 'Reddi' },
            { value: 'Gtel' },
          ],
        },
        {
          fields: [
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
      ],
    },
    {
      label: 'Giá cước dịch vụ Chăm sóc khách hàng',
      unit: '(E-Point/ bản tin MT)',
      hasField: true,
      rows: [
        {
          isHeader: true,
          fields: [
            { value: 'Lĩnh vực' },
            { value: 'Vinaphone' },
            { value: 'Vietnamobile' },
            { value: 'Mobifone' },
            { value: 'Viettel' },
            { value: 'Itel' },
            { value: 'Reddi' },
            { value: 'Gtel' },
          ],
        },
        {
          fields: [
            { field: 'Y tế', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Giáo dục', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Hành chính công', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Mạng xã hội trong nước', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            {
              field: 'Tài chính',
              value: '',
              placeholder: PLACEHOLDER.TYPE_PRICE,
            },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            {
              field: 'Chứng khoán',
              value: '',
              placeholder: PLACEHOLDER.TYPE_PRICE,
            },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            {
              field: 'Bảo hiểm',
              value: '',
              placeholder: PLACEHOLDER.TYPE_PRICE,
            },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Nước', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Thương mại điện tử', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Ngân hàng', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Mạng xã hội Quốc tế', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Điện', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Logistics', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Ví điện tử', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Lĩnh vực khác', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
      ],
    },
    {
      label: 'Giá cước dịch vụ Quảng cáo',
      unit: '(E-Point/ bản tin MT)',
      hasField: true,
      rows: [
        {
          isHeader: true,
          fields: [
            { value: 'Lĩnh vực' },
            { value: 'Vinaphone' },
            { value: 'Vietnamobile' },
            { value: 'Mobifone' },
            { value: 'Viettel' },
            { value: 'Itel' },
            { value: 'Reddi' },
            { value: 'Gtel' },
          ],
        },
        {
          fields: [
            { field: 'Y tế', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Giáo dục/ tuyển sinh', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Thương mại điện tử', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Ngân hàng', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Bất động sản', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Gas', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Hóa chất', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Khách sạn', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Karaoke', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Bảo vệ', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Nhà hàng', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Thực phẩm/ đồ uống', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Du lịch', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Dược phẩm', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Vận tải, taxi', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Thời trang', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            {
              field: 'Siêu thị/ trung tâm thương mại',
              value: '',
              placeholder: PLACEHOLDER.TYPE_PRICE,
            },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Hóa mĩ phẩm', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Giải trí', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Tuyển dụng', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
        {
          fields: [
            { field: 'Lĩnh vực khác', value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
          ],
        },
      ],
    },
  ],
};

export const initSellingPriceValueZNS: TZNSWrapper = {
  platformCost: null,
  data: [
    {
      label: 'Phí hệ thống',
      rows: [
        {
          isHeader: true,
          fields: [{ value: 'Danh mục' }, { value: 'Giá' }, { value: 'Đơn vị' }],
        },
        {
          fields: [
            { value: '', field: 'Phí thiết lập hệ thống gửi tin' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ năm' },
          ],
        },
        {
          fields: [
            { value: '', field: 'Phí khai báo OA' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ OA' },
          ],
        },
        {
          fields: [
            { value: '', field: 'Phí duy trì OA (gói nâng cao)' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ OA/ 6 tháng' },
          ],
        },
        {
          fields: [
            { value: '', field: 'Phí duy trì OA (gói premium)' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ OA/ 6 tháng' },
          ],
        },
      ],
    },
    {
      label: 'Phí dịch vụ ZNS',
      rows: [
        {
          isHeader: true,
          fields: [{ value: 'Danh mục' }, { value: 'Giá' }, { value: 'Đơn vị' }],
        },
        {
          fields: [
            { value: '', field: 'ZNS Dạng văn bản' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'ZNS Dạng bảng' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'ZNS Dạng OTP' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'ZNS Dạng đánh giá' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'ZNS Dạng yêu cầu thanh toán' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'ZNS Dạng Voucher' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'ZNS Hành chính công/ Cơ Quan Nhà Nước' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ ZNS' },
          ],
        },
      ],
    },
    {
      label: 'Phí CTA',
      rows: [
        {
          isHeader: true,
          fields: [{ value: 'Danh mục' }, { value: 'Giá' }, { value: 'Đơn vị' }],
        },
        {
          fields: [
            { value: '', field: 'Đến trang của doanh nghiệp' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ CTA/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'Gọi điện' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ CTA/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'Đến trang thông tin OA' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ CTA/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'Đến ứng dụng Zalo mini app của doanh nghiệp' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ CTA/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'Đến trang phân phối sản phẩm' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ CTA/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'Đến trang web/ Zalo mini app' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ CTA/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'Đến ứng dụng hoặc trang tải ứng dụng' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ CTA/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'Đến ứng dụng khác hoặc trang tải ứng dụng khác' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ CTA/ ZNS' },
          ],
        },
        {
          fields: [
            { value: '', field: 'Đến bài viết của OA' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ CTA/ ZNS' },
          ],
        },
      ],
    },
    {
      label: 'Phí thành phần đặc biệt',
      rows: [
        {
          isHeader: true,
          fields: [{ value: 'Danh mục' }, { value: 'Giá' }, { value: 'Đơn vị' }],
        },
        {
          fields: [
            { value: '', field: 'Hình ảnh' },
            { value: '', placeholder: PLACEHOLDER.TYPE_PRICE },
            { value: '', unit: 'E-Point/ ZNS' },
          ],
        },
      ],
    },
  ],
};
