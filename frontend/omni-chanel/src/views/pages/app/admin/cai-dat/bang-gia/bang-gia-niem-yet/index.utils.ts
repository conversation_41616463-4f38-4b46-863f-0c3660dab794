import { cloneDeep } from 'lodash';
import {
  ADMINI<PERSON>RATIVE,
  ADS_<PERSON><PERSON><PERSON>,
  ADS_CHEMICAL,
  ADS_COSMETICS,
  ADS_ECOMMERCE,
  ADS_EDUCATION_ENROLLMENT,
  ADS_ENTERTAINMENT,
  ADS_FASHION,
  ADS_<PERSON>OOD_DRINK,
  ADS_GAS,
  ADS_<PERSON>UAR<PERSON>,
  ADS_HOTEL,
  ADS_KARA,
  ADS_MALL,
  ADS_MEDICAL,
  ADS_OTHERS,
  ADS_PHARMACEUTICALS,
  ADS_REAL_ESTATE,
  ADS_RECRUITMENT,
  ADS_RESTAURANT,
  ADS_TOURISM,
  ADS_TRANSPORT,
  ADVERTISEMENT_SERVICE,
  APP_INSTALL_SITE,
  AS<PERSON><PERSON>MENT,
  BUSINESS_MA_SITE,
  BUSINESS_SITE,
  CONFIG_SYSTEM,
  CONTACT,
  CTA_SERVICE,
  CUSTOMER_ADMINISTRATIVE,
  CUSTOMER_BANK,
  CUSTOMER_DOMESTIC_SM,
  CUSTOMER_ECOMMERCE,
  CUSTOMER_EDUCATION,
  CUSTOMER_ELECTRICITY,
  CUSTOMER_EWALLET,
  CUSTOMER_FINANCE,
  CUSTOMER_INSURANCE,
  CUSTOMER_INTERNATIONAL_SM,
  CUSTOMER_LOGISTICS,
  CUSTOMER_MEDICAL,
  CUSTOMER_OTHERS,
  CUSTOMER_SERVICE,
  CUSTOMER_STOCK,
  CUSTOMER_WATER,
  GTL,
  GTL_FIELD,
  IMAGE,
  INIT_BRN,
  INIT_OA,
  ITL,
  ITL_FIELD,
  MAINTAIN_ADVANCE_OA,
  MAINTAIN_BRN,
  MAINTAIN_PREMIUM_OA,
  MBP,
  MBP_FIELD,
  OA_POST,
  OA_SITE,
  OTHERS_APP_INSTALL_SITE,
  OTP,
  PAYMENT,
  PRODUCT_DISTRIBUTED_SITE,
  RDI,
  RDI_FIELD,
  SPECIFIC_SERVICE,
  SYSTEM_SERVICE,
  TABLE,
  TEXT,
  VALUES,
  VNM,
  VNM_FIELD,
  VNP,
  VNP_FIELD,
  VOUCHER,
  VTL,
  VTL_FIELD,
  ZALO_MA_SITE,
  ZNS_SERVICE,
  ZNS_VALUE,
  ZNS_UNIT,
} from '../index.constants';
import { initListPriceValueSMS, initListPriceValueZNS } from './index.constants';
import type { TSMSWrapper, TZNSWrapper } from '../index.type';

export const getListPriceSMSPayload = (datasource: TSMSWrapper) => {
  return {
    fixed: {
      platformCost: datasource.platformCost ? +datasource.platformCost : datasource.platformCost,
      initBrn: {
        vnp: +datasource.data[INIT_BRN].rows[VALUES].fields[VNP].value,
        vnm: +datasource.data[INIT_BRN].rows[VALUES].fields[VNM].value,
        mbp: +datasource.data[INIT_BRN].rows[VALUES].fields[MBP].value,
        vtl: +datasource.data[INIT_BRN].rows[VALUES].fields[VTL].value,
        itl: +datasource.data[INIT_BRN].rows[VALUES].fields[ITL].value,
        rdi: +datasource.data[INIT_BRN].rows[VALUES].fields[RDI].value,
        gtl: +datasource.data[INIT_BRN].rows[VALUES].fields[GTL].value,
      },
      maintainBrn: {
        vnp: +datasource.data[MAINTAIN_BRN].rows[VALUES].fields[VNP].value,
        vnm: +datasource.data[MAINTAIN_BRN].rows[VALUES].fields[VNM].value,
        mbp: +datasource.data[MAINTAIN_BRN].rows[VALUES].fields[MBP].value,
        vtl: +datasource.data[MAINTAIN_BRN].rows[VALUES].fields[VTL].value,
        itl: +datasource.data[MAINTAIN_BRN].rows[VALUES].fields[ITL].value,
        rdi: +datasource.data[MAINTAIN_BRN].rows[VALUES].fields[RDI].value,
        gtl: +datasource.data[MAINTAIN_BRN].rows[VALUES].fields[GTL].value,
      },
      customerService: {
        medical: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[VNP_FIELD].value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[VNM_FIELD].value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[MBP_FIELD].value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[VTL_FIELD].value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[ITL_FIELD].value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[RDI_FIELD].value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[GTL_FIELD].value,
        },
        education: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[VNP_FIELD].value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[VNM_FIELD].value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[MBP_FIELD].value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[VTL_FIELD].value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[ITL_FIELD].value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[RDI_FIELD].value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[GTL_FIELD].value,
        },
        administrative: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[VNP_FIELD]
            .value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[VNM_FIELD]
            .value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[MBP_FIELD]
            .value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[VTL_FIELD]
            .value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[ITL_FIELD]
            .value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[RDI_FIELD]
            .value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[GTL_FIELD]
            .value,
        },
        domesticSM: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[VNP_FIELD]
            .value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[VNM_FIELD]
            .value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[MBP_FIELD]
            .value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[VTL_FIELD]
            .value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[ITL_FIELD]
            .value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[RDI_FIELD]
            .value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[GTL_FIELD]
            .value,
        },
        finance: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[VNP_FIELD].value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[VNM_FIELD].value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[MBP_FIELD].value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[VTL_FIELD].value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[ITL_FIELD].value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[RDI_FIELD].value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[GTL_FIELD].value,
        },
        stock: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[VNP_FIELD].value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[VNM_FIELD].value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[MBP_FIELD].value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[VTL_FIELD].value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[ITL_FIELD].value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[RDI_FIELD].value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[GTL_FIELD].value,
        },
        insurance: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[VNP_FIELD].value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[VNM_FIELD].value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[MBP_FIELD].value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[VTL_FIELD].value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[ITL_FIELD].value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[RDI_FIELD].value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[GTL_FIELD].value,
        },
        water: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[VNP_FIELD].value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[VNM_FIELD].value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[MBP_FIELD].value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[VTL_FIELD].value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[ITL_FIELD].value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[RDI_FIELD].value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[GTL_FIELD].value,
        },
        eCommerce: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[VNP_FIELD].value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[VNM_FIELD].value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[MBP_FIELD].value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[VTL_FIELD].value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[ITL_FIELD].value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[RDI_FIELD].value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[GTL_FIELD].value,
        },
        bank: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[VNP_FIELD].value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[VNM_FIELD].value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[MBP_FIELD].value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[VTL_FIELD].value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[ITL_FIELD].value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[RDI_FIELD].value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[GTL_FIELD].value,
        },
        internationalSM: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[VNP_FIELD]
            .value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[VNM_FIELD]
            .value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[MBP_FIELD]
            .value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[VTL_FIELD]
            .value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[ITL_FIELD]
            .value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[RDI_FIELD]
            .value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[GTL_FIELD]
            .value,
        },
        electricity: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[VNP_FIELD]
            .value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[VNM_FIELD]
            .value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[MBP_FIELD]
            .value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[VTL_FIELD]
            .value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[ITL_FIELD]
            .value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[RDI_FIELD]
            .value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[GTL_FIELD]
            .value,
        },
        logistics: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[VNP_FIELD].value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[VNM_FIELD].value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[MBP_FIELD].value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[VTL_FIELD].value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[ITL_FIELD].value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[RDI_FIELD].value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[GTL_FIELD].value,
        },
        eWallet: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[VNP_FIELD].value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[VNM_FIELD].value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[MBP_FIELD].value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[VTL_FIELD].value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[ITL_FIELD].value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[RDI_FIELD].value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[GTL_FIELD].value,
        },
        others: {
          vnp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[VNP_FIELD].value,
          vnm: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[VNM_FIELD].value,
          mbp: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[MBP_FIELD].value,
          vtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[VTL_FIELD].value,
          itl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[ITL_FIELD].value,
          rdi: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[RDI_FIELD].value,
          gtl: +datasource.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[GTL_FIELD].value,
        },
      },
      advertisementService: {
        medical: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[GTL_FIELD].value,
        },
        eductionEnrollment: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[
            VNP_FIELD
          ].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[
            VNM_FIELD
          ].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[
            MBP_FIELD
          ].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[
            VTL_FIELD
          ].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[
            ITL_FIELD
          ].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[
            RDI_FIELD
          ].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[
            GTL_FIELD
          ].value,
        },
        eCommerce: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[GTL_FIELD].value,
        },
        bank: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[GTL_FIELD].value,
        },
        realEstate: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[VNP_FIELD]
            .value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[VNM_FIELD]
            .value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[MBP_FIELD]
            .value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[VTL_FIELD]
            .value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[ITL_FIELD]
            .value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[RDI_FIELD]
            .value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[GTL_FIELD]
            .value,
        },
        gas: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[GTL_FIELD].value,
        },
        chemical: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[GTL_FIELD].value,
        },
        hotel: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[GTL_FIELD].value,
        },
        kara: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[GTL_FIELD].value,
        },
        guard: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[GTL_FIELD].value,
        },
        restaurant: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[GTL_FIELD].value,
        },
        foodDrink: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[GTL_FIELD].value,
        },
        tourism: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[GTL_FIELD].value,
        },
        pharmaceuticals: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[VNP_FIELD]
            .value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[VNM_FIELD]
            .value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[MBP_FIELD]
            .value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[VTL_FIELD]
            .value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[ITL_FIELD]
            .value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[RDI_FIELD]
            .value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[GTL_FIELD]
            .value,
        },
        transport: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[GTL_FIELD].value,
        },
        fashion: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[GTL_FIELD].value,
        },
        mall: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[GTL_FIELD].value,
        },
        cosmetics: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[GTL_FIELD].value,
        },
        entertainment: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[VNP_FIELD]
            .value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[VNM_FIELD]
            .value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[MBP_FIELD]
            .value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[VTL_FIELD]
            .value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[ITL_FIELD]
            .value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[RDI_FIELD]
            .value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[GTL_FIELD]
            .value,
        },
        recruitment: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[VNP_FIELD]
            .value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[VNM_FIELD]
            .value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[MBP_FIELD]
            .value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[VTL_FIELD]
            .value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[ITL_FIELD]
            .value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[RDI_FIELD]
            .value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[GTL_FIELD]
            .value,
        },
        others: {
          vnp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[VNP_FIELD].value,
          vnm: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[VNM_FIELD].value,
          mbp: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[MBP_FIELD].value,
          vtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[VTL_FIELD].value,
          itl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[ITL_FIELD].value,
          rdi: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[RDI_FIELD].value,
          gtl: +datasource.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[GTL_FIELD].value,
        },
      },
    },
  };
};

export const getListPriceZNSPayload = (datasource: TZNSWrapper) => {
  return {
    fixed: {
      platformCost: datasource.platformCost ? +datasource.platformCost : datasource.platformCost,
      systemService: {
        configSystem: {
          price: +datasource.data[SYSTEM_SERVICE].rows[CONFIG_SYSTEM].fields[ZNS_VALUE].value,
          unit: datasource.data[SYSTEM_SERVICE].rows[CONFIG_SYSTEM].fields[ZNS_UNIT].unit,
        },
        initOA: {
          price: +datasource.data[SYSTEM_SERVICE].rows[INIT_OA].fields[ZNS_VALUE].value,
          unit: datasource.data[SYSTEM_SERVICE].rows[INIT_OA].fields[ZNS_UNIT].unit,
        },
        maintainAdvanceOA: {
          price: +datasource.data[SYSTEM_SERVICE].rows[MAINTAIN_ADVANCE_OA].fields[ZNS_VALUE].value,
          unit: datasource.data[SYSTEM_SERVICE].rows[MAINTAIN_ADVANCE_OA].fields[ZNS_UNIT].unit,
        },
        maintainPremiumOA: {
          price: +datasource.data[SYSTEM_SERVICE].rows[MAINTAIN_PREMIUM_OA].fields[ZNS_VALUE].value,
          unit: datasource.data[SYSTEM_SERVICE].rows[MAINTAIN_PREMIUM_OA].fields[ZNS_UNIT].unit,
        },
      },
      znsService: {
        textFormat: {
          price: +datasource.data[ZNS_SERVICE].rows[TEXT].fields[ZNS_VALUE].value,
          unit: datasource.data[ZNS_SERVICE].rows[TEXT].fields[ZNS_UNIT].unit,
        },
        tableFormat: {
          price: +datasource.data[ZNS_SERVICE].rows[TABLE].fields[ZNS_VALUE].value,
          unit: datasource.data[ZNS_SERVICE].rows[TABLE].fields[ZNS_UNIT].unit,
        },
        otpFormat: {
          price: +datasource.data[ZNS_SERVICE].rows[OTP].fields[ZNS_VALUE].value,
          unit: datasource.data[ZNS_SERVICE].rows[OTP].fields[ZNS_UNIT].unit,
        },
        assessmentFormat: {
          price: +datasource.data[ZNS_SERVICE].rows[ASSESSMENT].fields[ZNS_VALUE].value,
          unit: datasource.data[ZNS_SERVICE].rows[ASSESSMENT].fields[ZNS_UNIT].unit,
        },
        paymentFormat: {
          price: +datasource.data[ZNS_SERVICE].rows[PAYMENT].fields[ZNS_VALUE].value,
          unit: datasource.data[ZNS_SERVICE].rows[PAYMENT].fields[ZNS_UNIT].unit,
        },
        voucherFormat: {
          price: +datasource.data[ZNS_SERVICE].rows[VOUCHER].fields[ZNS_VALUE].value,
          unit: datasource.data[ZNS_SERVICE].rows[VOUCHER].fields[ZNS_UNIT].unit,
        },
        administrativeFormat: {
          price: +datasource.data[ZNS_SERVICE].rows[ADMINISTRATIVE].fields[ZNS_VALUE].value,
          unit: datasource.data[ZNS_SERVICE].rows[ADMINISTRATIVE].fields[ZNS_UNIT].unit,
        },
      },
      ctaService: {
        businessSite: {
          price: +datasource.data[CTA_SERVICE].rows[BUSINESS_SITE].fields[ZNS_VALUE].value,
          unit: datasource.data[CTA_SERVICE].rows[BUSINESS_SITE].fields[ZNS_UNIT].unit,
        },
        contact: {
          price: +datasource.data[CTA_SERVICE].rows[CONTACT].fields[ZNS_VALUE].value,
          unit: datasource.data[CTA_SERVICE].rows[CONTACT].fields[ZNS_UNIT].unit,
        },
        oasite: {
          price: +datasource.data[CTA_SERVICE].rows[OA_SITE].fields[ZNS_VALUE].value,
          unit: datasource.data[CTA_SERVICE].rows[OA_SITE].fields[ZNS_UNIT].unit,
        },
        businessMASite: {
          price: +datasource.data[CTA_SERVICE].rows[BUSINESS_MA_SITE].fields[ZNS_VALUE].value,
          unit: datasource.data[CTA_SERVICE].rows[BUSINESS_MA_SITE].fields[ZNS_UNIT].unit,
        },
        productDistributedSite: {
          price:
            +datasource.data[CTA_SERVICE].rows[PRODUCT_DISTRIBUTED_SITE].fields[ZNS_VALUE].value,
          unit: datasource.data[CTA_SERVICE].rows[PRODUCT_DISTRIBUTED_SITE].fields[ZNS_UNIT].unit,
        },
        zaloMASite: {
          price: +datasource.data[CTA_SERVICE].rows[ZALO_MA_SITE].fields[ZNS_VALUE].value,
          unit: datasource.data[CTA_SERVICE].rows[ZALO_MA_SITE].fields[ZNS_UNIT].unit,
        },
        appInstallSite: {
          price: +datasource.data[CTA_SERVICE].rows[APP_INSTALL_SITE].fields[ZNS_VALUE].value,
          unit: datasource.data[CTA_SERVICE].rows[APP_INSTALL_SITE].fields[ZNS_UNIT].unit,
        },
        othersAppInstallSite: {
          price:
            +datasource.data[CTA_SERVICE].rows[OTHERS_APP_INSTALL_SITE].fields[ZNS_VALUE].value,
          unit: datasource.data[CTA_SERVICE].rows[OTHERS_APP_INSTALL_SITE].fields[ZNS_UNIT].unit,
        },
        oapost: {
          price: +datasource.data[CTA_SERVICE].rows[OA_POST].fields[ZNS_VALUE].value,
          unit: datasource.data[CTA_SERVICE].rows[OA_POST].fields[ZNS_UNIT].unit,
        },
      },
      specificService: {
        image: {
          price: +datasource.data[SPECIFIC_SERVICE].rows[IMAGE].fields[ZNS_VALUE].value,
          unit: datasource.data[SPECIFIC_SERVICE].rows[IMAGE].fields[ZNS_UNIT].unit,
        },
      },
    },
  };
};

export const getListPriceSMSValue = (responseData: any) => {
  const resultData: TSMSWrapper = cloneDeep(initListPriceValueSMS);
  resultData.platformCost = responseData.platformCost;
  resultData.data[INIT_BRN].rows[VALUES].fields[VNP].value = responseData.initBrn?.vnp;
  resultData.data[INIT_BRN].rows[VALUES].fields[VNM].value = responseData.initBrn?.vnm;
  resultData.data[INIT_BRN].rows[VALUES].fields[MBP].value = responseData.initBrn?.mbp;
  resultData.data[INIT_BRN].rows[VALUES].fields[VTL].value = responseData.initBrn?.vtl;
  resultData.data[INIT_BRN].rows[VALUES].fields[ITL].value = responseData.initBrn?.itl;
  resultData.data[INIT_BRN].rows[VALUES].fields[RDI].value = responseData.initBrn?.rdi;
  resultData.data[INIT_BRN].rows[VALUES].fields[GTL].value = responseData.initBrn?.gtl;
  resultData.data[MAINTAIN_BRN].rows[VALUES].fields[VNP].value = responseData.maintainBrn?.vnp;
  resultData.data[MAINTAIN_BRN].rows[VALUES].fields[VNM].value = responseData.maintainBrn?.vnm;
  resultData.data[MAINTAIN_BRN].rows[VALUES].fields[MBP].value = responseData.maintainBrn?.mbp;
  resultData.data[MAINTAIN_BRN].rows[VALUES].fields[VTL].value = responseData.maintainBrn?.vtl;
  resultData.data[MAINTAIN_BRN].rows[VALUES].fields[ITL].value = responseData.maintainBrn?.itl;
  resultData.data[MAINTAIN_BRN].rows[VALUES].fields[RDI].value = responseData.maintainBrn?.rdi;
  resultData.data[MAINTAIN_BRN].rows[VALUES].fields[GTL].value = responseData.maintainBrn?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[VNP_FIELD].value =
    responseData.customerService?.medical?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[VNM_FIELD].value =
    responseData.customerService?.medical?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[MBP_FIELD].value =
    responseData.customerService?.medical?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[VTL_FIELD].value =
    responseData.customerService?.medical?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[ITL_FIELD].value =
    responseData.customerService?.medical?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[RDI_FIELD].value =
    responseData.customerService?.medical?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_MEDICAL].fields[GTL_FIELD].value =
    responseData.customerService?.medical?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[VNP_FIELD].value =
    responseData.customerService?.education?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[VNM_FIELD].value =
    responseData.customerService?.education?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[MBP_FIELD].value =
    responseData.customerService?.education?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[VTL_FIELD].value =
    responseData.customerService?.education?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[ITL_FIELD].value =
    responseData.customerService?.education?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[RDI_FIELD].value =
    responseData.customerService?.education?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EDUCATION].fields[GTL_FIELD].value =
    responseData.customerService?.education?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[VNP_FIELD].value =
    responseData.customerService?.administrative?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[VNM_FIELD].value =
    responseData.customerService?.administrative?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[MBP_FIELD].value =
    responseData.customerService?.administrative?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[VTL_FIELD].value =
    responseData.customerService?.administrative?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[ITL_FIELD].value =
    responseData.customerService?.administrative?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[RDI_FIELD].value =
    responseData.customerService?.administrative?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ADMINISTRATIVE].fields[GTL_FIELD].value =
    responseData.customerService?.administrative?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[VNP_FIELD].value =
    responseData.customerService?.domesticSM?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[VNM_FIELD].value =
    responseData.customerService?.domesticSM?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[MBP_FIELD].value =
    responseData.customerService?.domesticSM?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[VTL_FIELD].value =
    responseData.customerService?.domesticSM?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[ITL_FIELD].value =
    responseData.customerService?.domesticSM?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[RDI_FIELD].value =
    responseData.customerService?.domesticSM?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_DOMESTIC_SM].fields[GTL_FIELD].value =
    responseData.customerService?.domesticSM?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[VNP_FIELD].value =
    responseData.customerService?.finance?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[VNM_FIELD].value =
    responseData.customerService?.finance?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[MBP_FIELD].value =
    responseData.customerService?.finance?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[VTL_FIELD].value =
    responseData.customerService?.finance?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[ITL_FIELD].value =
    responseData.customerService?.finance?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[RDI_FIELD].value =
    responseData.customerService?.finance?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_FINANCE].fields[GTL_FIELD].value =
    responseData.customerService?.finance?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[VNP_FIELD].value =
    responseData.customerService?.stock?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[VNM_FIELD].value =
    responseData.customerService?.stock?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[MBP_FIELD].value =
    responseData.customerService?.stock?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[VTL_FIELD].value =
    responseData.customerService?.stock?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[ITL_FIELD].value =
    responseData.customerService?.stock?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[RDI_FIELD].value =
    responseData.customerService?.stock?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_STOCK].fields[GTL_FIELD].value =
    responseData.customerService?.stock?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[VNP_FIELD].value =
    responseData.customerService?.insurance?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[VNM_FIELD].value =
    responseData.customerService?.insurance?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[MBP_FIELD].value =
    responseData.customerService?.insurance?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[VTL_FIELD].value =
    responseData.customerService?.insurance?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[ITL_FIELD].value =
    responseData.customerService?.insurance?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[RDI_FIELD].value =
    responseData.customerService?.insurance?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INSURANCE].fields[GTL_FIELD].value =
    responseData.customerService?.insurance?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[VNP_FIELD].value =
    responseData.customerService?.water?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[VNM_FIELD].value =
    responseData.customerService?.water?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[MBP_FIELD].value =
    responseData.customerService?.water?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[VTL_FIELD].value =
    responseData.customerService?.water?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[ITL_FIELD].value =
    responseData.customerService?.water?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[RDI_FIELD].value =
    responseData.customerService?.water?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_WATER].fields[GTL_FIELD].value =
    responseData.customerService?.water?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[VNP_FIELD].value =
    responseData.customerService?.eCommerce?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[VNM_FIELD].value =
    responseData.customerService?.eCommerce?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[MBP_FIELD].value =
    responseData.customerService?.eCommerce?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[VTL_FIELD].value =
    responseData.customerService?.eCommerce?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[ITL_FIELD].value =
    responseData.customerService?.eCommerce?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[RDI_FIELD].value =
    responseData.customerService?.eCommerce?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ECOMMERCE].fields[GTL_FIELD].value =
    responseData.customerService?.eCommerce?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[VNP_FIELD].value =
    responseData.customerService?.bank?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[VNM_FIELD].value =
    responseData.customerService?.bank?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[MBP_FIELD].value =
    responseData.customerService?.bank?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[VTL_FIELD].value =
    responseData.customerService?.bank?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[ITL_FIELD].value =
    responseData.customerService?.bank?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[RDI_FIELD].value =
    responseData.customerService?.bank?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_BANK].fields[GTL_FIELD].value =
    responseData.customerService?.bank?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[VNP_FIELD].value =
    responseData.customerService?.internationalSM?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[VNM_FIELD].value =
    responseData.customerService?.internationalSM?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[MBP_FIELD].value =
    responseData.customerService?.internationalSM?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[VTL_FIELD].value =
    responseData.customerService?.internationalSM?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[ITL_FIELD].value =
    responseData.customerService?.internationalSM?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[RDI_FIELD].value =
    responseData.customerService?.internationalSM?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_INTERNATIONAL_SM].fields[GTL_FIELD].value =
    responseData.customerService?.internationalSM?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[VNP_FIELD].value =
    responseData.customerService?.electricity?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[VNM_FIELD].value =
    responseData.customerService?.electricity?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[MBP_FIELD].value =
    responseData.customerService?.electricity?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[VTL_FIELD].value =
    responseData.customerService?.electricity?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[ITL_FIELD].value =
    responseData.customerService?.electricity?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[RDI_FIELD].value =
    responseData.customerService?.electricity?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_ELECTRICITY].fields[GTL_FIELD].value =
    responseData.customerService?.electricity?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[VNP_FIELD].value =
    responseData.customerService?.logistics?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[VNM_FIELD].value =
    responseData.customerService?.logistics?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[MBP_FIELD].value =
    responseData.customerService?.logistics?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[VTL_FIELD].value =
    responseData.customerService?.logistics?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[ITL_FIELD].value =
    responseData.customerService?.logistics?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[RDI_FIELD].value =
    responseData.customerService?.logistics?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_LOGISTICS].fields[GTL_FIELD].value =
    responseData.customerService?.logistics?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[VNP_FIELD].value =
    responseData.customerService?.eWallet?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[VNM_FIELD].value =
    responseData.customerService?.eWallet?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[MBP_FIELD].value =
    responseData.customerService?.eWallet?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[VTL_FIELD].value =
    responseData.customerService?.eWallet?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[ITL_FIELD].value =
    responseData.customerService?.eWallet?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[RDI_FIELD].value =
    responseData.customerService?.eWallet?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_EWALLET].fields[GTL_FIELD].value =
    responseData.customerService?.eWallet?.gtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[VNP_FIELD].value =
    responseData.customerService?.others?.vnp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[VNM_FIELD].value =
    responseData.customerService?.others?.vnm;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[MBP_FIELD].value =
    responseData.customerService?.others?.mbp;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[VTL_FIELD].value =
    responseData.customerService?.others?.vtl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[ITL_FIELD].value =
    responseData.customerService?.others?.itl;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[RDI_FIELD].value =
    responseData.customerService?.others?.rdi;
  resultData.data[CUSTOMER_SERVICE].rows[CUSTOMER_OTHERS].fields[GTL_FIELD].value =
    responseData.customerService?.others?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[VNP_FIELD].value =
    responseData.advertisementService?.medical?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[VNM_FIELD].value =
    responseData.advertisementService?.medical?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[MBP_FIELD].value =
    responseData.advertisementService?.medical?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[VTL_FIELD].value =
    responseData.advertisementService?.medical?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[ITL_FIELD].value =
    responseData.advertisementService?.medical?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[RDI_FIELD].value =
    responseData.advertisementService?.medical?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MEDICAL].fields[GTL_FIELD].value =
    responseData.advertisementService?.medical?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[VNP_FIELD].value =
    responseData.advertisementService?.eductionEnrollment?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[VNM_FIELD].value =
    responseData.advertisementService?.eductionEnrollment?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[MBP_FIELD].value =
    responseData.advertisementService?.eductionEnrollment?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[VTL_FIELD].value =
    responseData.advertisementService?.eductionEnrollment?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[ITL_FIELD].value =
    responseData.advertisementService?.eductionEnrollment?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[RDI_FIELD].value =
    responseData.advertisementService?.eductionEnrollment?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_EDUCATION_ENROLLMENT].fields[GTL_FIELD].value =
    responseData.advertisementService?.eductionEnrollment?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[VNP_FIELD].value =
    responseData.advertisementService?.eCommerce?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[VNM_FIELD].value =
    responseData.advertisementService?.eCommerce?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[MBP_FIELD].value =
    responseData.advertisementService?.eCommerce?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[VTL_FIELD].value =
    responseData.advertisementService?.eCommerce?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[ITL_FIELD].value =
    responseData.advertisementService?.eCommerce?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[RDI_FIELD].value =
    responseData.advertisementService?.eCommerce?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ECOMMERCE].fields[GTL_FIELD].value =
    responseData.advertisementService?.eCommerce?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[VNP_FIELD].value =
    responseData.advertisementService?.bank?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[VNM_FIELD].value =
    responseData.advertisementService?.bank?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[MBP_FIELD].value =
    responseData.advertisementService?.bank?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[VTL_FIELD].value =
    responseData.advertisementService?.bank?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[ITL_FIELD].value =
    responseData.advertisementService?.bank?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[RDI_FIELD].value =
    responseData.advertisementService?.bank?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_BANK].fields[GTL_FIELD].value =
    responseData.advertisementService?.bank?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[VNP_FIELD].value =
    responseData.advertisementService?.realEstate?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[VNM_FIELD].value =
    responseData.advertisementService?.realEstate?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[MBP_FIELD].value =
    responseData.advertisementService?.realEstate?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[VTL_FIELD].value =
    responseData.advertisementService?.realEstate?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[ITL_FIELD].value =
    responseData.advertisementService?.realEstate?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[RDI_FIELD].value =
    responseData.advertisementService?.realEstate?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_REAL_ESTATE].fields[GTL_FIELD].value =
    responseData.advertisementService?.realEstate?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[VNP_FIELD].value =
    responseData.advertisementService?.gas?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[VNM_FIELD].value =
    responseData.advertisementService?.gas?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[MBP_FIELD].value =
    responseData.advertisementService?.gas?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[VTL_FIELD].value =
    responseData.advertisementService?.gas?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[ITL_FIELD].value =
    responseData.advertisementService?.gas?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[RDI_FIELD].value =
    responseData.advertisementService?.gas?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GAS].fields[GTL_FIELD].value =
    responseData.advertisementService?.gas?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[VNP_FIELD].value =
    responseData.advertisementService?.chemical?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[VNM_FIELD].value =
    responseData.advertisementService?.chemical?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[MBP_FIELD].value =
    responseData.advertisementService?.chemical?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[VTL_FIELD].value =
    responseData.advertisementService?.chemical?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[ITL_FIELD].value =
    responseData.advertisementService?.chemical?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[RDI_FIELD].value =
    responseData.advertisementService?.chemical?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_CHEMICAL].fields[GTL_FIELD].value =
    responseData.advertisementService?.chemical?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[VNP_FIELD].value =
    responseData.advertisementService?.hotel?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[VNM_FIELD].value =
    responseData.advertisementService?.hotel?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[MBP_FIELD].value =
    responseData.advertisementService?.hotel?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[VTL_FIELD].value =
    responseData.advertisementService?.hotel?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[ITL_FIELD].value =
    responseData.advertisementService?.hotel?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[RDI_FIELD].value =
    responseData.advertisementService?.hotel?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_HOTEL].fields[GTL_FIELD].value =
    responseData.advertisementService?.hotel?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[VNP_FIELD].value =
    responseData.advertisementService?.kara?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[VNM_FIELD].value =
    responseData.advertisementService?.kara?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[MBP_FIELD].value =
    responseData.advertisementService?.kara?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[VTL_FIELD].value =
    responseData.advertisementService?.kara?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[ITL_FIELD].value =
    responseData.advertisementService?.kara?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[RDI_FIELD].value =
    responseData.advertisementService?.kara?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_KARA].fields[GTL_FIELD].value =
    responseData.advertisementService?.kara?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[VNP_FIELD].value =
    responseData.advertisementService?.guard?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[VNM_FIELD].value =
    responseData.advertisementService?.guard?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[MBP_FIELD].value =
    responseData.advertisementService?.guard?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[VTL_FIELD].value =
    responseData.advertisementService?.guard?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[ITL_FIELD].value =
    responseData.advertisementService?.guard?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[RDI_FIELD].value =
    responseData.advertisementService?.guard?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_GUARD].fields[GTL_FIELD].value =
    responseData.advertisementService?.guard?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[VNP_FIELD].value =
    responseData.advertisementService?.restaurant?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[VNM_FIELD].value =
    responseData.advertisementService?.restaurant?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[MBP_FIELD].value =
    responseData.advertisementService?.restaurant?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[VTL_FIELD].value =
    responseData.advertisementService?.restaurant?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[ITL_FIELD].value =
    responseData.advertisementService?.restaurant?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[RDI_FIELD].value =
    responseData.advertisementService?.restaurant?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RESTAURANT].fields[GTL_FIELD].value =
    responseData.advertisementService?.restaurant?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[VNP_FIELD].value =
    responseData.advertisementService?.foodDrink?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[VNM_FIELD].value =
    responseData.advertisementService?.foodDrink?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[MBP_FIELD].value =
    responseData.advertisementService?.foodDrink?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[VTL_FIELD].value =
    responseData.advertisementService?.foodDrink?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[ITL_FIELD].value =
    responseData.advertisementService?.foodDrink?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[RDI_FIELD].value =
    responseData.advertisementService?.foodDrink?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FOOD_DRINK].fields[GTL_FIELD].value =
    responseData.advertisementService?.foodDrink?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[VNP_FIELD].value =
    responseData.advertisementService?.tourism?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[VNM_FIELD].value =
    responseData.advertisementService?.tourism?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[MBP_FIELD].value =
    responseData.advertisementService?.tourism?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[VTL_FIELD].value =
    responseData.advertisementService?.tourism?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[ITL_FIELD].value =
    responseData.advertisementService?.tourism?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[RDI_FIELD].value =
    responseData.advertisementService?.tourism?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TOURISM].fields[GTL_FIELD].value =
    responseData.advertisementService?.tourism?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[VNP_FIELD].value =
    responseData.advertisementService?.pharmaceuticals?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[VNM_FIELD].value =
    responseData.advertisementService?.pharmaceuticals?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[MBP_FIELD].value =
    responseData.advertisementService?.pharmaceuticals?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[VTL_FIELD].value =
    responseData.advertisementService?.pharmaceuticals?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[ITL_FIELD].value =
    responseData.advertisementService?.pharmaceuticals?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[RDI_FIELD].value =
    responseData.advertisementService?.pharmaceuticals?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_PHARMACEUTICALS].fields[GTL_FIELD].value =
    responseData.advertisementService?.pharmaceuticals?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[VNP_FIELD].value =
    responseData.advertisementService?.transport?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[VNM_FIELD].value =
    responseData.advertisementService?.transport?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[MBP_FIELD].value =
    responseData.advertisementService?.transport?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[VTL_FIELD].value =
    responseData.advertisementService?.transport?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[ITL_FIELD].value =
    responseData.advertisementService?.transport?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[RDI_FIELD].value =
    responseData.advertisementService?.transport?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_TRANSPORT].fields[GTL_FIELD].value =
    responseData.advertisementService?.transport?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[VNP_FIELD].value =
    responseData.advertisementService?.fashion?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[VNM_FIELD].value =
    responseData.advertisementService?.fashion?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[MBP_FIELD].value =
    responseData.advertisementService?.fashion?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[VTL_FIELD].value =
    responseData.advertisementService?.fashion?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[ITL_FIELD].value =
    responseData.advertisementService?.fashion?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[RDI_FIELD].value =
    responseData.advertisementService?.fashion?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_FASHION].fields[GTL_FIELD].value =
    responseData.advertisementService?.fashion?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[VNP_FIELD].value =
    responseData.advertisementService?.mall?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[VNM_FIELD].value =
    responseData.advertisementService?.mall?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[MBP_FIELD].value =
    responseData.advertisementService?.mall?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[VTL_FIELD].value =
    responseData.advertisementService?.mall?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[ITL_FIELD].value =
    responseData.advertisementService?.mall?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[RDI_FIELD].value =
    responseData.advertisementService?.mall?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_MALL].fields[GTL_FIELD].value =
    responseData.advertisementService?.mall?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[VNP_FIELD].value =
    responseData.advertisementService?.cosmetics?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[VNM_FIELD].value =
    responseData.advertisementService?.cosmetics?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[MBP_FIELD].value =
    responseData.advertisementService?.cosmetics?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[VTL_FIELD].value =
    responseData.advertisementService?.cosmetics?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[ITL_FIELD].value =
    responseData.advertisementService?.cosmetics?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[RDI_FIELD].value =
    responseData.advertisementService?.cosmetics?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_COSMETICS].fields[GTL_FIELD].value =
    responseData.advertisementService?.cosmetics?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[VNP_FIELD].value =
    responseData.advertisementService?.entertainment?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[VNM_FIELD].value =
    responseData.advertisementService?.entertainment?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[MBP_FIELD].value =
    responseData.advertisementService?.entertainment?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[VTL_FIELD].value =
    responseData.advertisementService?.entertainment?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[ITL_FIELD].value =
    responseData.advertisementService?.entertainment?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[RDI_FIELD].value =
    responseData.advertisementService?.entertainment?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_ENTERTAINMENT].fields[GTL_FIELD].value =
    responseData.advertisementService?.entertainment?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[VNP_FIELD].value =
    responseData.advertisementService?.recruitment?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[VNM_FIELD].value =
    responseData.advertisementService?.recruitment?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[MBP_FIELD].value =
    responseData.advertisementService?.recruitment?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[VTL_FIELD].value =
    responseData.advertisementService?.recruitment?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[ITL_FIELD].value =
    responseData.advertisementService?.recruitment?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[RDI_FIELD].value =
    responseData.advertisementService?.recruitment?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_RECRUITMENT].fields[GTL_FIELD].value =
    responseData.advertisementService?.recruitment?.gtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[VNP_FIELD].value =
    responseData.advertisementService?.others?.vnp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[VNM_FIELD].value =
    responseData.advertisementService?.others?.vnm;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[MBP_FIELD].value =
    responseData.advertisementService?.others?.mbp;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[VTL_FIELD].value =
    responseData.advertisementService?.others?.vtl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[ITL_FIELD].value =
    responseData.advertisementService?.others?.itl;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[RDI_FIELD].value =
    responseData.advertisementService?.others?.rdi;
  resultData.data[ADVERTISEMENT_SERVICE].rows[ADS_OTHERS].fields[GTL_FIELD].value =
    responseData.advertisementService?.others?.gtl;
  return resultData;
};

export const getListPriceZNSValue = (responseData: any) => {
  const resultData: TZNSWrapper = cloneDeep(initListPriceValueZNS);
  resultData.platformCost = responseData.platformCost;
  resultData.data[SYSTEM_SERVICE].rows[CONFIG_SYSTEM].fields[ZNS_VALUE].value =
    responseData.systemService?.configSystem?.price;
  resultData.data[SYSTEM_SERVICE].rows[INIT_OA].fields[ZNS_VALUE].value =
    responseData.systemService?.initOA?.price;
  resultData.data[SYSTEM_SERVICE].rows[MAINTAIN_ADVANCE_OA].fields[ZNS_VALUE].value =
    responseData.systemService?.maintainAdvanceOA?.price;
  resultData.data[SYSTEM_SERVICE].rows[MAINTAIN_PREMIUM_OA].fields[ZNS_VALUE].value =
    responseData.systemService?.maintainPremiumOA?.price;
  resultData.data[ZNS_SERVICE].rows[TEXT].fields[ZNS_VALUE].value =
    responseData.znsService?.textFormat?.price;
  resultData.data[ZNS_SERVICE].rows[TABLE].fields[ZNS_VALUE].value =
    responseData.znsService?.tableFormat?.price;
  resultData.data[ZNS_SERVICE].rows[OTP].fields[ZNS_VALUE].value =
    responseData.znsService?.otpFormat?.price;
  resultData.data[ZNS_SERVICE].rows[ASSESSMENT].fields[ZNS_VALUE].value =
    responseData.znsService?.assessmentFormat?.price;
  resultData.data[ZNS_SERVICE].rows[PAYMENT].fields[ZNS_VALUE].value =
    responseData.znsService?.paymentFormat?.price;
  resultData.data[ZNS_SERVICE].rows[VOUCHER].fields[ZNS_VALUE].value =
    responseData.znsService?.voucherFormat?.price;
  resultData.data[ZNS_SERVICE].rows[ADMINISTRATIVE].fields[ZNS_VALUE].value =
    responseData.znsService?.administrativeFormat?.price;
  resultData.data[CTA_SERVICE].rows[BUSINESS_SITE].fields[ZNS_VALUE].value =
    responseData.ctaService?.businessSite?.price;
  resultData.data[CTA_SERVICE].rows[CONTACT].fields[ZNS_VALUE].value =
    responseData.ctaService?.contact?.price;
  resultData.data[CTA_SERVICE].rows[OA_SITE].fields[ZNS_VALUE].value =
    responseData.ctaService?.oasite?.price;
  resultData.data[CTA_SERVICE].rows[BUSINESS_MA_SITE].fields[ZNS_VALUE].value =
    responseData.ctaService?.businessMASite?.price;
  resultData.data[CTA_SERVICE].rows[PRODUCT_DISTRIBUTED_SITE].fields[ZNS_VALUE].value =
    responseData.ctaService?.productDistributedSite?.price;
  resultData.data[CTA_SERVICE].rows[ZALO_MA_SITE].fields[ZNS_VALUE].value =
    responseData.ctaService?.zaloMASite?.price;
  resultData.data[CTA_SERVICE].rows[APP_INSTALL_SITE].fields[ZNS_VALUE].value =
    responseData.ctaService?.appInstallSite?.price;
  resultData.data[CTA_SERVICE].rows[OTHERS_APP_INSTALL_SITE].fields[ZNS_VALUE].value =
    responseData.ctaService?.othersAppInstallSite?.price;
  resultData.data[CTA_SERVICE].rows[OA_POST].fields[ZNS_VALUE].value =
    responseData.ctaService?.oapost?.price;
  resultData.data[SPECIFIC_SERVICE].rows[IMAGE].fields[ZNS_VALUE].value =
    responseData.specificService?.image?.price;
  return resultData;
};
