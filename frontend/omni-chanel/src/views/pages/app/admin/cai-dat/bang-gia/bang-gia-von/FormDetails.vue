<script setup lang="ts">
import { inject, onMounted, ref, watch } from 'vue';
import * as yup from 'yup';
import { useApi } from '@/store/useApi';
import { useForm } from 'vee-validate';
import { useRouter } from 'vue-router';
import { color } from '@/constants/statusColor';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import PopupCancelConfirm from '@/components/base/common/PopupCancelConfirm.vue';
import { ROUTE_NAME, ROUTE_PATH, TabValue, TEXT } from '@/shared';
import { initCostPriceValueSMS, initCostPriceValueZNS } from './index.constants';
import SMSBoard from '../components/SMSBoard.vue';
import ZNSBoard from '../components/ZNSBoard.vue';
import { ChannelValue, type TSMSWrapper, type TTab, type TZNSWrapper } from '../index.type';
import { cloneDeep } from 'lodash';
import PopupAudit from '../components/PopupAudit.vue';
import BaseTabs from '../components/BaseTabs.vue';
import { getCostPriceSMSValue, getCostPriceZNSValue } from './index.utils';
import { PriceType, ServiceType } from '../index.constants';
import { AccountType, PageType } from '@/enums/common';
import { generateImage, getPriceBoardFileName } from '../index.utils';
import { useTab } from '@/store/useTab';

const props = defineProps<{
  type: PageType;
  accountType: AccountType;
}>();

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const haveClickedCancel = ref(false);
const tabStore = useTab();

//#region Datasource
const oldSmsDataSource = ref<TSMSWrapper>(cloneDeep(initCostPriceValueSMS));
const smsDataSource = ref<TSMSWrapper>(cloneDeep(initCostPriceValueSMS));
const oldZnsDataSource = ref<TZNSWrapper>(cloneDeep(initCostPriceValueZNS));
const znsDataSource = ref<TZNSWrapper>(cloneDeep(initCostPriceValueZNS));

const mapDataSMS = (responseData: any) => {
  const capitalData = responseData.capital;
  oldSmsDataSource.value = cloneDeep(getCostPriceSMSValue(capitalData));
  smsDataSource.value = cloneDeep(getCostPriceSMSValue(capitalData));
  setTimeout(() => {
    smsBoardRef.value?.resetFields();
  }, 100);
};

const mapDataZNS = (responseData: any) => {
  const capitalData = responseData.capital;
  oldZnsDataSource.value = cloneDeep(getCostPriceZNSValue(capitalData));
  znsDataSource.value = cloneDeep(getCostPriceZNSValue(capitalData));
  setTimeout(() => {
    znsBoardRef.value?.resetFields();
  }, 100);
};

const mapDataDetails = (tab: TabValue, responseData: any) => {
  if (tab === TabValue.Tab1) {
    mapDataSMS(responseData);
  } else if (tab === TabValue.Tab2) {
    mapDataZNS(responseData);
  }
};

const getDetail = async function (tab: TabValue = activeTab.value) {
  overlayLoading.toggleLoading(true);
  let url = `business/v1/api/price-list/sms`;
  if (tab === TabValue.Tab2) {
    url = `business/v1/api/price-list/zns`;
  }
  const { data } = await api.get(url);
  if (data.code === 0) {
    mapDataDetails(tab, data.data);
  } else {
    toast('error', data.message);
  }
  overlayLoading.toggleLoading(false);
};
//#endregion

//#region Form
const smsBoardRef = ref();
const znsBoardRef = ref();
const validationSchema = yup.object().shape({});

const { handleSubmit } = useForm({
  validationSchema,
});

const handleSubmitSMS = async () => {
  await smsBoardRef.value.onSubmit();
};

const handleSubmitZNS = async () => {
  await znsBoardRef.value.onSubmit();
};

const handleUploadFile = async (datetime: any) => {
  try {
    overlayLoading.toggleLoading(true);
    const fileName = getPriceBoardFileName(
      PriceType.CostPrice,
      activeTab.value === TabValue.Tab1 ? ChannelValue.SMS : ChannelValue.ZNS,
    );
    const fileImage = await generateImage(
      activeTab.value === TabValue.Tab1 ? smsBoardRef.value?.element : znsBoardRef.value?.element,
      fileName,
      datetime,
    );
    if (fileImage) {
      const formData = new FormData();
      formData.append('file', fileImage);
      const res = await api.post('business/v1/api/common/file/upload?type=40', formData, {
        skipToastErr: true,
      });
      if (res.data.code === 0) {
        return res.data.data?.file_upload_id;
      } else {
        toast('error', res.data.message);
        return false;
      }
    }
    overlayLoading.toggleLoading(false);
  } catch (err: any) {
    console.error(err);
    toast('error', TEXT.ERROR_OCCURRED);
    overlayLoading.toggleLoading(false);
  }
};

const handleSubmitFormSMS = async (payload: any) => {
  const now = new Date();
  overlayLoading.toggleLoading(true);
  const fileUploadId = await handleUploadFile(now);
  if (!fileUploadId) {
    overlayLoading.toggleLoading(false);
    return;
  }
  const { data } = await api.put(
    `business/v1/api/price-list/sms?logFileId=${fileUploadId}&createdAt=${now.getTime()}`,
    payload,
  );
  if (data.code === 0) {
    moveToDetail();
    toast('success', data.message);
  } else {
    toast('error', data.message);
  }
  overlayLoading.toggleLoading(false);
};

const handleSubmitFormZNS = async (payload: any) => {
  const now = new Date();
  overlayLoading.toggleLoading(true);
  const fileUploadId = await handleUploadFile(now);
  if (!fileUploadId) {
    overlayLoading.toggleLoading(false);
    return;
  }
  const { data } = await api.put(
    `business/v1/api/price-list/zns?logFileId=${fileUploadId}&createdAt=${now.getTime()}`,
    payload,
  );
  if (data.code === 0) {
    moveToDetail();
    toast('success', data.message);
  } else {
    toast('error', data.message);
  }
  overlayLoading.toggleLoading(false);
};

const onSubmit = handleSubmit(() => {
  overlayLoading.toggleLoading(true);
  setTimeout(() => {
    if (activeTab.value === TabValue.Tab1) {
      handleSubmitSMS();
    } else if (activeTab.value === TabValue.Tab2) {
      handleSubmitZNS();
    }
  }, 100);
});
//#endregion

//#region Audit logs
const isVisibleHistoriesPopup = ref(false);

const clickViewAuditLog = async () => {
  isVisibleHistoriesPopup.value = true;
};
//#endregion

//#region Cancel validation
const isVisibleCancelPopup = ref(false);

const handleCloseCancel = () => {
  isVisibleCancelPopup.value = false;
};

const handleConfirmCancel = () => {
  isVisibleCancelPopup.value = false;
  if (selectedTab.value !== activeTab.value) {
    if (!haveClickedCancel.value) {
      activeTab.value = selectedTab.value;
      oldTab.value = selectedTab.value;
      tabStore.setCostAndListPriceTab(selectedTab.value);
    }
    moveToDetail();
  } else {
    moveToDetail();
  }
};
//#endregion

//#region Router
const router = useRouter();

const moveToUpdate = () => {
  router.push({
    name: ROUTE_NAME.UPDATE_COST_PRICE,
  });
};

const moveToDetail = () => {
  router.push({
    name: ROUTE_NAME.DETAILS_COST_PRICE,
  });
};
//#endregion

//#region Tab
const oldTab = ref<TabValue | undefined>(TabValue.Tab1);
const activeTab = ref<TabValue>(TabValue.Tab1);
const selectedTab = ref<TabValue>(TabValue.Tab1);

const tabs: TTab[] = [
  {
    label: 'SMS',
    value: TabValue.Tab1,
  },
  {
    label: 'ZNS',
    value: TabValue.Tab2,
  },
];

const isChangedValue = async () => {
  if (activeTab.value === TabValue.Tab1 && smsBoardRef.value) {
    await smsBoardRef.value?.updateDatasource();
    return JSON.stringify(oldSmsDataSource.value) !== JSON.stringify(smsDataSource.value);
  }
  if (znsBoardRef.value) {
    await znsBoardRef.value?.updateDatasource();
    return JSON.stringify(oldZnsDataSource.value) !== JSON.stringify(znsDataSource.value);
  }
  return true;
};

const handleTabChanged = async (tabValue: TabValue) => {
  haveClickedCancel.value = false;
  if (tabValue === oldTab.value) return;
  if (props.type !== PageType.Details && (await isChangedValue())) {
    isVisibleCancelPopup.value = true;
    selectedTab.value = tabValue;
  } else {
    activeTab.value = tabValue;
    oldTab.value = tabValue;
    tabStore.setCostAndListPriceTab(tabValue);
    if (props.type === PageType.Details) {
      getDetail(tabValue);
    } else {
      moveToDetail();
    }
  }
};

const handleClickCancel = async () => {
  haveClickedCancel.value = true;
  if (await isChangedValue()) {
    isVisibleCancelPopup.value = true;
  } else moveToDetail();
};
//#endregion

//#region Watcher
watch(
  () => props.type,
  (value) => {
    if (value === PageType.Details) {
      getDetail();
    }
  },
);
//#endregion

onMounted(() => {
  activeTab.value = tabStore.costAndListPriceTab;
  oldTab.value = undefined;
  getDetail(activeTab.value);
});
</script>

<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:settings" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>{{ 'Cài đặt' }}</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: ROUTE_PATH.DETAILS_COST_PRICE }"
          >Bảng giá vốn</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <VElementButton
      icon="history"
      label="Xem lịch sử chỉnh sửa"
      :bgColor="color.main"
      @click="clickViewAuditLog"
    />
  </div>
  <div class="overflow-y-auto w-full" :class="'view-height'">
    <BaseTabs :tabs :active-tab="activeTab" tabClass="w-[90%]" @click-tab="handleTabChanged">
      <template #tab-1>
        <SMSBoard
          ref="smsBoardRef"
          note="Lưu ý: Giá khai báo phải bao gồm VAT"
          :type
          :activeTab
          :price-type="PriceType.CostPrice"
          v-model:datasource="smsDataSource"
          @onSubmitForm="handleSubmitFormSMS"
        />
      </template>
      <template #tab-2>
        <ZNSBoard
          ref="znsBoardRef"
          note="Lưu ý: Giá khai báo phải bao gồm VAT"
          :type
          :activeTab
          :price-type="PriceType.CostPrice"
          v-model:datasource="znsDataSource"
          @onSubmitForm="handleSubmitFormZNS"
        />
      </template>
    </BaseTabs>
  </div>
  <div
    class="flex z-[999] justify-end py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke"
  >
    <VElementButton
      v-if="props.type === PageType.Update && accountType !== AccountType.Agent"
      label="Hủy"
      styleButton="s"
      :bgColor="color.closeButton"
      @click="handleClickCancel"
    />
    <VElementButton
      v-if="props.type === PageType.Update && accountType !== AccountType.Agent"
      label="Lưu"
      styleButton="s"
      :bgColor="color.main"
      @click="onSubmit"
    />
    <VElementButton
      v-else-if="accountType !== AccountType.Agent"
      styleButton="s"
      :bgColor="color.main"
      :label="props.type === PageType.Details ? 'Cập nhật' : 'Lưu'"
      @click="moveToUpdate"
    />
  </div>
  <PopupCancelConfirm
    v-model:popupVisible="isVisibleCancelPopup"
    @onClose="handleCloseCancel"
    @onConfirm="handleConfirmCancel"
  />
  <PopupAudit
    :serviceType="activeTab === TabValue.Tab1 ? ServiceType.SMS : ServiceType.ZNS"
    :adminView="accountType !== AccountType.Agent ? false : true"
    :priceType="PriceType.CostPrice"
    :visible="isVisibleHistoriesPopup"
    :use-click-hook="accountType === AccountType.Agent ? true : false"
    @onClose="isVisibleHistoriesPopup = false"
  />
</template>

<style lang="scss" scoped>
.view-height {
  height: calc(100vh - 215px);
  margin-bottom: 40px;
}
</style>
