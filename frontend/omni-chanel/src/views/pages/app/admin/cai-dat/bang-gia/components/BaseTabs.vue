<script setup lang="ts">
import { TabValue } from '@/shared';
import type { TTab } from '../index.type';

const props = defineProps<{
  tabs: TTab[];
  tabClass?: string;
}>();

const emit = defineEmits<(e: 'clickTab', tabValue: TabValue) => void>();

const activeTab = defineModel<TabValue>('activeTab');
</script>

<template>
  <div>
    <div
      class="flex items-center bg-[#FBFAFF] w-full border-b border-[#DFE4EA] shadow-sm mb-[10px]"
    >
      <div
        v-for="tab of props.tabs"
        :key="tab.value"
        class="flex items-center justify-center min-w-[95px] px-[14px] h-[44px] font-semibold cursor-pointer text-[#A3A3A3] hover:text-[#3758F9] hover:bg-[#4361ff1a]"
        :class="`${activeTab === tab.value ? '!text-[#3758F9] bg-[#4361ff1a] border-b-[2px] border-[#3758F9]' : ''}`"
        @click="emit('clickTab', tab.value)"
      >
        {{ tab.label }}
      </div>
    </div>
    <div class="w-full flex items-center justify-center">
      <div
        v-for="tab of props.tabs"
        :key="tab.value"
        v-show="activeTab === tab.value"
        :class="tabClass"
      >
        <slot :name="`tab-${tab.value}`" />
      </div>
    </div>
  </div>
</template>
