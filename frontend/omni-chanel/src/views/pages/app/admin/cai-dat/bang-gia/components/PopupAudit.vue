<script setup lang="ts">
import { color } from '@/constants/statusColor';
import { inject, reactive, ref, watch } from 'vue';
import VOldTable from '@/components/base/VOldTable.vue';
import moment from 'moment';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useApi } from '@/store/useApi';
import {
  popupAuditLogPt,
  popupAuditLogStyle,
  auditLogHeaders,
  auditLogStyle,
  AccountType,
  PriceType,
  ServiceType,
  popupCostPriceAuditLogPt,
  popupCostPriceAuditLogStyle,
} from '../index.constants';
import { useRouter } from 'vue-router';
import { ROUTE_NAME, TabValue } from '@/shared';
import VDownLoadFilePrice from './VDownLoadFilePrice.vue';
import type { CustomerType, TSMSWrapper, TZNSWrapper } from '../index.type';
import SMSBoard from './SMSBoard.vue';
import { PageType } from '@/enums/common';
import { cloneDeep } from 'lodash';
import { initCostPriceValueSMS, initCostPriceValueZNS } from '../bang-gia-von/index.constants';
import { getCostPriceSMSValue, getCostPriceZNSValue } from '../bang-gia-von/index.utils';
import ZNSBoard from './ZNSBoard.vue';

const props = defineProps<{
  visible: boolean;
  customerType?: CustomerType;
  customerId?: number;
  adminView: boolean;
  priceType: PriceType;
  serviceType: ServiceType;
  useClickHook?: boolean;
}>();

const emit = defineEmits(['onClose']);

//#region Table
const api = useApi();
const toast = inject('toast') as any;
const smsBoardRef = ref();
const znsBoardRef = ref();
const smsDataSource = ref<TSMSWrapper>(cloneDeep(initCostPriceValueSMS));
const znsDataSource = ref<TZNSWrapper>(cloneDeep(initCostPriceValueZNS));
const visibleCostPrice = ref<boolean>(false);

const mapDataSMS = (responseData: any) => {
  smsDataSource.value = cloneDeep(getCostPriceSMSValue(responseData));
  setTimeout(() => {
    smsBoardRef.value?.resetFields();
  }, 100);
};

const mapDataZNS = (responseData: any) => {
  znsDataSource.value = cloneDeep(getCostPriceZNSValue(responseData));
  setTimeout(() => {
    znsBoardRef.value?.resetFields();
  }, 100);
};

const mapDataDetails = (responseData: any) => {
  if (props.serviceType === ServiceType.SMS) {
    mapDataSMS(responseData);
  } else if (props.serviceType === ServiceType.ZNS) {
    mapDataZNS(responseData);
  }
};

const getDetail = async function (id: number) {
  visibleCostPrice.value = true;
  overlayLoading.toggleLoading(true);
  const { data } = await api.get(`business/v1/api/price-list/changes/${id}`);
  if (data.code === 0) {
    mapDataDetails(data.data);
  } else {
    toast('error', data.message);
  }
  overlayLoading.toggleLoading(false);
};

const params = reactive({
  page: 1,
  size: 20,
});

const pageInfo = reactive<any>({
  currentPage: null,
  total_pages: null,
  total_records: null,
});

const tableRef = ref();
const auditDatas = ref<any[]>([]);
const overlayLoading = useOverLayLoadingStore();

const onPageChange = (page: number) => {
  params.page = page;
  getList();
};

const onPerPageChange = (size: number) => {
  params.size = size;
};

const sortTable = () => {
  getList();
};
//#endregion

//#region Datasource
const getList = async () => {
  overlayLoading.toggleLoading(true);
  let url = 'business/v1/api/price-list/changes';
  let requestParams: any = {
    customerId: props.customerId,
    customerType: props.customerType,
    page: params.page - 1,
    size: params.size,
    type: props.priceType - 1,
    serviceType: props.serviceType,
  };
  if (props.adminView) {
    url = 'business/v1/api/price-list/admin-changes';
    requestParams = {
      page: params.page - 1,
      size: params.size,
      serviceType: props.serviceType,
    };
  }
  const api = useApi();
  const { data } = await api.get(url, {
    params: {
      ...requestParams,
    },
  });
  if (data.code === 0) {
    params.page = 1;
    params.size = 20;
    auditDatas.value = data.data?.content;
    pageInfo.total_pages = data?.data?.totalPages ?? 0;
    pageInfo.total_records = data.data?.totalElements ?? 0;
  }
  overlayLoading.toggleLoading(false);
};
//#endregion

//#region Router
const router = useRouter();

const navigateToDetailsAccount = (accountId: number, accountType: number) => {
  if (accountType === AccountType.Admin) {
    router.push({
      name: ROUTE_NAME.ACCOUNT_ADMIN_DETAILS,
      params: {
        id: accountId,
      },
    });
  } else {
    router.push({
      name: ROUTE_NAME.ACCOUNT_AGENCY_DETAILS,
      params: {
        id: accountId,
      },
    });
  }
};
//#endregion

watch(
  () => props.visible,
  async (newValue) => {
    if (newValue) {
      await getList();
    }
  },
);
</script>

<template>
  <VDialog
    modal
    ref="refDialog"
    header="Lịch sử chỉnh sửa"
    :visible="visible"
    :draggable="false"
    :pt="popupAuditLogPt"
    :style="popupAuditLogStyle"
    @update:visible="emit('onClose')"
  >
    <div class="mt-[20px]">
      <VOldTable
        ref="tableRef"
        idTable="audit-log-table"
        class="!p-0 mb-[40px]"
        :useOrder="false"
        :rows="auditDatas"
        :isCursorRow="false"
        :headers="auditLogHeaders"
        :styleHeaders="auditLogStyle"
        :showAction="false"
        :pagination="{
          totalPage: pageInfo.total_pages ?? 0,
          total: pageInfo.total_records ?? 0,
          perPage: params.size ?? 0,
        }"
        @pageChanged="onPageChange"
        @perPageChange="onPerPageChange"
        @sort-table="sortTable"
      >
        <template v-slot:items="{ row }">
          <td class="text-primaryText px-2">
            <div class="column-container text-center">
              {{ moment(row?.createdAt).format('DD/MM/YYYY HH:mm') }}
            </div>
          </td>
          <td class="text-primaryText px-2">
            <div
              :id="`campaign-link-${row.accountId}`"
              :title="row.account"
              class="column-container text-main hover:underline hover:text-[#3a658a] cursor-pointer max-w-[150px]"
              @click="navigateToDetailsAccount(row.accountId, row.accountType)"
            >
              {{ row.account }}
            </div>
          </td>
          <td class="text-primaryText px-2">
            <div class="column-container text-center">
              {{ row?.channel }}
            </div>
          </td>
          <td class="text-primaryText px-2">
            <div class="column-container flex items-center justify-center">
              <VDownLoadFilePrice
                :label="''"
                :url="row?.fileViewUrl"
                :useClickHook="useClickHook"
                @on-click="() => getDetail(row.id)"
              />
            </div>
          </td>
        </template>
      </VOldTable>
      <div class="save-container flex justify-center items-center gap-5">
        <VElementButton label="Đóng" :bgColor="color.closeButton" @click="emit('onClose')" />
      </div>
    </div>
  </VDialog>
  <VDialog
    modal
    header="Lịch sử chỉnh sửa"
    :visible="visibleCostPrice"
    :draggable="false"
    :pt="popupCostPriceAuditLogPt"
    :style="popupCostPriceAuditLogStyle"
    @update:visible="visibleCostPrice = false"
  >
    <div class="my-[10px]">
      <div class="h-[680px] overflow-auto">
        <SMSBoard
          v-if="serviceType === ServiceType.SMS"
          ref="smsBoardRef"
          note="Lưu ý: Giá khai báo phải bao gồm VAT"
          :type="PageType.Details"
          :activeTab="TabValue.Tab1"
          :price-type="PriceType.CostPrice"
          v-model:datasource="smsDataSource"
        />
        <ZNSBoard
          v-if="serviceType === ServiceType.ZNS"
          ref="znsBoardRef"
          note="Lưu ý: Giá khai báo phải bao gồm VAT"
          :type="PageType.Details"
          :activeTab="TabValue.Tab1"
          :price-type="PriceType.CostPrice"
          v-model:datasource="znsDataSource"
        />
      </div>
      <div class="save-container flex justify-center items-center gap-5">
        <VElementButton
          label="Đóng"
          :bgColor="color.closeButton"
          @click="visibleCostPrice = false"
        />
      </div>
    </div>
  </VDialog>
</template>

<style scoped>
:deep(.v-old-table-container) {
  height: 350px;
}

:deep(.p-dialog-content) {
  overflow-y: hidden !important;
}
</style>
