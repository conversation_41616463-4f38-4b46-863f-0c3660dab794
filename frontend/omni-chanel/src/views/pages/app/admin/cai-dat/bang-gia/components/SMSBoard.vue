<script setup lang="ts">
import type { T<PERSON><PERSON><PERSON>, TSMSWrapper } from '../index.type';
import { PLACEHOLDER, REGEX, TabValue } from '@/shared';
import { Icon } from '@iconify/vue';
import { ElInput, ElTooltip } from 'element-plus';
import { ref, watch } from 'vue';
import { getCostPriceSMSPayload } from '../bang-gia-von/index.utils';
import { PriceType } from '../index.constants';
import { getListPriceSMSPayload } from '../bang-gia-niem-yet/index.utils';
import { AccountType, PageType } from '@/enums/common';
import { getSellingPriceSMSPayload } from '../bang-gia-ban/index.utils';
import { isFalsyButNotZero } from '@/utils/validate';

const props = defineProps<{
  type: PageType;
  note?: string;
  activeTab: TabValue;
  priceType: PriceType;
  capitalData?: TSMSWrapper;
  fixedData?: TSMSWrapper;
  accountType?: AccountType;
}>();

const emit = defineEmits(['onSubmitForm']);

const smsBoardRef = ref();
const datasource = defineModel<TSMSWrapper>('datasource');
const platformValue = ref(datasource.value?.platformCost);
const platformError = ref('');
const accType = props.accountType || localStorage.getItem('accountType') || AccountType.Admin;

const isShowPriceField = (item: TSMSBlock, indexField: number) => {
  return props.priceType !== PriceType.CostPrice && item.hasField && indexField === 1;
};

const inputValues = ref(
  datasource.value?.data.map((item) =>
    item.rows.map((row) => row.fields.map((field) => field.value)),
  ) ?? [],
);
const inputErrors = ref(
  datasource.value?.data.map((item) =>
    item.rows.map((row) => row.fields.map((field) => field.value)),
  ) ?? [],
);

const resetFields = () => {
  platformValue.value = datasource.value?.platformCost;

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((fieldItem, indexField) => {
        inputValues.value[indexData][indexRow][indexField] = fieldItem.value;
      });
    });
  });
};

const resetErrors = () => {
  platformError.value = '';

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((_, indexField) => {
        inputErrors.value[indexData][indexRow][indexField] = '';
      });
    });
  });
};

const updateDatasource = () => {
  if (datasource.value) datasource.value.platformCost = platformValue.value;

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((fieldItem, indexField) => {
        fieldItem.value = inputValues.value[indexData][indexRow][indexField];
      });
    });
  });
};

const handleChangePlatformValue = (value: any) => {
  const positiveAndZeroRegex = REGEX.POSITIVE_NUMBER_OR_ZERO;
  const isValid = positiveAndZeroRegex.test(value);
  if (isValid) {
    platformError.value = '';
  } else if (isFalsyButNotZero(value)) {
    platformError.value = 'Không được để trống';
  } else {
    platformError.value = 'Chỉ nhập số ≥ 0';
  }
};

const handleChangeValue = (value: any, indexData: number, indexRow: number, indexField: number) => {
  const positiveAndZeroRegex = REGEX.POSITIVE_NUMBER_OR_ZERO;
  const isValid = positiveAndZeroRegex.test(value);
  if (isValid) {
    inputErrors.value[indexData][indexRow][indexField] = '';
  } else if (isFalsyButNotZero(value)) {
    inputErrors.value[indexData][indexRow][indexField] = 'Không được để trống';
  } else {
    inputErrors.value[indexData][indexRow][indexField] = 'Chỉ nhập số ≥ 0';
  }
};

const isValidAll = () => {
  let isValidData = true;

  if (props.priceType !== PriceType.CostPrice) {
    const positiveAndZeroRegex = REGEX.POSITIVE_NUMBER_OR_ZERO;
    const isValid = positiveAndZeroRegex.test(`${platformValue.value}`);
    if (isValid) {
      platformError.value = '';
    } else if (isFalsyButNotZero(platformValue.value)) {
      isValidData = false;
      platformError.value = 'Không được để trống';
    } else {
      isValidData = false;
      platformError.value = 'Chỉ nhập số ≥ 0';
    }
  }

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((fieldItem, indexField) => {
        const value = fieldItem.value;
        const positiveAndZeroRegex = REGEX.POSITIVE_NUMBER_OR_ZERO;
        const isValid = positiveAndZeroRegex.test(value);
        if (rowItem.isHeader || fieldItem.field || isValid) {
          inputErrors.value[indexData][indexRow][indexField] = '';
        } else if (isFalsyButNotZero(value)) {
          isValidData = false;
          inputErrors.value[indexData][indexRow][indexField] = 'Không được để trống';
        } else {
          isValidData = false;
          inputErrors.value[indexData][indexRow][indexField] = 'Chỉ nhập số ≥ 0';
        }
      });
    });
  });

  return isValidData;
};

const processDatasource = () => {
  let isValidData = true;
  platformError.value = '';
  const positiveAndZeroRegex = REGEX.POSITIVE_NUMBER_OR_ZERO;
  const ds = datasource.value;
  const inputs = inputValues.value;
  const inputErr = inputErrors.value;

  if (props.priceType !== PriceType.CostPrice) {
    const isValid = positiveAndZeroRegex.test(`${platformValue.value}`);

    if (!isValid) {
      isValidData = false;
      platformError.value = isFalsyButNotZero(platformValue.value)
        ? 'Không được để trống'
        : 'Chỉ nhập số ≥ 0';
    }
  }

  if (ds) {
    ds.platformCost = platformValue.value;

    ds.data.forEach((item, indexData) => {
      const rows = item.rows;
      rows.forEach((rowItem, indexRow) => {
        const fields = rowItem.fields;
        fields.forEach((fieldItem, indexField) => {
          const value = inputs[indexData][indexRow][indexField];
          fieldItem.value = value;
          let errMsg = '';
          if (!(rowItem.isHeader || fieldItem.field || positiveAndZeroRegex.test(value))) {
            isValidData = false;
            errMsg = isFalsyButNotZero(value) ? 'Không được để trống' : 'Chỉ nhập số ≥ 0';
          }

          inputErr[indexData][indexRow][indexField] = errMsg;
        });
      });
    });
  }

  return isValidData;
};

const onSubmit = async () => {
  const isValid = processDatasource();
  if (datasource.value && isValid) {
    let payload;
    if (props.priceType === PriceType.CostPrice) {
      payload = getCostPriceSMSPayload(datasource.value);
    } else if (props.priceType === PriceType.ListPrice) {
      payload = getListPriceSMSPayload(datasource.value);
    } else if (props.priceType === PriceType.SellingPrice) {
      payload = getSellingPriceSMSPayload(datasource.value);
    }
    emit('onSubmitForm', payload);
  }
};

watch(
  () => props.activeTab,
  (newValue: TabValue) => {
    if (newValue === TabValue.Tab1) {
      resetErrors();
    }
  },
  {
    immediate: true,
  },
);

watch(
  () => props.type,
  () => {
    resetErrors();
  },
);

defineExpose({
  element: smsBoardRef,
  onSubmit,
  updateDatasource,
  resetFields,
  isValidAll,
  resetErrors,
});
</script>

<template>
  <div ref="smsBoardRef" class="text-[#292D32] text-[14px]">
    <div class="mb-[16px]">
      <p class="italic">{{ props.note }}</p>
    </div>
    <div
      v-if="
        props.type !== PageType.Details &&
        props.priceType !== PriceType.CostPrice &&
        accType != AccountType.Agent
      "
      class="mb-[20px] flex items-center"
    >
      <div class="flex items-start gap-x-[6px] w-full">
        <p class="font-bold mt-[3px]">Phí nền tảng</p>
        <div class="flex flex-col">
          <el-input
            v-model.trim="platformValue"
            :input-style="{
              width: '220px',
            }"
            :class="`${platformError ? 'invalid' : ''} ${props.priceType === PriceType.SellingPrice && fixedData?.platformCost != platformValue ? 'input-highlight' : 'input-normal'}`"
            :min="0"
            :maxlength="5"
            :placeholder="PLACEHOLDER.TYPE_PRICE"
            :show-word-limit="false"
            @change="handleChangePlatformValue"
          />
          <span v-if="platformError" class="text-[12px] text-[#f56c6c]">{{ platformError }}</span>
        </div>
        <p class="ml-[22px] mt-[3px]">(E-Point/ bản tin MT)</p>
      </div>
    </div>
    <div
      v-else-if="props.priceType !== PriceType.CostPrice && accType == AccountType.Agent"
      class="mb-[20px]"
    >
      <div class="w-full flex items-center justify-between mb-[6px]">
        <p class="font-bold">Phí nền tảng</p>
        <p>(E-Point/ bản tin MT)</p>
      </div>
      <div class="w-full overflow-x-auto">
        <div class="w-full flex items-center font-bold rounded-t-[6px] shadow bg-[#F6F8FB]">
          <div
            class="flex items-center justify-center h-[36px] min-w-[120px] bg-[#F6F8FB] rounded-t-[6px]"
          >
            Giá
          </div>
        </div>
        <div class="w-full">
          <div class="flex items-stretch gap-x-[4px] min-h-[40px]">
            <div
              class="flex min-w-[120px] py-[4px] justify-center border-b border-b-[#DFE4EA]"
              :class="props.type === PageType.Details ? 'items-center' : 'items-start'"
            >
              <p class="text-center italic text-main font-medium">Giá vốn</p>
            </div>
            <div
              class="flex-1 flex py-[4px] justify-center border-b border-b-[#DFE4EA]"
              :class="props.type === PageType.Details ? 'items-center' : 'items-start'"
            >
              <div
                v-if="capitalData?.platformCost || capitalData?.platformCost == '0'"
                class="min-w-[120px] w-full text-center italic text-main font-medium"
              >
                {{
                  `${capitalData?.platformCost?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.')}`
                }}
              </div>
            </div>
          </div>
          <div class="flex items-stretch gap-x-[4px] min-h-[40px]">
            <div
              class="flex min-w-[120px] py-[4px] justify-center border-b border-b-[#DFE4EA]"
              :class="props.type === PageType.Details ? 'items-center' : 'items-start'"
            >
              <p class="text-center">
                {{ props.priceType === PriceType.ListPrice ? 'Giá niêm yết' : 'Giá bán' }}
              </p>
            </div>
            <div
              class="flex-1 flex py-[4px] justify-center border-b border-b-[#DFE4EA]"
              :class="props.type === PageType.Details ? 'items-center' : 'items-start'"
            >
              <div v-if="props.type !== PageType.Details" class="flex flex-col w-full">
                <el-input
                  v-model.trim="platformValue"
                  :input-style="{
                    width: '100%',
                  }"
                  :class="`${platformError ? 'invalid' : ''} ${props.priceType === PriceType.SellingPrice && fixedData?.platformCost != platformValue ? 'input-highlight' : 'input-normal'}`"
                  :min="0"
                  :maxlength="5"
                  :placeholder="PLACEHOLDER.TYPE_PRICE"
                  :show-word-limit="false"
                  @change="handleChangePlatformValue"
                />
                <span v-if="platformError" class="text-[12px] text-[#f56c6c]">{{
                  platformError
                }}</span>
              </div>
              <div
                v-else
                class="min-w-[120px] w-full text-center"
                :class="`${props.priceType === PriceType.SellingPrice && fixedData?.platformCost != platformValue ? 'bg-[#FBB040] px-[4px]' : ''}`"
              >
                <span v-if="platformValue || platformValue == '0'">
                  {{ `${platformValue?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.')}` }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="
        props.priceType !== PriceType.CostPrice &&
        props.type === PageType.Details &&
        accType != AccountType.Agent
      "
      class="mb-[20px] flex items-center"
    >
      <p class="font-bold mr-[8px]">Phí nền tảng</p>
      <div class="flex items-center gap-[4px]">
        <p
          v-if="datasource?.platformCost || datasource?.platformCost == '0'"
          :class="`${props.priceType === PriceType.SellingPrice && fixedData?.platformCost != platformValue ? 'bg-[#FBB040] px-[4px]' : ''}`"
        >
          {{ `${datasource?.platformCost?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.')}` }}
        </p>
        <span>(E-Point/ bản tin MT)</span>
      </div>
    </div>
    <div class="">
      <div
        v-for="(item, indexData) of datasource?.data"
        :key="`${item.label}-${indexData}`"
        class="mb-[20px]"
      >
        <div class="w-full flex items-center justify-between mb-[6px]">
          <p class="font-bold">{{ item.label }}</p>
          <p>{{ item.unit }}</p>
        </div>
        <div class="w-full overflow-x-auto">
          <div
            v-for="(rowItem, indexRow) of item.rows"
            :key="`row-${rowItem}-${indexRow}`"
            class="w-full"
            :class="props.priceType !== PriceType.CostPrice && item.hasField ? 'relative' : ''"
          >
            <div
              v-if="rowItem.isHeader"
              class="flex items-center font-bold rounded-t-[6px] shadow gap-x-[4px] bg-[#F6F8FB]"
            >
              <div
                v-if="props.priceType !== PriceType.CostPrice && !item.hasField"
                class="flex-1 flex items-center justify-center h-[36px] min-w-[120px] bg-[#F6F8FB] rounded-tl-[6px]"
              >
                Giá
              </div>
              <div
                v-for="(fieldItem, indexField) of rowItem.fields"
                :key="`header-${fieldItem.value}-${indexField}`"
                class="flex-1 flex items-center justify-center h-[36px] bg-[#F6F8FB]"
                :class="`${indexField === 0 ? 'rounded-tl-[6px]' : ''} ${indexField === rowItem.fields.length - 1 ? 'rounded-tr-[6px]' : ''} ${isShowPriceField(item, indexField) ? 'min-w-[240px]' : 'min-w-[120px]'}`"
              >
                <p
                  v-if="isShowPriceField(item, indexField)"
                  class="flex items-center justify-center h-[36px] min-w-[120px] bg-[#F6F8FB]"
                >
                  Giá
                </p>
                <p
                  class="text-center"
                  :class="`${isShowPriceField(item, indexField) ? 'flex-1' : ''}`"
                >
                  {{ fieldItem.value }}
                </p>
              </div>
            </div>
            <div
              v-else-if="!rowItem.isHeader && props.priceType !== PriceType.CostPrice"
              class="flex items-stretch gap-x-[4px] min-h-[40px]"
            >
              <div
                v-if="!item.hasField"
                class="flex-1 flex py-[4px] justify-center border-b border-b-[#DFE4EA]"
                :class="props.type === PageType.Details ? 'items-center' : 'items-start'"
              >
                <div class="flex items-center justify-center min-w-[120px]">
                  <p class="text-center italic overflow-hidden !line-clamp-2 text-main font-medium">
                    Giá vốn
                  </p>
                </div>
              </div>
              <div
                v-for="(fieldItem, indexField) of capitalData?.data[indexData].rows[indexRow]
                  .fields"
                :key="`field-${fieldItem.value}-${indexField}`"
                class="flex-1 flex py-[4px] justify-center"
                :class="`${indexField === 0 && item.hasField ? '' : 'border-b border-b-[#DFE4EA]'} ${props.type === PageType.Details ? 'items-center' : 'items-start'}`"
              >
                <div
                  v-if="item.hasField && indexField === 1"
                  class="min-w-[120px] h-full text-center italic border-r border-r-[#DFE4EA] text-main font-medium"
                  :class="props.type === PageType.Details ? 'flex items-center justify-center' : ''"
                >
                  Giá vốn
                </div>
                <div
                  class="min-w-[120px] w-full text-center italic text-main font-medium"
                  :class="`${item.hasField ? 'h-full border-r border-r-[#DFE4EA]' : ''} ${props.type === PageType.Details ? 'flex items-center justify-center' : ''}`"
                >
                  {{ fieldItem.value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') }}
                </div>
              </div>
            </div>
            <div v-if="!rowItem.isHeader" class="flex items-stretch gap-x-[4px] min-h-[40px]">
              <div
                v-if="!item.hasField && props.priceType !== PriceType.CostPrice"
                class="flex-1 flex py-[4px] justify-center border-b border-b-[#DFE4EA]"
                :class="props.type === PageType.Details ? 'items-center' : 'items-start'"
              >
                <div class="flex items-center justify-center min-w-[120px]">
                  <p class="text-center overflow-hidden !line-clamp-2">
                    {{ props.priceType === PriceType.ListPrice ? 'Giá niêm yết' : 'Giá bán' }}
                  </p>
                </div>
              </div>
              <div
                v-for="(fieldItem, indexField) of rowItem.fields"
                :key="`field-${fieldItem.value}-${indexField}`"
                class="flex-1 flex py-[4px] justify-center border-b border-b-[#DFE4EA] relative"
                :class="`${isShowPriceField(item, indexField) ? 'min-w-[240px]' : 'min-w-[120px]'} ${props.type === PageType.Details ? 'items-center' : 'items-start'}`"
              >
                <div
                  v-if="fieldItem.field"
                  class="flex items-center justify-center"
                  :class="
                    props.priceType !== PriceType.CostPrice && item.hasField && indexField === 0
                      ? 'absolute top-0 translate-y-[-50%] h-[190%] max-h-[76px] border-r w-full px-[10px] bg-white border-r-[#DFE4EA]'
                      : ''
                  "
                >
                  <p
                    class="text-center overflow-hidden !line-clamp-2 text-[13px]"
                    :title="fieldItem.field"
                  >
                    {{ fieldItem.field }}
                  </p>
                  <el-tooltip v-if="fieldItem.description" placement="right">
                    <template #content>
                      <p class="max-w-[320px]">
                        {{ fieldItem.description }}
                      </p>
                    </template>
                    <Icon
                      icon="mingcute:information-fill"
                      class="text-[#354052] ml-[8px] cursor-pointer"
                    />
                  </el-tooltip>
                </div>
                <div
                  v-if="!fieldItem.field && item.hasField && indexField === 1"
                  class="min-w-[120px] text-center h-full"
                  :class="
                    props.type === PageType.Details
                      ? 'flex items-center justify-center border-r border-r-[#DFE4EA]'
                      : ''
                  "
                >
                  {{ props.priceType === PriceType.ListPrice ? 'Giá niêm yết' : 'Giá bán' }}
                </div>
                <div v-if="!fieldItem.field && type !== PageType.Details" class="w-full">
                  <el-input
                    v-model.trim="inputValues[indexData][indexRow][indexField]"
                    class="min-w-[120px]"
                    :class="`${inputErrors[indexData][indexRow][indexField] ? 'invalid' : ''} ${props.priceType === PriceType.SellingPrice && fixedData?.data[indexData].rows[indexRow].fields[indexField].value != inputValues[indexData][indexRow][indexField] ? 'input-highlight' : 'input-normal'}`"
                    :min="0"
                    :maxlength="5"
                    :placeholder="props.type === PageType.Details ? '' : fieldItem.placeholder"
                    :disabled="props.type === PageType.Details"
                    :show-word-limit="false"
                    @change="
                      (value: any) => handleChangeValue(value, indexData, indexRow, indexField)
                    "
                  />
                  <span class="text-[12px] text-[#f56c6c]">{{
                    inputErrors[indexData][indexRow][indexField]
                  }}</span>
                </div>
                <div
                  v-else-if="!fieldItem.field"
                  class="min-w-[120px] w-full text-center h-full flex items-center justify-center"
                  :class="`${
                    props.priceType !== PriceType.CostPrice && item.hasField
                      ? 'border-r border-r-[#DFE4EA]'
                      : ''
                  } ${props.priceType === PriceType.SellingPrice && fixedData?.data[indexData].rows[indexRow].fields[indexField].value !== fieldItem.value ? 'bg-[#FBB040]' : ''}`"
                >
                  {{ fieldItem.value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-input.invalid .el-input__wrapper) {
  box-shadow: 0 0 0 1px #f56c6c inset;
}
:deep(.input-highlight .el-input__wrapper) {
  background-color: #fbb040;
}
:deep(.input-normal .el-input__wrapper) {
  background-color: white;
}
</style>
