<script setup lang="ts">
import type { TZNSWrapper } from '../index.type';
import { PLACEHOLDER, REGEX, TabValue } from '@/shared';
import { Icon } from '@iconify/vue';
import { ElInput, ElTooltip } from 'element-plus';
import { ref, watch } from 'vue';
import { getCostPriceZNSPayload } from '../bang-gia-von/index.utils';
import { PriceType } from '../index.constants';
import { getListPriceZNSPayload } from '../bang-gia-niem-yet/index.utils';
import { AccountType, PageType } from '@/enums/common';
import { getSellingPriceZNSPayload } from '../bang-gia-ban/index.utils';
import { isFalsyButNotZero } from '@/utils/validate';

const props = defineProps<{
  type: PageType;
  note?: string;
  activeTab: TabValue;
  priceType: PriceType;
  capitalData?: TZNSWrapper;
  fixedData?: TZNSWrapper;
  accountType?: AccountType;
}>();

const emit = defineEmits(['onSubmitForm']);

const znsBoardRef = ref();
const datasource = defineModel<TZNSWrapper>('datasource');
const platformValue = ref(datasource.value?.platformCost);
const platformError = ref('');
const accType = props.accountType || localStorage.getItem('accountType') || AccountType.Admin;

const inputValues = ref(
  datasource.value?.data.map((item) =>
    item.rows.map((row) => row.fields.map((field) => field.value)),
  ) ?? [],
);
const inputErrors = ref(
  datasource.value?.data.map((item) =>
    item.rows.map((row) => row.fields.map((field) => field.value)),
  ) ?? [],
);

const resetFields = () => {
  platformValue.value = datasource.value?.platformCost;

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((fieldItem, indexField) => {
        inputValues.value[indexData][indexRow][indexField] = fieldItem.value;
      });
    });
  });
};

const resetErrors = () => {
  platformError.value = '';

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((_, indexField) => {
        inputErrors.value[indexData][indexRow][indexField] = '';
      });
    });
  });
};

const updateDatasource = () => {
  if (datasource.value) datasource.value.platformCost = platformValue.value;

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((fieldItem, indexField) => {
        fieldItem.value = inputValues.value[indexData][indexRow][indexField];
      });
    });
  });
};

const handleChangePlatformValue = (value: any) => {
  const positiveAndZeroRegex = REGEX.POSITIVE_NUMBER_OR_ZERO;
  const isValid = positiveAndZeroRegex.test(value);
  if (isValid) {
    platformError.value = '';
  } else if (isFalsyButNotZero(value)) {
    platformError.value = 'Không được để trống';
  } else {
    platformError.value = 'Chỉ nhập số ≥ 0';
  }
};

const handleChangeValue = (value: any, indexData: number, indexRow: number, indexField: number) => {
  const positiveAndZeroRegex = REGEX.POSITIVE_NUMBER_OR_ZERO;
  const isValid = positiveAndZeroRegex.test(value);
  if (isValid) {
    inputErrors.value[indexData][indexRow][indexField] = '';
  } else if (isFalsyButNotZero(value)) {
    inputErrors.value[indexData][indexRow][indexField] = 'Không được để trống';
  } else {
    inputErrors.value[indexData][indexRow][indexField] = 'Chỉ nhập số ≥ 0';
  }
};

const isValidAll = () => {
  let isValidData = true;

  if (props.priceType !== PriceType.CostPrice) {
    const positiveAndZeroRegex = REGEX.POSITIVE_NUMBER_OR_ZERO;
    const isValid = positiveAndZeroRegex.test(`${platformValue.value}`);
    if (isValid) {
      platformError.value = '';
    } else if (isFalsyButNotZero(platformValue.value)) {
      isValidData = false;
      platformError.value = 'Không được để trống';
    } else {
      isValidData = false;
      platformError.value = 'Chỉ nhập số ≥ 0';
    }
  }

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((fieldItem, indexField) => {
        const value = fieldItem.value;
        const positiveAndZeroRegex = REGEX.POSITIVE_NUMBER_OR_ZERO;
        const isValid = positiveAndZeroRegex.test(value);
        if (rowItem.isHeader || fieldItem.field || fieldItem.unit || isValid) {
          inputErrors.value[indexData][indexRow][indexField] = '';
        } else if (isFalsyButNotZero(value)) {
          isValidData = false;
          inputErrors.value[indexData][indexRow][indexField] = 'Không được để trống';
        } else {
          isValidData = false;
          inputErrors.value[indexData][indexRow][indexField] = 'Chỉ nhập số ≥ 0';
        }
      });
    });
  });

  return isValidData;
};

const processDatasource = () => {
  let isValidData = true;
  platformError.value = '';
  const positiveAndZeroRegex = REGEX.POSITIVE_NUMBER_OR_ZERO;
  const ds = datasource.value;
  const inputs = inputValues.value;
  const inputErr = inputErrors.value;

  if (props.priceType !== PriceType.CostPrice) {
    const isValid = positiveAndZeroRegex.test(`${platformValue.value}`);

    if (!isValid) {
      isValidData = false;
      platformError.value = isFalsyButNotZero(platformValue.value)
        ? 'Không được để trống'
        : 'Chỉ nhập số ≥ 0';
    }
  }

  if (ds) {
    ds.platformCost = platformValue.value;

    ds.data.forEach((item, indexData) => {
      const rows = item.rows;
      rows.forEach((rowItem, indexRow) => {
        const fields = rowItem.fields;
        fields.forEach((fieldItem, indexField) => {
          const value = inputs[indexData][indexRow][indexField];
          fieldItem.value = value;
          let errMsg = '';
          if (
            !(
              rowItem.isHeader ||
              fieldItem.field ||
              fieldItem.unit ||
              positiveAndZeroRegex.test(value)
            )
          ) {
            isValidData = false;
            errMsg = isFalsyButNotZero(value) ? 'Không được để trống' : 'Chỉ nhập số ≥ 0';
          }

          inputErr[indexData][indexRow][indexField] = errMsg;
        });
      });
    });
  }

  return isValidData;
};

const onSubmit = async () => {
  const isValid = processDatasource();
  if (datasource.value && isValid) {
    let payload;
    if (props.priceType === PriceType.CostPrice) {
      payload = getCostPriceZNSPayload(datasource.value);
    } else if (props.priceType === PriceType.ListPrice) {
      payload = getListPriceZNSPayload(datasource.value);
    } else if (props.priceType === PriceType.SellingPrice) {
      payload = getSellingPriceZNSPayload(datasource.value);
    }
    emit('onSubmitForm', payload);
  }
};

watch(
  () => props.activeTab,
  (newValue: TabValue) => {
    if (newValue === TabValue.Tab1) {
      resetErrors();
    }
  },
  {
    immediate: true,
  },
);

watch(
  () => props.type,
  () => {
    resetErrors();
  },
);

defineExpose({
  element: znsBoardRef,
  onSubmit,
  updateDatasource,
  resetFields,
  isValidAll,
  resetErrors,
});
</script>

<template>
  <div ref="znsBoardRef" class="text-[#292D32] text-[14px]">
    <div class="mb-[16px]">
      <p class="italic">{{ props.note }}</p>
    </div>
    <div
      v-if="
        props.type !== PageType.Details &&
        props.priceType !== PriceType.CostPrice &&
        accType != AccountType.Agent
      "
      class="mb-[20px] flex items-center"
    >
      <div class="flex items-start gap-x-[6px] w-full">
        <p class="font-bold mt-[3px]">Phí nền tảng</p>
        <div class="flex flex-col">
          <el-input
            v-model.trim="platformValue"
            :input-style="{
              width: '220px',
            }"
            :class="`${platformError ? 'invalid' : ''} ${props.priceType === PriceType.SellingPrice && fixedData?.platformCost != platformValue ? 'input-highlight' : 'input-normal'}`"
            :min="0"
            :maxlength="5"
            :placeholder="PLACEHOLDER.TYPE_PRICE"
            :show-word-limit="false"
            @change="handleChangePlatformValue"
          />
          <span v-if="platformError" class="text-[12px] text-[#f56c6c]">{{ platformError }}</span>
        </div>
        <p class="ml-[22px] mt-[3px]">(E-Point/ ZNS)</p>
      </div>
    </div>
    <div
      v-else-if="props.priceType !== PriceType.CostPrice && accType == AccountType.Agent"
      class="mb-[20px]"
    >
      <div class="w-full flex items-center justify-between mb-[6px]">
        <p class="font-bold">Phí nền tảng</p>
        <p>(E-Point/ ZNS)</p>
      </div>
      <div class="w-full overflow-x-auto">
        <div class="w-full flex items-center font-bold rounded-t-[6px] shadow bg-[#F6F8FB]">
          <div
            class="flex items-center justify-center h-[36px] min-w-[120px] bg-[#F6F8FB] rounded-t-[6px]"
          >
            Giá
          </div>
        </div>
        <div class="w-full">
          <div class="flex items-stretch gap-x-[4px] min-h-[40px]">
            <div
              class="flex min-w-[120px] py-[4px] justify-center border-b border-b-[#DFE4EA]"
              :class="props.type === PageType.Details ? 'items-center' : 'items-start'"
            >
              <p class="text-center italic text-main font-medium">Giá vốn</p>
            </div>
            <div
              class="flex-1 flex py-[4px] justify-center border-b border-b-[#DFE4EA]"
              :class="props.type === PageType.Details ? 'items-center' : 'items-start'"
            >
              <div
                v-if="capitalData?.platformCost || capitalData?.platformCost == '0'"
                class="min-w-[120px] w-full text-center italic text-main font-medium"
              >
                {{
                  `${capitalData?.platformCost?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.')}`
                }}
              </div>
            </div>
          </div>
          <div class="flex items-stretch gap-x-[4px] min-h-[40px]">
            <div
              class="flex min-w-[120px] py-[4px] justify-center border-b border-b-[#DFE4EA]"
              :class="props.type === PageType.Details ? 'items-center' : 'items-start'"
            >
              <p class="text-center">
                {{ props.priceType === PriceType.ListPrice ? 'Giá niêm yết' : 'Giá bán' }}
              </p>
            </div>
            <div
              class="flex-1 flex py-[4px] justify-center border-b border-b-[#DFE4EA]"
              :class="props.type === PageType.Details ? 'items-center' : 'items-start'"
            >
              <div v-if="props.type !== PageType.Details" class="flex flex-col w-full">
                <el-input
                  v-model.trim="platformValue"
                  :input-style="{
                    width: '100%',
                  }"
                  :class="`${platformError ? 'invalid' : ''} ${props.priceType === PriceType.SellingPrice && fixedData?.platformCost != platformValue ? 'input-highlight' : 'input-normal'}`"
                  :min="0"
                  :maxlength="5"
                  :placeholder="PLACEHOLDER.TYPE_PRICE"
                  :show-word-limit="false"
                  @change="handleChangePlatformValue"
                />
                <span v-if="platformError" class="text-[12px] text-[#f56c6c]">{{
                  platformError
                }}</span>
              </div>
              <div
                v-else
                class="min-w-[120px] w-full text-center"
                :class="`${props.priceType === PriceType.SellingPrice && fixedData?.platformCost != platformValue ? 'bg-[#FBB040] px-[4px]' : ''}`"
              >
                <span v-if="platformValue || platformValue == '0'">
                  {{ `${platformValue?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.')}` }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="
        props.priceType !== PriceType.CostPrice &&
        props.type === PageType.Details &&
        accType != AccountType.Agent
      "
      class="mb-[20px] flex items-center"
    >
      <p class="font-bold mr-[8px]">Phí nền tảng</p>
      <div class="flex items-center gap-x-[6px]">
        <p
          v-if="datasource?.platformCost || datasource?.platformCost == '0'"
          :class="`${props.priceType === PriceType.SellingPrice && fixedData?.platformCost != platformValue ? 'bg-[#FBB040] px-[4px]' : ''}`"
        >
          {{ `${datasource?.platformCost?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.')}` }}
        </p>
        <span>(E-Point/ ZNS)</span>
      </div>
    </div>
    <div class="">
      <div
        v-for="(item, indexData) of datasource?.data"
        :key="`${item.label}-${indexData}`"
        class="mb-[20px]"
      >
        <div class="w-full flex items-center justify-between mb-[6px]">
          <p class="font-bold">{{ item.label }}</p>
        </div>
        <div class="w-full overflow-x-auto overflow-y-hidden">
          <div
            v-for="(rowItem, indexRow) of item.rows"
            :key="`row-${rowItem}-${indexRow}`"
            class="w-full"
            :class="props.priceType !== PriceType.CostPrice ? 'relative' : ''"
          >
            <div
              v-if="rowItem.isHeader"
              class="flex items-center font-bold rounded-t-[6px] shadow gap-x-[4px] bg-[#F6F8FB]"
            >
              <div
                v-for="(fieldItem, indexField) of rowItem.fields"
                :key="`header-${fieldItem.value}-${indexField}`"
                class="px-[10px] flex items-center justify-start h-[36px] min-w-[120px] bg-[#F6F8FB]"
                :class="`${indexField === 0 ? 'rounded-tl-[6px] w-[30%]' : indexField === rowItem.fields.length - 1 ? 'w-[20%] rounded-tr-[6px]' : props.priceType !== PriceType.CostPrice ? 'flex-1 !px-0' : 'w-[50%]'}`"
              >
                <p
                  v-if="props.priceType !== PriceType.CostPrice && indexField === 1"
                  class="w-[120px]"
                ></p>
                <p
                  :class="`${props.priceType !== PriceType.CostPrice && indexField === 1 ? 'flex items-center justify-center w-full pl-[20px]' : ''}`"
                >
                  {{ fieldItem.value }}
                </p>
              </div>
            </div>
            <div
              v-else-if="!rowItem.isHeader && props.priceType !== PriceType.CostPrice"
              class="flex items-stretch gap-x-[4px] min-h-[40px]"
            >
              <div
                v-for="(fieldItem, indexField) of capitalData?.data[indexData].rows[indexRow]
                  .fields"
                :key="`field-${fieldItem.value}-${indexField}`"
                class="min-w-[120px] flex py-[4px] justify-center"
                :class="`${indexField === 0 ? 'w-[30%]' : 'border-b border-b-[#DFE4EA]'} ${indexField === 1 ? 'w-[49%]' : ''} ${props.type === PageType.Details ? 'items-center' : 'items-start'}`"
              >
                <div
                  v-if="indexField === 1"
                  class="min-w-[120px] h-full text-center italic border-r border-r-[#DFE4EA] text-main font-medium"
                  :class="props.type === PageType.Details ? 'flex items-center justify-center' : ''"
                >
                  Giá vốn
                </div>
                <div
                  v-if="indexField !== 2"
                  class="w-full h-full text-center italic text-main font-medium"
                  :class="props.type === PageType.Details ? 'flex items-center justify-center' : ''"
                >
                  {{ fieldItem.value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') }}
                </div>
              </div>
            </div>
            <div v-if="!rowItem.isHeader" class="flex items-stretch gap-x-[4px] min-h-[40px]">
              <div
                v-for="(fieldItem, indexField) of rowItem.fields"
                :key="`field-${fieldItem.value}-${indexField}`"
                class="flex justify-start py-[4px] border-b border-b-[#DFE4EA]"
                :class="`${fieldItem.field ? 'w-[30%]' : fieldItem.unit ? 'w-[20%]' : 'w-[50%]'} ${props.priceType !== PriceType.CostPrice ? 'px-[2px]' : 'px-[10px]'} ${props.type === PageType.Details ? 'items-center' : 'items-start'}`"
              >
                <div v-if="fieldItem.field" class="flex items-center justify-center">
                  <div
                    class="overflow-hidden !line-clamp-2"
                    :class="`${
                      props.priceType !== PriceType.CostPrice && indexField === 0
                        ? 'absolute top-0 left-0 h-full border-r min-w-[120px] w-[30%] px-[10px] border-r-[#DFE4EA]'
                        : ''
                    }`"
                    :title="fieldItem.field"
                  >
                    <p
                      class="text-[13px]"
                      :class="`${props.priceType !== PriceType.CostPrice && indexField === 0 ? 'flex items-center h-full' : ''}`"
                    >
                      {{ fieldItem.field }}
                    </p>
                  </div>
                  <el-tooltip v-if="fieldItem.description" placement="right">
                    <template #content>
                      <p class="max-w-[320px]">
                        {{ fieldItem.description }}
                      </p>
                    </template>
                    <Icon
                      icon="mingcute:information-fill"
                      class="text-[#354052] ml-[8px] cursor-pointer"
                    />
                  </el-tooltip>
                </div>
                <div
                  v-if="
                    props.priceType !== PriceType.CostPrice && !fieldItem.field && indexField === 1
                  "
                  class="min-w-[120px] text-center h-full"
                  :class="
                    props.type === PageType.Details
                      ? 'border-r border-r-[#DFE4EA] flex items-center justify-center'
                      : ''
                  "
                >
                  {{ props.priceType === PriceType.ListPrice ? 'Giá niêm yết' : 'Giá bán' }}
                </div>
                <div
                  v-if="!fieldItem.field && !fieldItem.unit"
                  :class="`${props.priceType !== PriceType.CostPrice ? 'size-full' : ''} ${props.type === PageType.Details ? '' : 'w-full'}`"
                >
                  <div v-if="type !== PageType.Details" class="w-full">
                    <el-input
                      v-model.trim="inputValues[indexData][indexRow][indexField]"
                      class="min-w-[120px]"
                      :class="`${inputErrors[indexData][indexRow][indexField] ? 'invalid' : ''} ${props.priceType === PriceType.SellingPrice && fixedData?.data[indexData].rows[indexRow].fields[indexField].value != inputValues[indexData][indexRow][indexField] ? 'input-highlight' : 'input-normal'}`"
                      :min="0"
                      :maxlength="5"
                      :placeholder="props.type === PageType.Details ? '' : fieldItem.placeholder"
                      :disabled="props.type === PageType.Details"
                      :show-word-limit="false"
                      @change="
                        (value: any) => handleChangeValue(value, indexData, indexRow, indexField)
                      "
                    />
                    <span class="text-[12px] text-[#f56c6c]">{{
                      inputErrors[indexData][indexRow][indexField]
                    }}</span>
                  </div>
                  <div
                    v-else
                    class="text-center h-full"
                    :class="`${props.priceType === PriceType.SellingPrice && fixedData?.data[indexData].rows[indexRow].fields[indexField].value !== fieldItem.value ? 'bg-[#FBB040]' : ''} ${props.type === PageType.Details ? 'flex items-center justify-center' : ''}`"
                  >
                    {{ fieldItem.value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') }}
                  </div>
                </div>
                <div
                  v-else
                  :class="
                    props.priceType !== PriceType.CostPrice
                      ? 'absolute h-[90%] top-1/2 right-0 pl-[10px] translate-y-[-50%] bg-white w-[20%] border-l border-[#DFE4EA]'
                      : ''
                  "
                >
                  <p
                    :class="
                      props.priceType !== PriceType.CostPrice ? 'h-full flex items-center' : ''
                    "
                  >
                    {{ fieldItem.unit }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-input.invalid .el-input__wrapper) {
  box-shadow: 0 0 0 1px #f56c6c inset;
}
:deep(.input-highlight .el-input__wrapper) {
  background-color: #fbb040;
}
:deep(.input-normal .el-input__wrapper) {
  background-color: white;
}
</style>
