import { ALIGN } from '@/shared';

export const popupAuditLogPt = {
  root: { class: 'bg-[#fff]' },
  header: {
    class: '!pl-[280px] !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
  },
};

export const popupAuditLogStyle = {
  width: '700px',
  backgroundColor: '#fff',
  maxHeight: '700px',
};

export const popupCostPriceAuditLogPt = {
  root: { class: 'bg-[#fff]' },
  header: {
    class: '!pl-[430px] !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
  },
};

export const popupCostPriceAuditLogStyle = {
  width: '1000px',
  backgroundColor: '#fff',
  maxHeight: '800px',
};

export const auditLogStyle = [
  { idx: 0, class: 'w-[20%]' },
  { idx: 1, class: 'w-[30%]' },
  { idx: 2, class: 'w-[20%]' },
  { idx: 3, class: 'w-[20%]' },
];

export const auditLogHeaders = [
  {
    key: 'updated_at',
    name: 'Thời gian chỉnh sửa',
    visible: true,
    pin: false,
    align: ALIGN.CENTER,
  },
  {
    key: 'account',
    name: 'Tài khoản',
    visible: true,
    pin: false,
  },
  {
    key: 'channel_type',
    name: 'Kênh gửi tin',
    visible: true,
    pin: false,
    align: ALIGN.CENTER,
  },
  {
    key: 'download',
    name: 'Tải về',
    visible: true,
    pin: false,
    align: ALIGN.CENTER,
  },
];

export enum AccountType {
  Admin = 1,
  Agent = 2,
}

export enum PriceType {
  CostPrice = 1,
  ListPrice = 2,
  SellingPrice = 3,
}

export enum ServiceType {
  SMS = 0,
  ZNS = 1,
}

//#region SMS
// General

// Init brandname
export const INIT_BRN = 0;
export const VALUES = 1;
export const VNP = 0;
export const VNM = 1;
export const MBP = 2;
export const VTL = 3;
export const ITL = 4;
export const RDI = 5;
export const GTL = 6;
export const VNP_FIELD = 1;
export const VNM_FIELD = 2;
export const MBP_FIELD = 3;
export const VTL_FIELD = 4;
export const ITL_FIELD = 5;
export const RDI_FIELD = 6;
export const GTL_FIELD = 7;
// Maintain brandname
export const MAINTAIN_BRN = 1;
// Customer service
export const CUSTOMER_SERVICE = 2;
export const CUSTOMER_MEDICAL = 1;
export const CUSTOMER_EDUCATION = 2;
export const CUSTOMER_ADMINISTRATIVE = 3;
export const CUSTOMER_DOMESTIC_SM = 4;
export const CUSTOMER_FINANCE = 5;
export const CUSTOMER_STOCK = 6;
export const CUSTOMER_INSURANCE = 7;
export const CUSTOMER_WATER = 8;
export const CUSTOMER_ECOMMERCE = 9;
export const CUSTOMER_BANK = 10;
export const CUSTOMER_INTERNATIONAL_SM = 11;
export const CUSTOMER_ELECTRICITY = 12;
export const CUSTOMER_LOGISTICS = 13;
export const CUSTOMER_EWALLET = 14;
export const CUSTOMER_OTHERS = 15;
// Advertisement service
export const ADVERTISEMENT_SERVICE = 3;
export const ADS_MEDICAL = 1;
export const ADS_EDUCATION_ENROLLMENT = 2;
export const ADS_ECOMMERCE = 3;
export const ADS_BANK = 4;
export const ADS_REAL_ESTATE = 5;
export const ADS_GAS = 6;
export const ADS_CHEMICAL = 7;
export const ADS_HOTEL = 8;
export const ADS_KARA = 9;
export const ADS_GUARD = 10;
export const ADS_RESTAURANT = 11;
export const ADS_FOOD_DRINK = 12;
export const ADS_TOURISM = 13;
export const ADS_PHARMACEUTICALS = 14;
export const ADS_TRANSPORT = 15;
export const ADS_FASHION = 16;
export const ADS_MALL = 17;
export const ADS_COSMETICS = 18;
export const ADS_ENTERTAINMENT = 19;
export const ADS_RECRUITMENT = 20;
export const ADS_OTHERS = 21;
//#endregion

//#region ZNS
// General
export const ZNS_VALUE = 1;
export const ZNS_UNIT = 2;
// System service
export const SYSTEM_SERVICE = 0;
export const CONFIG_SYSTEM = 1;
export const INIT_OA = 2;
export const MAINTAIN_ADVANCE_OA = 3;
export const MAINTAIN_PREMIUM_OA = 4;
// ZNS service
export const ZNS_SERVICE = 1;
export const TEXT = 1;
export const TABLE = 2;
export const OTP = 3;
export const ASSESSMENT = 4;
export const VOUCHER = 5;
export const PAYMENT = 6;
export const ADMINISTRATIVE = 7;
// CTA service
export const CTA_SERVICE = 2;
export const BUSINESS_SITE = 1;
export const CONTACT = 2;
export const OA_SITE = 3;
export const BUSINESS_MA_SITE = 4;
export const PRODUCT_DISTRIBUTED_SITE = 5;
export const ZALO_MA_SITE = 6;
export const APP_INSTALL_SITE = 7;
export const OTHERS_APP_INSTALL_SITE = 8;
export const OA_POST = 9;
// Specific service
export const SPECIFIC_SERVICE = 3;
export const IMAGE = 1;
//#endregion
