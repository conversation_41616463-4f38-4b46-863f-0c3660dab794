import { PriceType } from './index.constants';
import type { ChannelValue } from './index.type';
import { toPng } from 'html-to-image';
import { useUserSession } from '@/store/userSession';

export const getPriceBoardFileName = (priceType: PriceType, channel: ChannelValue) => {
  const now = new Date();
  const day = String(now.getDate()).padStart(2, '0');
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const year = now.getFullYear();
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  let priceTypeName = '';
  if (priceType === PriceType.CostPrice) {
    priceTypeName = 'Bảng giá vốn';
  } else if (priceType === PriceType.ListPrice) {
    priceTypeName = 'Bảng giá niêm yết';
  } else if (priceType === PriceType.SellingPrice) {
    priceTypeName = 'Bảng giá bán';
  }
  const fileName = `${priceTypeName}_${channel}_${day}${month}${year}_${hours}${minutes}.png`;
  return fileName;
};

export const generateImage = async (element: any, fileName: string, datetime: any) => {
  if (element) {
    const userStore = useUserSession();
    // Wrapper
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.top = '0';
    container.style.left = '0';
    container.style.width = '100%';
    container.style.backgroundColor = '#ffffff';
    container.style.padding = '20px 30px';
    container.style.zIndex = '-10000';
    // Infor wrapper
    const inforContainer = document.createElement('div');
    // Title
    const title = document.createElement('h1');
    title.textContent = 'LỊCH SỬ CHỈNH SỬA';
    title.style.fontWeight = '500';
    title.style.fontSize = '16px';
    inforContainer.appendChild(title);
    // Modified at
    const modifiedAt = document.createElement('p');
    const day = String(datetime.getDate()).padStart(2, '0');
    const month = String(datetime.getMonth() + 1).padStart(2, '0');
    const year = datetime.getFullYear();
    const hours = String(datetime.getHours()).padStart(2, '0');
    const minutes = String(datetime.getMinutes()).padStart(2, '0');
    modifiedAt.innerHTML = `Thời gian chỉnh sửa: <span style="font-weight: 500;">${day}/${month}/${year} ${hours}:${minutes}</span>`;
    modifiedAt.style.fontSize = '14px';
    inforContainer.appendChild(modifiedAt);
    // Account
    const account = document.createElement('p');
    account.innerHTML = `Tài khoản: ${(userStore.user as any)?.email}`;
    account.style.fontSize = '14px';
    account.style.marginBottom = '10px';
    inforContainer.appendChild(account);
    // Append dome
    container.appendChild(inforContainer);
    container.appendChild(element.cloneNode(true));
    document.body.appendChild(container);
    // Height
    const containerHeight = container.scrollHeight;
    container.style.height = `${containerHeight}px`;

    const imgData = await toPng(container, {
      quality: 1,
      backgroundColor: '#FFFFFF',
      skipFonts: true,
    });

    document.body.removeChild(container);
    const response = await fetch(imgData);
    const blob = await response.blob();
    const fileImage = new File([blob], fileName, { type: 'image/png' });

    return fileImage;
  }
};
