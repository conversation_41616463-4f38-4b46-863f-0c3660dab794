<script setup lang="ts">
import { inject, onMounted, ref, watch } from 'vue';
import * as yup from 'yup';
import { useApi } from '@/store/useApi';
import { useForm } from 'vee-validate';
import { useRouter } from 'vue-router';
import { color } from '@/constants/statusColor';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import PopupCancelConfirm from '@/components/base/common/PopupCancelConfirm.vue';

import { ROUTE_NAME, ROUTE_PATH, FORM_TYPE } from '@/shared';

const props = defineProps<{
  type: string;
}>();

const toast = inject('toast') as any;
const api = useApi();
const router = useRouter();
const overlayLoading = useOverLayLoadingStore();
const popupVisible = ref(false);

const validationSchema = yup.object();

const { values, handleSubmit, setFieldValue } = useForm({
  validationSchema,
});

const openUpdate = () => {
  router.push({
    name: ROUTE_NAME.CONFIG_CANCEL_BRN_UPDATE,
  });
};

const openDetail = () => {
  popupVisible.value = false;
  router.push({
    name: ROUTE_NAME.CONFIG_CANCEL_BRN,
  });
};

const getDetail = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const res = await api.get('/wallet/v1/api/admin/policy/PAYMENT_DAY_OF_MONTH');
    if (res.data.code === 0) {
      setFieldValue('cancel_brandname_at', res.data.data.value);
    }
    overlayLoading.toggleLoading(false);
  } catch {
    overlayLoading.toggleLoading(false);
  }
};

const onSubmit = handleSubmit(async () => {
  const body = {
    code: 'PAYMENT_DAY_OF_MONTH',
    value: values.cancel_brandname_at,
  };
  const { data } = await api.put('/wallet/v1/api/admin/policy', body);
  if (data.code === 0) {
    overlayLoading.toggleLoading(false);
    openDetail();
    toast('success', data.message);
    return;
  }
  overlayLoading.toggleLoading(false);
  toast('error', data.message);
});

onMounted(async () => {
  await getDetail();
});

watch(
  () => props.type,
  (value) => {
    if (value === 'xem') {
      getDetail();
    }
  },
);

const disabledDate = (date: Date) => {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();
  const currentDay = currentDate.getDate();

  // Kiểm tra nếu ngày không thuộc tháng hiện tại
  if (date.getFullYear() !== currentYear || date.getMonth() !== currentMonth) {
    return true; // Vô hiệu hóa các ngày không thuộc tháng hiện tại
  }

  // Chỉ cho phép chọn từ ngày 1 đến ngày 28
  const dayOfMonth = date.getDate();
  if (dayOfMonth < 1 || dayOfMonth > 28) {
    return true;
  }

  // Không cho phép chọn ngày quá khứ so với ngày hiện tại
  if (dayOfMonth < currentDay) {
    return true;
  }

  return false;
};
</script>

<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:settings" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>{{ 'Cài đặt' }}</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: ROUTE_PATH.CONFIG_CANCEL_BRN }"
          >Cấu hình hạn hủy Brandname</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
  </div>
  <div class="overflow-auto flex justify-center w-full" :class="'view-height'">
    <div class="w-[70%] mt-5">
      <el-form label-position="top">
        <VElementDateTimePicker
          :disabled="props.type === FORM_TYPE.DETAILS"
          required
          id="cancel-brandname-at"
          size="default"
          name="cancel_brandname_at"
          type="date"
          format="DD/MM/YYYY"
          :label="'Hạn huỷ Brandname'"
          :style="'!w-[100%]'"
          :disabledDates="disabledDate"
          placeholder="Chọn hạn huỷ Brandname"
        />
      </el-form>
    </div>
  </div>
  <div
    class="flex z-[999] justify-end py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke"
  >
    <VElementButton
      v-if="props.type === 'sua'"
      @click="
        () => {
          popupVisible = true;
        }
      "
      :bgColor="color.closeButton"
      label="Hủy"
      styleButton="s"
    ></VElementButton>
    <VElementButton
      v-if="props.type === 'sua'"
      @click="onSubmit"
      :bgColor="color.main"
      label="Lưu"
      styleButton="s"
    >
    </VElementButton>
    <VElementButton
      v-else
      :bgColor="color.main"
      styleButton="s"
      :label="props.type === 'sua' ? 'Lưu' : 'Cập nhật'"
      @click="openUpdate"
    ></VElementButton>
  </div>
  <PopupCancelConfirm
    v-model:popupVisible="popupVisible"
    @onClose="popupVisible = false"
    @onConfirm="openDetail"
  />
</template>

<style lang="scss" scoped>
:deep(.p-tag) {
  width: 120px;
}
.phone_num::after {
  content: '*';
  color: var(--el-color-danger);
  margin-left: 4px;
}
.view-height {
  height: calc(100vh - 215px);
  margin-bottom: 40px;
}
.view-height-customer {
  height: calc(100vh - 175px);
}
.el-form-item {
  margin-bottom: 6px;
}

/* Điều chỉnh chiều cao của el-input để phù hợp với el-select-v2 */
:deep(.el-input__wrapper) {
  height: 32px;
}

:deep(.el-input__inner) {
  line-height: normal;
}
</style>
