<template>
  <!-- Header -->
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:users" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item id="agent-breadcrumb" :to="{ path: ROUTE_PATH.AGENT }"
          >Đ<PERSON>i lý</el-breadcrumb-item
        >
        <el-breadcrumb-item>Xem chi tiết</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>

  <!-- Tabs -->
  <BaseTabs :tabs :active-tab="activeTab" tabClass="w-[94%]" @click-tab="handleTabChanged">
    <template #tab-1>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <GeneralInfoTab :type="PageType.Details" :values="values" />
      </div>
    </template>
    <template #tab-2>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <FormDetailsAgent
          ref="priceBoardRef"
          :customer-id="props.id ?? 0"
          :type="PageType.Details"
        />
      </div>
    </template>
    <template #tab-3>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <ContractTab ref="contractTabRef" :agentId="agentId" />
      </div>
    </template>
  </BaseTabs>

  <!-- Footer -->
  <div
    class="flex z-[999] py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke items-center justify-end"
  >
    <VElementButton
      v-if="activeTab === TabValue.Tab2"
      icon="history"
      label="Xem lịch sử chỉnh sửa"
      :bgColor="color.secondary"
      @click="clickViewAuditLog"
    />
    <VElementButton
      v-if="activeTab === TabValue.Tab1 || activeTab === TabValue.Tab2"
      label="Cập nhật"
      styleButton="s"
      id="agent-update-button"
      :bgColor="color.main"
      @click="openUpdate"
    />
    <VElementButton
      v-if="activeTab === TabValue.Tab3"
      label="Thêm hợp đồng"
      styleButton="s"
      id="agent-add-contract-button"
      :bgColor="color.main"
      @click="handleAddContract"
    />
  </div>

  <PopupAudit
    :serviceType="priceBoardRef?.activeTab === TabValue.Tab1 ? ServiceType.SMS : ServiceType.ZNS"
    :adminView="false"
    :priceType="PriceType.SellingPrice"
    :customer-id="props.id"
    :visible="isVisibleHistoriesPopup"
    :customerType="CustomerType.Agent"
    @onClose="isVisibleHistoriesPopup = false"
  />
</template>

<script setup lang="ts">
import { onMounted, ref, inject, computed, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useForm } from 'vee-validate';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { ROUTE_NAME, ROUTE_PATH, TabValue } from '@/shared';
import GeneralInfoTab from './components/GeneralInfoTab.vue';
import ContractTab from './components/ContractTab.vue';
import { AccountType, PageType } from '@/enums/common';
import BaseTabs from '../cai-dat/bang-gia/components/BaseTabs.vue';
import PopupAudit from '../cai-dat/bang-gia/components/PopupAudit.vue';
import { CustomerType, type TTab } from '../cai-dat/bang-gia/index.type';
import FormDetailsAgent from '../cai-dat/bang-gia/bang-gia-ban/FormDetailsAgent.vue';
import { useContractStore } from '@/store/contract';
import { useTab } from '@/store/useTab';
import { handleApiError } from '@/utils/useErrorHandler';
import { PriceType, ServiceType } from '../cai-dat/bang-gia/index.constants';

const props = defineProps<{
  id?: number;
  type: PageType;
}>();

const route = useRoute();
const router = useRouter();
const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const contractStore = useContractStore();
const tabStore = useTab();

// State
const contractTabRef = ref();

//#region Tabs
const activeTab = ref<TabValue>(TabValue.Tab1);
const accType = localStorage.getItem('accountType') || AccountType.Admin;

const tabs: TTab[] =
  accType == AccountType.Admin
    ? [
        {
          label: 'Thông tin chung',
          value: TabValue.Tab1,
        },
        {
          label: 'Bảng giá',
          value: TabValue.Tab2,
        },
        {
          label: 'Hợp đồng',
          value: TabValue.Tab3,
        },
      ]
    : [
        {
          label: 'Thông tin chung',
          value: TabValue.Tab1,
        },
        {
          label: 'Hợp đồng',
          value: TabValue.Tab3,
        },
      ];

const priceBoardRef = ref();

const handleTabChanged = async (tabValue: TabValue) => {
  activeTab.value = tabValue;
  tabStore.setAgentActiveTab(tabValue);
  await triggerTabApi(tabValue);
};

const triggerTabApi = async (tabValue: TabValue) => {
  await nextTick();
  if (tabValue === TabValue.Tab1) {
    await getDetail();
  } else if (tabValue === TabValue.Tab2) {
    priceBoardRef.value?.getDetail();
  } else if (tabValue === TabValue.Tab3 && contractTabRef.value) {
    contractTabRef.value.getContracts();
  }
};
//#endregion

//#region Audit logs
const isVisibleHistoriesPopup = ref(false);

const clickViewAuditLog = async () => {
  isVisibleHistoriesPopup.value = true;
};
//#endregion

// Get agent ID from route
const agentId = computed(() => Number(route.params.id));

// Form setup
const { values, setFieldValue } = useForm();

// Navigation
const openUpdate = () => {
  router.push({
    name: ROUTE_NAME.UPDATE_AGENT,
    params: {
      id: agentId.value,
    },
    query: {
      source: 'agent-detail',
      tab: activeTab.value,
    },
  });
};

const handleAddContract = async () => {
  try {
    overlayLoading.toggleLoading(true);

    const response = await api.get('/business/v1/api/admin/agent/list', {
      params: {
        status: 1,
      },
    });

    if (response.data.code === 0) {
      const activeEnterprises = response.data.data;
      const currentEnterprise = activeEnterprises.find((item: any) => item.id === agentId.value);

      if (currentEnterprise) {
        contractStore.setPreSelectedId(agentId.value);

        const currentPath = `${route.path}?tab=${TabValue.Tab3}`;
        contractStore.setListContractPath(currentPath);

        router.push({
          name: ROUTE_NAME.CONTRACT_AGENT_ADD,
          query: {
            source: 'agent-detail',
            agentId: agentId.value,
          },
        });
      } else {
        toast('error', 'Đại lý phải ở trạng thái hoạt động mới được thêm hợp đồng');
      }
    } else {
      toast('error', response.data.message);
    }
  } catch (error: any) {
    console.error('handleAddContract error:', error);
    handleApiError(error);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

// API calls
const getDetail = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const response = await api.get(`/business/v1/api/admin/agent/${agentId.value}`);

    if (response.data.code === 0) {
      const res = response.data.data;
      setFieldValue('agent_name', res.agent_name);
      setFieldValue('agent_code', res.agent_code);
      setFieldValue('address', res.address);
      setFieldValue('tax_code', res.tax_code);
      setFieldValue('agent_phone', res.agent_phone);
      setFieldValue('agent_email', res.agent_email);
      setFieldValue('contact_name', res.contact_name);
      setFieldValue('contact_phone', res.contact_phone);
      setFieldValue('account_number', res.account_number);
      setFieldValue('contact_email', res.contact_email);
      setFieldValue('agent_type', res.agent_type);
      setFieldValue('status', res.status);
      setFieldValue('message_usage_limit', res.message_usage_limit);
      setFieldValue('contract_deposit', res.contract_deposit);
      setFieldValue('referral_code', res.referral_code);
    } else {
      toast('error', response.data.message);
    }
  } catch (error: any) {
    console.error('getDetail error:', error);
    handleApiError(error);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

watch(
  () => route.query.tab,
  async (newTab) => {
    if (newTab && typeof newTab === 'string') {
      const tabValue = +newTab as TabValue;
      activeTab.value = tabValue;
      tabStore.setAgentActiveTab(tabValue);

      await triggerTabApi(tabValue);
      router.replace({
        path: route.path,
        query: { ...route.query, tab: undefined },
      });
    }
  },
  { immediate: true },
);

onMounted(async () => {
  if (!route.query.tab) {
    activeTab.value = tabStore.agentActiveTab;
    await triggerTabApi(activeTab.value);
  }
});
</script>

<style scoped>
.view-height {
  height: calc(100vh - 265px);
}
</style>
