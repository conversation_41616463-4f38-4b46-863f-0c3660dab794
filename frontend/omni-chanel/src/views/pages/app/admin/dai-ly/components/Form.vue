<template>
  <div class="flex flex-col items-center gap-3 w-[100%]">
    <div class="w-[80%] p-5">
      <div
        class="border-1 border-solid px-[15px] py-[6px] border-[#1A34B5] rounded-[30px] h-[34px] w-fit text-[#1A34B5] text-[14px] font-semibold flex items-center"
      >
        Thông tin đại lý
      </div>
      <el-form label-position="top" class="flex justify-center gap-[75px] mt-5">
        <div class="flex flex-col gap-[15px] w-[50%]">
          <VElementInput
            name="agent_name"
            size="default"
            label="Tên đại lý"
            id="agent-agent_name-input"
            placeholder="Nhập tên đại lý"
            :disabled="props.type === PageType.Details"
            :maxlength="200"
            :showLimit="false"
            :required="true"
          />
          <VElementInput
            v-if="props.type !== PageType.Add"
            name="agent_code"
            size="default"
            label="Mã đại lý"
            id="agent-agent_code-input"
            placeholder="Nhập mã đại lý"
            :disabled="true"
            :showLimit="false"
          />
          <VElementInput
            name="address"
            size="default"
            label="Địa chỉ"
            :maxlength="200"
            :showLimit="false"
            id="agent-address-input"
            placeholder="Nhập địa chỉ"
            :disabled="props.type === PageType.Details"
          />
          <VElementInput
            name="tax_code"
            size="default"
            label="Mã số thuế"
            :maxlength="50"
            :showLimit="false"
            id="agent-tax_code-input"
            placeholder="Nhập mã số thuế"
            :disabled="props.type === PageType.Details"
          />

          <VElementInput
            size="default"
            name="agent_email"
            label="Email đại lý"
            :maxlength="50"
            :showLimit="false"
            id="agent-agent_email-input"
            placeholder="Nhập email đại lý"
            :disabled="props.type === PageType.Details"
          />
          <VElementDropdown
            name="agent_type"
            label="Loại đại lý"
            :formType="type"
            :filterable="false"
            id="agent-agent_type-dropdown"
            :placeholder="getPlaceholder(PLACEHOLDER.SELECT, type)"
            :disabled="props.type === PageType.Details"
            :option="agentTypeOptions"
            :style="'w-[100%]'"
          />
          <el-form-item
            v-if="props.type !== PageType.Add"
            id="agent-account_number-input"
            label="Tài khoản đã tạo"
          >
            <div
              class="h-[33px] w-[100%] flex gap-[5px] items-center bg-[#f4f7fa] border-[1px] border-[#e4e7ec] rounded-[6px] px-[10px] select-none"
            >
              <div class="text-main">{{ values.account_number }}</div>
              <div>Tài khoản</div>
            </div>
          </el-form-item>
        </div>
        <div class="flex flex-col gap-[15px] w-[50%]">
          <VElementInput
            name="agent_phone"
            size="default"
            label="SĐT đại lý"
            :maxlength="50"
            :showLimit="false"
            id="agent-agent_phone-input"
            placeholder="Nhập số điện thoại đại lý"
            :disabled="props.type === PageType.Details"
          />
          <VElementDropdown
            name="status"
            label="Trạng thái"
            id="agent-status-dropdown"
            placeholder="Chọn trạng thái"
            :disabled="props.type === PageType.Details"
            :formType="type"
            :filterable="false"
            :option="statusValueOptions"
            :style="'w-[100%]'"
          />
        </div>
      </el-form>
    </div>
    <div class="w-[80%] mt-2 bg-[#fbfaff] p-5 border-t-2 border-b-2 border-solid border-[#e4e7ec]">
      <div
        class="border-1 border-solid px-[15px] py-[6px] border-[#1A34B5] rounded-[30px] h-[34px] w-fit text-[#1A34B5] text-[14px] font-semibold flex items-center"
      >
        Thông tin người liên hệ
      </div>
      <el-form label-position="top" class="flex justify-center gap-[75px] mt-5">
        <div class="flex flex-col gap-[15px] w-[50%]">
          <VElementInput
            name="contact_name"
            size="default"
            label="Tên người liên hệ"
            :maxlength="50"
            :showLimit="false"
            id="agent-contact_name-input"
            placeholder="Nhập tên người liên hệ"
            :disabled="props.type === PageType.Details"
            :required="true"
          />
          <VElementInput
            name="contact_email"
            size="default"
            label="Email"
            id="agent-contact_email-input"
            placeholder="Nhập email"
            :disabled="props.type === PageType.Details"
            :showLimit="false"
            :maxlength="50"
            :required="true"
          />
        </div>
        <div class="flex flex-col gap-[15px] w-[50%]">
          <VElementInput
            name="contact_phone"
            size="default"
            label="SĐT người liên hệ"
            id="agent-contact_phone-input"
            placeholder="Nhập số điện thoại người liên hệ"
            :disabled="props.type === PageType.Details"
            :showLimit="false"
            :maxlength="50"
            :required="true"
          />
        </div>
      </el-form>
    </div>
    <div class="w-[80%] mt-2 p-5">
      <div
        class="border-1 border-solid px-[15px] py-[6px] border-[#1A34B5] rounded-[30px] h-[34px] w-fit text-[#1A34B5] text-[14px] font-semibold flex items-center"
      >
        Thông tin khác
      </div>
      <el-form label-position="top" class="flex justify-center gap-[75px] pb-[52px] mt-5">
        <div class="flex flex-col gap-[15px] w-[50%]">
          <VElementInput
            required
            name="message_usage_limit"
            size="default"
            label="Hạn mức sử dụng dịch vụ gửi tin"
            id="agent-max_sms_limit-input"
            type="onlyNumber"
            placeholder="Nhập hạn mức sử dụng dịch vụ gửi tin"
            useAppendTemplate
            :maxlength="10"
            :disabled="props.type === PageType.Details"
          >
            <template #append> VND </template>
          </VElementInput>
          <VElementInput
            name="referral_code"
            size="default"
            label="Mã giới thiệu"
            id="agent-referral_code-input"
            placeholder="Nhập mã giới thiệu"
            :disabled="props.type === PageType.Details"
            :maxlength="10"
          />
        </div>
        <div class="flex flex-col gap-[15px] w-[50%]">
          <VElementInput
            name="contract_deposit"
            size="default"
            label="Tiền cọc hợp đồng"
            id="agent-deposit_contract-input"
            type="onlyNumber"
            placeholder="Nhập tiền cọc hợp đồng"
            useAppendTemplate
            :maxlength="10"
            :disabled="props.type === PageType.Details"
          >
            <template #append> VND </template>
          </VElementInput>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useForm } from 'vee-validate';
import { PLACEHOLDER, type TDropdownItem, agentStatusOptions } from '@/shared';
import { agentTypes } from '../index.constant';
import { getPlaceholder } from '@/utils';
import { PageType } from '@/enums/common';

interface Props {
  type: PageType;
  values: any;
}

const props = defineProps<Props>();

const statusValueOptions: TDropdownItem[] = agentStatusOptions.filter(
  (_, index: number) => index !== 0,
);
const agentTypeOptions: TDropdownItem[] = agentTypes.filter((_, index: number) => index !== 0);
</script>
