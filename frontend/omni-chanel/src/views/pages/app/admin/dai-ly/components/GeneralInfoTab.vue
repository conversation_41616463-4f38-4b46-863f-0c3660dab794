<template>
  <div class="mx-[10px] flex justify-center h-[calc(100vh-322px)] mb-[50px] overflow-auto">
    <Form :type="props.type" :values="values" />
  </div>
</template>

<script setup lang="ts">
import { PageType } from '@/enums/common';
import Form from './Form.vue';

interface Props {
  type: PageType;
  values: any;
}

const props = defineProps<Props>();
</script>

<style scoped>
.view-height {
  height: calc(100vh - 265px);
}
</style>
