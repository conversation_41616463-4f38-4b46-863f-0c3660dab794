<template>
  <!-- Header -->
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:building-bank" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item id="enterprise-breadcrumb" :to="{ path: ROUTE_PATH.ENTERPRISE }"
          >Doanh nghiệp</el-breadcrumb-item
        >
        <el-breadcrumb-item :to="{ path: ROUTE_PATH.ENTERPRISE }">Đã duyệt</el-breadcrumb-item>
        <el-breadcrumb-item>Xem chi tiết</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>

  <!-- Tabs -->
  <BaseTabs :tabs :active-tab="activeTab" tabClass="w-[94%]" @click-tab="handleTabChanged">
    <template #tab-1>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <GeneralInfoTab :type="PageType.Details" :values="values" :listAgent="listAgent" />
      </div>
    </template>
    <template #tab-2>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <FormDetailsEnterprise
          ref="priceBoardRef"
          :customer-id="props.id ?? 0"
          :type="PageType.Details"
        />
      </div>
    </template>
    <template #tab-3>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <ContractTab ref="contractTabRef" :enterpriseId="enterpriseId" />
      </div>
    </template>
    <template #tab-4>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-auto pt-[10px] pb-[40px]">
        <EPointTab ref="epointTabRef" />
      </div>
    </template>
  </BaseTabs>

  <!-- Footer -->
  <div
    class="flex z-[999] py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke items-center justify-end"
  >
    <VElementButton
      v-if="activeTab === TabValue.Tab2"
      icon="history"
      label="Xem lịch sử chỉnh sửa"
      :bgColor="color.secondary"
      @click="clickViewAuditLog"
    />
    <VElementButton
      v-if="activeTab === TabValue.Tab1 || activeTab === TabValue.Tab2"
      label="Cập nhật"
      styleButton="s"
      id="enterprise-update-button"
      :bgColor="color.main"
      @click="openUpdate"
    />
    <VElementButton
      v-if="activeTab === TabValue.Tab3"
      label="Thêm hợp đồng"
      styleButton="s"
      id="enterprise-add-contract-button"
      :bgColor="color.main"
      @click="handleAddContract"
    />
  </div>

  <PopupAudit
    :serviceType="priceBoardRef?.activeTab === TabValue.Tab1 ? ServiceType.SMS : ServiceType.ZNS"
    :adminView="false"
    :priceType="PriceType.SellingPrice"
    :customer-id="props.id"
    :visible="isVisibleHistoriesPopup"
    :customerType="CustomerType.Enterprise"
    @onClose="isVisibleHistoriesPopup = false"
  />
</template>

<script setup lang="ts">
import { onMounted, ref, inject, computed, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useForm } from 'vee-validate';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { ROUTE_NAME, ROUTE_PATH, TabValue, TEXT, type TDropdownItem } from '@/shared';
import { useContractStore } from '@/store/contract';
import GeneralInfoTab from './components/GeneralInfoTab.vue';
import ContractTab from './components/ContractTab.vue';
import BaseTabs from '../cai-dat/bang-gia/components/BaseTabs.vue';
import { CustomerType, type TTab } from '../cai-dat/bang-gia/index.type';
import FormDetailsEnterprise from '../cai-dat/bang-gia/bang-gia-ban/FormDetailsEnterprise.vue';
import PopupAudit from '../cai-dat/bang-gia/components/PopupAudit.vue';
import { AccountType, PageType } from '@/enums/common';
import { useTab } from '@/store/useTab';
import { handleApiError } from '@/utils/useErrorHandler';
import { PriceType, ServiceType } from '../cai-dat/bang-gia/index.constants';
import { CUSTOMER_SOURCE } from '../doanh-nghiep/index.constant';
import EPointTab from './components/EPointTab.vue';
import { useEpointStep } from '@/store/useEPointStep';

const props = defineProps<{
  id?: number;
  type: PageType;
}>();

const route = useRoute();
const router = useRouter();
const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const contractStore = useContractStore();
const tabStore = useTab();
const accType: any = localStorage.getItem('accountType') || AccountType.Admin;
const epointTabRef = ref();
const epointStore: any = useEpointStep();

// State
const listAgent = ref<TDropdownItem[]>([]);
const contractTabRef = ref();

//#region Tabs
const activeTab = ref<TabValue>(TabValue.Tab1);

const tabs = ref<TTab[]>([
  {
    label: 'Thông tin chung',
    value: TabValue.Tab1,
  },
  {
    label: 'Bảng giá',
    value: TabValue.Tab2,
  },
  {
    label: 'Hợp đồng',
    value: TabValue.Tab3,
  },
  {
    label: 'E-Point',
    value: TabValue.Tab4,
  },
]);

const priceBoardRef = ref();

const handleTabChanged = async (tabValue: TabValue) => {
  activeTab.value = tabValue;
  tabStore.setEnterpriseActiveTab(tabValue);
  await triggerTabApi(tabValue);
};

const triggerTabApi = async (tabValue: TabValue) => {
  await getDetail();
  await nextTick();
  if (tabValue === TabValue.Tab1) {
    await getAgent();
  } else if (tabValue === TabValue.Tab2) {
    priceBoardRef.value?.getDetail();
  } else if (tabValue === TabValue.Tab3 && contractTabRef.value) {
    contractTabRef.value.getContracts();
  } else if (tabValue === TabValue.Tab4) {
    epointTabRef.value?.getInfoEpoint();
  }
};
//#endregion

//#region Audit logs
const isVisibleHistoriesPopup = ref(false);

const clickViewAuditLog = async () => {
  isVisibleHistoriesPopup.value = true;
};
//#endregion

// Get enterprise ID from route
const enterpriseId = computed(() => Number(route.params.id));

// Form setup
const { values, setFieldValue } = useForm();

// Navigation
const openUpdate = () => {
  router.push({
    name: ROUTE_NAME.UPDATE_ENTERPRISE,
    params: {
      id: enterpriseId.value,
    },
    query: {
      source: 'enterprise-detail',
      tab: activeTab.value,
    },
  });
};

const handleAddContract = async () => {
  try {
    overlayLoading.toggleLoading(true);

    const response = await api.get('/business/v1/api/admin/business/list', {
      params: {
        status: 1,
      },
    });

    if (response.data.code === 0) {
      const activeEnterprises = response.data.data;
      const currentEnterprise = activeEnterprises.find(
        (item: any) => item.id === enterpriseId.value,
      );

      if (currentEnterprise) {
        contractStore.setPreSelectedId(enterpriseId.value);

        const currentPath = `${route.path}?tab=${TabValue.Tab3}`;
        contractStore.setListContractPath(currentPath);

        router.push({
          name: ROUTE_NAME.CONTRACT_ENTERPRISE_ADD,
          query: {
            source: 'enterprise-detail',
            enterpriseId: enterpriseId.value,
          },
        });
      } else {
        toast('error', 'Doanh nghiệp phải ở trạng thái hoạt động mới được thêm hợp đồng');
      }
    } else {
      toast('error', response.data.message);
    }
  } catch (error: any) {
    console.error('handleAddContract error:', error);
    handleApiError(error);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

// API calls
const getDetail = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const response = await api.get(`/business/v1/api/admin/business/${enterpriseId.value}`);

    if (response.data.code === 0) {
      const res = response.data.data;
      setFieldValue('business_name', res.business_name);
      setFieldValue('business_code', res.business_code);
      setFieldValue('address', res.address);
      setFieldValue('tax_code', res.tax_code);
      setFieldValue('business_phone', res.business_phone);
      setFieldValue('business_email', res.business_email);
      setFieldValue('contact_name', res.contact_name);
      setFieldValue('contact_phone', res.contact_phone);
      setFieldValue('account_number', res.account_number);
      setFieldValue('contact_email', res.contact_email);
      setFieldValue('customer_source', res.customer_source);
      setFieldValue('agent_id', res.agent_id);
      setFieldValue('status', res.status);
      setFieldValue('payment_method', res.payment_method);
      setFieldValue('contract_deposit', res.contract_deposit);
      setFieldValue('referral_code', res.referral_code);
      setFieldValue('message_usage_limit', res.message_usage_limit);
      setFieldValue('business_label_type', res.business_label_type);
      setFieldValue('channel_types', res.channel_types);
      setFieldValue('channel_type', res.channel_type);
      epointStore.businessId = res.agent_id;
      epointStore.businessName = res.business_name;
    } else {
      toast('error', response.data.message);
    }
  } catch (error: any) {
    console.error('getDetail error:', error);
    handleApiError(error);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

const getAgent = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const response = await api.get(`/business/v1/api/admin/agent/list`, {
      params: { status: null },
    });

    if (response.data.code === 0 && response.data.data) {
      listAgent.value = response.data.data.map((item: { id: number; agent_name: string }) => ({
        value: item.id,
        label: item.agent_name,
      }));
    } else {
      toast('error', response.data.message);
    }
  } catch (error) {
    console.error('getAgent error:', error);
    toast('error', TEXT.ERROR_OCCURRED);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

watch(
  () => route.query.tab,
  async (newTab) => {
    if (newTab && typeof newTab === 'string') {
      const tabValue = +newTab as TabValue;
      activeTab.value = tabValue;
      tabStore.setEnterpriseActiveTab(tabValue);
      await triggerTabApi(tabValue);

      router.replace({
        path: route.path,
        query: { ...route.query, tab: undefined },
      });
    }
  },
  { immediate: true },
);

const setTabs = () => {
  if (
    (values.customer_source &&
      values.customer_source !== CUSTOMER_SOURCE.AGENT &&
      accType == AccountType.Admin) ||
    accType == AccountType.Agent
  ) {
    if (values.payment_method === 1) {
      tabs.value = [
        {
          label: 'Thông tin chung',
          value: TabValue.Tab1,
        },
        {
          label: 'Bảng giá',
          value: TabValue.Tab2,
        },
        {
          label: 'Hợp đồng',
          value: TabValue.Tab3,
        },
        {
          label: 'E-Point',
          value: TabValue.Tab4,
        },
      ];
    } else {
      tabs.value = [
        {
          label: 'Thông tin chung',
          value: TabValue.Tab1,
        },
        {
          label: 'Bảng giá',
          value: TabValue.Tab2,
        },
        {
          label: 'Hợp đồng',
          value: TabValue.Tab3,
        },
      ];
    }
  } else if (values.customer_source === CUSTOMER_SOURCE.AGENT && accType == AccountType.Admin) {
    if (values.payment_method === 1) {
      tabs.value = [
        {
          label: 'Thông tin chung',
          value: TabValue.Tab1,
        },
        {
          label: 'E-Point',
          value: TabValue.Tab4,
        },
      ];
    } else {
      tabs.value = [
        {
          label: 'Thông tin chung',
          value: TabValue.Tab1,
        },
      ];
    }
  }
};

onMounted(async () => {
  if (!route.query.tab) {
    activeTab.value = tabStore.enterpriseActiveTab;
    await triggerTabApi(activeTab.value);
    setTabs();
  }
  if (route.query.tab === '4' || (route.query.tab as any) === 4) {
    await triggerTabApi(activeTab.value);
    setTabs();
    activeTab.value = 4;
    epointTabRef.value?.getInfoEpoint();
  }
});
</script>

<style scoped>
.view-height {
  height: calc(100vh - 265px);
}
</style>
