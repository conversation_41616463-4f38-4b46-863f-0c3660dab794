<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:building-bank" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: ROUTE_PATH.PENDING_ENTERPRISE }"
          >Doanh nghiệp</el-breadcrumb-item
        >
        <el-breadcrumb-item>Chờ duyệt</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
  <div class="mx-[10px] mt-[20px]">
    <div class="relative filter-container">
      <div class="flex gap-[12px]">
        <VElementButton
          styleIcon="text-[20px] text-[#fff] mr-[7px]"
          icon="filter-plus"
          class="filter"
          styleButton="s"
          id="enterprise-filter-button"
          :bgColor="color.tertiary"
          :label="`Bộ lọc ${filterCount}`"
          @click="isShowFilter = !isShowFilter"
        />
        <VElementInput
          size="default"
          name="searchKey"
          prefix="search"
          placeholder="Tìm kiếm theo Tên doanh nghiệp, Tên, SĐT, Email người liên hệ ..."
          id="enterprise-search-input"
          :style="'!w-[480px]'"
          @keyup.enter="getList()"
        />
        <VElementButton
          label="Tìm kiếm"
          styleButton="s"
          id="enterprise-search-button"
          :bgColor="color.main"
          @click="onFilter"
        />
        <Transition>
          <FilterCard
            v-show="isShowFilter"
            :visible="isShowFilter"
            ref="filterRef"
            v-on-click-outside="onClickOutsideHandler"
            class="w-[360px]"
            widthInput="!w-[190px]"
            id="enterprise-filter-card"
            :dataFilter="dataFilter"
            @filter="filterData"
            @onChange="handleChangeFilter"
            @onResetForm="handleResetFilter"
          />
        </Transition>
      </div>
    </div>
    <VOldTable
      ref="tableRef"
      idTable="enterprise-table"
      emptyText="Không tìm thấy doanh nghiệp phù hợp"
      :isRowClick="true"
      :rows="listEnterprise"
      :styleHeaders="filteredStyleHeaders"
      :headers="filteredHeaders"
      :keySortTable="sortTableKeys"
      :pagination="{
        totalPage: pagination.total_page,
        total: pagination.record_count,
        perPage: params.page_size,
      }"
      @sortTable="sortTable"
      @selectRow="getRow"
      @pageChanged="onPageChange"
      @perPageChange="onPerPageChange"
      @setWidth="onSetWidth"
      @rowClick="handleNavigateToDetail"
    >
      <template v-slot:items="{ row, index }">
        <td class="text-primaryText px-2">
          <div class="column-container text-left" :title="row?.business_name">
            {{ row?.business_name }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container text-left" :title="row?.contact_name">
            {{ row?.contact_name }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container text-center" :title="row?.contact_phone">
            {{ row?.contact_phone }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container text-left" :title="row?.contact_email">
            {{ row?.contact_email }}
          </div>
        </td>
        <td v-if="accountTypeStorage === AccountType.Admin" class="text-primaryText px-2">
          <div class="column-container text-left" :title="row?.agent_name">
            {{ row?.agent_name }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div
            class="column-container text-left"
            :title="getLabelBusinessType(row?.label_types, listLabelType)"
          >
            {{ getLabelBusinessType(row?.label_types, listLabelType) }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div
            class="column-container text-left"
            :title="getServiceNamesFromIds(row?.channel_types)"
          >
            {{ getServiceNamesFromIds(row?.channel_types) }}
          </div>
        </td>
        <td class="text-primaryText px-2 text-center">
          <Tag :class="getColorStatus(row?.status)" :value="getLabelStatus(row?.status)" />
        </td>
      </template>
      <template v-slot:actions="{ row }">
        <li
          class="item min-w-[100px]"
          :id="`enterprise-action-details-${row?.id}`"
          @click="handleNavigateToDetail(row)"
          @keydown="handleNavigateToDetail(row)"
        >
          <i class="pi pi-eye text-xs mr-3"></i><span>Xem</span>
        </li>
        <li
          v-if="
            row?.status === PendingEnterpriseStatus.Pending ||
            row?.status === PendingEnterpriseStatus.Reject
          "
          class="item min-w-[100px]"
          :id="`enterprise-action-approve-${row?.id}`"
          @click="handleApproveItem(row)"
          @keydown="handleApproveItem(row)"
        >
          <i class="pi pi-check text-xs mr-3"></i><span>Duyệt</span>
        </li>
        <li
          v-if="row?.status === PendingEnterpriseStatus.Pending"
          class="item min-w-[100px]"
          :id="`enterprise-action-reject-${row?.id}`"
          @click="handleRejectItem(row)"
          @keydown="handleRejectItem(row)"
        >
          <i class="pi pi-times text-xs mr-3"></i><span>Từ chối</span>
        </li>
        <li
          class="item min-w-[100px]"
          :id="`enterprise-action-update-${row?.id}`"
          @click="handleNavigateToUpdate(row)"
          @keydown="handleNavigateToUpdate(row)"
        >
          <i class="pi pi-pencil text-xs mr-3"></i><span>Cập nhật</span>
        </li>
        <li
          class="item min-w-[100px]"
          :id="`enterprise-action-delete-${row?.id}`"
          @click="handleDeleteItem(row)"
          @keydown="handleDeleteItem(row)"
        >
          <i class="pi pi-trash text-xs mr-3"></i><span>Xóa</span>
        </li>
      </template>
    </VOldTable>
  </div>
  <PopupDelete
    v-model:visible="visibleModalDelete"
    :id="deleteId"
    :name="deleteName"
    path="/business/v1/api/admin/business/"
    @onConfirm="getList"
  />

  <ApprovePendingEnterprisePopup
    v-if="visibleApprovePopup"
    v-model:visible="visibleApprovePopup"
    :id="selectedEnterprise?.id"
    :agentId="selectedEnterprise?.agent_id"
    :customerSource="selectedEnterprise?.customer_source"
    :enterprise="selectedEnterprise"
    @onClose="visibleApprovePopup = false"
    @onConfirm="handleCloseApprovePopup"
  />
</template>

<script setup lang="ts">
import { onMounted, ref, inject, onBeforeUnmount, computed } from 'vue';
import { useForm } from 'vee-validate';
import { useRouter } from 'vue-router';
import { vOnClickOutside } from '@vueuse/components';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useTab } from '@/store/useTab';
import {
  FILTER_FIELD,
  PLACEHOLDER,
  ROUTE_NAME,
  ROUTE_PATH,
  TEXT,
  type IEmit,
  type TPagination,
} from '@/shared';
import { AccountType } from '@/enums/common';
import { PendingEnterpriseStatus, EnterpriseCategoryType } from '@/enums/enterprise';
import { handleApiError } from '@/utils/useErrorHandler';
import {
  pendingAdminHeaders,
  pendingHeaders,
  pendingAdminStyleHeaders,
  pendingStyleHeaders,
  sortTableKeys,
  PENDING_STATUS_OPTIONS,
} from './index.constant';
import { getColorStatus, getLabelStatus } from './index.utils';
import { getServiceNamesFromIds } from '@/components/pages/Contract/index.utils';
import FilterCard from '@/components/base/container/FilterCard.vue';
import { PopupDelete } from '@/components/base/new';
import VOldTable from '@/components/base/VOldTable.vue';
import ApprovePendingEnterprisePopup from './components/popup/ApprovePendingEnterprisePopup.vue';
import { SERVICE_OPTIONS } from '@/constants/common';

export interface IParamsSearch {
  page_index: number;
  page_size: number;
  keyword?: string;
  businessCategory?: number;
  labelTypes?: string | number;
  channelTypes?: string | number;
  status?: number | null;
}

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const tabStore = useTab();
const router = useRouter();
const { values } = useForm({});

const filteredHeaders = computed(() => {
  if (accountTypeStorage === AccountType.Admin) {
    return pendingAdminHeaders;
  } else {
    return pendingHeaders;
  }
});

const filteredStyleHeaders = computed(() => {
  if (accountTypeStorage === AccountType.Admin) {
    return pendingAdminStyleHeaders;
  } else {
    return pendingStyleHeaders;
  }
});
const dataFilter = computed(() => {
  const filters = [];

  filters.push({
    label: 'Lĩnh vực hoạt động',
    type: FILTER_FIELD.DROPDOWN,
    valueName: 'labelTypes',
    placeholder: PLACEHOLDER.SELECT,
    filterable: true,
    multiple: true,
    clearable: true,
    searchable: true,
    dropdownConfig: {
      option: listLabelType.value,
    },
  });

  filters.push({
    label: 'Dịch vụ sử dụng',
    type: FILTER_FIELD.DROPDOWN,
    valueName: 'channelTypes',
    multiple: true,
    clearable: false,
    placeholder: PLACEHOLDER.SELECT,
    dropdownConfig: {
      option: SERVICE_OPTIONS,
    },
  });

  filters.push({
    label: 'Trạng thái',
    type: FILTER_FIELD.DROPDOWN,
    valueName: 'status',
    placeholder: PLACEHOLDER.SELECT,
    clearable: true,
    dropdownConfig: {
      option: PENDING_STATUS_OPTIONS,
    },
  });

  return filters;
});

const filterRef = ref<InstanceType<typeof FilterCard>>();
const filterCount = ref('');
const isShowFilter = ref(false);
const pagination = ref<TPagination>({
  total_page: 1,
  record_count: 20,
});
const order = ref();
const tableRef = ref();
const tableWidth = ref();
const params = ref<IParamsSearch>({
  page_index: 1,
  page_size: 20,
});
const deleteName = ref();
const deleteId = ref();
const listIDSelected = ref([]);
const visibleModalDelete = ref(false);
const visibleApprovePopup = ref(false);
const selectedEnterprise = ref<any>(null);
const listEnterprise = ref<any>([]);
const listLabelType = ref<any>([]);
const accountTypeStorage = Number(localStorage.getItem('accountType'));

const filterData = (value: any, isInputSearch: boolean = false) => {
  isShowFilter.value = false;
  if (filterRef.value && !isInputSearch) {
    if (filterRef.value.count && filterRef.value.count > 0 && !isDefaultFilter()) {
      filterCount.value = `(${filterRef.value.count})`;
    } else {
      filterCount.value = '';
    }
  }
  tableRef.value.filterData();
};

const isDefaultFilter = () => {
  return !params.value.labelTypes && !params.value.channelTypes && !params.value.status;
};

const onClickOutsideHandler = (ev: Event) => {
  const target = ev.target as Element;
  if (target.classList.contains('el-select-dropdown__option-item')) return;
  if (target.classList.contains('el-scrollbar__thumb')) return;
  if (target.closest('.el-select-dropdown__option-item')) return;
  if (target.closest('.el-picker-panel__body')) return;
  if (target.closest('.filter')) return;
  isShowFilter.value = false;
};

const onFilter = () => {
  if (filterRef.value) {
    filterRef.value.onSubmit();
    if (filterRef.value.count && filterRef.value.count > 0 && !isDefaultFilter()) {
      filterCount.value = `(${filterRef.value.count})`;
    } else {
      filterCount.value = '';
    }
    return;
  }
  tableRef.value.filterData();
};

const handleChangeFilter = (data: IEmit) => {
  switch (data.name) {
    case 'labelTypes':
    case 'channelTypes':
    case 'status':
      params.value[data.name] = data.value;
      break;
  }
};

const handleResetFilter = () => {
  filterCount.value = '';
  params.value.labelTypes = undefined;
  params.value.channelTypes = undefined;
  params.value.status = null;
  filterRef.value?.setFieldValue('labelTypes', undefined);
  filterRef.value?.setFieldValue('channelTypes', undefined);
  filterRef.value?.setFieldValue('status', null);
};

const handleCloseApprovePopup = () => {
  visibleApprovePopup.value = false;
  getList();
};

const handleNavigateToDetail = (item: any) => {
  router.push({
    name: ROUTE_NAME.DETAILS_PENDING_ENTERPRISE,
    params: {
      id: item.id,
    },
  });
};

const handleNavigateToUpdate = (item: any) => {
  router.push({
    name: ROUTE_NAME.UPDATE_PENDING_ENTERPRISE,
    params: {
      id: item.id,
    },
  });
};

const onSetWidth = (value: number) => {
  tableWidth.value = value;
};

const onPageChange = (value: number) => {
  params.value.page_index = value;
  getList();
};

const onPerPageChange = (value: number) => {
  params.value.page_size = value;
};

const getRow = function (value: any) {
  listIDSelected.value = value.map((item: any) => item.id);
};

const sortTable = (value: any) => {
  if (Object.values(value)[0] === 'desc') {
    order.value = `-${Object.keys(value)[0]}` as string;
  } else {
    order.value = Object.keys(value)[0] as string;
  }
  getList();
};

const handleDeleteItem = (data: any) => {
  deleteId.value = data.id;
  deleteName.value = data.business_name;
  visibleModalDelete.value = true;
};

const handleApproveItem = (data: any) => {
  selectedEnterprise.value = data;
  visibleApprovePopup.value = true;
};

const handleRejectItem = async (data: any) => {
  try {
    overlayLoading.toggleLoading(true);
    const response = await api.put(`/business/v1/api/admin/business/refuse/${data.id}`);
    if (response.data.code === 0) {
      toast('success', response.data.message || 'Từ chối doanh nghiệp thành công');
      await getList();
    } else {
      toast('error', response.data.message || 'Có lỗi xảy ra khi từ chối doanh nghiệp');
    }
  } catch (error: any) {
    console.error('Reject error:', error);
    if (!error?.response?.status) {
      handleApiError(error);
    }
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

const getLabels = async () => {
  try {
    const res = await api.get(`/business/v1/api/admin/business/label-types`);
    if (res.data.code === 0 && res.data.data) {
      listLabelType.value = res.data.data;
    } else {
      toast('error', res.data.message);
    }
    overlayLoading.toggleLoading(false);
  } catch (error: any) {
    overlayLoading.toggleLoading(false);
    if (!error?.response?.status) {
      toast('error', TEXT.ERROR_OCCURRED);
    }
  }
};

const getLabelBusinessType = (labelTypes: number[], listLabelType: any[]): string => {
  if (!labelTypes || labelTypes.length === 0) return '';

  const labels = labelTypes
    .map((id) => {
      const field = listLabelType.find((option) => option.value === id);
      return field ? field.label : id.toString();
    })
    .filter(Boolean);

  return labels.join(', ') || '';
};

const getList = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const url = `/business/v1/api/admin/business`;

    const requestData: any = {
      pageIndex: params.value?.page_index,
      pageSize: params.value?.page_size,
      businessCategory: EnterpriseCategoryType.Pending,
      labelTypes: params.value?.labelTypes,
      channelTypes: params.value?.channelTypes,
      status: params.value?.status,
      keyword: values?.searchKey ? values?.searchKey.trim() : undefined,
    };

    if (!requestData.keyword && requestData.keyword !== '') {
      delete requestData.keyword;
    }
    if (!requestData.labelTypes) {
      delete requestData.labelTypes;
    }
    if (!requestData.channelTypes) {
      delete requestData.channelTypes;
    }
    if (!requestData.status) {
      delete requestData.status;
    }

    const requestEncoded = new URLSearchParams(requestData);
    const res = await api.get(url, { params: requestEncoded });

    if (res.data.code === 0 && res.data.data) {
      listEnterprise.value = res.data.data.items;
      pagination.value = {
        total_page: res.data.data.paging?.total_pages,
        record_count: res.data.data.paging?.total_records,
      };
      if (res.data.data?.items?.length === 0 && params.value.page_index !== 1) {
        tableRef.value.onPageChange(1);
        tableRef.value.filterData();
        return;
      }
    } else {
      toast('error', res.data.message);
    }
    overlayLoading.toggleLoading(false);
  } catch (error: any) {
    overlayLoading.toggleLoading(false);
    if (!error?.response?.status) {
      toast('error', TEXT.ERROR_OCCURRED);
    }
  }
};

onMounted(async () => {
  await getLabels();
  await getList();
});

onBeforeUnmount(() => {
  tabStore.clear();
});
</script>

<style lang="scss" scoped>
.item {
  padding: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 131px;
  font-size: 14px;
  font-weight: 400;
  &:hover {
    color: var(--main-color);
  }
}
:deep(.p-tag) {
  width: 120px;
}
</style>
