<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:building-bank" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: ROUTE_PATH.PENDING_ENTERPRISE }"
          >Doanh nghiệp</el-breadcrumb-item
        >
        <el-breadcrumb-item :to="{ path: ROUTE_PATH.PENDING_ENTERPRISE }"
          >Chờ duyệt</el-breadcrumb-item
        >
        <el-breadcrumb-item>{{ getLabelForm(type) }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>

  <div class="h-[calc(100vh-80px-42px-53px)] overflow-hidden">
    <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-auto">
      <div class="flex flex-col items-center gap-3 w-[100%]">
        <div class="w-[80%] p-5">
          <div
            class="border-1 border-solid px-[15px] py-[6px] border-[#1A34B5] rounded-[30px] h-[34px] w-fit text-[#1A34B5] text-[14px] font-semibold flex items-center"
          >
            Thông tin doanh nghiệp
          </div>
          <el-form label-position="top" class="w-[100%] flex justify-center gap-[75px] mt-5">
            <div class="flex flex-col gap-[15px] w-[50%]">
              <VElementInput
                name="business_name"
                size="default"
                label="Tên doanh nghiệp"
                id="onboarding-business_name-input"
                :placeholder="getPlaceholder(PLACEHOLDER.ENTERPRISE.TYPE_ENTERPRISE_NAME, type)"
                :disabled="isDisabled"
                :required="true"
                :maxlength="200"
                :showLimit="false"
              />
              <VElementInput
                name="address"
                size="default"
                label="Địa chỉ"
                id="onboarding-address-input"
                :placeholder="getPlaceholder(PLACEHOLDER.ENTERPRISE.TYPE_ADDRESS, type)"
                :disabled="isDisabled"
                :maxlength="200"
                :showLimit="false"
              />
              <VElementInput
                name="tax_code"
                size="default"
                label="Mã số thuế"
                id="onboarding-tax_code-input"
                :placeholder="getPlaceholder(PLACEHOLDER.ENTERPRISE.TYPE_TAX_CODE, type)"
                :disabled="isDisabled"
                :maxlength="50"
                :showLimit="false"
              />
              <VElementInput
                size="default"
                name="business_email"
                label="Email doanh nghiệp"
                id="onboarding-business_email-input"
                :placeholder="getPlaceholder(PLACEHOLDER.ENTERPRISE.TYPE_ENTERPRISE_EMAIL, type)"
                :disabled="isDisabled"
                :showLimit="false"
              />
            </div>
            <div class="flex flex-col gap-[15px] w-[50%]">
              <VElementInput
                name="business_phone"
                size="default"
                label="SĐT doanh nghiệp"
                id="onboarding-business_phone-input"
                :placeholder="getPlaceholder(PLACEHOLDER.ENTERPRISE.TYPE_ENTERPRISE_PHONE, type)"
                :disabled="isDisabled"
                :maxlength="50"
                :showLimit="false"
              />
              <VElementDropdown
                name="business_label_type"
                label="Lĩnh vực hoạt động"
                multiple
                clearable
                :formType="type"
                :filterable="true"
                id="onboarding-business_label_type-dropdown"
                :placeholder="getPlaceholder(PLACEHOLDER.ENTERPRISE.SELECT_LABEL_TYPE, type)"
                :disabled="isDisabled"
                :required="true"
                :option="labelTypeOptions"
              />
              <VElementDropdown
                name="channel_types"
                label="Dịch vụ sử dụng"
                id="onboarding-channel_types-dropdown"
                multiple
                clearable
                :placeholder="getPlaceholder(PLACEHOLDER.ENTERPRISE.SELECT_SERVICE, type)"
                :disabled="isDisabled"
                :required="true"
                :formType="type"
                :filterable="false"
                :option="SERVICE_OPTIONS"
              />
              <VElementDropdown
                name="status"
                label="Trạng thái"
                id="onboarding-status-dropdown"
                :placeholder="getPlaceholder(PLACEHOLDER.ENTERPRISE.SELECT_STATUS, type)"
                :disabled="true"
                :formType="type"
                :filterable="false"
                :option="PENDING_STATUS_OPTIONS"
              />
              <el-form-item
                v-if="props.type !== PageType.Add"
                id="enterprise-account_number-input"
                label="Tài khoản đã tạo"
              >
                <div
                  class="h-[33px] w-[100%] flex gap-[5px] items-center bg-[#f4f7fa] border-[1px] border-[#e4e7ec] rounded-[6px] px-[10px] select-none"
                >
                  <div class="text-main">{{ values.account_number }}</div>
                  <div>Tài khoản</div>
                </div>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <div
          class="w-[80%] p-5 w-[80%] mt-2 bg-[#fbfaff] p-5 border-t-2 border-b-2 border-solid border-[#e4e7ec]"
        >
          <div
            class="border-1 border-solid px-[15px] py-[6px] border-[#1A34B5] rounded-[30px] h-[34px] w-fit text-[#1A34B5] text-[14px] font-semibold flex items-center"
          >
            Thông tin người liên hê
          </div>
          <el-form label-position="top" class="w-[100%] flex justify-center gap-[75px] mt-5">
            <div class="flex flex-col gap-[15px] w-[50%]">
              <VElementInput
                name="contact_name"
                size="default"
                label="Tên người liên hệ"
                id="onboarding-contact_name-input"
                :placeholder="getPlaceholder(PLACEHOLDER.ENTERPRISE.TYPE_CONTACT_NAME, type)"
                :disabled="isDisabled"
                :required="true"
                :maxlength="50"
                :showLimit="false"
              />
              <VElementInput
                name="contact_email"
                size="default"
                label="Email"
                id="onboarding-contact_email-input"
                :placeholder="getPlaceholder(PLACEHOLDER.ENTERPRISE.TYPE_CONTACT_EMAIL, type)"
                :disabled="isDisabled"
                :required="true"
                :maxlength="50"
                :showLimit="false"
              />
            </div>
            <div class="flex flex-col gap-[15px] w-[50%]">
              <VElementInput
                name="contact_phone"
                size="default"
                label="SĐT người liên hệ"
                id="onboarding-contact_phone-input"
                :placeholder="getPlaceholder(PLACEHOLDER.ENTERPRISE.TYPE_CONTACT_PHONE, type)"
                :disabled="isDisabled"
                :required="true"
                :maxlength="50"
                :showLimit="false"
              />
            </div>
          </el-form>
        </div>
        <div class="w-[80%] p-5">
          <div
            class="border-1 border-solid px-[15px] py-[6px] border-[#1A34B5] rounded-[30px] h-[34px] w-fit text-[#1A34B5] text-[14px] font-semibold flex items-center"
          >
            Thông tin khác
          </div>
          <el-form label-position="top" class="w-[100%] flex gap-[75px] mt-5">
            <div class="flex flex-col gap-[15px] w-[50%]">
              <VElementDropdown
                name="payment_method"
                label="Hình thức thanh toán"
                id="onboarding-payment_method-dropdown"
                :disabled="true"
                :formType="type"
                :filterable="false"
                :option="PAYMENT_METHOD_OPTIONS"
              />
            </div>
            <div class="flex flex-col gap-[15px] w-[50%]"></div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
  <BottomSection>
    <template #left>
      <VElementButton
        v-if="type === PageType.Details"
        icon="arrow-narrow-left"
        text="black"
        bgColor="#EBEBEB"
        label="Quay lại"
        @click="handleNavigateToList"
      />
    </template>
    <template #right>
      <div class="flex">
        <VElementButton
          v-if="type !== PageType.Details"
          styleButton="s"
          label="Hủy"
          id="enterprise-cancel-button"
          :bgColor="color.closeButton"
          @click="handleClickCancel"
        />
        <VElementButton
          v-if="type !== PageType.Details"
          label="Lưu"
          styleButton="s"
          id="enterprise-save-button"
          :bgColor="color.main"
          @click="onSubmit"
        />
        <VElementButton
          v-if="
            type === PageType.Details &&
            (statusDetail === PendingEnterpriseStatus.Pending ||
              statusDetail === PendingEnterpriseStatus.Reject)
          "
          :bgColor="color.tertiary"
          label="Duyệt"
          @click="isVisibleModalApprove = true"
        />
        <VElementButton
          v-if="type === PageType.Details && statusDetail === PendingEnterpriseStatus.Pending"
          label="Từ chối"
          :bgColor="color.secondary"
          @click="handleRejectItem"
        />
        <VElementButton
          v-if="type === PageType.Details"
          label="Cập nhật"
          styleButton="s"
          id="enterprise-update-button"
          :bgColor="color.main"
          @click="handleNavigateToUpdate"
        />
      </div>
    </template>
  </BottomSection>

  <ApprovePendingEnterprisePopup
    v-if="isVisibleModalApprove"
    v-model:visible="isVisibleModalApprove"
    :id="props.id"
    :enterprise="enterprise"
    :agentId="enterprise.agent_id"
    :customerSource="values?.customer_source"
    @onClose="isVisibleModalApprove = false"
    @onConfirm="handleApproveSuccess"
  />

  <PopupCancelConfirm
    v-model:popupVisible="isShowConfirmPopup"
    @onClose="isShowConfirmPopup = false"
    @onConfirm="back"
  />
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch } from 'vue';
import * as yup from 'yup';
import { useForm } from 'vee-validate';
import { useRouter } from 'vue-router';
import { REGEX, ROUTE_NAME, ROUTE_PATH } from '@/shared';
import { PageType } from '@/enums/common';
import { ERROR_MESSAGE } from '@/constants/templateText';
import { PLACEHOLDER, TEXT } from '@/shared/text.shared';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { toast } from '@/utils/useToast';
import { handleApiError } from '@/utils/useErrorHandler';
import { getLabelForm, getNullableString, getPlaceholder } from '@/utils';
import { PendingEnterpriseStatus } from '@/enums/enterprise';
import { SERVICE_OPTIONS } from '@/constants/common';
import { PAYMENT_METHOD_OPTIONS, PENDING_STATUS_OPTIONS } from './index.constant';
import BottomSection from '@/components/pages/layouts/BottomSection.vue';
import ApprovePendingEnterprisePopup from './components/popup/ApprovePendingEnterprisePopup.vue';
import PopupCancelConfirm from '@/components/base/common/PopupCancelConfirm.vue';

interface Props {
  id: number;
  type: PageType;
}

const props = defineProps<Props>();

const api = useApi();
const router = useRouter();
const overlayLoading = useOverLayLoadingStore();

const validationSchema = yup.object({
  business_name: yup.string().trim().required(ERROR_MESSAGE.REQUIRED_FIELD('Tên doanh nghiệp')),
  business_email: yup
    .string()
    .trim()
    .nullable()
    .email(TEXT.INVALID_EMAIL)
    .test('valid-email', TEXT.INVALID_EMAIL, (value) => {
      if (!value || value.trim() === '') {
        return true;
      }
      const emailRegex = REGEX.EMAIL;
      return emailRegex.test(value);
    }),
  business_label_type: yup
    .array()
    .min(1, ERROR_MESSAGE.REQUIRED_SELECT('Lĩnh vực hoạt động'))
    .required(ERROR_MESSAGE.REQUIRED_SELECT('Lĩnh vực hoạt động')),
  channel_types: yup
    .array()
    .min(1, ERROR_MESSAGE.REQUIRED_SELECT('Dịch vụ sử dụng'))
    .required(ERROR_MESSAGE.REQUIRED_SELECT('Dịch vụ sử dụng')),
  contact_name: yup.string().trim().required(ERROR_MESSAGE.REQUIRED_FIELD('Tên người liên hệ')),
  contact_phone: yup.string().trim().required(ERROR_MESSAGE.REQUIRED_FIELD('SĐT người liên hệ')),
  contact_email: yup
    .string()
    .trim()
    .email(TEXT.INVALID_EMAIL)
    .required(ERROR_MESSAGE.REQUIRED_FIELD('Email'))
    .test('valid-email', TEXT.INVALID_EMAIL, (value) => {
      if (value === undefined || value === null || value === '') {
        return true;
      }
      const emailRegex = REGEX.EMAIL;
      return emailRegex.test(value);
    })
    .when((email, schema) => {
      if (!email[0]) {
        return schema;
      }
      return schema.matches(REGEX.EMAIL, TEXT.INVALID_EMAIL);
    }),
  status: yup.number(),
});

const { handleSubmit, values, setFieldValue } = useForm({
  validationSchema,
});

const isDisabled = computed(() => props.type === PageType.Details);

const enterprise = ref<any>({});
const isVisibleModalApprove = ref(false);
const statusDetail = ref();
const labelTypeOptions = ref<any>([]);
const originalFormData = ref<any>({});
const isShowConfirmPopup = ref(false);

const back = () => {
  isShowConfirmPopup.value = false;
  router.push({
    name: ROUTE_NAME.PENDING_ENTERPRISE,
    params: {
      id: props.id,
    },
  });
};

const handleClickCancel = () => {
  if (isMutate.value) {
    isShowConfirmPopup.value = true;
  } else {
    back();
  }
};

const handleNavigateToUpdate = () => {
  router.push({
    name: ROUTE_NAME.UPDATE_PENDING_ENTERPRISE,
    params: {
      id: props.id,
    },
  });
};

const handleNavigateToDetail = (id?: number) => {
  router.push({
    name: ROUTE_NAME.DETAILS_PENDING_ENTERPRISE,
    params: {
      id: id ?? props.id,
    },
  });
};

const handleNavigateToList = () => {
  router.push({
    name: ROUTE_NAME.PENDING_ENTERPRISE,
  });
};

const handleRejectItem = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const response = await api.put(`/business/v1/api/admin/business/refuse/${props.id}`);
    if (response.data.code === 0) {
      toast('success', response.data.message || 'Từ chối doanh nghiệp thành công');
      await getDetail();
    } else {
      toast('error', response.data.message || 'Có lỗi xảy ra khi từ chối doanh nghiệp');
    }
  } catch (error: any) {
    console.error('Reject error:', error);
    if (!error?.response?.status) {
      handleApiError(error);
    }
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

const handleApproveSuccess = () => {
  isVisibleModalApprove.value = false;
  handleNavigateToList();
};

const getLabels = async () => {
  try {
    const res = await api.get(`/business/v1/api/admin/business/label-types`);
    if (res.data.code === 0 && res.data.data) {
      labelTypeOptions.value = res.data.data;
    } else {
      toast('error', res.data.message);
    }
    overlayLoading.toggleLoading(false);
  } catch (error: any) {
    overlayLoading.toggleLoading(false);
    if (!error?.response?.status) {
      toast('error', TEXT.ERROR_OCCURRED);
    }
  }
};

const getDetail = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const res = await api.get(`/business/v1/api/admin/business/${props.id}`);

    if (res.data.code === 0) {
      enterprise.value = res.data.data;
      if (
        enterprise.value.status !== PendingEnterpriseStatus.Pending &&
        enterprise.value.status !== PendingEnterpriseStatus.Reject
      ) {
        toast('error', 'Doanh nghiệp bạn chọn không tồn tại');
        return;
      }
      await getLabels();
      statusDetail.value = enterprise.value.status;
      setFieldValue('business_name', enterprise.value.business_name);
      setFieldValue('address', enterprise.value.address);
      setFieldValue('account_number', enterprise.value.account_number);
      setFieldValue('tax_code', enterprise.value.tax_code);
      setFieldValue('business_email', enterprise.value.business_email);
      setFieldValue('business_phone', enterprise.value.business_phone);
      setFieldValue('business_label_type', enterprise.value.business_label_type);
      setFieldValue('channel_types', enterprise.value.channel_types);
      setFieldValue('contact_name', enterprise.value.contact_name);
      setFieldValue('contact_email', enterprise.value.contact_email);
      setFieldValue('contact_phone', enterprise.value.contact_phone);
      setFieldValue('customer_source', enterprise.value.customer_source);
      setFieldValue('status', enterprise.value.status || 1);
      setFieldValue('payment_method', enterprise.value.payment_method || 'prepaid');

      originalFormData.value = {
        business_name: enterprise.value.business_name,
        address: null,
        tax_code: enterprise.value.tax_code,
        business_email: null,
        business_phone: enterprise.value.business_phone,
        business_label_type: enterprise.value.business_label_type,
        channel_types: enterprise.value.channel_types,
        contact_name: enterprise.value.contact_name,
        contact_email: enterprise.value.contact_email,
        contact_phone: enterprise.value.contact_phone,
        status: enterprise.value.status,
        payment_method: enterprise.value.payment_method || 'prepaid',
      };
    } else {
      toast('error', res.data.message);
    }
  } catch (error: any) {
    console.error('getDetail error:', error);
    handleApiError(error);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

const submitEnterpriseForm = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const params = {
      business_name: getNullableString(values.business_name),
      address: getNullableString(values.address),
      tax_code: getNullableString(values.tax_code),
      business_phone: getNullableString(values.business_phone),
      business_email: getNullableString(values.business_email),
      contact_name: getNullableString(values.contact_name),
      contact_phone: getNullableString(values.contact_phone),
      contact_email: getNullableString(values.contact_email),
      customer_source: values.customer_source,
      agent_id: values.agent_id,
      status: values.status,
      payment_method: values.payment_method,
      contract_deposit: values.contract_deposit,
      referral_code: values.referral_code,
      business_label_type: values.business_label_type,
      channel_types: values.channel_types,
      message_usage_limit: values.message_usage_limit,
    };

    const response = await api.put(`/business/v1/api/admin/business/${props.id}`, params);

    if (response.data.code === 0) {
      toast('success', response.data.message || 'Cập nhật doanh nghiệp thành công');
      handleNavigateToDetail();
    } else {
      toast('error', response.data.message || 'Có lỗi xảy ra khi cập nhật doanh nghiệp');
    }
  } catch (error: any) {
    // User no exists
    if (error?.response?.status === 404) {
      handleNavigateToList();
    }
    console.error('Submit enterprise form error:', error);
    if (!error?.response?.status) {
      handleApiError(error);
    }
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

const onSubmit = handleSubmit(async () => {
  submitEnterpriseForm();
});

const isMutate = ref(false);
onMounted(async () => {
  await getDetail();
  watch(
    values,
    () => {
      isMutate.value = true;
    },
    {
      deep: true,
    },
  );
});
</script>
