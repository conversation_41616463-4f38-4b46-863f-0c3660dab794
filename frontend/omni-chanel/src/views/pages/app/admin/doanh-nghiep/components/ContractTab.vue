<template>
  <ContractTable
    ref="contractTableRef"
    :dataContract="dataContract"
    :dataConfig="contractConfig"
    :params="params"
    @pageChanged="onPageChange"
    @perPageChange="onPerPageChange"
    @setWidth="onSetWidth"
    @rowClick="handleRowClick"
    @action="handleAction"
  />

  <LiquidationPopup
    v-model:visible="visibleLiquidationPopup"
    :id="liquidationContract?.id"
    :contract="liquidationContract"
    @onClose="handleLiquidationClose"
    @onConfirm="handleLiquidationConfirm"
  />

  <AddAppendixPopup
    v-if="showAppendixPopup"
    v-model:visible="showAppendixPopup"
    :contractId="selectedContractId"
    type="add"
    @success="handleAppendixSuccess"
  />
</template>

<script setup lang="ts">
import { ref, inject } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useContractStore } from '@/store/contract';
import { useUserSession } from '@/store/userSession';
import { ROUTE_NAME } from '@/shared';
import { enterpriseContractConfig } from '../index.constant';
import ContractTable from '@/components/pages/Contract/components/ContractTable.vue';
import { ContractPayloadType } from '@/enums/contract';
import LiquidationPopup from '@/components/pages/Contract/components/Popup/LiquidationPopup.vue';
import AddAppendixPopup from '@/components/pages/Contract/detail/components/popup/AddAppendixPopup.vue';
import { AccountType } from '@/enums/common';
import { handleApiError } from '@/utils/useErrorHandler';
import { TAB_CONTRACTS } from '@/constants/contract';

interface Props {
  enterpriseId: number;
}

const props = defineProps<Props>();

const api = useApi();
const router = useRouter();
const route = useRoute();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const contractStore = useContractStore();
const user = useUserSession();

// Refs
const contractTableRef = ref();
const dataContract = ref<any>({});
const contractConfig = ref(enterpriseContractConfig);
const params = ref({
  page_index: 1,
  page_size: 20,
});
const visibleLiquidationPopup = ref(false);
const liquidationContract = ref();
const showAppendixPopup = ref(false);
const selectedContractId = ref<number | null>(null);
// Methods
const getContracts = async () => {
  try {
    overlayLoading.toggleLoading(true);

    const baseRequestData = {
      page_index: params.value.page_index,
      page_size: params.value.page_size,
      businessId: props.enterpriseId,
    };

    let requestData;

    if (user.accountType === AccountType.Admin) {
      requestData = {
        ...baseRequestData,
        contractTypeId: ContractPayloadType.AdminEnterprise,
      };
    } else if (user.accountType === AccountType.Agent) {
      requestData = {
        ...baseRequestData,
        agentId: user.user?.id_agent,
        contractTypeId: ContractPayloadType.AgentEnterprise,
      };
    } else {
      requestData = {
        ...baseRequestData,
        contractTypeId: ContractPayloadType.AdminEnterprise,
      };
    }

    const response = await api.get('/business/v1/api/admin/contract', {
      params: requestData,
    });

    if (response.data.code === 0) {
      dataContract.value = {
        items: response.data.data.items || [],
        paging: {
          total_pages: response.data.data.paging?.total_pages || 0,
          total_records: response.data.data.paging?.total_records || 0,
        },
      };
    } else {
      toast('error', response.data.message);
    }
  } catch (error: any) {
    console.error('getContracts error:', error);
    handleApiError(error);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

// Event handlers
const onPageChange = (value: number) => {
  params.value.page_index = value;
  getContracts();
};

const onPerPageChange = (value: number) => {
  params.value.page_size = value;
  getContracts();
};

const onSetWidth = (value: number) => {
  // Handle table width change if needed
};

const handleRowClick = (row: any) => {
  contractStore.setListContractPath(route.fullPath, TAB_CONTRACTS);

  router.push({
    name: ROUTE_NAME.CONTRACT_ENTERPRISE_DETAIL,
    params: {
      id: row.id,
    },
    query: {
      source: 'enterprise-detail',
      enterpriseId: props.enterpriseId,
    },
  });
};
const handleAddAppendix = (row: any) => {
  selectedContractId.value = row.id;
  showAppendixPopup.value = true;
};

const handleLiquidate = (row: any) => {
  liquidationContract.value = row;
  visibleLiquidationPopup.value = true;
};

const handleLiquidationClose = () => {
  visibleLiquidationPopup.value = false;
  liquidationContract.value = null;
};

const handleLiquidationConfirm = () => {
  visibleLiquidationPopup.value = false;
  liquidationContract.value = null;
  getContracts();
};

const handleAppendixSuccess = () => {
  showAppendixPopup.value = false;
  selectedContractId.value = null;
  getContracts();
};

const handleAction = ({ actionKey, row }: { actionKey: string; row: any }) => {
  const actions = {
    view: () => handleRowClick(row),
    update: () => {
      contractStore.setListContractPath(route.fullPath, TAB_CONTRACTS);

      router.push({
        name: ROUTE_NAME.CONTRACT_ENTERPRISE_UPDATE,
        params: {
          id: row.id,
        },
        query: {
          source: 'enterprise-detail',
          enterpriseId: props.enterpriseId,
        },
      });
    },
    addAppendix: () => {
      handleAddAppendix(row);
    },
    liquidate: () => {
      handleLiquidate(row);
    },
  };

  const actionFn = actions[actionKey as keyof typeof actions];
  if (actionFn) {
    actionFn();
  } else {
    console.warn('Unknown action:', actionKey);
  }
};


defineExpose({
  getContracts,
});
</script>
