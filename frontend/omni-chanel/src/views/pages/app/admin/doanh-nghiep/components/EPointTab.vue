<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-auto bg-[#DEE4FF] rounded-[15px] mr-[10px]"
  >
    <div class="flex items-center font-semibold">
      <img
        src="@/assets/icons/wallet-epoint.svg"
        alt=""
        class="w-[150px] h-[150px] rounded-tl-[15px] rounded-bl-[15px]"
      />
      <div class="pl-[30px]">
        <div class="text-[16px]">Số E-Point hiện có của {{ epointStore.businessName }}</div>
        <div class="text-[40px]">
          <span class="font-bold">{{ existingEpoint?.toLocaleString('vi-VN') || 0 }}</span> E-Point
        </div>
      </div>
    </div>
    <VElementButton
      class="mr-[20px]"
      styleButton="s"
      id="e-point-button"
      :bgColor="color.main"
      label="Nạp E-Point"
      @click="RechargeEPoint"
    />
  </div>
  <div class="mx-[10px] mt-[20px] mb-[10px]">
    <div class="relative filter-container">
      <div class="flex gap-[12px]">
        <VElementButton
          styleIcon="text-[20px] text-[#fff] mr-[7px]"
          icon="filter-plus"
          class="filter"
          styleButton="s"
          id="e-point-filter-button"
          :bgColor="color.tertiary"
          :label="`Bộ lọc ${filterCount}`"
          @click="isShowFilter = !isShowFilter"
        />
        <VElementInput
          size="default"
          name="searchKey"
          prefix="search"
          placeholder="Tìm kiếm theo mã giao dịch"
          id="e-point-search-input"
          :style="'!w-[480px]'"
          @keyup.enter="getList()"
        />
        <VElementButton
          label="Tìm kiếm"
          styleButton="s"
          id="e-point-search-button"
          :bgColor="color.main"
          @click="onFilter"
        />
        <Transition>
          <FilterCard
            v-show="isShowFilter"
            ref="filterRef"
            v-on-click-outside="onClickOutsideHandler"
            filterHeight="h-[155px]"
            class="w-[400px]"
            id="e-point-filter-card"
            :widthInput="'!w-[250px]'"
            :dataFilter="dataFilter"
            @filter="filterData"
            @onResetForm="handleResetFilter"
          />
        </Transition>
      </div>
    </div>
    <VOldTable
      ref="tableRef"
      idTable="e-point-table"
      emptyText="Không tìm thấy doanh nghiệp phù hợp"
      :classTableContainer="
        listEnterprise.length === 0
          ? 'h-[calc(100vh-500px)]'
          : listEnterprise.length > 1
            ? '!max-h-[calc(100vh-427px)]'
            : 'h-[calc(100vh-556px)]'
      "
      :isRowClick="true"
      :rows="listEnterprise"
      :styleHeaders="styleHeaders"
      :headers="headers"
      :keySortTable="sortTableKeys"
      :pagination="{
        totalPage: pagination.total_page,
        total: pagination.record_count,
        perPage: params.page_size,
      }"
      @sortTable="sortTable"
      @pageChanged="onPageChange"
      @perPageChange="onPerPageChange"
      @setWidth="onSetWidth"
      @rowClick="redirectToDetails"
    >
      <template v-slot:items="{ row }">
        <td class="text-primaryText px-2">
          <div
            class="column-container text-left"
            :title="row.value"
            :class="row?.value < 0 ? 'text-red-500' : 'text-green-500'"
          >
            {{
              row?.value > 0
                ? `+ ${row.value?.toLocaleString('vi-VN')}`
                : `${row.value?.toLocaleString('vi-VN')}`
            }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container text-left" :title="row.orderCode">
            {{ row.orderCode }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container text-left" :title="row.description">
            {{ row.description }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container" :title="moment(row.createdAt).format('DD/MM/YYYY HH:mm')">
            {{ moment(row.createdAt).format('DD/MM/YYYY HH:mm') }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container" :title="getTransactionTypeName(row?.type)">
            {{ getTransactionTypeName(row?.type) }}
          </div>
        </td>
        <td class="text-primaryText px-2 text-center">
          <div
            class="center-container"
            :title="
              row.status === 'PENDING'
                ? 'Đang xử lý'
                : row.status === 'SUCCESS'
                  ? 'Thành công'
                  : row.status === 'FAILED'
                    ? 'Thất bại'
                    : ''
            "
          >
            <Tag
              :class="
                row.status === 'PENDING'
                  ? color.Yellow
                  : row.status === 'SUCCESS'
                    ? color.Green
                    : row.status === 'FAILED'
                      ? color.Red
                      : ''
              "
              :value="
                row.status === 'PENDING'
                  ? 'Đang xử lý'
                  : row.status === 'SUCCESS'
                    ? 'Thành công'
                    : row.status === 'FAILED'
                      ? 'Thất bại'
                      : ''
              "
            ></Tag>
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container font-semibold" :title="row?.lastBalance">
            {{ row?.lastBalance?.toLocaleString('vi-VN') }}
          </div>
        </td>
      </template>
      <template v-slot:actions="{ row }">
        <li
          class="item min-w-[100px]"
          :id="`e-point-action-details-${row.id}`"
          @click="redirectToDetails(row)"
          @keydown="redirectToDetails(row)"
        >
          <i class="pi pi-eye text-xs mr-3"></i><span>Xem chi tiết</span>
        </li>
      </template>
    </VOldTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject } from 'vue';
import * as yup from 'yup';
import { useForm } from 'vee-validate';
import { useRouter } from 'vue-router';
import { vOnClickOutside } from '@vueuse/components';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import FilterCard from '@/components/base/container/FilterCard.vue';
import { ALIGN, ROUTE_NAME } from '@/shared';
import { useEpointStep } from '@/store/useEPointStep';
import moment from 'moment';
import VOldTable from '@/components/base/VOldTable.vue';

export interface ParamsSearch {
  page_index: number;
  page_size: number;
  transactionType?: string;
  startDate?: number;
  endDate?: number;
  businessId: number;
  transactionStatus?: number;
}

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const epointStore = useEpointStep();

//#region Filter
const filterRef = ref<InstanceType<typeof FilterCard>>();
const filterCount = ref('');
const isShowFilter = ref(false);

const pagination = ref<TPagination>({
  total_page: 1,
  record_count: 20,
});
const transactionOptions = [
  { value: '0', label: 'Tất cả' },
  { value: 'T1', label: 'Nạp tiền' },
  { value: 'P1', label: 'Phí gửi tin' },
  { value: 'C1', label: 'Phí thiết lập hệ thống' },
  { value: 'C2', label: 'Phí khởi tạo Brandname' },
  { value: 'C3', label: 'Phí khởi tạo OA' },
  { value: 'P2', label: 'Phí duy trì Brandname' },
  { value: 'P3', label: 'Phí duy trì OA' },
  { value: 'R1', label: 'Hoàn tiền' },
];
const statusOptions = [
  { value: 3, label: 'Tất cả' },
  { value: 0, label: 'Đang xử lý' },
  { value: 1, label: 'Thành công' },
  { value: 2, label: 'Thất bại' },
];

const dataFilter = reactive([
  {
    label: 'Loại giao dịch',
    valueName: 'transactionType',
    type: 'dropdown',
    placeholder: 'Chọn giá trị',
    defaultValue: '0',
    clearable: false,
    dropdownConfig: {
      option: transactionOptions,
    },
  },
  {
    label: 'Thời gian',
    type: 'calendar_range',
    valueName: 'time',
    defaultValue: '',
  },
  {
    label: 'Trạng thái',
    type: 'dropdown',
    valueName: 'transactionStatus',
    placeholder: 'Chọn giá trị',
    defaultValue: 3,
    clearable: false,
    dropdownConfig: {
      option: statusOptions,
    },
  },
]);
const headers = ref([
  {
    key: '',
    name: 'E-Point',
    visible: true,
    align: ALIGN.START,
  },
  {
    key: 'contract_number',
    name: 'Mã đơn hàng',
    visible: true,
    align: ALIGN.START,
  },
  {
    key: 'contract_name',
    name: 'Mô tả',
    visible: true,
    align: ALIGN.START,
  },
  {
    key: 'service',
    name: 'Thời gian',
    visible: true,
    align: ALIGN.START,
  },
  {
    key: 'contract_type',
    name: 'Loại giao dịch',
    visible: true,
    align: ALIGN.START,
  },

  {
    key: 'status',
    name: 'Trạng thái',
    visible: true,
    align: ALIGN.CENTER,
  },
  {
    key: 'status',
    name: 'Số dư cuối(E-Point)',
    visible: true,
    align: ALIGN.CENTER,
  },
]);
const styleHeaders = ref([
  {
    idx: 0,
    class: 'w-[10%]',
  },
  {
    idx: 1,
    class: 'w-[20%]',
  },
  {
    idx: 2,
    class: 'w-[30%]',
  },
  {
    idx: 3,
    class: 'w-[10%]',
  },
  {
    idx: 4,
    class: 'w-[10%]',
  },

  {
    idx: 5,
    class: 'w-[10%]',
  },
  {
    idx: 6,
    class: 'w-[10%]',
  },
]);

const sortTableKeys = [];
const filterData = (value: any, isInputSearch: boolean = false) => {
  isShowFilter.value = false;
  params.value.transactionType = value.transactionType;
  params.value.transactionStatus = value.transactionStatus;

  if (value.time && value.time.length > 0) {
    params.value.startDate = moment(value.time[0])
      .utcOffset(7)
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss');
    params.value.endDate = moment(value.time[1])
      .utcOffset(7)
      .endOf('day')
      .format('YYYY-MM-DDTHH:mm:ss');
  } else {
    params.value.startDate = null;
    params.value.endDate = null;
  }

  if (filterRef.value) {
    if (filterRef.value.count && filterRef.value.count > 0 && !isDefaultFilter()) {
      filterCount.value = `(${filterRef.value.count})`;
    } else {
      filterCount.value = '';
    }
  }
  dataFilter.forEach((item) => {
    if (value.hasOwnProperty(item.valueName)) {
      item.defaultValue = value[item.valueName] ?? '';
    }
  });
  tableRef.value.filterData();
};

const onClickOutsideHandler = (ev: Event) => {
  const target = ev.target as Element;
  if (target.classList.contains('el-select-dropdown__option-item')) return;
  if (target.closest('.el-select-dropdown__option-item')) return;
  if (target.closest('.el-picker-panel__body')) return;
  if (target.closest('.filter')) return;
  isShowFilter.value = false;
};

const onFilter = () => {
  if (filterRef.value) {
    filterRef.value.onSubmit();
    if (filterRef.value.count && filterRef.value.count > 0 && !isDefaultFilter()) {
      filterCount.value = `(${filterRef.value.count})`;
    } else {
      filterCount.value = '';
    }
    return;
  }
  tableRef.value.filterData();
};

const handleResetFilter = () => {
  filterCount.value = '';
  params.value.transactionType = '0';
  params.value.startDate = null;
  params.value.endDate = null;
  params.value.transactionStatus = 3;
  filterRef.value?.setFieldValue('transactionType', '0');
  filterRef.value?.setFieldValue('transactionStatus', 3);
};

const isDefaultFilter = () => {
  const isDefaultFilter =
    params.value.transactionType === '0' &&
    params.value.transactionStatus === 3 &&
    params.value.startDate === null &&
    params.value.endDate === null;
  const isUndefinedFilter =
    params.value.transactionType === undefined &&
    params.value.transactionStatus === undefined &&
    params.value.startDate === null &&
    params.value.endDate === null;
  return isDefaultFilter || isUndefinedFilter;
};
//#endregion

//#region Router
const router = useRouter();

const redirectToDetails = (item: any) => {
  router.push({
    name: ROUTE_NAME.TRANSACTION_HISTORY_DETAILS,
    params: {
      id: item.transactionId,
    },
  });
};
//#endregion

//#region Table
const order = ref();
const tableRef = ref();
const tableWidth = ref();

const { values } = useForm({
  validationSchema: yup.object({
    org_id: yup.mixed(),
  }),
});

const params = ref<ParamsSearch>({
  page_index: 1,
  page_size: 20,
  businessId: router.currentRoute.value.params.id,
});

const onSetWidth = (value: number) => {
  tableWidth.value = value;
};

const onPageChange = (value: number) => {
  params.value.page_index = value;
  getList();
};

const onPerPageChange = (value: number) => {
  params.value.page_size = value;
};

const sortTable = (value: any) => {
  if (Object.values(value)[0] === 'desc') {
    order.value = `-${Object.keys(value)[0]}` as string;
  } else {
    order.value = Object.keys(value)[0] as string;
  }
  getList();
};

const getTransactionTypeName = (type: string) => {
  const option = transactionOptions.find((item) => item.value === type);
  return option ? option.label : '';
};
//#endregion

//#region Delete

const existingEpoint = ref();
//#endregion

//#region Datasource
const listEnterprise = ref<any>([]);

const getList = async () => {
  try {
    overlayLoading.toggleLoading(true);
    let url = `/wallet/v1/api/admin/transaction?page_size=${params.value.page_size}&page_index=${params.value.page_index}&businessId=${router.currentRoute.value.params.id}`;

    if (values.searchKey) {
      url += `&searchValue=${values.searchKey.trim()}`;
    }
    if (params.value.startDate && params.value.endDate) {
      url += `&startDate=${params.value.startDate}&endDate=${params.value.endDate}`;
    }
    if (params.value.transactionType && params.value.transactionType !== '0') {
      url += `&transactionType=${params.value.transactionType}`;
    }
    if (params.value.transactionStatus < 3) {
      url += `&transactionStatus=${params.value.transactionStatus}`;
    }
    const res = await api.get(url);
    if (res.data.code === 0 && res.data.data) {
      listEnterprise.value = res.data.data.items;
      pagination.value = {
        total_page: res.data.data.paging?.total_pages,
        record_count: res.data.data.paging?.total_records,
      };
      if (res.data.data?.items?.length === 0 && params.value.page_index !== 1) {
        tableRef.value.onPageChange(1);
        tableRef.value.filterData();
        return;
      }
    } else {
      toast('error', res.data.message);
    }
    overlayLoading.toggleLoading(false);
  } catch (error: any) {
    overlayLoading.toggleLoading(false);
    if (!error?.response?.status) {
      toast('error', TEXT.ERROR_OCCURRED);
    }
  }
};
//#endregion

const RechargeEPoint = () => {
  router.push({
    name: ROUTE_NAME.RECHARGE_EPOINT,
    params: {
      id: router.currentRoute.value.params.id,
    },
  });
  // emit('rechargeEPoint', true);
};
const getEPoint = async () => {
  const { data } = await api.get(
    `wallet/v1/api/wallet/balance/${router.currentRoute.value.params.id}`,
  );
  if (data.code === 0) {
    existingEpoint.value = data?.data || 0;
    epointStore.epoint_number = data?.data;
  }
};

const getInfoEpoint = async () => {
  await getEPoint();
  await getList();
};
defineExpose({
  getInfoEpoint,
});
</script>

<style lang="scss" scoped>
.item {
  padding: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 131px;
  font-size: 14px;
  font-weight: 400;
  &:hover {
    color: var(--main-color);
  }
}
:deep(.p-tag) {
  width: 120px;
}
</style>

<script setup lang="ts"></script>
