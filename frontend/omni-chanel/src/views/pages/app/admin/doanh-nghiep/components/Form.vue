<template>
  <div class="flex flex-col items-center gap-3 w-[100%]">
    <div class="w-[80%] p-5">
      <div
        class="border-1 border-solid px-[15px] py-[6px] border-[#1A34B5] rounded-[30px] h-[34px] w-fit text-[#1A34B5] text-[14px] font-semibold flex items-center"
      >
        Thông tin doanh nghiệp
      </div>
      <el-form label-position="top" class="w-[100%] flex justify-center gap-[75px] mt-5">
        <div class="flex flex-col gap-[15px] w-[50%]">
          <VElementInput
            name="business_name"
            size="default"
            label="Tên doanh nghiệp"
            id="enterprise-business_name-input"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
            :disabled="props.type === PageType.Details"
            :maxlength="200"
            :showLimit="false"
            :required="true"
          />
          <VElementInput
            v-if="props.type !== PageType.Add"
            name="business_code"
            size="default"
            label="Mã doanh nghiệp"
            id="enterprise-business_code-input"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
            :disabled="true"
            :showLimit="false"
          />
          <VElementInput
            name="address"
            size="default"
            label="Địa chỉ"
            id="enterprise-address-input"
            :maxlength="200"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
            :disabled="props.type === PageType.Details"
            :showLimit="false"
          />
          <VElementInput
            name="tax_code"
            size="default"
            label="Mã số thuế"
            :maxlength="50"
            id="enterprise-tax_code-input"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
            :disabled="props.type === PageType.Details"
            :showLimit="false"
          />

          <VElementInput
            size="default"
            name="business_email"
            label="Email doanh nghiệp"
            :maxlength="50"
            id="enterprise-business_email-input"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
            :disabled="props.type === PageType.Details"
            :showLimit="false"
          />
          <el-form-item
            v-if="props.type !== PageType.Add"
            id="enterprise-account_number-input"
            label="Tài khoản đã tạo"
          >
            <div
              class="h-[33px] w-[100%] flex gap-[5px] items-center bg-[#f4f7fa] border-[1px] border-[#e4e7ec] rounded-[6px] px-[10px] select-none"
            >
              <div class="text-main">{{ values.account_number }}</div>
              <div>Tài khoản</div>
            </div>
          </el-form-item>
        </div>
        <div class="flex flex-col gap-[15px] w-[50%]">
          <VElementInput
            name="business_phone"
            size="default"
            label="SĐT doanh nghiệp"
            :maxlength="50"
            id="enterprise-business_phone-input"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
            :disabled="props.type === PageType.Details"
            :showLimit="false"
          />
          <VElementDropdown
            name="business_label_type"
            label="Lĩnh vực hoạt động"
            :formType="type"
            :filterable="false"
            id="business_label_type-dropdown"
            :placeholder="getPlaceholder(PLACEHOLDER.SELECT, type)"
            :disabled="props.type === PageType.Details"
            :option="listLabelType"
            multiple
            :style="'w-[100%]'"
            :maxCollapseTags="2"
          />
          <VElementDropdown
            name="channel_types"
            label="Dịch vụ sử dụng"
            id="channel_type-dropdown"
            :multiple="true"
            :placeholder="getPlaceholder(PLACEHOLDER.SELECT, type)"
            :disabled="props.type === PageType.Details"
            :formType="type"
            :filterable="false"
            :option="channelTypes"
            :style="'w-[100%]'"
            :maxCollapseTags="2"
          />
          <VElementDropdown
            name="status"
            label="Trạng thái"
            id="enterprise-status-dropdown"
            :placeholder="getPlaceholder(PLACEHOLDER.SELECT, type)"
            :disabled="props.type === PageType.Details"
            :formType="type"
            :filterable="false"
            :option="statusValueOptions"
            :style="'w-[100%]'"
          />
        </div>
      </el-form>
    </div>
    <div class="w-[80%] p-5">
      <div
        class="border-1 border-solid px-[15px] py-[6px] border-[#1A34B5] rounded-[30px] h-[34px] w-fit text-[#1A34B5] text-[14px] font-semibold flex items-center"
      >
        Thông tin người liên hệ
      </div>
      <el-form label-position="top" class="w-[100%] flex justify-center gap-[75px] mt-5">
        <div class="flex flex-col gap-[15px] w-[50%]">
          <VElementInput
            name="contact_name"
            size="default"
            label="Tên người liên hệ"
            :maxlength="50"
            id="enterprise-contact_name-input"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
            :disabled="props.type === PageType.Details"
            :required="true"
            :showLimit="false"
          />
          <VElementInput
            name="contact_email"
            size="default"
            label="Email"
            id="enterprise-contact_email-input"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
            :disabled="props.type === PageType.Details"
            :maxlength="50"
            :required="true"
            :showLimit="false"
          />
        </div>
        <div class="flex flex-col gap-[15px] w-[50%]">
          <VElementInput
            name="contact_phone"
            size="default"
            label="SĐT người liên hệ"
            id="enterprise-contact_phone-input"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
            :disabled="props.type === PageType.Details"
            :maxlength="50"
            :required="true"
            :showLimit="false"
          />
        </div>
      </el-form>
    </div>
    <div class="w-[80%] p-5">
      <div
        class="border-1 border-solid px-[15px] py-[6px] border-[#1A34B5] rounded-[30px] h-[34px] w-fit text-[#1A34B5] text-[14px] font-semibold flex items-center"
      >
        Thông tin khác
      </div>
      <el-form label-position="top" class="w-[100%] flex justify-center gap-[75px] mt-5">
        <div class="flex flex-col gap-[15px] w-[50%]">
          <VElementDropdown
            v-if="accountType !== 2"
            name="customer_source"
            label="Nguồn khách hàng"
            :formType="type"
            :filterable="false"
            id="enterprise-customer_source-dropdown"
            :placeholder="getPlaceholder(PLACEHOLDER.SELECT, type)"
            :disabled="props.type === PageType.Details"
            :option="customerSourceOptions"
            :style="'w-[100%]'"
          />
          <VElementDropdown
            v-if="values.customer_source === CUSTOMER_SOURCE.AGENT"
            name="agent_id"
            label="Chọn đại lý"
            :formType="type"
            :filterable="false"
            id="enterprise-agent_id-dropdown"
            :placeholder="getPlaceholder(PLACEHOLDER.SELECT_AGENT, type)"
            :disabled="props.type === PageType.Details"
            :option="listAgent"
            :style="'w-[100%]'"
            :required="true"
            :clearable="true"
            @change="handleAgentChange"
          />
          <VElementDropdown
            name="payment_method"
            label="Hình thức thanh toán"
            :formType="type"
            :filterable="false"
            id="enterprise-payment_method-dropdown"
            :placeholder="getPlaceholder(PLACEHOLDER.SELECT, type)"
            :disabled="props.type !== PageType.Add"
            :option="paymentMethodOptions"
            :style="'w-[100%]'"
          />
          <VElementInput
            required
            name="message_usage_limit"
            size="default"
            label="Hạn mức sử dụng dịch vụ gửi tin"
            id="enterprise-message_usage_limit-input"
            type="onlyNumber"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
            useAppendTemplate
            :maxlength="10"
            :disabled="props.type === PageType.Details"
            @onInput="handleMessageUsageLimit"
          >
            <template #append> VND </template>
          </VElementInput>
        </div>
        <div
          class="flex flex-col gap-[15px] w-[50%]"
          :class="accountType === 2 ? 'mt-[0px]' : 'mt-[73px]'"
        >
          <VElementInput
            v-if="values.customer_source === CUSTOMER_SOURCE.AGENT"
            name="referral_code"
            size="default"
            label="Mã giới thiệu"
            id="agent-referral_code-input"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
            :disabled="props.type === PageType.Details"
            :maxlength="10"
            @onInput="handleReferralCodeDebounce"
          />
          <VElementInput
            name="contract_deposit"
            size="default"
            label="Tiền cọc hợp đồng"
            id="enterprise-contract_deposit-input"
            type="onlyNumber"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
            useAppendTemplate
            :maxlength="10"
            :disabled="props.type === PageType.Details"
          >
            <template #append> VND </template>
          </VElementInput>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PLACEHOLDER, type TDropdownItem, statusOptions } from '@/shared';
import { CUSTOMER_SOURCE, channelTypes, customerSources } from '../index.constant';
import { getPlaceholder } from '@/utils';
import { PageType } from '@/enums/common';
import { computed, onMounted, ref, watch } from 'vue';
import { useApi } from '@/store/useApi';
import { useUserSession } from '@/store/userSession';

interface Props {
  type: PageType;
  values: any;
  listAgent: TDropdownItem[];
}

const api = useApi();
const user: any = useUserSession();
const accountType = computed<number>(() => user.accountType); // 1: van hanh, 2: dai ly

const props = defineProps<Props>();
const maxMessageUsageLimit = ref<number>(0);
const isValidatingReferralCode = ref<boolean>(false);
const hasReferralCodeError = ref<boolean>(false);

const emit = defineEmits(['setFieldValue', 'setFieldError', 'setReferralCodeValidation']);

const statusValueOptions: TDropdownItem[] = statusOptions.filter((_, index: number) => index !== 0);
const customerSourceOptions: TDropdownItem[] = customerSources.filter(
  (_, index: number) => index !== 0,
);
const paymentMethodOptions: TDropdownItem[] = [
  {
    label: 'Trả trước',
    value: 1,
  },
  {
    label: 'Trả sau',
    value: 2,
  },
];

const listLabelType = ref<any[]>([]);
const getListLabelType = async () => {
  try {
    const res = await api.get(`/business/v1/api/admin/business/label-types`);
    if (res.data.code === 0 && res.data.data) {
      listLabelType.value = res.data.data;
    }
  } catch (error: any) {
    console.error(error);
  }
};

// Debounce timer
let debounceTimer: number | null = null;

const handleReferralCodeDebounce = (event: any) => {
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }

  // Nếu trường trống, clear validation state
  if (!event || event.trim() === '') {
    emit('setFieldValue', 'agent_id', null);
    isValidatingReferralCode.value = false;
    hasReferralCodeError.value = false;
    emit('setReferralCodeValidation', { isValidating: false, hasError: false });
    return;
  }

  // Reset validation state
  isValidatingReferralCode.value = true;
  hasReferralCodeError.value = false;
  emit('setReferralCodeValidation', { isValidating: true, hasError: false });

  debounceTimer = setTimeout(() => {
    // Kiểm tra không có ký tự đặc biệt
    if (isValidReferralCode(event)) {
      validateReferralCode(event);
    } else {
      emit('setFieldValue', 'agent_id', null);
      isValidatingReferralCode.value = false;
      hasReferralCodeError.value = true;
      emit('setReferralCodeValidation', { isValidating: false, hasError: true });
    }
  }, 500);
};

const isValidReferralCode = (value: string): boolean => {
  if (!value || value.trim() === '') return false;

  // Chỉ cho phép chữ cái, số và dấu gạch dưới
  const validPattern = /^[a-zA-Z0-9_]+$/;
  return validPattern.test(value);
};

const validateReferralCode = async (value: string) => {
  try {
    const res = await api.get(`/business/v1/api/admin/business/validate-referral-code`, {
      params: {
        referralCode: value,
      },
    });
    if (res.data.code === 0) {
      emit('setFieldValue', 'agent_id', res.data.data.agent_id);
      hasReferralCodeError.value = false;
      emit('setReferralCodeValidation', { isValidating: false, hasError: false });
    } else {
      emit('setFieldError', 'referral_code', res.data.message);
      hasReferralCodeError.value = true;
      emit('setReferralCodeValidation', { isValidating: false, hasError: true });
    }
  } catch (error) {
    console.error('Error validating referral code:', error);
    hasReferralCodeError.value = true;
    emit('setReferralCodeValidation', { isValidating: false, hasError: true });
  } finally {
    isValidatingReferralCode.value = false;
  }
};

watch(
  () => props.values.agent_id,
  (newVal) => {
    if (newVal) {
      const agent: any = props.listAgent.find((item) => item.value === newVal);
      maxMessageUsageLimit.value = agent?.message_usage_limit;
    }
  },
);

const handleAgentChange = () => {
  emit('setFieldValue', 'message_usage_limit', '');
  maxMessageUsageLimit.value = 0;

  // Clear referral code validation state khi thay đổi agent
  hasReferralCodeError.value = false;
  isValidatingReferralCode.value = false;
  emit('setReferralCodeValidation', { isValidating: false, hasError: false });

  if (props.values.agent_id) {
    const agent: any = props.listAgent.find((item) => item.value === props.values.agent_id);
    if (agent) {
      emit('setFieldValue', 'referral_code', agent.referral_code);
    } else {
      emit('setFieldValue', 'referral_code', null);
    }
    maxMessageUsageLimit.value = agent?.message_usage_limit;
  } else {
    emit('setFieldValue', 'referral_code', null);
  }
};

const handleMessageUsageLimit = (event: any) => {
  if (event) {
    if (
      Number(event) > maxMessageUsageLimit.value &&
      props.values.customer_source === CUSTOMER_SOURCE.AGENT
    ) {
      emit('setFieldValue', 'message_usage_limit', maxMessageUsageLimit.value);
    }
  }
};

onMounted(async () => {
  await getListLabelType();
});
</script>
