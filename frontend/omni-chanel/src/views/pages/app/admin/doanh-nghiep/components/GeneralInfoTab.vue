<template>
  <div class="mx-[10px] flex justify-center h-[calc(100vh-322px)] mb-[50px] overflow-auto">
    <Form :type="type" :values="values" :listAgent="listAgent" />
  </div>
</template>

<script setup lang="ts">
import { type TDropdownItem } from '@/shared';
import Form from './Form.vue';
import type { PageType } from '@/enums/common';

interface Props {
  type: PageType;
  values: any;
  listAgent: TDropdownItem[];
}

defineProps<Props>();
</script>
