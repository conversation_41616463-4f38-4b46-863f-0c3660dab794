<template>
  <Dialog
    modal
    v-model:visible="isVisible"
    :draggable="false"
    :closable="true"
    :pt="{
      content: { class: ' mb-[50px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: 'text-center',
        style: {
          justifyContent: 'center',
        },
      },
    }"
    header="Xác nhận người yêu cầu nạp E-Point"
    :style="{
      width: '450px',
      height: 'auto',
      backgroundColor: '#fff',
      maxHeight: '90vh',
    }"
  >
    <el-form label-position="top" class="flex justify-center">
      <div class="flex flex-col gap-4 w-full">
        <div class="font-semibold">Chọn tài kho<PERSON>n <span class="text-red-600">*</span></div>
        <VElementDropdown
          name="username"
          label=""
          :filterable="true"
          id="username-dropdown"
          placeholder="Chọn giá trị"
          :option="props.listUsername || []"
          :style="'w-full'"
          :required="true"
          :enableFuzzySearch="false"
        />
      </div>
    </el-form>

    <!-- Buttons -->
    <div class="save-container flex justify-center items-center gap-5 mt-5">
      <div>
        <VElementButton
          styleButton="s"
          label="Đóng"
          :bgColor="color.closeButton"
          @click="closePopup"
        />
        <VElementButton styleButton="s" label="Tiếp tục" :bgColor="color.main" @click="accept" />
      </div>
    </div>
  </Dialog>
</template>
<script lang="ts" setup>
import { onMounted } from 'vue';
import { color } from '@/constants/statusColor';
import Dialog from 'primevue/dialog';
import * as yup from 'yup';
import { useForm } from 'vee-validate';

const props = defineProps<{
  listUsername?: array<any>;
}>();
const emit = defineEmits(['close', 'accept']);
const isVisible = defineModel<boolean>('visible');

onMounted(() => {});

const validationSchema = yup.object({
  username: yup.number().required('Vui lòng chọn tài khoản'),
});
const { resetForm, handleSubmit } = useForm({
  validationSchema,
});

const accept = handleSubmit(async (value) => {
  const user = props.listUsername.find((item) => item.value === value.username);
  emit('accept', user);
});
const closePopup = () => {
  resetForm();
  isVisible.value = false;
  emit('close');
};
</script>
