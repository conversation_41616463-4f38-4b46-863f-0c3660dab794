<template>
  <div class="relative w-full text-center container pt-[95px]">
    <div class="absolute left-1/2 -translate-x-1/2 -top-[-60px] z-10">
      <img :src="epointStore.step2Data.status === 'Thành công' ? statusSucces : statusFail" />
    </div>
    <div
      class="w-[30%] mx-auto bg-indigo-50 rounded-md shadow-[0px_0px_40px_-11px_rgba(16,24,40,0.10)] border border-zinc-200 pt-[70px] pb-[25px]"
    >
      <!-- Thành công -->
      <div v-if="epointStore.step2Data.status === 'Thành công'">
        <div class="text-center justify-start text-black text-base font-medium leading-snug">
          Chúc mừng bạn đã thanh toán thành công!
        </div>
        <div class="justify-start text-black text-sm font-semibold leading-relaxed py-[10px]">
          Thông tin đơn hàng
        </div>
        <div
          class="bg-white rounded-md border border-zinc-200 mx-[20px] mb-[10px] pt-[20px] pb-[10px] flex flex-col gap-y-[10px] text-left"
        >
          <div class="flex justify-between px-[17px]">
            <div class="justify-start text-black text-sm font-normal leading-relaxed">
              Nhà cung cấp
            </div>
            <div class="text-right justify-start text-black text-sm font-semibold leading-relaxed">
              VNPT Omnichannel
            </div>
          </div>
          <div class="flex justify-between px-[17px]">
            <div class="justify-start text-black text-sm font-normal leading-relaxed">
              Mã đơn hàng
            </div>
            <div class="text-right justify-start text-black text-sm font-semibold leading-relaxed">
              {{ epointStore.step2Data?.orderCode }}
            </div>
          </div>
          <div class="px-[17px]">
            <div class="text-start text-black text-sm font-normal leading-relaxed">Mô tả:</div>
            <div class="text-zinc-800 text-start text-sm font-semibold leading-tight">
              Thanh toán đơn hàng {{ epointStore.step2Data?.orderCode }} từ VNPT Omnichannel. Nạp
              tiền cho doanh nghiệp {{ epointStore.businessName }}. Người thực hiện:
              {{ user.email }}
            </div>
          </div>
          <div class="h-0 outline outline-1 outline-offset-[-0.50px] outline-zinc-200"></div>
          <div class="flex justify-between px-[17px]">
            <div class="justify-start text-black text-sm font-normal leading-relaxed">Số tiền</div>
            <div
              class="text-right justify-start text-red-500 text-base font-semibold leading-relaxed"
            >
              {{ Number(epointStore.step1Data?.epoint_number).toLocaleString('vi-VN') }} VNĐ
            </div>
          </div>
          <div class="h-0 outline outline-1 outline-offset-[-0.50px] outline-zinc-200"></div>
          <div class="flex justify-between px-[17px]">
            <div class="justify-start text-black text-sm font-normal leading-relaxed">
              Số dư E-Point cuối
            </div>
            <div
              class="text-right justify-start text-red-500 text-base font-semibold leading-relaxed"
            >
              {{ Number(epointStore.step2Data?.lastBalance).toLocaleString('vi-VN') }} E-Point
            </div>
          </div>
        </div>
      </div>
      <div
        v-else
        class="text-center justify-start text-black text-base font-medium leading-relaxed px-[17px]"
      >
        Có lỗi xảy ra!<br />Vui lòng thử lại sau hoặc liên hệ với quản trị viên
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useEpointStep } from '@/store/useEPointStep';
import { useUserSession, type UserData } from '@/store/userSession';
import statusSucces from '@/assets/icons/status-succes.svg';
import statusFail from '@/assets/icons/status-fail.svg';

const epointStore = useEpointStep();
const user = useUserSession().user as UserData;
</script>
<style scoped>
.container {
  margin: 0 auto;
}
</style>
