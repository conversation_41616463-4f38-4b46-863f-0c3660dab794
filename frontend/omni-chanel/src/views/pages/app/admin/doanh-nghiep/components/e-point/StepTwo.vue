<template>
  <div class="flex items-start justify-center px-[2vw] py-[20px]">
    <div class="w-[40%]">
      <div
        class="bg-indigo-50 rounded-md shadow-[0px_0px_40px_-11px_rgba(16,24,40,0.10)] border border-zinc-200 p-[16px]"
      >
        <div class="text-black text-sm font-semibold leading-relaxed pb-[10px]">
          Thông tin đơn hàng
        </div>
        <div class="bg-white rounded-md border border-zinc-200 py-[19px]">
          <div class="flex justify-between pb-[20px] px-[19px]">
            <div class="text-black text-sm font-normal leading-relaxed">Nhà cung cấp</div>
            <div class="text-black text-sm font-semibold leading-relaxed">VNPT Omnichannel</div>
          </div>
          <div class="px-[19px]">
            <div class="text-black text-sm font-normal leading-relaxed">M<PERSON> tả:</div>
            <div
              class="w-80 justify-start text-zinc-800 text-sm font-semibold font-['Inter'] leading-tight"
            >
              Nạp tiền cho doanh nghiệp {{ epointStore.businessName }}. Người thực hiện:
              {{ user.email }}
            </div>
          </div>
          <div class="outline outline-1 outline-offset-[-0.50px] outline-zinc-200 mt-[20px]"></div>
          <div class="flex justify-between px-[19px] pt-[10px]">
            <div class="text-black text-sm font-normal leading-relaxed">Số tiền</div>

            <div class="text-red-500 text-base font-semibold leading-relaxed">
              {{ Number(epointStore.step1Data?.epoint_number)?.toLocaleString('vi-VN') }} VNĐ
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useEpointStep } from '@/store/useEPointStep';
const epointStore = useEpointStep();
// import { ref, watch } from 'vue';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useApi } from '@/store/useApi';
import { useUserSession, type UserData } from '@/store/userSession';

const overlayLoading = useOverLayLoadingStore();
const api = useApi();
const user = useUserSession().user as UserData;

const onSubmit = async () => {
  overlayLoading.toggleLoading(true);
  await api
    .post(`/wallet/v1/api/wallet/deposit/${epointStore.userId}`, {
      amount: Number(epointStore.step1Data.epoint_number),
    })
    .then((response) => {
      if (response.data.code === 0) {
        overlayLoading.toggleLoading(false);
        epointStore.setValueStep2(response.data.data);
        toast('success', response.data.message);
      } else {
        overlayLoading.toggleLoading(false);
        epointStore.setValueStep2({
          status: 'Thất bại',
        });
        toast('error', response.data.message);
      }
    })
    .catch(() => {
      overlayLoading.toggleLoading(false);
    });
  epointStore.next();
};

defineExpose({
  onSubmit,
});
</script>
