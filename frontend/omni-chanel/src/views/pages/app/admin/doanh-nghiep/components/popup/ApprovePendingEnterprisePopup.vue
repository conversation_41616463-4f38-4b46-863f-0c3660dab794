<template>
  <Dialog
    modal
    header="<PERSON><PERSON><PERSON>t doanh nghiệp"
    v-model:visible="isVisible"
    :draggable="false"
    :pt="{
      content: { class: 'mb-[60px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: '!pl-[184px] !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
      },
    }"
    :style="{
      width: '550px',
      height: 'fit-content',
      backgroundColor: '#fff',
      maxHeight: '100%',
    }"
  >
    <div class="pt-[20px]">
      <el-form label-position="top" class="flex flex-col flex-1 gap-y-[20px]">
        <div class="flex flex-col">
          <VElementInput
            required
            name="message_usage_limit"
            size="default"
            label="Hạn mức sử dụng dịch vụ gửi tin"
            id="enterprise-message_usage_limit-input"
            :style="'!w-full'"
            type="onlyNumber"
            :placeholder="PLACEHOLDER.TYPE_PRICE"
            useAppendTemplate
            :maxlength="10"
            @onInput="handleMessageUsageLimit"
          >
            <template #append> VND </template>
          </VElementInput>
        </div>

        <div class="flex flex-col">
          <VElementInput
            name="contract_deposit"
            size="default"
            label="Tiền cọc hợp đồng"
            id="enterprise-contract_deposit-input"
            type="onlyNumber"
            :placeholder="PLACEHOLDER.TYPE_PRICE"
            useAppendTemplate
            :maxlength="10"
          >
            <template #append> VND </template>
          </VElementInput>
        </div>
      </el-form>
    </div>
    <div class="save-container flex justify-center items-center mt-5">
      <VElementButton :bgColor="color.closeButton" @click="handleClose" label="Đóng" />
      <VElementButton :bgColor="color.main" @click="clickSubmit" label="Xác nhận" />
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { watch, inject, ref } from 'vue';
import * as yup from 'yup';
import { useForm } from 'vee-validate';
import Dialog from 'primevue/dialog';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { handleApiError } from '@/utils/useErrorHandler';
import { ERROR_MESSAGE } from '@/constants/templateText';
import { PLACEHOLDER, TEXT } from '@/shared';
import { CUSTOMER_SOURCE } from '../../index.constant';
import { onMounted } from 'vue';

interface AgentItem {
  id: number;
  message_usage_limit: number;
}

interface Props {
  id: number;
  enterprise?: any;
  agentId?: any;
  customerSource?: number;
}
const props = defineProps<Props>();
const emit = defineEmits(['onClose', 'onConfirm']);

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();

const validationSchema = yup.object({
  message_usage_limit: yup
    .string()
    .trim()
    .required(ERROR_MESSAGE.REQUIRED_FIELD('Hạn mức sử dụng dịch vụ gửi tin')),
});

const { values, handleSubmit, setFieldValue, resetForm } = useForm({
  validationSchema: validationSchema,
  validateOnMount: false,
});
const isVisible = defineModel<boolean>('visible');

const maxMessageUsageLimit = ref<number>(0);

const handleClose = () => {
  emit('onClose');
  resetForm();
};

const handleGetAgent = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const response = await api.get(`/business/v1/api/admin/agent/list`, {
      params: { status: null },
    });

    if (response.data.code === 0 && response.data.data) {
      const agent = response.data.data.find((item: AgentItem) => item.id === props?.agentId);
      maxMessageUsageLimit.value = agent?.message_usage_limit;
    } else {
      toast('error', response.data.message);
    }
  } catch (error) {
    console.error('getAgent error:', error);
    toast('error', TEXT.ERROR_OCCURRED);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

const handleMessageUsageLimit = (event: any) => {
  if (event) {
    if (
      Number(event) > maxMessageUsageLimit.value &&
      props.customerSource === CUSTOMER_SOURCE.AGENT
    ) {
      setFieldValue('message_usage_limit', maxMessageUsageLimit.value);
    }
  }
};

const onSubmit = handleSubmit(async () => {
  try {
    overlayLoading.toggleLoading(true);

    const requestData = {
      business_id: props.id,
      message_usage_limit: parseInt(values.message_usage_limit) || 0,
      contract_deposit: parseInt(values.contract_deposit) || 0,
    };

    const response = await api.put('/business/v1/api/admin/business/approve', requestData);

    if (response.data.code === 0) {
      toast('success', `Duyệt doanh nghiệp ${props.enterprise?.business_name} thành công`);
      emit('onClose');
      emit('onConfirm');
      resetForm();
    } else {
      toast('error', response.data.message || 'Có lỗi xảy ra khi duyệt doanh nghiệp');
    }
  } catch (error: any) {
    console.error('Approve error:', error);
    if (!error?.response?.status) {
      handleApiError(error);
    }
  } finally {
    emit('onClose');
    overlayLoading.toggleLoading(false);
  }
});

const clickSubmit = () => {
  onSubmit();
};

onMounted(async () => {
  await handleGetAgent();
  resetForm();
});
</script>

<style scoped>
:deep(.p-dialog) {
  border-radius: 12px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

:deep(.p-dialog-header) {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
}

:deep(.p-dialog-content) {
  padding: 0 1.5rem 1rem 1.5rem;
}
</style>
