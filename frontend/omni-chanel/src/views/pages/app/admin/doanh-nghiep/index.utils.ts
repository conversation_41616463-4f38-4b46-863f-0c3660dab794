import { color } from '@/constants/statusColor';
import { PendingEnterpriseStatus, PendingEnterpriseStatusLabel } from '@/enums/enterprise';

export const getColorStatus = (status: PendingEnterpriseStatus) => {
  switch (status) {
    case PendingEnterpriseStatus.Pending:
      return color.Green;
    case PendingEnterpriseStatus.Reject:
      return color.Red;
    default:
      return color.Draft;
  }
};

export const getLabelStatus = (status: PendingEnterpriseStatus) => {
  switch (status) {
    case PendingEnterpriseStatus.Pending:
      return PendingEnterpriseStatusLabel.Pending;
    case PendingEnterpriseStatus.Reject:
      return PendingEnterpriseStatusLabel.Reject;
    default:
      return 'Không xác định';
  }
};
