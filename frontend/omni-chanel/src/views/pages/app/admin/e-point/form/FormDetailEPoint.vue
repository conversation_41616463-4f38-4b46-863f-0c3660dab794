<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { color } from '@/constants/statusColor';
import { ROUTE_PATH, ROUTE_NAME } from '@/shared';
import { useRouter } from 'vue-router';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useApi } from '@/store/useApi';
import moment from 'moment';
import { useToast } from 'vue-toastification';
const toast = useToast();

const router = useRouter();
const overlayLoading = useOverLayLoadingStore();
const api = useApi();
const dataTransaction = ref();

const getStatusOrder = (status?: string) => {
  switch (status) {
    case 'PENDING':
      return { label: 'Đang xử lý', color: color.Pending };
    case 'SUCCESS':
      return { label: 'Thành công', color: color.Green };
    case 'FAILED':
      return { label: 'Thất bại', color: color.Red };

    default:
      return { label: 'Không xác định', color: color.Dark };
  }
};

const getDetailTransaction = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const response = await api.get(
      `/wallet/v1/api/admin/transaction/${router.currentRoute.value.params.id}`,
    );

    if (response.data.code === 0) {
      const res = response.data.data;
      dataTransaction.value = res;
    } else {
      toast('error', response.data.message);
    }
  } catch (error: any) {
    console.error('getDetail error:', error);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

const changeStatus = async (status: string) => {
  try {
    overlayLoading.toggleLoading(true);
    const isAccept = status === 'Xác nhận thanh toán';
    const response = await api.put(
      `/wallet/v1/api/admin/transaction/verify/${router.currentRoute.value.params.id}?isAccept=${isAccept}`,
    );

    if (response.data.code === 0) {
      toast.success(response.data.message, {
        timeout: 3000,
      });
      handleNavigateToList();
    } else {
      toast('error', response.data.message);
    }
  } catch (error: any) {
    console.error('getDetail error:', error);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};
onMounted(async () => {
  await getDetailTransaction();
});

const handleNavigateToList = () => {
  router.push({
    name: ROUTE_NAME.TRANSACTION_HISTORY,
  });
};
</script>

<template>
  <div class="">
    <div
      class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
    >
      <div class="flex gap-[15px] items-center">
        <Icon icon="tabler:wallet" class="text-[20px] text-primaryText" />
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: ROUTE_PATH.TRANSACTION_HISTORY }"
            >E-Point</el-breadcrumb-item
          >
          <el-breadcrumb-item> Chi tiết </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    <!-- <Title :mode="type" icon="users-group" /> -->
    <div class="view-height my-[20px] overflow-auto flex justify-center w-full">
      <div class="w-[60%]">
        <div
          class="px-3.5 py-1.5 rounded-[30px] outline outline-1 outline-offset-[-1px] outline-red-500 inline-flex justify-center items-center gap-2.5 overflow-hidden"
        >
          <div class="text-center justify-start text-red-500 text-sm font-semibold leading-snug">
            Thông tin đơn hàng
          </div>
        </div>
        <div class="text-[14px] flex justify-between pt-[25px]">
          <div class="w-1/2 flex flex-col gap-y-[20px]">
            <div class="flex flex-col gap-y-[7px]">
              <div class="justify-start text-zinc-800 text-xs font-normal leading-snug">
                Nhà cung cấp
              </div>
              <div class="justify-start text-zinc-800 text-sm font-semibold leading-snug">
                VNPT Omnichannel
              </div>
            </div>
            <div class="flex flex-col gap-y-[7px]">
              <div class="justify-start text-zinc-800 text-sm font-normal leading-snug">
                Mã đơn hàng
              </div>
              <div class="justify-start text-zinc-800 text-sm font-semibold leading-snug">
                {{ dataTransaction?.orderCode }}
              </div>
            </div>
            <div class="flex flex-col gap-y-[7px]">
              <div class="justify-start text-zinc-800 text-sm font-normal leading-snug">Mô tả</div>
              <div class="justify-start text-zinc-800 text-sm font-semibold leading-snug">
                {{ dataTransaction?.description }}
              </div>
            </div>
            <div class="flex flex-col gap-y-[7px]">
              <div class="justify-start text-zinc-800 text-sm font-normal leading-snug">
                Số tiền
              </div>
              <div class="justify-start text-red-500 text-lg font-semibold leading-snug">
                {{ Number(dataTransaction?.value).toLocaleString('vi-VN') }} VND
              </div>
            </div>

            <div class="flex flex-col gap-y-[7px]">
              <div class="justify-start text-zinc-800 text-sm font-normal leading-snug">
                Thời gian
              </div>
              <div class="justify-start text-zinc-800 text-sm font-semibold leading-snug">
                {{ moment(dataTransaction?.createdAt).format('DD/MM/YYYY HH:mm') }}
              </div>
            </div>
          </div>
          <div class="w-1/2 flex flex-col gap-y-[20px]">
            <div class="flex flex-col gap-y-[7px]">
              <div class="justify-start text-zinc-800 text-sm font-normal leading-snug">
                Doanh nghiệp thanh toán
              </div>
              <div class="justify-start text-zinc-800 text-sm font-semibold leading-snug">
                {{ dataTransaction?.businessName }}
              </div>
            </div>
            <div class="flex flex-col gap-y-[7px]">
              <div class="justify-start text-zinc-800 text-sm font-normal leading-snug">
                Hình thức thanh toán
              </div>
              <div class="justify-start text-zinc-800 text-sm font-semibold leading-snug">
                {{ dataTransaction?.paymentMethod }}
              </div>
            </div>
            <div class="flex flex-col gap-y-[7px]">
              <div class="justify-start text-zinc-800 text-sm font-normal leading-snug">
                Người yêu cầu
              </div>
              <div class="justify-start text-zinc-800 text-sm font-semibold leading-snug">
                {{ dataTransaction?.requesterName }}
              </div>
            </div>
            <div class="flex flex-col gap-y-[7px]">
              <div class="justify-start text-zinc-800 text-sm font-normal leading-snug">
                Người nạp E-Point
              </div>
              <div class="justify-start text-zinc-800 text-sm font-semibold leading-snug">
                {{ dataTransaction?.handlerName }}
              </div>
            </div>
            <div class="flex flex-col gap-y-[7px]">
              <div class="justify-start text-zinc-800 text-sm font-normal leading-snug">
                Trạng thái
              </div>
              <Tag
                class="text-nowrap py-3"
                :class="getStatusOrder(dataTransaction?.status).color"
                :value="getStatusOrder(dataTransaction?.status).label"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex z-[999] justify-between py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke"
    >
      <VElementButton
        icon="arrow-narrow-left"
        text="black"
        bgColor="#EBEBEB"
        label="Quay lại"
        @click="handleNavigateToList"
      />
      <div v-if="dataTransaction?.status === 'PENDING'">
        <VElementButton
          text="white"
          bgColor="#9D9D9D"
          label="Từ chối"
          @click="changeStatus('Từ chối')"
        />
        <VElementButton
          text="white"
          bgColor="#F95341"
          label="Xác nhận thanh toán"
          @click="changeStatus('Xác nhận thanh toán')"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.p-tag) {
  width: 120px;
}
.view-height {
  height: calc(100vh - 215px);
}
.el-form-item {
  margin-bottom: 6px;
}
</style>
