<script setup lang="ts">
import { onMounted, ref, reactive } from 'vue';
import * as yup from 'yup';
import moment from 'moment';
import { useForm } from 'vee-validate';
import { useRouter } from 'vue-router';
import { vOnClickOutside } from '@vueuse/components';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import VOldTable from '@/components/base/VOldTable.vue';
import FilterCard from '@/components/base/container/FilterCard.vue';
import { ROUTE_NAME, ROUTE_PATH } from '@/shared';

export interface ParamsSearch {
  page_index: number;
  page_size: number;
  transactionType?: string;
  startDate?: number;
  endDate?: number;
  businessId?: number;
  status?: number;
}

const showFilterCard = ref(false);
const router = useRouter();
const overlayLoading = useOverLayLoadingStore();
const api = useApi();
const tableRef = ref();
const filterRef = ref<InstanceType<typeof FilterCard>>();
const filterCount = ref('');
const listBussiness = ref<{ value: number; label: string }[]>([]);

const { values } = useForm({
  validationSchema: yup.object({
    org_id: yup.mixed(),
  }),
});

const params = ref<ParamsSearch>({
  page_index: 1,
  page_size: 20,
});

const headers = ref([
  {
    name: 'Doanh nghiệp',
    visible: true,
    pin: false,
  },
  {
    name: 'E-Point',
    visible: true,
    pin: false,
  },
  {
    name: 'Mã đơn hàng',
    visible: true,
    pin: false,
  },

  {
    name: 'Mô tả',
    visible: true,
    pin: false,
  },
  {
    name: 'Thời gian',
    visible: true,
    pin: false,
  },
  {
    name: 'Loại giao dịch',
    visible: true,
    pin: false,
  },
  {
    name: 'Trạng thái',
    align: 'center',
    visible: true,
    pin: false,
  },
  {
    name: 'Số dư cuối (E-Point)',
    align: 'center',
    visible: true,
    pin: false,
  },
]);

const list = ref([]) as any;

const getList = async () => {
  try {
    overlayLoading.toggleLoading(true);
    let url = `/wallet/v1/api/admin/transaction?page_size=${params.value.page_size}&page_index=${params.value.page_index}`;

    if (values.searchKey) {
      url += `&searchValue=${values.searchKey.trim()}`;
    }
    if (params.value.startDate && params.value.endDate) {
      url += `&startDate=${params.value.startDate}&endDate=${params.value.endDate}`;
    }
    if (params.value.businessId) {
      url += `&businessId=${params.value.businessId}`;
    }
    if (params.value.transactionType && params.value.transactionType !== '0') {
      url += `&transactionType=${params.value.transactionType}`;
    }
    if (params.value.transactionStatus < 3) {
      url += `&transactionStatus=${params.value.transactionStatus}`;
    }
    const res = await api.get(url);
    if (res.data.code === 0 && res.data.data) {
      list.value = res.data.data;
      // pagination.value = {
      //   total_page: res.data.data.paging?.total_pages,
      //   record_count: res.data.data.paging?.total_records,
      // };
      if (res.data.data?.items?.length === 0 && params.value.page_index !== 1) {
        tableRef.value.onPageChange(1);
        tableRef.value.filterData();
        return;
      }
    } else {
      toast('error', res.data.message);
    }
    overlayLoading.toggleLoading(false);
  } catch (error: any) {
    overlayLoading.toggleLoading(false);
    if (!error?.response?.status) {
      toast('error', TEXT.ERROR_OCCURRED);
    }
  }
};

const getListAgency = async () => {
  const { data } = await api.get('/business/v1/api/admin/business/list');
  if (data.code === 0) {
    listBussiness.value = data.data.map((i: any) => ({
      label: i?.business_name,
      value: i?.id,
    }));
  }
};

const transactionOptions = [
  { value: '0', label: 'Tất cả' },
  { value: 'T1', label: 'Nạp tiền' },
  { value: 'P1', label: 'Phí gửi tin' },
  { value: 'C1', label: 'Phí thiết lập hệ thống' },
  { value: 'C2', label: 'Phí khởi tạo Brandname' },
  { value: 'C3', label: 'Phí khởi tạo OA' },
  { value: 'P2', label: 'Phí duy trì Brandname' },
  { value: 'P3', label: 'Phí duy trì OA' },
  { value: 'R1', label: 'Hoàn tiền' },
];
const statusOptions = [
  { value: 3, label: 'Tất cả' },
  { value: 0, label: 'Đang xử lý' },
  { value: 1, label: 'Thành công' },
  { value: 2, label: 'Thất bại' },
];
const dataFilter = reactive([
  {
    label: 'Doanh nghiệp',
    valueName: 'businessId',
    type: 'dropdown',
    placeholder: 'Chọn giá trị',
    dropdownConfig: {
      option: listBussiness,
    },
  },
  {
    label: 'Loại giao dịch',
    valueName: 'transactionType',
    type: 'dropdown',
    placeholder: 'Chọn giá trị',
    defaultValue: '0',
    clearable: false,
    dropdownConfig: {
      option: transactionOptions,
    },
  },
  {
    label: 'Thời gian',
    type: 'calendar_range',
    valueName: 'time',
    defaultValue: '',
  },
  {
    label: 'Trạng thái',
    type: 'dropdown',
    valueName: 'status',
    placeholder: 'Chọn giá trị',
    defaultValue: 3,
    clearable: false,
    dropdownConfig: {
      option: statusOptions,
    },
  },
]);

const filterData = (value: any) => {
  showFilterCard.value = false;

  params.value.transactionType = value.transactionType;
  params.value.businessId = value.businessId;
  params.value.transactionStatus = value.status;
  if (value.time && value.time.length > 0) {
    params.value.startDate = moment(value.time[0])
      .utcOffset(7)
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss');
    params.value.endDate = moment(value.time[1])
      .utcOffset(7)
      .endOf('day')
      .format('YYYY-MM-DDTHH:mm:ss');
  }
  if (filterRef.value) {
    if (filterRef.value.count && filterRef.value.count > 0 && !isDefaultFilter()) {
      filterCount.value = `(${filterRef.value.count})`;
    } else {
      filterCount.value = '';
    }
  }
  dataFilter.forEach((item) => {
    if (value.hasOwnProperty(item.valueName)) {
      item.defaultValue = value[item.valueName] ?? '';
    }
  });
  tableRef.value.filterData();
};

const onPageChange = (value: number) => {
  params.value.page_index = value;
  getList();
};

const onPerPageChange = (value: number) => {
  params.value.page_size = value;
};

const onFilter = () => {
  if (filterRef.value) {
    filterRef.value.onSubmit();
    if (filterRef.value.count && filterRef.value.count > 0 && !isDefaultFilter()) {
      filterCount.value = `(${filterRef.value.count})`;
    } else {
      filterCount.value = '';
    }
    return;
  }
  tableRef.value.filterData();
};
const handleResetFilter = () => {
  filterCount.value = '';
  params.value.transactionType = '0';
  params.value.businessId = null;
  params.value.startDate = null;
  params.value.endDate = null;
  params.value.transactionStatus = 3;
  filterRef.value?.setFieldValue('transactionType', '0');
  filterRef.value?.setFieldValue('status', 3);
  filterRef.value?.setFieldValue('businessId', null);
};
const detailEPoint = (item: any) => {
  router.push({
    name: ROUTE_NAME.TRANSACTION_HISTORY_DETAILS,
    params: {
      id: item.transactionId,
    },
  });
};
const isDefaultFilter = () => {
  const isDefaultFilter =
    params.value.transactionType === '0' &&
    params.value.businessId === null &&
    params.value.transactionStatus === 3 &&
    params.value.startDate === null &&
    params.value.endDate === null;
  const isUndefinedFilter =
    params.value.transactionType === undefined &&
    params.value.businessId === undefined &&
    params.value.startDate === null &&
    params.value.endDate === null &&
    params.value.transactionStatus === undefined;
  return isDefaultFilter || isUndefinedFilter;
};
const onClickOutsideHandler = (ev: Event) => {
  const target = ev.target as Element;
  if (target.classList.contains('el-select-dropdown__option-item')) return;
  if (target.closest('.el-select-dropdown__option-item')) return;
  if (target.closest('.el-picker-panel__body')) return;
  if (target.closest('.filter')) return;
  showFilterCard.value = false;
};

const tableWidth = ref();

const onSetWidth = (value: number) => {
  tableWidth.value = value;
};

const getBusinessName = (businessId: number) => {
  const business = listBussiness.value.find((item) => item.value === businessId);

  return business ? business.label : '';
};
const getTransactionTypeName = (type: string) => {
  const option = transactionOptions.find((item) => item.value === type);
  return option ? option.label : '';
};
onMounted(async () => {
  overlayLoading.toggleLoading(true);
  await getList();
  await getListAgency();
  overlayLoading.toggleLoading(false);
});
</script>

<template>
  <!-- <InformationTop></InformationTop> -->
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:wallet" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: ROUTE_PATH.TRANSACTION_HISTORY }"
          >E-Point</el-breadcrumb-item
        >
        <el-breadcrumb-item> Danh sách</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
  <div class="mx-[10px] mt-[20px]">
    <div class="relative filter-container">
      <div class="flex gap-[10px]">
        <VElementButton
          styleIcon="text-[20px] text-[#fff] mr-[7px]"
          styleButton="s"
          icon="filter-plus"
          class="filter"
          :bgColor="color.tertiary"
          :label="`Bộ lọc ${filterCount}`"
          @click="showFilterCard = !showFilterCard"
        />
        <VElementInput
          size="default"
          name="keyword"
          prefix="search"
          placeholder="Tìm kiếm theo mã giao dịch"
          :style="'!w-[480px]'"
          @keyup.enter="getList()"
        />
        <VElementButton styleButton="s" :bgColor="color.main" label="Tìm kiếm" @click="onFilter" />
        <Transition>
          <FilterCard
            v-if="showFilterCard"
            ref="filterRef"
            v-on-click-outside="onClickOutsideHandler"
            filterHeight="h-[190px]"
            class="w-[400px]"
            :widthInput="'!w-[250px]'"
            :dataFilter="dataFilter"
            @filter="filterData"
            @onResetForm="handleResetFilter"
          />
        </Transition>
      </div>
    </div>
    <VOldTable
      idTable="tai-khoan-table"
      ref="tableRef"
      :rows="list.items ?? []"
      :styleHeaders="[
        {
          idx: 0,
          class: 'w-[15%]',
        },
        {
          idx: 1,
          class: 'w-[15%]',
        },
        {
          idx: 2,
          class: 'w-[10%]',
        },
        {
          idx: 3,
          class: 'w-[25%]',
        },
        {
          idx: 4,
          class: 'w-[10%]',
        },
        {
          idx: 5,
          class: 'w-[10%]',
        },
        {
          idx: 6,
          class: 'w-[10%]',
        },
        {
          idx: 7,
          class: 'w-[10%]',
        },
        {
          idx: 8,
          class: 'w-[10%]',
        },
      ]"
      :headers="headers"
      :keySortTable="['read_count']"
      :pagination="{
        totalPage: list?.paging?.total_pages ?? 0,
        total: list?.paging?.total_records ?? 0,
        perPage: params.page_size ?? 0,
      }"
      :actionLength="3"
      @pageChanged="onPageChange"
      @perPageChange="onPerPageChange"
      @setWidth="onSetWidth"
      @rowClick="detailEPoint"
    >
      <template v-slot:items="{ row }">
        <td class="text-primaryText px-2">
          <div class="column-container" :title="getBusinessName(row?.businessId)">
            {{ getBusinessName(row?.businessId) }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div
            class="column-container max-w-[150px]"
            :title="row.value"
            :class="row?.value < 0 ? 'text-red-500' : 'text-green-500'"
          >
            {{
              row?.value > 0
                ? `+ ${row.value?.toLocaleString('vi-VN')}`
                : `${row.value?.toLocaleString('vi-VN')}`
            }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container max-w-[180px]" :title="row.orderCode">
            {{ row.orderCode }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container" :title="row?.description">
            {{ row?.description }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container" :title="moment(row.createdAt).format('DD/MM/YYYY HH:mm')">
            {{ moment(row.createdAt).format('DD/MM/YYYY HH:mm') }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container" :title="getTransactionTypeName(row?.type)">
            {{ getTransactionTypeName(row?.type) }}
          </div>
        </td>

        <td class="text-primaryText px-2 text-center">
          <div
            class="center-container"
            :title="
              row.status === 'PENDING'
                ? 'Đang xử lý'
                : row.status === 'SUCCESS'
                  ? 'Thành công'
                  : row.status === 'FAILED'
                    ? 'Thất bại'
                    : ''
            "
          >
            <Tag
              :class="
                row.status === 'PENDING'
                  ? color.Yellow
                  : row.status === 'SUCCESS'
                    ? color.Green
                    : row.status === 'FAILED'
                      ? color.Red
                      : ''
              "
              :value="
                row.status === 'PENDING'
                  ? 'Đang xử lý'
                  : row.status === 'SUCCESS'
                    ? 'Thành công'
                    : row.status === 'FAILED'
                      ? 'Thất bại'
                      : ''
              "
            ></Tag>
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container font-semibold" :title="row?.lastBalance">
            {{ row?.lastBalance?.toLocaleString('vi-VN') }}
          </div>
        </td>
      </template>
      <template v-slot:actions="{ row }">
        <li class="item min-w-[100px]" @click="detailEPoint(row)" @keydown="detailEPoint(row)">
          <i class="pi pi-eye text-xs"></i><span>Xem chi tiết</span>
        </li>
      </template>
    </VOldTable>
  </div>
</template>

<style lang="scss" scoped>
.item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: start;
  padding: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 131px;
  font-size: 14px;
  font-weight: 400;
  &:hover {
    color: var(--main-color);
  }
}
:deep(.p-tag) {
  width: 120px;
}
</style>
