<script setup lang="ts">
import VElementInput from '@/components/base/ElementComponent/VElementInput.vue';
import { color } from '@/constants/statusColor';
import Dialog from 'primevue/dialog';
import { useForm } from 'vee-validate';
import * as yup from 'yup';
const props = withDefaults(
  defineProps<{
    visible: boolean;
    header?: string;
    label?: string;
  }>(),
  {
    label: 'Lý do từ chối',
  },
);
const emit = defineEmits(['closeForm', 'cancelConfirm', 'confirm']);
const { values, handleSubmit } = useForm({
  validationSchema: yup.object({
    reason: yup.string().trim().required(`${props.label} không được để trống`),
  }),
});

const onSubmit = handleSubmit(() => {
  emit('confirm', values.reason);
});
</script>
<template>
  <Dialog
    ref="refDialog"
    :visible="props.visible"
    :draggable="false"
    :header="header"
    @update:visible="emit('closeForm')"
    :pt="{
      content: { class: '' },
      root: { class: 'bg-[#fff]' },
      header: {
        class:
          '!pl-[140px] !border-b-[1px] !border-solid !border-stroke  !text-[12px] !font-medium !bg-[#fbfaff] !h-[54px]',
      },
    }"
    modal
    :style="{
      width: '500px',
      height: '200px',
      maxHeight: '100%',
    }"
  >
    <el-form @submit.prevent label-position="top" class="pt-2">
      <VElementInput name="reason" required :label="label" show-limit :maxlength="250" />
      <div class="save-container flex justify-center items-center mt-5 pr-5">
        <VElementButton
          :bgColor="color.closeButton"
          @click="emit('closeForm')"
          label="Đóng"
        ></VElementButton>
        <VElementButton :bgColor="color.main" @click="onSubmit" label="Lưu"></VElementButton>
      </div>
    </el-form>
  </Dialog>
</template>
