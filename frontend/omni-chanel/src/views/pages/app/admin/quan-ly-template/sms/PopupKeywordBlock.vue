<script setup lang="ts">
import { color } from '@/constants/statusColor';
import { blockedKeywordsHeaders } from './const.sms';
import { reactive, ref, watch } from 'vue';
import VOldTable from '@/components/base/VOldTable.vue';
import { useForm } from 'vee-validate';
import moment from 'moment';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { blockKeywordServices } from '@/services/blockKeyword.services';
interface IKeyword {
  keyword?: string;
  description?: string;
  created_at?: string;
}
const props = defineProps<{
  visible: boolean;
}>();
const emit = defineEmits(['closeForm', 'getList', 'updateChange']);
const { values } = useForm({
  initialValues: {
    keyword: '',
  },
});
const params = reactive({
  pageIndex: 1,
  pageSize: 20,
});
const pageInfo = reactive<any>({
  currentPage: null,
  total_pages: null,
  total_records: null,
});
const listBlockedKeyword = ref<IKeyword[]>([]);
const tableRef = ref();
const onFilter = () => {
  tableRef.value.filterData();
};

const onPageChange = (page: number) => {
  params.pageIndex = page;
  getList();
};
const getList = async () => {
  const { data } = await blockKeywordServices.getList({ ...params, ...values });
  if (data.code === 0) {
    listBlockedKeyword.value = data.data?.items;
    pageInfo.total_pages = data?.data?.paging?.total_pages;
    pageInfo.total_records = data.data?.paging?.total_records;
  }
};
const onPerPageChange = (size: number) => {
  params.pageSize = size;
};
const overlayLoading = useOverLayLoadingStore();
watch(
  () => props.visible,
  async (newValue) => {
    if (newValue) {
      overlayLoading.toggleLoading(true);
      await getList();
      overlayLoading.toggleLoading(false);
    }
  },
);
const sortTable = () => {
  getList();
};
</script>
<template>
  <VDialog
    ref="refDialog"
    :visible="visible"
    @update:visible="emit('closeForm')"
    :draggable="false"
    :pt="{
      root: { class: 'bg-[#fff]' },
      header: {
        class: '!pl-[200px] !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
      },
    }"
    modal
    header="Danh sách từ khóa bị chặn"
    :class="'h-[600px] '"
    :style="{
      width: '700px',
      backgroundColor: '#fff',
      maxHeight: '700px',
    }"
  >
    <div class="relative filter-container">
      <el-form @submit.prevent class="flex gap-[12px]">
        <VElementInput
          size="small"
          name="keyword"
          type="text"
          placeholder="Tìm kiếm từ khóa"
          id="keyword-search-input"
          class="grow"
          @keyup.enter="onFilter"
        />
        <VElementButton
          label="Tìm kiếm"
          styleButton="s"
          id="keyword-search-button"
          :bgColor="color.main"
          @click="onFilter"
        />
      </el-form>
    </div>
    <VOldTable
      idTable="sms-template-table"
      ref="tableRef"
      :rows="listBlockedKeyword"
      @pageChanged="onPageChange"
      @perPageChange="onPerPageChange"
      @sort-table="sortTable"
      :headers="blockedKeywordsHeaders"
      :styleHeaders="[
        { idx: 0, class: 'w-[32%]' },
        { idx: 1, class: 'w-[32%]' },
        { idx: 2, class: 'w-[32%]' },
      ]"
      :showAction="false"
      :pagination="{
        totalPage: pageInfo.total_pages,
        total: pageInfo.total_records,
        perPage: params.pageSize,
      }"
    >
      <template v-slot:items="{ row }">
        <td class="text-primaryText text-center px-2">
          <div class="column-container">
            {{ row?.keyword }}
          </div>
        </td>
        <td class="text-primaryText text-center px-2">
          <div class="column-container">
            {{ row?.description }}
          </div>
        </td>
        <td class="text-primaryText text-center px-2">
          <div class="column-container">
            {{ moment(row?.created_at).format('DD/MM/YYYY hh:mm') }}
          </div>
        </td>
      </template>
    </VOldTable>
  </VDialog>
</template>
<style scoped>
:deep(.v-old-table-container) {
  height: 350px;
}
</style>
