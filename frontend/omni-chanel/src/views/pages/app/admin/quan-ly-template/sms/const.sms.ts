import { color } from '@/constants/statusColor';
import { ALIGN, REGEX_CHECK_PARAMETER } from '@/shared';
import * as yup from 'yup';
export interface IBrandName {
  business_name?: string;
  created_at?: string;
  expired_at?: string;
  id?: number;
  name?: string;
  sms_type?: string;
  sms_type_id?: number;
  status?: number;
}
export const SMS_TYPE = {
  CSKH: 1,
  QC: 2,
  ALL: 3,
};
export const TEMPLATE_STATUS = {
  DRAFT: 0,
  ACTIVE: 1,
  PENDING: 2,
  PAUSE: 3,
  REJECTED: 4,
};
export const smsTypeList = [
  { value: 1, label: 'CSKH' },
  { value: 2, label: ' QC' },
  { value: 3, label: ' CSKH, QC' },
];
export const statusList = [
  { value: 1, label: 'Hoạt động' },
  { value: 2, label: 'Chờ duyệt' },
  { value: 3, label: 'Tạm dừng' },
  { value: 4, label: 'Từ chối' },
];
export const getStatusMeta = (status?: number) => {
  switch (status) {
    case TEMPLATE_STATUS.DRAFT:
      return { label: 'Nháp', color: color.Dark };
    case TEMPLATE_STATUS.ACTIVE:
      return { label: 'Hoạt động', color: color.Green };
    case TEMPLATE_STATUS.PENDING:
      return { label: 'Chờ duyệt', color: color.Pending };
    case TEMPLATE_STATUS.PAUSE:
      return { label: 'Tạm dừng', color: color.Pause };
    case TEMPLATE_STATUS.REJECTED:
      return { label: 'Từ chối', color: color.Red };
    default:
      return { label: 'Không xác định', color: color.Dark };
  }
};
export const PENDING_MESSAGE =
  'Gửi xét duyệt thành công. Kết quả sẽ được cập nhật sau 3-5 ngày làm việc kể từ ngày cung cấp đầy đủ thông tin';
export const getString = (inputString: string) => {
  const regex1 = /<([^<>]+)>/;
  const match = regex1.exec(inputString);
  if (match && match[1]) {
    return match[1];
  }
  return null;
};
export const technicalSchema = yup.object({
  sample_data: yup
    .string()
    .required('Trường dữ liệu mẫu không được để trống')
    // eslint-disable-next-line prefer-arrow-callback
    .trim()
    .test('check-maxlength', 'error', function (value, context) {
      const maxlength = context.options.context?.maxLength;
      if (value.length > maxlength) {
        return this.createError({ message: `Trường dữ liệu mẫu tối đa ${maxlength} ký tự` });
      }
      return true;
    }),
});

export const regex = /<([^>]*)>/g;
export const regexCheckParam = REGEX_CHECK_PARAMETER;
export const hasVietnameseAccents = (text: string) => {
  // Regex cho các ký tự có dấu trong tiếng Việt
  const vietnameseAccentsRegex =
    /[àáạảãâấầẩẫậăắằẳẵặđéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵÀÁẠẢÃÂẤẦẨẪẬĂẮẰẲẴẶĐÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴ]/;
  return vietnameseAccentsRegex.test(text);
};

export const contentValidation = yup
  .string()
  .trim()
  .required('Template không được để trống')
  .max(2000, '')
  .test('check-tag-content', 'Tham số không hợp lệ', function (value) {
    if (!value) return true;
    const tagRegex = /<([^>]*)>/g;
    const matches = value.match(tagRegex);
    // Nếu không có thẻ <>, không cần kiểm tra
    if (!matches || matches.length === 0) return true;

    for (const match of matches) {
      // Lấy nội dung bên trong thẻ <> (bỏ dấu < và >)
      const content = match.slice(1, -1);

      // Nếu nội dung rỗng, báo lỗi
      if (!content.trim()) {
        return this.createError({ message: 'Tham số không hợp lệ' });
      }

      // Kiểm tra khoảng trống
      if (content.includes(' ')) {
        return this.createError({
          message: 'Tham số không hợp lệ',
        });
      }

      // Kiểm tra ký tự tiếng Việt có dấu
      if (hasVietnameseAccents(content)) {
        return this.createError({
          message: 'Tham số không hợp lệ',
        });
      }

      // Kiểm tra ký tự đặc biệt không hợp lệ
      if (!/^[a-zA-Z0-9_]+$/.test(content)) {
        return this.createError({
          message: 'Tham số không hợp lệ',
        });
      }
    }

    return true;
  })
  .test('check-unicode', 'Vui lòng nhập nội dung không dấu', function (value, context) {
    if (!value || context.options.context?.unicode !== false) return true;
    const contentWithoutTags = value.replace(/<([^>]*)>/g, '');
    return !hasVietnameseAccents(contentWithoutTags);
  });
export const blockedKeywordsHeaders = [
  {
    key: 'keyword',
    name: 'Từ khóa',
    visible: true,
    pin: false,
    align: ALIGN.CENTER,
  },
  {
    key: 'description',
    name: 'Mô tả',
    visible: true,
    pin: false,
    align: ALIGN.CENTER,
  },
  {
    key: 'address',
    name: 'Thời gian tạo',
    visible: true,
    pin: false,
    align: ALIGN.CENTER,
  },
];
export const BUTTON_TYPE = {
  DELETE: 0,
  UPDATE: 1,
  SAVE: 2,
  PAUSE: 3,
  PENDING: 4,
  ACTIVE: 5,
  REJECTED: 6,
};
export const checkBlockedKeywords = (
  value: string | undefined,
  blockedKeywords: string[],
  createError: (options: { message: string }) => yup.ValidationError | true,
): yup.ValidationError | true => {
  if (!value) return true;
  const contentLower = value.toLowerCase().trim();
  const contentWords = contentLower.split(/\s+/);

  for (const keyword of blockedKeywords) {
    const keywordLower = keyword.toLowerCase().trim();
    const keywordWords = keywordLower.split(/\s+/);
    const keywordLength = keywordWords.length;
    const contentLength = contentWords.length;

    if (keywordLength > contentLength) {
      continue;
    }

    for (let i = 0; i <= contentLength - keywordLength; i++) {
      const match = keywordWords.every((word, index) => contentWords[i + index] === word);
      if (match) {
        return createError({
          message: `Nội dung không được phép chứa từ khóa chặn "${keyword}"`,
        });
      }
    }
  }
  return true;
};
