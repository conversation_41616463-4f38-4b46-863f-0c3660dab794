<!-- eslint-disable import/no-cycle -->
<script setup lang="ts">
import { computed, inject, onMounted, ref, watch } from 'vue';
import * as yup from 'yup';
import { useForm, useFieldArray } from 'vee-validate';
import { useRoute, useRouter } from 'vue-router';
import { useApi } from '@/store/useApi';
import { color } from '@/constants/statusColor';
import SmsIcon from '@/components/icon/SmsIcon.vue';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import PopupCancelConfirm from '@/components/base/common/PopupCancelConfirm.vue';
import {
  BUTTON_TYPE,
  contentValidation,
  getStatusMeta,
  hasVietnameseAccents,
  PENDING_MESSAGE,
  regex,
  regexCheckParam,
  SMS_TYPE,
  smsTypeList,
  technicalSchema,
  TEMPLATE_STATUS,
  type IBrandName,
} from '../const.sms';
import PopupConfirm from '../popupConfirm.vue';
import { ROUTE_NAME, ROUTE_PATH, SYSTEM_CODE } from '@/shared';
import PopupDeny from '../PopupDeny.vue';
import PopupKeywordBlock from '../PopupKeywordBlock.vue';
import { brandNameClientServices } from '@/services/brandName.services';
import { PageType } from '@/enums/common';
interface settingInterface {
  name: string;
  sample_data: string;
  field360_id: number | null;
  field_zalo_id: number | null;
}
interface formData {
  reason?: string;
  id?: number;
  label_id: number | null;
  type_id: number | null;
  unicode: boolean;
  content: string;
  status?: number;
  note: string;
  params: settingInterface[];
  submit: boolean;
  label_name?: string;
  business_name?: string;
  template_name?: string;
}

const props = defineProps<{
  id?: number;
  type: PageType;
}>();

const route = useRoute();
const router = useRouter();
const api = useApi();
const overlayLoading = useOverLayLoadingStore();
const toast = inject('toast') as any;
const popupVisible = ref(false);
const popupPause = ref(false);
const popupDelete = ref(false);
const popupKeywordBlock = ref(false);
const listBlockedKeyword = ref([]);
const existed_in_campaign = ref(0);
const emojiRegex = /[\p{Emoji_Presentation}\p{Extended_Pictographic}]/gu;
const validationSchema: any = yup.object({
  content: contentValidation,
  //   .test(
  //   'check-blocked-keywords',
  //   'Nội dung không được phép chứa từ khóa chặn',
  //   function (value) {
  //     return checkBlockedKeywords(value, blockedKeywords.value, (errorOptions) =>
  //       this.createError(errorOptions),
  //     );
  //   },
  // ),
  params: yup.array().of(technicalSchema),
  label_id: yup.string().required('Vui lòng chọn brandname').trim(),
  type_id: yup.string().required('Vui lòng chọn loại template').trim(),
  template_name: yup.string().required('Tên template không được để trống').trim(),
});
const { values, handleSubmit, setFieldValue, defineField, errors, setFieldError } =
  useForm<formData>({
    validationSchema,
    initialValues: {
      content: '',
      unicode: true,
      template_name: '',
    },
  });
const [unicode, unicodeAtr] = defineField('unicode');

const { push: pushSetting, fields: params } = useFieldArray<settingInterface>('params');
const tempParams = ref<settingInterface[]>([]);
const valueTexts = computed(() => ['', values.content]);
const getParamTag = (arrStrings: string[]) => {
  const extractedParams = extractUniqueParams(arrStrings);
  const currentParamsData = values.params ? [...values.params] : [];
  setFieldValue('params', []);
  if (!extractedParams.length) return;
  createParamsList(extractedParams, currentParamsData).forEach((param) => pushSetting(param));
};

const extractUniqueParams = (arrStrings: string[]): string[] => {
  const extractedParams: string[] = [];

  arrStrings.forEach((str) => {
    const matches = str.match(regex);
    if (!matches) return;

    matches.forEach((item) => {
      const paramNameWithoutBrackets = item.slice(1, -1);
      if (!regexCheckParam.test(paramNameWithoutBrackets)) return;

      if (!extractedParams.includes(item)) {
        extractedParams.push(item);
      }
    });
  });

  return extractedParams;
};
const createParamsList = (
  extractedParams: string[],
  currentParams: settingInterface[] = [],
): settingInterface[] =>
  extractedParams.map((paramName) => {
    const paramNameWithoutBrackets = getNormalizedParamName(paramName);
    const existingParam = findExistingParam(paramNameWithoutBrackets);
    const currentParam = currentParams.find(
      (param) => getNormalizedParamName(param.name) === paramNameWithoutBrackets,
    );

    if (currentParam) {
      return {
        name: paramName,
        sample_data: currentParam.sample_data,
        field360_id: currentParam.field360_id,
        field_zalo_id: currentParam.field_zalo_id,
      };
    }
    return existingParam
      ? {
          name: paramName,
          sample_data: existingParam.sample_data,
          field360_id: existingParam.field360_id,
          field_zalo_id: existingParam.field_zalo_id,
        }
      : {
          name: paramName,
          sample_data: '',
          field360_id: null,
          field_zalo_id: null,
        };
  });
const getNormalizedParamName = (paramName: string): string => paramName.replace(/^<|>$/g, '');

const findExistingParam = (paramName: string): settingInterface | null =>
  tempParams.value.length
    ? tempParams.value.find((param) => param.name === paramName) || null
    : null;

watch(valueTexts, async () => {
  getParamTag(valueTexts.value);
});

const listBrandName = ref<IBrandName[]>([]);

const normalizeParamsForSubmit = (params: settingInterface[]): settingInterface[] =>
  !params?.length
    ? []
    : params.map((param) => ({
        ...param,
        name: getNormalizedParamName(param.name),
      }));

const onSubmit = handleSubmit(async () => {
  setFieldError('content', '');
  const hasError = params.value.some((param) => getError(param.value.sample_data));
  if (hasError && params.value.length > 0) return;

  const cleanedParams = normalizeParamsForSubmit(values.params || []);
  try {
    const { data } = await api.put(
      'business/v1/api/admin/sms-template',
      {
        ...values,
        params: cleanedParams,
      },
      { skipToastErr: true },
    );

    if (data.code === 0) {
      toast('success', values.submit ? PENDING_MESSAGE : 'Cập nhật thành công');
      getDetail();
      router.push({ name: ROUTE_NAME.DETAIL_SMS, params: { id: data?.data?.id } });
    }
  } catch (err: any) {
    if (err.code === SYSTEM_CODE.BLOCKED_KEYWORD) {
      return setFieldError('content', err?.message);
    }
    toast('error', err?.message);
  }
});

const getDetail = async () => {
  overlayLoading.toggleLoading(true);
  const { data } = await api.get(`business/v1/api/admin/sms-template/${route.params?.id}`);
  if (data.code !== 0) {
    overlayLoading.toggleLoading(false);
    return;
  }

  const {
    label_id,
    type_id,
    content,
    status,
    note,
    params = [],
    id,
    reason,
    unicode,
    label_name,
    business_name,
  } = data?.data ?? {};

  const hasParams = !!params.length;
  tempParams.value = hasParams ? [...params] : [];
  setFieldValue('unicode', unicode);
  setFieldValue('label_id', label_id);
  setFieldValue('id', id);
  setFieldValue('reason', reason);
  setFieldValue('type_id', type_id);
  setFieldValue('status', status);
  setFieldValue('content', content);
  setFieldValue('note', note);
  setFieldValue('label_name', label_name);
  setFieldValue('business_name', business_name);
  setFieldValue('template_name', data?.data?.template_name);
  existed_in_campaign.value = data?.data?.existed_in_campaign;
  if (label_id) {
    const {
      data: { data },
    } = await brandNameClientServices().getDetail(label_id);
    if (data && data.service_sms_type) {
      listBrandName.value = [
        //@ts-expect-error Disabled error
        { value: label_id, label: label_name, id: label_id, sms_type_id: data.service_sms_type },
      ];
    }
  }
  setTemplateType();
  overlayLoading.toggleLoading(false);
};
const handleCancel = () => {
  if (isMutate.value) {
    popupVisible.value = true;
    return;
  }
  router.push(ROUTE_PATH.SMS);
};

const turnBack = () => {
  router.push({
    name: ROUTE_NAME.SMS,
  });
};
const templateTypeList = ref<any>([]);

const setTemplateType = () => {
  const selectedBrandname = listBrandName.value?.find((i: any) => i.id === values.label_id);
  if (!selectedBrandname?.sms_type_id) return;
  templateTypeList.value =
    selectedBrandname.sms_type_id === SMS_TYPE.ALL
      ? [
          { value: SMS_TYPE.CSKH, label: 'CSKH' },
          { value: SMS_TYPE.QC, label: 'QC' },
        ]
      : [smsTypeList?.find((item) => item.value === selectedBrandname.sms_type_id)];

  // setFieldValue('type_id', templateTypeList.value[0]?.value);
};

const [content] = defineField('content');
const contentWithParams = computed(() => {
  let result = content.value;
  params.value.forEach((param) => {
    const regex = new RegExp(param.value.name, 'g');
    result = result.replace(regex, param.value.sample_data);
  });
  return result;
});
const removeEmoji = (value: string) => {
  content.value = value.replace(emojiRegex, '');
  validationSchema
    .validateAt('content', { content: content.value })
    .then(() => setFieldError('content', ''))
    .catch((error: any) => error.message && setFieldError('content', error.message));
};

const sampleDataSchema = yup
  .string()
  .trim()
  .required('Nội dung mẫu không được để trống')
  .test('check-unicode', 'Vui lòng nhập nội dung không dấu', function (value) {
    if (!value || values?.unicode !== false) return true;
    const contentWithoutTags = value.replace(/<([^>]*)>/g, '');
    return !hasVietnameseAccents(contentWithoutTags);
  });
const getError = (value: string): string => {
  try {
    sampleDataSchema.validateSync(value);
    return '';
  } catch (err: any) {
    return err.message;
  }
};
const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault();
  const target = event.target as HTMLInputElement | HTMLTextAreaElement;
  const start = target.selectionStart ?? 0;
  const end = target.selectionEnd ?? 0;
  const clipboardText = event.clipboardData?.getData('text').replace(/[\r\n\t\f\v]/g, '') || '';
  const cleanedText = clipboardText.replace(emojiRegex, '');
  const maxLength = 2000 - (content.value.length - (end - start));
  const truncatedText = cleanedText.substring(0, Math.max(0, maxLength));
  const newContent =
    content.value.substring(0, start) + truncatedText + content.value.substring(end);
  removeEmoji(newContent);
};
const popupDenyVisible = ref(false);
const popupPauseVisible = ref(false);

const handleDeny = () => {
  popupDenyVisible.value = true;
};

const confirmDeny = async (reason: string, status: number) => {
  const { data } = await api.put(`business/v1/api/admin/sms-template/${route.params?.id}/status`, {
    status,
    reason,
  });
  if (data.code === 0) {
    if (status === TEMPLATE_STATUS.PAUSE) {
      toast('success', 'Tạm dừng template thành công');
    }
    if (status === TEMPLATE_STATUS.REJECTED) {
      toast('success', 'Từ chối template SMS thành công');
    }
    popupDenyVisible.value = false;
    popupPauseVisible.value = false;
    getDetail();
    router.push({ name: ROUTE_NAME.DETAIL_SMS, params: { id: route.params?.id } });
  }
};
const handleUpdateTemplate = async (status: number) => {
  const { data } = await api.put(`business/v1/api/admin/sms-template/${route.params?.id}/status`, {
    status,
    reason: '',
  });
  if (data.code === 0) {
    toast('success', data?.message);
    popupDenyVisible.value = false;
    await getDetail();
    router.push({ name: ROUTE_NAME.DETAIL_SMS, params: { id: values?.id } });
    isMutate.value = false;
  }
};
const deleteTemplate = async () => {
  popupDelete.value = true;
};

const filteredBlockedKeywords = computed(() => {
  if (values.type_id) {
    return listBlockedKeyword.value?.filter((i: any) => i.sms_type === values.type_id);
  }
  return listBlockedKeyword.value;
});
const showBlockedKeywordLink = computed(() => {
  return errors.value.content && errors.value.content.includes('từ khóa chặn');
});
const getButtonByStatus = (type: number) => {
  if (type === BUTTON_TYPE.DELETE) {
    if (
      values.status !== TEMPLATE_STATUS.REJECTED ||
      props.type !== 'xem' ||
      existed_in_campaign.value
    )
      return false;
    return true;
  }
  if (type === BUTTON_TYPE.ACTIVE) {
    if (values.status !== TEMPLATE_STATUS.PAUSE || props.type !== 'xem') return false;
    return true;
  }
  if (type === BUTTON_TYPE.UPDATE) {
    if (
      props.type !== 'xem' ||
      values.status === TEMPLATE_STATUS.ACTIVE ||
      existed_in_campaign.value
    )
      return false;
    return true;
  }
  if (type === BUTTON_TYPE.SAVE) {
    if (props.type === 'xem') return false;
    return true;
  }
  if (type === BUTTON_TYPE.PENDING) {
    if (props.type !== 'xem' || (values.status && values.status !== TEMPLATE_STATUS.PENDING))
      return false;
    return true;
  }
  if (type === BUTTON_TYPE.PAUSE) {
    if (props.type !== 'xem' || values.status !== TEMPLATE_STATUS.ACTIVE) return false;
    return true;
  }
  if (type === BUTTON_TYPE.REJECTED) {
    if (props.type !== 'xem' || (values.status && values.status !== TEMPLATE_STATUS.PENDING))
      return false;
    return true;
  }
};
const isMutate = ref(false);
onMounted(async () => {
  // getBlockedKeyword();
  if (props.type !== 'them') {
    await getDetail();
  }
  watch(
    () => values,
    () => {
      isMutate.value = true;
    },
    { immediate: false, deep: true },
  );
});
</script>

<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <SmsIcon />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/admin/sms-template' }">Template SMS</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: route.path }">
          {{
            props.type === PageType.Add
              ? 'Thêm mới'
              : props.type === PageType.Update
                ? 'Cập nhật'
                : 'Xem chi tiết'
          }}</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
  </div>
  <el-form
    class="w-[100%] flex justify-center pt-[28px] px-2 mb-[70px] view-height"
    label-position="top"
  >
    <div class="grid grid-cols-12 gap-4">
      <div class="col-span-7">
        <div class="font-semibold leading-[22px] mb-2">Thông tin Template SMS</div>
        <el-form label-position="top" :disabled="props.type === 'xem'">
          <div class="grid grid-cols-2 font-medium text-[0.9rem] gap-3">
            <div v-if="props.type !== 'them'">ID: {{ values.id }}</div>
            <div class="relative" v-if="props.type !== 'them'">
              Trạng thái
              <Tag
                class="text-nowrap ml-1"
                :class="getStatusMeta(values?.status).color"
                :value="getStatusMeta(values?.status).label"
              />
              <el-tooltip v-if="!!values.reason" popper-class="!max-w-[380px]" placement="top">
                <template #content> {{ values?.reason }}</template>
                <i class="pi absolute pi-info-circle ml-2 mt-[3px] !text-[16px]"></i>
              </el-tooltip>
            </div>
            <div class="col-span-2">
              Doanh nghiệp:
              <span> {{ values?.business_name }} </span>
            </div>
            <VElementDropdown
              :placeholder="'Chọn Brandname'"
              required
              :filterable="false"
              :label="'Chọn giá trị'"
              :option="listBrandName"
              :style="'w-[100%]'"
              name="label_id"
              :disabled="props.type !== 'them'"
              @change="setTemplateType"
            />
            <VElementDropdown
              :placeholder="'Chọn giá trị'"
              required
              :filterable="false"
              :label="'Loại template'"
              :option="templateTypeList"
              :style="'w-[100%]'"
              name="type_id"
            />
          </div>
          <el-radio-group class="mt-3" v-model="unicode" v-bind="unicodeAtr">
            <el-radio :label="true">Gửi tin có dấu</el-radio>
            <el-radio :label="false">Gửi tin không dấu </el-radio>
          </el-radio-group>
          <div class="pt-2 relative">
            <span
              v-if="showBlockedKeywordLink"
              @click="popupKeywordBlock = true"
              class="cursor-pointer absolute right-0 top-2 underline text-blue-600 hover:text-blue-800"
            >
              Xem danh sách từ khóa bị chặn
            </span>
            <VElementInput
              required
              label="Tên template"
              name="template_name"
              class="w-full"
              placeholder="Nhập tên template"
              type="text"
              :maxlength="60"
              show-word-limit
            />
            <el-form-item required label="Template" class="w-full mt-3">
              <el-input
                v-model="content"
                class="w-full"
                type="textarea"
                :autosize="{
                  minRows: 5,
                }"
                show-word-limit
                placeholder="Nhập giá trị"
                :maxlength="2000"
                @input="removeEmoji"
                @paste="handlePaste"
              />
            </el-form-item>
            <small class="flex items-center gap-4 p-error pt-1">{{
              errors.content || '&nbsp;'
            }}</small>
          </div>
          <div class="h-fit text-[14px]">
            <span class="font-medium mb-1">Lưu ý:</span>
            <ul class="list-disc l ml-8">
              <li>
                Nội dung nào là biến thay đổi để trong dấu "&lt;...&gt;" và viết liền không dấu. Ví
                dụ: &lt;ten_khach_hang&gt;, &lt;ngay_sinh&gt;
              </li>
              <li>
                Nếu nội dung không có dấu, hãy chọn "Gửi tin không dấu" để tránh bị tính phí Gửi tin
                có dấu
              </li>
            </ul>
          </div>
          <VElementInput
            class="mt-[15px]"
            type="textarea"
            :style="'w-[100%]'"
            :placeholder="'VD: Tin nhắn cảm ơn khách hàng'"
            sizeInput="none"
            label="Ghi chú"
            :showLimit="true"
            :maxlength="250"
            name="note"
          />
        </el-form>
      </div>
      <div class="col-span-5">
        <div class="font-semibold leading-[22px]">Cài đặt tham số</div>
        <div class="grid grid-cols-2 gap-[5px] pt-[10px]">
          <div class="text-[13px] text-[#6b7280]">Tên tham số</div>
          <!-- <div class="text-[13px] text-[#6b7280]">Trường dữ liệu zalo <span class="text-[#F10000]">*</span></div> -->
          <div class="text-[13px] text-[#6b7280]">
            Nội dung mẫu <span class="text-[#F10000]">*</span>
          </div>
        </div>
        <div
          v-for="(setting, idx) in params"
          :key="idx"
          :class="{
            'pt-[5px]': idx === 0,
            'pt-[20px]': idx !== 0,
          }"
          class="grid grid-cols-2 gap-[5px]"
        >
          <el-input
            :style="'w-[100%]'"
            size="default"
            :disabled="true"
            v-model="setting.value.name"
          />

          <el-form-item :error="getError(setting.value.sample_data)">
            <el-input
              show-word-limit
              :style="'w-[100%]'"
              :maxlength="100"
              size="default"
              v-model="setting.value.sample_data"
              :disabled="props.type === 'xem'"
            />
          </el-form-item>
        </div>
        <div v-if="!params.length" class="pt-[5px] grid grid-cols-2 gap-[5px]">
          <el-input :style="'w-[100%]'" size="default" :disabled="true" />

          <el-form-item>
            <el-input show-word-limit :maxlength="100" size="default" disabled />
          </el-form-item>
        </div>
        <el-form-item label="Tin nhắn mẫu" class="w-full mt-3">
          <el-input
            v-model="contentWithParams"
            class="w-full"
            type="textarea"
            :autosize="{
              minRows: 5,
            }"
            disabled
          />
        </el-form-item>
      </div>
    </div>
  </el-form>
  <div
    class="flex z-[999] py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke items-center"
    :class="type === PageType.Details ? 'justify-between' : 'justify-end'"
  >
    <VElementButton
      v-if="props.type === 'xem'"
      icon="arrow-narrow-left"
      text="black"
      @click="router.push({ name: ROUTE_NAME.SMS })"
      bgColor="#EBEBEB"
      label="Quay lại"
    >
    </VElementButton>
    <div>
      <VElementButton
        v-if="props.type !== 'xem'"
        @click="handleCancel"
        :bgColor="color.closeButton"
        label="Hủy"
      >
      </VElementButton>
      <VElementButton
        v-if="getButtonByStatus(BUTTON_TYPE.DELETE)"
        @click="deleteTemplate"
        :bgColor="color.secondary"
        label="Xóa"
      />
      <VElementButton
        v-if="getButtonByStatus(BUTTON_TYPE.PAUSE)"
        @click="popupPauseVisible = true"
        :bgColor="color.stroke"
        label="Tạm dừng"
      />
      <VElementButton
        v-if="getButtonByStatus(BUTTON_TYPE.ACTIVE)"
        @click="handleUpdateTemplate(TEMPLATE_STATUS.ACTIVE)"
        :bgColor="color.tertiary"
        label="Hoạt động"
      />
      <VElementButton
        v-if="getButtonByStatus(BUTTON_TYPE.PENDING)"
        @click="handleUpdateTemplate(TEMPLATE_STATUS.ACTIVE)"
        :bgColor="color.tertiary"
        label="Duyệt"
      />
      <VElementButton
        v-if="getButtonByStatus(BUTTON_TYPE.REJECTED)"
        @click="handleDeny"
        :bgColor="color.secondary"
        label="Từ chối"
      />
      <VElementButton
        v-if="getButtonByStatus(BUTTON_TYPE.UPDATE)"
        @click="router.push({ name: ROUTE_NAME.UPDATE_SMS, params: { id: route.params.id } })"
        :bgColor="color.main"
        label="Cập nhật"
      ></VElementButton>
      <VElementButton
        v-if="getButtonByStatus(BUTTON_TYPE.SAVE)"
        @click="onSubmit"
        :bgColor="color.main"
        label="Lưu"
      ></VElementButton>
    </div>
  </div>
  <PopupCancelConfirm
    v-model:popupVisible="popupVisible"
    @on-close="popupVisible = false"
    @onConfirm="turnBack"
  />

  <PopupConfirm
    :visible="popupDelete"
    @closeForm="popupDelete = false"
    :id="route?.params.id"
    @updateChange="router.push({ name: ROUTE_NAME.SMS })"
    :type="'xoa'"
  >
    Bạn có chắc chắn muốn xóa template ?
  </PopupConfirm>
  <PopupDeny
    @confirm="(event) => confirmDeny(event, TEMPLATE_STATUS.REJECTED)"
    header="Từ chối template SMS"
    @close-form="popupDenyVisible = false"
    :visible="popupDenyVisible"
  />
  <PopupDeny
    @confirm="(event) => confirmDeny(event, TEMPLATE_STATUS.PAUSE)"
    header="Tạm dừng Template SMS"
    @close-form="popupPauseVisible = false"
    :visible="popupPauseVisible"
    label="Lý do tạm dừng"
  />
  <PopupConfirm
    :visible="popupPause"
    @closeForm="popupPause = false"
    @updateChange="
      popupPause = false;
      popupPauseVisible = true;
    "
  >
    <span class="text-center flex item-center"
      >Tạm dừng template khiến dịch vụ gửi tin bị gián đoạn, bạn có chắc chắn?</span
    >
  </PopupConfirm>
  <PopupKeywordBlock
    :visible="popupKeywordBlock"
    @close-form="popupKeywordBlock = false"
    :listBlockedKeyword="filteredBlockedKeywords"
  />
</template>

<style scoped>
.view-height {
  height: calc(100vh - 243px);
  overflow: auto;
}
</style>
