<script setup lang="ts">
import { onMounted, ref, reactive, inject } from 'vue';
import * as yup from 'yup';
import { useForm } from 'vee-validate';
import { useRouter } from 'vue-router';
import { vOnClickOutside } from '@vueuse/components';
import { useScroll } from '@vueuse/core';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import SmsIcon from '@/components/icon/SmsIcon.vue';
import FilterCard from '@/components/base/container/FilterCard.vue';
import PopupConfirm from './popupConfirm.vue';
import { PLACEHOLDER, ROUTE_NAME } from '@/shared';
import VOldTable from '@/components/base/VOldTable.vue';
import { TEMPLATE_STATUS, smsTypeList, statusList, getStatusMeta } from './const.sms';
import PopupDeny from './PopupDeny.vue';

export interface ParamsSearch {
  page_index: number;
  page_size: number;
  keyword?: string;
  label_id?: number;
  status?: number;
  type_id?: number;
  business_id?: number;
  unicode?: boolean;
}

const showFilterCard = ref(false);
const router = useRouter();
const overlayLoading = useOverLayLoadingStore();
const toast = inject('toast') as any;
const api = useApi();
const filterCount = ref('');
const filterRef = ref<InstanceType<typeof FilterCard>>();
const tableRef = ref();
const popupVisible = ref(false);
const popupType = ref();
const container = ref();
const itemChoose = ref();

const { values } = useForm({
  validationSchema: yup.object({
    org_id: yup.mixed(),
  }),
});

const params = ref<ParamsSearch>({
  page_index: 1,
  page_size: 20,
});

const list = ref([]) as any;
const listBrandName = ref<{ value: number; label: string }[]>([]);
const listEnterprise = ref<{ value: number; label: string }[]>([]);

const getList = async () => {
  overlayLoading.toggleLoading(true);
  const { data } = await api.post('business/v1/api/admin/sms-template/search', {
    ...values,
    ...params.value,
  });
  if (data.code === 0) {
    list.value = data?.data;
  }
  overlayLoading.toggleLoading(false);
};

interface DataFilter {
  label?: string;
  type: string;
  valueName: string;
  placeholder?: string;
  defaultValue?: string | number;
  dropdownConfig?: any;
}

const listTemplate = ref([
  { value: 1, label: 'CSKH' },
  { value: 2, label: 'QC' },
]);

const headers = ref([
  {
    name: 'Brandname',
    visible: true,
    pin: false,
  },
  {
    name: 'Doanh nghiệp',
    visible: true,
    pin: false,
  },
  {
    name: 'Tên template',
    visible: true,
    pin: false,
  },
  {
    name: 'ID',
    visible: true,
    pin: false,
  },
  {
    name: 'Template',
    visible: true,
    pin: false,
    align: 'center',
  },

  {
    name: 'Loại template',
    align: 'center',
    visible: true,
    pin: false,
  },
  {
    name: 'Trạng thái',
    align: 'center',
    visible: true,
    pin: false,
  },
]);
const dataFilter: DataFilter[] = reactive([
  {
    label: 'Doanh nghiệp',
    type: 'dropdown',
    valueName: 'business_id',
    placeholder: PLACEHOLDER.SELECT,
    dropdownConfig: {
      option: listEnterprise,
    },
    filterable: true,
  },
  {
    label: 'Brandname',
    type: 'dropdown',
    valueName: 'label_id',
    placeholder: PLACEHOLDER.SELECT,
    dropdownConfig: {
      option: listBrandName,
    },
    filterable: true,
  },
  {
    label: 'Loại template',
    type: 'dropdown',
    valueName: 'type_id',
    placeholder: PLACEHOLDER.SELECT,
    dropdownConfig: {
      option: listTemplate.value ?? [],
    },
  },
  {
    label: ' Trạng thái',
    type: 'dropdown',
    valueName: 'status',
    placeholder: PLACEHOLDER.SELECT,
    dropdownConfig: {
      option: statusList,
    },
  },
]);

const filterData = (value: any) => {
  showFilterCard.value = false;
  params.value.label_id = value.label_id;
  params.value.type_id = value.type_id;
  params.value.status = value.status;
  params.value.business_id = value.business_id;
  if (filterRef.value) {
    if (filterRef.value.count && filterRef.value.count > 0) {
      filterCount.value = `(${filterRef.value.count})`;
    } else {
      filterCount.value = '';
    }
  }
  dataFilter.forEach((item) => {
    if (value.hasOwnProperty(item.valueName)) {
      item.defaultValue = value[item.valueName] ?? '';
    }
  });
  tableRef.value.filterData();
};

const onPageChange = (value: number) => {
  params.value.page_index = value;
  const { y } = useScroll(container);
  y.value = 0;
  getList();
};

const onPerPageChange = (value: number) => {
  params.value.page_size = value;
};

const onFilter = () => {
  if (filterRef.value) {
    filterRef.value.onSubmit();
    if (filterRef.value.count && filterRef.value.count > 0) {
      filterCount.value = `(${filterRef.value.count})`;
    } else {
      filterCount.value = '';
    }
    return;
  }

  tableRef.value.filterData();
};

const getListEnterprise = async () => {
  const { data } = await api.get('business/v1/api/admin/business/list');
  if (data.code === 0) {
    listEnterprise.value = data.data.map((item: any) => ({
      value: item.id,
      label: item.business_name,
    }));
  }
};

onMounted(async () => {
  overlayLoading.toggleLoading(true);
  await getListBrandName();
  await getListEnterprise();
  await getList();

  overlayLoading.toggleLoading(false);
});

const onClickOutsideHandler = (ev: Event) => {
  const target = ev.target as Element;
  if (target.classList.contains('el-select-dropdown__option-item')) return;
  if (target.closest('.el-select-dropdown__option-item')) return;
  if (target.closest('.el-picker-panel__body')) return;
  if (target.closest('.filter')) return;
  showFilterCard.value = false;
};

const deleteTemplate = async (value: any) => {
  itemChoose.value = { ...value };
  popupType.value = 'xoa';
  popupVisible.value = true;
};

const updateChange = () => {
  tableRef.value.filterData();
  popupVisible.value = false;
  getList();
};

const getListBrandName = async (businessId = null) => {
  const { data } = await api.get('business/v1/api/admin/brandname/get-all', {
    params: { businessId },
  });
  if (data.code === 0) {
    listBrandName.value = data?.data?.map((item: any) => ({
      ...item,
      value: item.id,
      label: item.name,
    }));
  }
};

const popupDenyVisible = ref(false);
const order = ref();

const sortTable = (value: any) => {
  if (Object.values(value)[0] === 'desc') {
    order.value = `-${Object.keys(value)[0]}` as string;
  } else {
    order.value = Object.keys(value)[0] as string;
  }
  getList();
};

const openDetail = (item: any) => [
  router.push({ name: ROUTE_NAME.DETAIL_SMS, params: { id: item?.id } }),
];

const confirmDeny = async (reason: string) => {
  const { data } = await api.put(
    `business/v1/api/admin/sms-template/${itemChoose.value?.id}/status`,
    {
      status: TEMPLATE_STATUS.REJECTED,
      reason,
    },
  );
  if (data.code === 0) {
    toast('success', data?.message);
    popupDenyVisible.value = false;
    getList();
  }
};

const handleChangeValue = async (value: { field: string; value: any; name: string }) => {
  if (value.name === 'business_id') {
    filterRef.value?.setFieldValue('label_id', null);
    await getListBrandName(value.value);
  }
};
</script>

<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <SmsIcon />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/admin/sms-template' }">Template SMS</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
  <div class="mx-[10px] mt-[20px]">
    <el-form @submit.prevent class="relative filter-container">
      <div class="flex gap-[12px] item-center">
        <VElementButton
          styleIcon="text-[20px] text-[#fff] mr-[7px]"
          icon="filter-plus"
          class="filter"
          :bgColor="color.tertiary"
          :label="`Bộ lọc ${filterCount}`"
          @click="showFilterCard = !showFilterCard"
        />
        <VElementInput
          name="keyword"
          prefix="search"
          placeholder="Tìm kiếm bằng ID, nội dung template"
          :style="'!w-[550px]'"
          @keyup.enter="onFilter"
        />
        <VElementButton label="Tìm kiếm" :bgColor="color.main" @click="onFilter" />
        <Transition>
          <FilterCard
            v-if="showFilterCard"
            ref="filterRef"
            label-width="w-[120px]"
            v-on-click-outside="onClickOutsideHandler"
            :filterHeight="'h-[200px]'"
            :dataFilter="dataFilter"
            @filter="filterData"
            @on-change="handleChangeValue"
          />
        </Transition>
      </div>
    </el-form>
    <VOldTable
      idTable="sms-template-table"
      ref="tableRef"
      :rows="list.items ?? []"
      :styleHeaders="[
        {
          idx: 0,
          class: 'w-[10%]',
        },
        {
          idx: 1,
          class: 'w-[10%]',
        },
        {
          idx: 2,
          class: 'w-[10%]',
        },
        {
          idx: 3,
          class: 'w-[10%]',
        },
        {
          idx: 4,
          class: 'w-[30%]',
        },
        {
          idx: 5,
          class: 'w-[10%]',
        },
        {
          idx: 6,
          class: 'w-[10%]',
        },
      ]"
      :headers="headers"
      :keySortTable="['read_count']"
      :pagination="{
        totalPage: list?.paging?.total_pages ?? 0,
        total: list?.paging?.total_records ?? 0,
        perPage: params.page_size ?? 0,
      }"
      @sortTable="sortTable"
      @pageChanged="onPageChange"
      @perPageChange="onPerPageChange"
      @row-click="openDetail"
    >
      <template v-slot:items="{ row }">
        <td class="text-primaryText px-2">
          <div class="column-container" :title="row?.label_name">
            {{ row?.label_name }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container" :title="row?.business_name">
            {{ row?.business_name }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container" :title="row?.template_name">
            {{ row?.template_name }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container">
            {{ row?.id }}
          </div>
        </td>
        <td class="text-primaryText py-2">
          <div
            class="w-full break-normal !text-wrap h-max max-h-20 overflow-y-auto rounded-md bg-info p-2"
          >
            {{ row?.content }}
          </div>
        </td>
        <td class="text-primaryText px-2 text-center">
          {{ smsTypeList.find((i) => row?.type_id === i.value)?.label }}
        </td>
        <td class="text-primaryText px-2">
          <div class="text-center relative">
            <Tag
              class="!max-w-[90px]"
              :class="getStatusMeta(row.status).color"
              :value="getStatusMeta(row.status).label"
            />
            <el-tooltip
              v-if="!!row.reason"
              popper-class="!max-w-[250px] "
              placement="top"
              :popper-options="
                row?.reason.length > 50
                  ? {
                      modifiers: [
                        {
                          name: 'offset',
                          options: {
                            offset: [-60, 10],
                          },
                        },
                      ],
                    }
                  : undefined
              "
            >
              <template #content> {{ row?.reason }} </template>
              <i class="text-[22px] absolute mt-1 text-slate-950 ml-2 pi pi-info-circle" />
            </el-tooltip>
          </div>
        </td>
      </template>
      <template v-slot:actions="{ row }">
        <li
          class="item min-w-[100px]"
          @click="router.push({ name: ROUTE_NAME.DETAIL_SMS, params: { id: row.id } })"
        >
          <i class="pi pi-eye text-xs mr-3"></i><span>Xem</span>
        </li>

        <li
          v-if="row.status !== TEMPLATE_STATUS.ACTIVE && !row.existed_in_campaign"
          class="item min-w-[100px]"
          @click="router.push({ name: ROUTE_NAME.UPDATE_SMS, params: { id: row?.id } })"
        >
          <i class="pi pi-pencil text-xs mr-3"></i><span>Cập nhật</span>
        </li>
        <li
          v-if="
            (row.status === TEMPLATE_STATUS.REJECTED || row.status === TEMPLATE_STATUS.PAUSE) &&
            !row.existed_in_campaign
          "
          class="item min-w-[100px]"
          @click="deleteTemplate(row)"
        >
          <i class="pi pi-trash text-xs mr-3"></i><span>Xóa</span>
        </li>
      </template>
    </VOldTable>
  </div>

  <PopupConfirm
    :visible="popupVisible"
    :id="itemChoose?.id"
    :type="'xoa'"
    @updateChange="updateChange"
    @closeForm="popupVisible = false"
    @getList="getList()"
  >
    Bạn có chắc chắn muốn xóa template ?
  </PopupConfirm>

  <PopupDeny
    :visible="popupDenyVisible"
    @confirm="confirmDeny"
    @close-form="popupDenyVisible = false"
  />
</template>

<style lang="scss" scoped>
.item {
  padding: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 131px;
  font-size: 14px;
  font-weight: 400;
  &:hover {
    color: var(--main-color);
  }
}

.disabled-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 10;
  pointer-events: all;
}

.item-detail {
  border: 1px solid #dfe4ea;
  box-shadow: 0px 0px 40px -11px rgba(16, 24, 40, 0.1);
}

.actionBar::-webkit-scrollbar {
  width: 4px; /* Độ rộng của thanh trượt */
}

:deep(.p-tag) {
  width: 120px;
}

.view-height {
  max-height: calc(100vh - 330px);
}

.status-approve {
  background-color: #daf8e6;
  color: #1a8245;
  padding: 1px 10px;
  font-size: 12px;
  border-radius: 30px;
}

.status-pending {
  background-color: #fffbeb;
  color: #d97706;
  padding: 1px 10px;
  font-size: 12px;
  border-radius: 30px;
}

.status-draft {
  background-color: #e5e6e8;
  color: #374151;
  padding: 1px 10px;
  font-size: 12px;
  border-radius: 30px;
}

.status-decline {
  background-color: #feebeb;
  color: #e10e0e;
  padding: 1px 10px;
  font-size: 12px;
  border-radius: 30px;
}

.status-lock {
  background-color: #637381;
  color: #ffffff;
  padding: 1px 10px;
  font-size: 12px;
  border-radius: 30px;
}

.list-sms {
  &.apply-last-style {
    & .item-detail {
      &:last-child,
      &:nth-last-child(2) {
        .active-container {
          bottom: 20px;
          top: unset;
        }
      }
    }
  }
}
</style>
