<script setup lang="ts">
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { inject, onMounted, ref } from 'vue';

const props = defineProps<{
  id?: any;
  visible: boolean;
  type?: string;
}>();

const emit = defineEmits(['closeForm', 'getList', 'updateChange']);

const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const api = useApi();
const question = ref();

onMounted(() => {
  if (props.type === 'xoa') {
    question.value = 'Bạn có chắc chắn muốn xoá mẫu SMS?';
  }
});

const handleDelete = async () => {
  overlayLoading.toggleLoading(true);
  try {
    const { data } = await api.delete(`business/v1/api/admin/sms-template/${props.id}`);
    if (data.code === 0) {
      toast('success', data.message);
      emit('getList');
      emit('closeForm');
      overlayLoading.toggleLoading(false);
    }
  } catch {
    emit('closeForm');
  }
  // overlayLoading.toggleLoading(false);
};
const handleClick = () => {
  if (props.type === 'xoa') {
    handleDelete();
  } else {
    emit('updateChange');
  }
};
</script>

<template>
  <VDialog
    ref="refDialog"
    :visible="visible"
    @update:visible="emit('closeForm')"
    :draggable="false"
    :pt="{
      content: { class: 'mb-[60px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: '!pl-[210px] !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
      },
    }"
    modal
    header="Xác nhận"
    :class="'h-285px'"
    :style="{
      width: '500px',
      backgroundColor: '#fff',
      maxHeight: '100%',
    }"
  >
    <div class="pt-[15px]">
      <div class="flex flex-col mt-[20px] items-center" id="changePassword-old-password-group">
        <img src="@/assets/images/warning.png" alt="warning" class="w-[65px]" />
        <div class="font-normal text-[19px] mt-[15px]">
          <slot>{{ question }}</slot>
        </div>
      </div>
    </div>
    <div class="save-container flex justify-center items-center mt-5">
      <VElementButton
        :bgColor="color.closeButton"
        @click="() => emit('closeForm')"
        label="Hủy"
      ></VElementButton>
      <VElementButton :bgColor="color.main" @click="handleClick" label="Đồng ý"></VElementButton>
    </div>
  </VDialog>
</template>
