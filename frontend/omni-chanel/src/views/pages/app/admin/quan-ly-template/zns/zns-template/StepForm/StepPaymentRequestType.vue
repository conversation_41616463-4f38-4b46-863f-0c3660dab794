<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import * as yup from 'yup';
import { useForm, useFieldArray, useField } from 'vee-validate';
import { useRouter } from 'vue-router';
import { watchDebounced } from '@vueuse/core';
import { PLACEHOLDER, ROUTE_NAME, REGEX_CHECK_PARAMETER, REGEX } from '@/shared';
import { useZNSStep } from '@/store/useZNSStep';
import { FileSize, FileSizeLabel, FileUploadApiType, PageType } from '@/enums/common';
import {
  ZnsPageType,
  ZnsTextConfigLogoType,
  ZnsType,
  ZnsTypeImg,
  ZnsButtonType,
  ZnsConfigImgType,
  ZnsConfigAccountType,
  ZnsImgType,
  ZnsConfigImg,
} from '@/enums/zns';
import { ERROR_MESSAGE } from '@/constants/templateText';
import RemoveIcon from '@/components/icon/RemoveIcon.vue';
import AddIcon from '@/components/icon/AddIcon.vue';
import VUploadImageNew from '@/components/base/OtherComponent/VUploadImageNew.vue';
import PaymentRequestType from '../info/PaymentRequestType.vue';
import type { IRow, ISetting } from '../index.type';
import {
  getString,
  removeDuplicates,
  isOnlyParameter,
  getNotOnlyParameter,
  getParameterTypeLabel,
} from '../index.utils';
import {
  ACCEPT_FILE_TYPE_LOGO,
  BUTTON_TYPES,
  GUIDE_TEXT,
  LOGO_PX_HEIGHT,
  LOGO_PX_WIDTH,
  REGEX_FILE_TYPE_LOGO,
  PARAMETER_TYPES,
  DEBOUNCE_TIME,
  REGEX_PARAMETER,
  BANK_TYPES,
  INITIAL_VALUE_PAYMENT_REQUEST,
  ACCEPT_FILE_MULTIPLE_TYPE_PAYMENT,
  MAX_LENGTH_TITLE_PAYMENT,
  TOOLTIP_PAYMENT,
  MAX_LENGTH_ACCOUNT_NAME_PAYMENT,
  MAX_LENGTH_ACCOUNT_NUMBER_PAYMENT,
  MAX_LENGTH_PRICE_PAYMENT,
  MAX_LENGTH_CONTENT_PAYMENT,
  MIN_PRICE_PAYMENT,
  MAX_PRICE_PAYMENT,
  MAX_LENGTH_BUTTON_LINK,
  MAX_LENGTH_BUTTON_PHONE,
} from '../index.constants';
import { contentDefaultButton, getMaxLengthParameterZns } from '../index.utils';
import ZnsWrapper from '../info/ZnsWrapper.vue';
import VUploadFileServerZns from '@/components/base/common/VUploadFileServerZns.vue';
import HintUploadImg from '../info/HintUploadImg.vue';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { enhancedFuzzyMatch } from '@/utils/fuzzySearch';
import { getPlaceholder } from '@/utils';
import VTooltip from '@/components/base/ElementComponent/VTooltip.vue';

const props = defineProps<{
  id?: number;
  type: PageType;
}>();

const router = useRouter();
const znsStore = useZNSStep();

const previewLogo = ref<{
  light: string;
  dark: string;
}>({
  light: '',
  dark: '',
});
const checkValidateSubmit = ref<boolean[]>([]);
const arrayParamTag = ref<string[]>([]);
const arrayParamDataNew = ref<string[]>([]);
const arrayParamCurrentData = ref<ISetting[]>([]);
const resetSubLink = ref(true);
const resetMainLink = ref(true);
const accountTypeRadioValue = ref<ZnsConfigAccountType>();
const fileUploadLogoLightRef = ref();
const fileUploadLogoDarkRef = ref();
const fileUploadServerRef = ref();
const bankOptions = ref([...BANK_TYPES]);

const valueTexts = computed(() => [
  values.title,
  ...values.body,
  ...values.table.map((item: any) => item.param),
  values.bank_content?.account_name,
  values.bank_content?.account_number,
  values.bank_content?.price,
  values.bank_content?.content,
]);

const technicalSchema = yup.object({
  default_data_field: yup
    .string()
    .required(ERROR_MESSAGE.REQUIRED_FIELD('Nội dung mẫu'))
    .trim()
    .test('check-maxlength', 'error', function (value, context) {
      const maxlength = context.options.context?.maxLength;
      if (value.length > maxlength) {
        return this.createError({
          message: ERROR_MESSAGE.MAX_LENGTH_FIELD('Nội dung mẫu', maxlength),
        });
      }
      return true;
    })
    .test(
      'submit',
      '',
      () => !checkValidateSubmit.value.includes(false) || checkValidateSubmit.value.length === 0,
    ),
});

const tableRowSchema = yup.object({
  name: yup
    .string()
    .notRequired()
    .min(3, ERROR_MESSAGE.MIN_LENGTH_FIELD_ZNS(3))
    .trim()
    .test('check-string', ERROR_MESSAGE.INVALID_NO_PARAM_ZNS, (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      if (matches) {
        return false;
      }
      return true;
    }),
  param: yup
    .string()
    .notRequired()
    .min(3, ERROR_MESSAGE.MIN_LENGTH_FIELD_ZNS(3))
    .trim()
    .test('check-string', ERROR_MESSAGE.INVALID_FIELD('Tên tham số'), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      let isValid = true;
      if (matches) {
        matches?.forEach((item) => {
          if (!REGEX_CHECK_PARAMETER.test(item.slice(1, -1)) || getString(item) === null) {
            isValid = false;
            return false;
          }
          return true;
        });
      }
      if (isValid) {
        return true;
      }
      return false;
    })
    .test('check-total', ERROR_MESSAGE.MAX_LENGTH_PARAMETER_ZNS('Nội dung', 1), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      if (matches) {
        if (removeDuplicates(matches).length > 1) {
          return false;
        }
      }
      return true;
    }),
});

const logoSchema = {
  fileLogoLight: yup.mixed().required(ERROR_MESSAGE.REQUIRED_ZNS_FILE),
  fileLogoDark: yup.mixed().required(ERROR_MESSAGE.REQUIRED_ZNS_FILE),
  fileSizeLogoLight: yup
    .number()
    .max(FileSize._15Mb, ERROR_MESSAGE.LIMIT_SIZE_FILE(FileSizeLabel._15Mb))
    .notRequired(),
  fileSizeLogoDark: yup
    .number()
    .max(FileSize._15Mb, ERROR_MESSAGE.LIMIT_SIZE_FILE(FileSizeLabel._15Mb))
    .notRequired(),
  fileTypeLogoLight: yup
    .string()
    .matches(
      REGEX_FILE_TYPE_LOGO,
      ERROR_MESSAGE.INVALID_FILE_ZNS_LOGO(LOGO_PX_WIDTH, LOGO_PX_HEIGHT),
    )
    .notRequired(),
  fileTypeLogoDark: yup
    .string()
    .matches(
      REGEX_FILE_TYPE_LOGO,
      ERROR_MESSAGE.INVALID_FILE_ZNS_LOGO(LOGO_PX_WIDTH, LOGO_PX_HEIGHT),
    )
    .notRequired(),
  fileWidthLogoLight: yup
    .number()
    .equals([LOGO_PX_WIDTH], ERROR_MESSAGE.INVALID_FILE_ZNS_LOGO(LOGO_PX_WIDTH, LOGO_PX_HEIGHT))
    .notRequired(),
  fileHeightLogoLight: yup
    .number()
    .equals([LOGO_PX_HEIGHT], ERROR_MESSAGE.INVALID_FILE_ZNS_LOGO(LOGO_PX_WIDTH, LOGO_PX_HEIGHT))
    .notRequired(),
  fileWidthLogoDark: yup
    .number()
    .equals([LOGO_PX_WIDTH], ERROR_MESSAGE.INVALID_FILE_ZNS_LOGO(LOGO_PX_WIDTH, LOGO_PX_HEIGHT))
    .notRequired(),
  fileHeightLogoDark: yup
    .number()
    .equals([LOGO_PX_HEIGHT], ERROR_MESSAGE.INVALID_FILE_ZNS_LOGO(LOGO_PX_WIDTH, LOGO_PX_HEIGHT))
    .notRequired(),
};

const bankSchema = yup.object({
  bank_name: yup.mixed().required(ERROR_MESSAGE.REQUIRED_SELECT('Ngân hàng')),
  account_name: yup
    .string()
    .required(ERROR_MESSAGE.REQUIRED_FIELD('Tên tài khoản'))
    .trim()
    .test('check-total', ERROR_MESSAGE.MAX_LENGTH_PARAMETER_ZNS('Tên tài khoản', 1), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      if (matches) {
        if (removeDuplicates(matches).length > 1) {
          return false;
        }
      }
      return true;
    }),
  account_number: yup
    .string()
    .required(ERROR_MESSAGE.REQUIRED_FIELD('Số tài khoản'))
    .trim()
    .test('check-total', ERROR_MESSAGE.MAX_LENGTH_PARAMETER_ZNS('Số tài khoản', 1), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      if (matches) {
        if (removeDuplicates(matches).length > 1) {
          return false;
        }
      }
      return true;
    }),
  price: yup
    .string()
    .required(ERROR_MESSAGE.REQUIRED_FIELD('Số tiền (VND)'))
    .trim()
    .test(
      'check-price-value',
      ERROR_MESSAGE.ZNS.INVALID_PRICE(MIN_PRICE_PAYMENT, MAX_PRICE_PAYMENT),
      (value) => {
        const isOnlyParams = isOnlyParameter(value);

        if (!isOnlyParams && isNaN(Number(value))) {
          return false;
        }
        if (!isNaN(Number(value))) {
          if (Number(value) < MIN_PRICE_PAYMENT || Number(value) > MAX_PRICE_PAYMENT) {
            return false;
          }
        }
        return true;
      },
    )
    .test('check-string', ERROR_MESSAGE.INVALID_FIELD('Tên tham số'), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      let isValid = true;
      if (matches) {
        matches?.forEach((item) => {
          if (!REGEX_CHECK_PARAMETER.test(item.slice(1, -1)) || getString(item) === null) {
            isValid = false;
            return false;
          }
          return true;
        });
      }
      if (isValid) {
        return true;
      }
      return false;
    })
    .test('check-total', ERROR_MESSAGE.MAX_LENGTH_PARAMETER_ZNS('Số tiền (VND)', 1), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      if (matches) {
        if (removeDuplicates(matches).length > 1) {
          return false;
        }
      }
      return true;
    }),
  content: yup
    .string()
    .trim()
    .notRequired()
    .test('check-string', ERROR_MESSAGE.INVALID_FIELD('Tên tham số'), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      let isValid = true;
      if (matches) {
        matches?.forEach((item) => {
          if (!REGEX_CHECK_PARAMETER.test(item.slice(1, -1)) || getString(item) === null) {
            isValid = false;
            return false;
          }
          return true;
        });
      }
      if (isValid) {
        return true;
      }
      return false;
    })
    .test('check-special-character', ERROR_MESSAGE.ZNS.INVALID_SPECIAL_CHARACTER, (value) => {
      if (value) {
        const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
        if (matches) {
          const strNotOnlyParams = getNotOnlyParameter(value);

          if (strNotOnlyParams) {
            if (strNotOnlyParams.match(REGEX.SPECIAL_CHARACTER_ZNS)) {
              return false;
            }
          }
        } else {
          if (value.match(REGEX.SPECIAL_CHARACTER_ZNS)) {
            return false;
          }
        }
        return true;
      }
      return true;
    })
    .test(
      'check-total',
      ERROR_MESSAGE.MAX_LENGTH_PARAMETER_ZNS('Nội dung chuyển khoản', 1),
      (value) => {
        const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
        if (matches) {
          if (removeDuplicates(matches).length > 1) {
            return false;
          }
        }
        return true;
      },
    ),
});

const filesSchema = {
  fileUploadBank: yup.lazy(() => {
    return yup.string().required(ERROR_MESSAGE.REQUIRED_ZNS_FILE);
  }),
};

const baseSchema = {
  title: yup
    .string()
    .required(ERROR_MESSAGE.REQUIRED_FIELD('Tiêu đề'))
    .trim()
    .test('check-string', 'Tên tham số không hợp lệ', (value) => {
      const matches: string[] | null = value.match(REGEX_PARAMETER);
      let isValid = true;
      if (matches) {
        matches?.forEach((item) => {
          if (!REGEX_CHECK_PARAMETER.test(item.slice(1, -1)) || getString(item) === null) {
            isValid = false;
            return false;
          }
          return true;
        });
      }
      if (isValid) {
        return true;
      }
      return false;
    })
    .test('check-total', 'Tiêu đề tối đa 2 tham số', (value) => {
      const matches: string[] | null = value.match(REGEX_PARAMETER);
      if (matches) {
        if (removeDuplicates(matches).length > 2) {
          return false;
        }
      }
      return true;
    }),
  body: yup.array().of(
    yup
      .string()
      .trim()
      .test('check-string', ERROR_MESSAGE.INVALID_FIELD('Tên tham số'), (value) => {
        const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
        let isValid = true;
        if (matches) {
          matches?.forEach((item) => {
            if (!REGEX_CHECK_PARAMETER.test(item.slice(1, -1)) || getString(item) === null) {
              isValid = false;
              return false;
            }
            return true;
          });
        }
        if (isValid) {
          return true;
        }
        return false;
      }),
  ),
  technical_config: yup.array().of(technicalSchema),
  table: yup.array().of(tableRowSchema),
  bank_content: bankSchema,
  mainContent: yup
    .string()
    .test('mainContent', '', function (value) {
      if (znsStore.isAddMainButton) {
        if (!value) {
          return this.createError({
            message: ERROR_MESSAGE.REQUIRED_FIELD('Nội dung button'),
          });
        }
        if (value.length < 5) {
          return this.createError({
            message: ERROR_MESSAGE.MIN_LENGTH_FIELD_ZNS(5),
          });
        }
      }
      return true;
    })
    .trim(),
  subContent: yup
    .string()
    .test('subContent', '', function (value) {
      if (znsStore.isAddSubButton) {
        if (!value) {
          return this.createError({
            message: ERROR_MESSAGE.REQUIRED_FIELD('Nội dung button'),
          });
        }
        if (value.length < 5) {
          return this.createError({
            message: ERROR_MESSAGE.MIN_LENGTH_FIELD_ZNS(5),
          });
        }
      }
      return true;
    })
    .trim(),
  subType: yup.number(),
  mainType: yup.number(),
  subLink: yup.string().when('subType', (subType, schema) => {
    if (subType[0] !== ZnsButtonType.Call && znsStore.isAddSubButton) {
      return schema.required('Đường dẫn liên kết không được để trống').trim();
    }
    if (subType[0] === ZnsButtonType.Call && znsStore.isAddSubButton) {
      return schema.required('Số điện thoại không được để trống').trim();
    }
    return schema;
  }),
  mainLink: yup.string().when('mainType', (mainType, schema) => {
    if (mainType[0] !== ZnsButtonType.Call && znsStore.isAddMainButton) {
      return schema.required('Đường dẫn liên kết không được để trống').trim();
    }
    if (mainType[0] === ZnsButtonType.Call && znsStore.isAddMainButton) {
      return schema.required('Số điện thoại không được để trống').trim();
    }
    return schema;
  }),
};

const createValidationSchema = (accountType: ZnsConfigAccountType) => {
  if (accountType === ZnsConfigAccountType.No) {
    return yup.object({ ...baseSchema, ...logoSchema, ...filesSchema });
  }
  return yup.object({ ...baseSchema, ...logoSchema });
};

const validationSchema = ref(
  createValidationSchema(accountTypeRadioValue.value as ZnsConfigAccountType),
);

const { values, handleSubmit, validateField, errors, setFieldValue, validate } = useForm<any>({
  validationSchema,
  initialValues: INITIAL_VALUE_PAYMENT_REQUEST,
});

const {
  remove: removeBody,
  replace: replaceBody,
  push: pushBody,
  fields: body,
} = useFieldArray('body');

const {
  remove: removeSetting,
  push: pushSetting,
  fields: technical_config,
} = useFieldArray<ISetting[]>('technical_config');

const {
  remove: removeRow,
  replace: replaceTable,
  push: pushRow,
  fields: table,
} = useFieldArray<IRow[]>('table');

const { value: bank_content, setValue: setBankContent } = useField<any>('bank_content');

const handleUploadLogo = (fileData: any, typeLogo: ZnsTextConfigLogoType) => {
  if (fileData) {
    // Lưu URL của ảnh để hiển thị
    if (typeLogo === ZnsTextConfigLogoType.Light) {
      previewLogo.value.light = fileData.link_url;
      setFieldValue('fileLogoLight' as any, fileData.link_url);
    } else if (typeLogo === ZnsTextConfigLogoType.Dark) {
      previewLogo.value.dark = fileData.link_url;
      setFieldValue('fileLogoDark' as any, fileData.link_url);
    }
  }
};

const watchFileWidthLogo = (
  newValue: { width: number; height: number },
  type: ZnsConfigImgType,
) => {
  if (newValue.width !== null) {
    if (type === ZnsConfigImgType.LogoLight) {
      setFieldValue(`fileWidthLogoLight` as any, newValue.width);
      setFieldValue(`fileHeightLogoLight` as any, newValue.height);
    } else {
      setFieldValue(`fileWidthLogoDark` as any, newValue.width);
      setFieldValue(`fileHeightLogoDark` as any, newValue.height);
    }
  }
};

const beforeUploadLogo = (newValue: { width: number; height: number }, type: ZnsConfigImgType) => {
  watchFileWidthLogo(newValue, type);
};

const handleRemoveLogo = (target: ZnsTextConfigLogoType) => {
  if (target === ZnsTextConfigLogoType.Light) {
    previewLogo.value.light = '';
    setFieldValue('fileLogoLight', undefined);
  } else if (target === ZnsTextConfigLogoType.Dark) {
    previewLogo.value.dark = '';
    setFieldValue('fileLogoDark', undefined);
  }
};

const checkTemplate = function (index: number, max: number, isCheckAll: boolean = false) {
  if (isCheckAll) {
    technicalSchema
      .validateAt(
        'default_data_field',
        { default_data_field: values.technical_config[index].default_data_field },
        { context: { maxLength: max } },
      )
      .then(() => {
        checkValidateSubmit.value.push(true);
        (technical_config.value[index].value as any).error = '';
      })
      .catch((err) => {
        if (err.message !== 'Nội dung mẫu không được để trống') {
          checkValidateSubmit.value.push(false);
        }
        (technical_config.value[index].value as any).error = err.message;
      });
  } else {
    setTimeout(() => {
      technicalSchema
        .validateAt(
          'default_data_field',
          { default_data_field: values.technical_config[index].default_data_field },
          { context: { maxLength: max } },
        )
        .then(() => {
          (technical_config.value[index].value as any).error = '';
        })
        .catch((err) => {
          (technical_config.value[index].value as any).error = err.message;
        });
    }, 10);
  }
};

const getParamTag = (arrStrings: string[]) => {
  try {
    if (values?.technical_config) {
      arrayParamCurrentData.value = [...values.technical_config];
    }
    arrayParamDataNew.value = [];
    arrStrings.forEach((str) => {
      const matches: string[] | null = str?.match(REGEX_PARAMETER);
      if (matches) {
        arrayParamDataNew.value.push(...matches);
        matches.forEach((item) => {
          if (REGEX_CHECK_PARAMETER.test(item.slice(1, -1))) {
            if (!arrayParamTag.value.includes(item)) {
              arrayParamTag.value.push(item);
            }
          }
        });
      }
    });
    arrayParamTag.value.forEach((item) => {
      const index = arrayParamCurrentData.value.findIndex(
        (element) => element.parameter_name === item,
      );
      if (index === -1) {
        pushSetting({
          parameter_name: item,
          zalo_data_field: 1,
          default_data_field: '',
          error: '',
        } as any);
        arrayParamCurrentData.value = [...values.technical_config];
        const indexRemove = arrayParamCurrentData.value
          .filter((itemRemove) => !arrayParamDataNew.value.includes(itemRemove.parameter_name))
          .map((itemRemove) => arrayParamCurrentData.value.indexOf(itemRemove));
        indexRemove.forEach((element) => {
          if (arrayParamCurrentData.value.length === 1) {
            removeSetting(0);
          } else {
            removeSetting(element);
          }
          arrayParamCurrentData.value = [...values.technical_config];
        });
      } else {
        const indexRemove = arrayParamCurrentData.value
          .filter((itemRemove) => !arrayParamDataNew.value.includes(itemRemove.parameter_name))
          .map((itemRemove) => arrayParamCurrentData.value.indexOf(itemRemove));
        indexRemove.forEach((element) => {
          if (arrayParamCurrentData.value.length === 1) {
            removeSetting(0);
          } else {
            removeSetting(element);
          }
          arrayParamCurrentData.value = [...values.technical_config];
        });
      }
    });
  } catch (err: any) {
    console.error(err);
  }
};

const mainTypeChange = () => {
  setFieldValue('mainContent', contentDefaultButton(values.mainType));
  resetMainLink.value = false;
  setTimeout(() => {
    resetMainLink.value = true;
    validateField('mainContent');
  }, 10);
};

const subTypeChange = () => {
  setFieldValue('subContent', contentDefaultButton(values.subType));
  resetSubLink.value = false;
  setTimeout(() => {
    resetSubLink.value = true;
    validateField('subContent');
  }, 10);
};

const mainPhoneChange = (value: any) => {
  setFieldValue('mainLink', value);
};

const subPhoneChange = (value: any) => {
  setFieldValue('subLink', value);
};

const handleChangeAccountType = (value: any) => {
  accountTypeRadioValue.value = value;
  setFieldValue('bank_content.is_business_account', value);
};

// Filter method for el-select-v2 - modifies bankOptions directly
const bankFilterMethod = (query: string) => {
  if (!query || query.trim() === '') {
    bankOptions.value = [...BANK_TYPES];
    return;
  }

  const filtered = BANK_TYPES.filter((option) =>
    enhancedFuzzyMatch(query.trim(), option.label || ''),
  );

  bankOptions.value = filtered;
};

const handleBankFocus = () => {
  bankOptions.value = [...BANK_TYPES];
};

const saveValues = () => {
  znsStore.setValueStep2({
    ...values,
    bank_content: {
      ...bank_content.value,
      account_type: accountTypeRadioValue.value,
      file: fileUploadServerRef.value?.listFileUpload.map((item: any) => ({
        ...item,
        template_type_id: ZnsTypeImg.Bank,
      })),
    },
    previewLogo: previewLogo.value,
  });
};

const setValue = () => {
  if (znsStore.step2Data) {
    setFieldValue('title', znsStore.step2Data?.title);
    if (znsStore.step2Data?.body) {
      replaceBody(znsStore.step2Data?.body);
    }
    if (znsStore.step2Data?.table) {
      replaceTable(znsStore.step2Data?.table);
    }
    if (znsStore.step2Data?.bank_content) {
      setBankContent(znsStore.step2Data.bank_content);
      accountTypeRadioValue.value = znsStore.step2Data.bank_content?.account_type || 0;
    }
    setFieldValue('technical_config', znsStore.step2Data?.technical_config);
    if (znsStore.step2Data?.mainLink) {
      setFieldValue('mainLink', znsStore.step2Data?.mainLink);
    }
    if (znsStore.step2Data?.subLink) {
      setFieldValue('subLink', znsStore.step2Data?.subLink);
    }
    if (znsStore.step2Data?.mainType) {
      setFieldValue('mainType', znsStore.step2Data?.mainType);
    }
    if (znsStore.step2Data?.subType) {
      setFieldValue('subType', znsStore.step2Data?.subType);
    }
    if (znsStore.step2Data?.subContent) {
      setFieldValue('subContent', znsStore.step2Data?.subContent);
    }
    if (znsStore.step2Data?.mainContent) {
      setFieldValue('mainContent', znsStore.step2Data?.mainContent);
    }

    // Restore logo
    if (znsStore.step2Data?.previewLogo && znsStore.step2Data?.file_type === ZnsConfigImg.Logo) {
      previewLogo.value = znsStore.step2Data.previewLogo;

      setTimeout(() => {
        if (fileUploadLogoLightRef.value && previewLogo.value.light) {
          fileUploadLogoLightRef.value.imagePreviewUrl = previewLogo.value.light;
          fileUploadLogoLightRef.value.rawFile = znsStore.step2Data.file.find(
            (item: any) => item.template_type_id === ZnsTypeImg.LogoLight,
          );
        }
        if (fileUploadLogoDarkRef.value && previewLogo.value.dark) {
          fileUploadLogoDarkRef.value.imagePreviewUrl = previewLogo.value.dark;
          fileUploadLogoDarkRef.value.rawFile = znsStore.step2Data.file.find(
            (item: any) => item.template_type_id === ZnsTypeImg.LogoDark,
          );
        }

        if (fileUploadLogoLightRef.value.rawFile) {
          setFieldValue('fileLogoLight' as any, fileUploadLogoLightRef.value.rawFile);
        }
        if (fileUploadLogoDarkRef.value.rawFile) {
          setFieldValue('fileLogoDark' as any, fileUploadLogoDarkRef.value.rawFile);
        }
        if (previewLogo.value.light) {
          setFieldValue('logoLightPath' as any, previewLogo.value.light);
        }
        if (previewLogo.value.dark) {
          setFieldValue('logoDarkPath' as any, previewLogo.value.dark);
        }
      }, 100);
    }

    // Restore bank info account type
    if (
      znsStore.step2Data?.bank_content?.account_type ||
      znsStore.step2Data?.bank_content?.account_type === ZnsConfigAccountType.No
    ) {
      setTimeout(() => {
        accountTypeRadioValue.value = znsStore.step2Data.bank_content?.account_type;
        setFieldValue(
          'bank_content.is_business_account',
          znsStore.step2Data.bank_content?.is_business_account,
        );

        // Restore bank_content info file
        if (znsStore.step2Data?.bank_content?.file && fileUploadServerRef.value) {
          fileUploadServerRef.value.listFileUpload = znsStore.step2Data.bank_content.file;
          setFieldValue('fileUploadBank', 'true');
        }
      }, 100);
    }
  }
};

const handleSubmitData = async (value: any, approved: boolean = false) => {
  const overlayLoading = useOverLayLoadingStore();
  overlayLoading.toggleLoading(true);
  try {
    let fileUploads: any;

    if (approved) {
      fileUploads = znsStore.step2Data.file;
    } else {
      fileUploads = [
        {
          ...fileUploadLogoLightRef.value?.rawFile,
          template_type_id: ZnsImgType.LogoLight,
        },
        {
          ...fileUploadLogoDarkRef.value?.rawFile,
          template_type_id: ZnsImgType.LogoDark,
        },
      ];
    }

    znsStore.setValueStep2({
      ...value,
      file: fileUploads,
      bank_content: {
        ...bank_content.value,
        account_type: accountTypeRadioValue.value,
        file: fileUploadServerRef.value?.listFileUpload.map((item: any) => ({
          ...item,
          template_type_id: ZnsImgType.Bank,
        })),
      },
    });

    const id = await znsStore.uploadZNS();

    if (id) {
      if (props.type !== PageType.Details) {
        overlayLoading.toggleLoading(false);
        router.push({
          name: ROUTE_NAME.DETAILS_ZNS,
          params: {
            id,
          },
        });
      }
    }
    overlayLoading.toggleLoading(false);
  } catch (error) {
    overlayLoading.toggleLoading(false);
    console.error(error);
  }
};

const onSubmitAll = handleSubmit(async (value) => {
  handleSubmitData(value);
});

const onSubmit = async () => {
  checkValidateSubmit.value = [];
  for (let i = 0; i < technical_config.value.length; i += 1) {
    checkTemplate(
      i,
      getMaxLengthParameterZns((technical_config.value[i].value as any).zalo_data_field),
      true,
    );
  }

  onSubmitAll();
};

//#endregion

//#region Watcher
watch(accountTypeRadioValue, (newValue: any) => {
  validationSchema.value = createValidationSchema(newValue as ZnsConfigAccountType);
  // validate();
});

watch(
  () => znsStore.isAddMainButton,
  () => {
    setFieldValue('mainType', ZnsButtonType.GoToEnterprisePage);
    setFieldValue('mainContent', 'Tìm hiểu thêm');
  },
);

watch(
  () => znsStore.isAddSubButton,
  () => {
    setFieldValue('subType', ZnsButtonType.GoToEnterprisePage);
    setFieldValue('subContent', 'Tìm hiểu thêm');
  },
);
//#endregion

watch(
  () => znsStore.active,
  (newValue: number) => {
    if (newValue === 2 && znsStore.znsType === ZnsType.Payment) {
      if (
        znsStore.step2Data?.bank_content?.account_type ||
        znsStore.step2Data?.bank_content?.account_type === ZnsConfigAccountType.No
      ) {
        accountTypeRadioValue.value = znsStore.step2Data.bank_content?.account_type;
        setFieldValue(
          'bank_content.is_business_account',
          znsStore.step2Data.bank_content?.is_business_account,
        );
      } else {
        accountTypeRadioValue.value = ZnsConfigAccountType.Yes;
        setFieldValue('bank_content.is_business_account', ZnsConfigAccountType.Yes);
      }
      setValue();
      getParamTag(valueTexts.value);
    }
  },
  {
    immediate: true,
  },
);

watch(
  () => values,
  (newValue: any) => {
    if (znsStore.active === 2 && znsStore.znsType === ZnsType.Payment) {
      znsStore.setValueStep2({
        ...newValue,
      });
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

watchDebounced(
  valueTexts,
  () => {
    getParamTag(valueTexts.value);
  },
  { debounce: DEBOUNCE_TIME },
);

defineExpose({
  setValue,
  onSubmit,
  saveValues,
});
</script>

<template>
  <!-- Main container for the entire form -->
  <div class="w-[100%] px-[4%] flex justify-between pt-[40px] mb-[70px] text-sm">
    <!-- Left side - Form content -->
    <div class="pr-14 w-full">
      <div class="">
        <div class="text-[14px]">
          <span class="font-semibold">Lưu ý:</span>
          {{ GUIDE_TEXT }}
        </div>

        <!-- Main form section -->
        <el-form class="pb-[15px]" label-position="top">
          <!-- Logo upload section -->
          <div class="my-4">
            <div class="flex gap-5 my-2">
              <!-- Logo upload fields -->
              <div class="w-2/3">
                <div>
                  <label for="" class="text-sm inline-block mb-[7px] text-[#6b7280] font-semibold"
                    >Giao diện sáng <span class="text-red-500 ml-0.5">*</span></label
                  >
                  <VUploadImageNew
                    id="zns-otp-light"
                    ref="fileUploadLogoLightRef"
                    fileSize="fileSizeLogoLight"
                    fileType="fileTypeLogoLight"
                    file="fileLogoLight"
                    fileWidth="fileWidthLogoLight"
                    fileHeight="fileHeightLogoLight"
                    :requiredWidth="LOGO_PX_WIDTH"
                    :requiredHeight="LOGO_PX_HEIGHT"
                    :sizeToValidate="FileSize._15Mb"
                    :typeToValidate="ACCEPT_FILE_TYPE_LOGO"
                    :disabled="props.type === PageType.Details"
                    @beforeUpload="
                      (newValue: any) => beforeUploadLogo(newValue, ZnsConfigImgType.LogoLight)
                    "
                    @uploadFile="
                      (files: any) => handleUploadLogo(files, ZnsTextConfigLogoType.Light)
                    "
                    @removeFile="handleRemoveLogo(ZnsTextConfigLogoType.Light)"
                  />
                </div>
                <div class="">
                  <label for="" class="text-sm inline-block mb-[7px] text-[#6b7280] font-semibold"
                    >Giao diện tối <span class="text-red-500 ml-0.5">*</span></label
                  >
                  <VUploadImageNew
                    id="zns-logo-dark"
                    isDark
                    ref="fileUploadLogoDarkRef"
                    fileSize="fileSizeLogoDark"
                    fileType="fileTypeLogoDark"
                    file="fileLogoDark"
                    fileWidth="fileWidthLogoDark"
                    fileHeight="fileHeightLogoDark"
                    :requiredWidth="LOGO_PX_WIDTH"
                    :requiredHeight="LOGO_PX_HEIGHT"
                    :sizeToValidate="FileSize._15Mb"
                    :typeToValidate="ACCEPT_FILE_TYPE_LOGO"
                    :disabled="props.type === PageType.Details"
                    @beforeUpload="
                      (newValue: any) => beforeUploadLogo(newValue, ZnsConfigImgType.LogoDark)
                    "
                    @uploadFile="
                      (files: any) => handleUploadLogo(files, ZnsTextConfigLogoType.Dark)
                    "
                    @removeFile="handleRemoveLogo(ZnsTextConfigLogoType.Dark)"
                  />
                </div>
              </div>
              <!-- Design suggestion section -->
              <HintUploadImg />
            </div>
          </div>

          <!-- Main title section -->
          <VElementInput
            name="title"
            size="default"
            :style="'w-[100%]'"
            :required="true"
            :label="'Tiêu đề'"
            :maxlength="MAX_LENGTH_TITLE_PAYMENT"
            :disabled="props.type === PageType.Details"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
          />

          <!-- Main content section -->
          <el-form-item
            v-for="(noiDung, idx) in body"
            v-bind="{
              error: (errors as any)[`body[${idx}]`] || null,
            }"
            class="mt-[20px] relative"
            :key="idx"
            :label="idx === 0 ? 'Nội dung' : ''"
            :disabled="props.type === PageType.Details"
          >
            <el-input
              v-model="noiDung.value"
              show-word-limit
              size="default"
              type="textarea"
              :maxlength="400"
              :style="'w-[100%]'"
              :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
              :disabled="props.type === PageType.Details"
            />
            <!-- Add/Remove content buttons -->
            <div class="absolute justify-start flex gap-0.5 pl-1 left-full top-[28px]">
              <RemoveIcon
                v-if="body.length > 1 && props.type !== PageType.Details"
                @click="removeBody(idx)"
              />
              <AddIcon
                v-if="body.length === idx + 1 && body.length < 4 && props.type !== PageType.Details"
                @click="pushBody('')"
              />
            </div>
          </el-form-item>
        </el-form>

        <!-- Table content section -->
        <div class="">
          <div class="text-[14px] text-[#6b7280] font-semibold">Nội dung bảng</div>
          <div class="grid grid-cols-2 gap-[5px] pt-[10px]">
            <div class="text-[13px] text-[#6b7280]">Tên hàng</div>
            <div class="text-[13px] text-[#6b7280]">Nội dung</div>
          </div>
          <div
            v-for="(row, idx) in table"
            class="grid grid-cols-2 gap-[5px] relative"
            :key="idx"
            :class="{
              'pt-[5px]': idx === 0,
              'pt-[20px]': idx !== 0,
            }"
          >
            <el-form-item
              v-bind="{
                error: (errors as any)[`table[${idx}].name`] || null,
              }"
              :disabled="props.type === PageType.Details"
            >
              <VTooltip
                placement="top"
                :content="(row.value as any).name"
                :disabled="!!!(row.value as any).name || props.type !== PageType.Details"
              >
                <template #trigger>
                  <el-input
                    v-model="(row.value as any).name"
                    size="default"
                    show-word-limit
                    :maxlength="35"
                    :style="'w-[100%]'"
                    :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
                    :disabled="props.type === PageType.Details"
                  />
                </template>
              </VTooltip>
            </el-form-item>
            <el-form-item
              v-bind="{
                error: (errors as any)[`table[${idx}].param`] || null,
              }"
              :disabled="props.type === PageType.Details"
            >
              <VTooltip
                placement="top"
                :content="(row.value as any).param"
                :disabled="!!!(row.value as any).param || props.type !== PageType.Details"
              >
                <template #trigger>
                  <el-input
                    v-model="(row.value as any).param"
                    size="default"
                    show-word-limit
                    :maxlength="90"
                    :style="'w-[100%]'"
                    :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
                    :disabled="props.type === PageType.Details"
                  />
                </template>
              </VTooltip>
            </el-form-item>
            <div
              class="absolute justify-start flex gap-0.5 pl-1 left-full"
              :class="{
                'top-[15px]': idx === 0,
                'top-[30px]': idx !== 0,
              }"
            >
              <RemoveIcon
                v-if="table.length > 1 && props.type !== PageType.Details"
                @click="removeRow(idx)"
              />
              <AddIcon
                v-if="
                  table.length === idx + 1 && table.length < 8 && props.type !== PageType.Details
                "
                @click="pushRow({ param: '', name: '' } as any)"
              />
            </div>
          </div>
        </div>

        <!-- Payment information section -->
        <div class="">
          <div class="flex flex-wrap items-center text-sm text-[#6b7280] font-semibold pt-[15px]">
            Thông tin thanh toán <span class="pl-1 text-[#F10000]">*</span>

            <VTooltip>
              <template #contentTemplate>
                <div class="max-w-[320px]">
                  <p class="">
                    {{ TOOLTIP_PAYMENT.CONTENT_1 }}
                  </p>
                  <p class="">
                    {{ TOOLTIP_PAYMENT.CONTENT_2 }}
                  </p>
                  <p class="">
                    {{ TOOLTIP_PAYMENT.CONTENT_3 }}
                  </p>
                  <p class="">
                    {{ TOOLTIP_PAYMENT.CONTENT_4 }}
                  </p>
                </div>
              </template>
            </VTooltip>
          </div>
          <div class="">
            <table class="table-auto w-full border-separate space-y-2">
              <tbody class="space-y-3">
                <!-- Bank -->
                <tr class="align-middle">
                  <td class="pr-4 py-2 w-1/3">
                    <label class="text-[13px] text-[#6b7280]">Ngân hàng *</label>
                  </td>
                  <td class="py-2">
                    <VElementDropdown
                      name="bank_content.bank_name"
                      isUseTooltip
                      :option="bankOptions"
                      :placeholder="getPlaceholder(PLACEHOLDER.ZNS.SELECT_BANK, type)"
                      :style="'w-[100%]'"
                      :filterMethod="bankFilterMethod"
                      :disabled="props.type === PageType.Details"
                      @focus="handleBankFocus"
                    />
                  </td>
                </tr>

                <!-- Account name -->
                <tr class="align-middle">
                  <td class="pr-4 py-2">
                    <label class="text-[13px] text-[#6b7280]">Tên tài khoản *</label>
                  </td>
                  <td class="py-2">
                    <VElementInput
                      class="truong-du-lieu-mau"
                      size="default"
                      name="bank_content.account_name"
                      :style="'w-[100%]'"
                      :required="true"
                      :maxlength="MAX_LENGTH_ACCOUNT_NAME_PAYMENT"
                      :placeholder="getPlaceholder(PLACEHOLDER.ZNS.TYPE_ACCOUNT_NAME, type)"
                      :disabled="props.type === PageType.Details"
                    />
                  </td>
                </tr>

                <!-- Account number -->
                <tr class="align-middle">
                  <td class="pr-4 py-2">
                    <label class="text-[13px] text-[#6b7280]">Số tài khoản *</label>
                  </td>
                  <td class="py-2">
                    <VElementInput
                      v-model="(bank_content as any).account_number"
                      class="truong-du-lieu-mau"
                      size="default"
                      name="bank_content.account_number"
                      :style="'w-[100%]'"
                      :required="true"
                      :maxlength="MAX_LENGTH_ACCOUNT_NUMBER_PAYMENT"
                      :placeholder="getPlaceholder(PLACEHOLDER.ZNS.TYPE_ACCOUNT_NUMBER, type)"
                      :disabled="props.type === PageType.Details"
                    />
                  </td>
                </tr>

                <!-- Transfer amount -->
                <tr class="align-middle">
                  <td class="pr-4 py-2">
                    <label class="flex flex-wrap items-center text-[13px] text-[#6b7280]"
                      >Số tiền (VND) *
                      <VTooltip content="Số tiền chỉ trong khoảng từ 2,000 - 500,000,000 VND">
                      </VTooltip>
                    </label>
                  </td>
                  <td class="py-2">
                    <VElementInput
                      class="truong-du-lieu-mau"
                      size="default"
                      name="bank_content.price"
                      id="bank-price"
                      :style="'w-[100%]'"
                      :required="true"
                      :maxlength="MAX_LENGTH_PRICE_PAYMENT"
                      :placeholder="getPlaceholder(PLACEHOLDER.ZNS.TYPE_PRICE, type)"
                      :disabled="props.type === PageType.Details"
                    />
                  </td>
                </tr>

                <!-- Transfer note -->
                <tr class="align-middle">
                  <td class="pr-4 py-2">
                    <label class="text-[13px] text-[#6b7280]">Nội dung chuyển khoản</label>
                  </td>
                  <td class="py-2">
                    <VElementInput
                      class="truong-du-lieu-mau"
                      size="default"
                      name="bank_content.content"
                      :style="'w-[100%]'"
                      :required="true"
                      :maxlength="MAX_LENGTH_CONTENT_PAYMENT"
                      :placeholder="getPlaceholder(PLACEHOLDER.ZNS.TYPE_CONTENT, type)"
                      :disabled="props.type === PageType.Details"
                    />
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Account type upload file -->
            <div class="my-2">
              <label class="text-[13px] text-[#6b7280]"
                >Tài khoản trên là tài khoản của doanh nghiệp sở hữu OA</label
              >
              <div class="flex items-center gap-x-10 pt-[5px]">
                <div class="flex items-center">
                  <RadioButton
                    v-model="accountTypeRadioValue"
                    id="config-step-radio-account-type"
                    type="radio"
                    inputId="accountTypeYes"
                    :disabled="props.type === PageType.Details"
                    :value="ZnsConfigAccountType.Yes"
                    @change="handleChangeAccountType(ZnsConfigAccountType.Yes)"
                  />
                  <label
                    for="accountTypeYes"
                    class="ml-2 select-none"
                    :class="type === PageType.Details ? '' : 'cursor-pointer'"
                    >Có</label
                  >
                </div>
                <div class="flex items-center">
                  <RadioButton
                    v-model="accountTypeRadioValue"
                    id="config-step-radio-account-type"
                    type="radio"
                    inputId="accountTypeNo"
                    :disabled="props.type === PageType.Details"
                    :value="ZnsConfigAccountType.No"
                    @change="handleChangeAccountType(ZnsConfigAccountType.No)"
                  />
                  <label
                    for="accountTypeNo"
                    class="ml-2 select-none"
                    :class="type === PageType.Details ? '' : 'cursor-pointer'"
                    >Không</label
                  >
                </div>
              </div>

              <div v-if="accountTypeRadioValue === ZnsConfigAccountType.No">
                <label for="" class="inline-block text-[#292D32] font-semibold text-[14px] my-1"
                  >Vui lòng cung cấp giấy tờ chứng minh: Chủ tài khoản là chủ doanh nghiệp hoặc
                  người đại diện pháp lý của doanh nghiệp/Chủ tài khoản được doanh nghiệp ủy quyền
                  thu hộ.
                </label>

                <VUploadFileServerZns
                  ref="fileUploadServerRef"
                  file="fileUploadBank"
                  listFile="listFileUploadBank"
                  :msgErrType="ERROR_MESSAGE.ZNS.INVALID_MULTI_FILE_UPLOAD"
                  :msgErrSize="ERROR_MESSAGE.ZNS.LIMIT_SIZE_FILE(FileSizeLabel._5Mb)"
                  :formType="type"
                  :fileTypes="ACCEPT_FILE_MULTIPLE_TYPE_PAYMENT"
                  :typeApi="FileUploadApiType.ImageFile"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Technical settings section -->
      <div class="">
        <div class="text-[14px] text-[#6b7280] font-semibold pt-[15px]">Cài đặt kỹ thuật</div>
        <div class="flex items-center gap-[5px] pt-[10px]">
          <div class="w-[30%] text-[13px] text-[#6b7280]">Tên tham số</div>
          <div class="w-[40%] text-[13px] text-[#6b7280]">
            Loại tham số và ký tự tối đa <span class="text-[#F10000]">*</span>
          </div>
          <div class="w-[30%] text-[13px] text-[#6b7280]">
            Nội dung mẫu <span class="text-[#F10000]">*</span>
          </div>
        </div>
        <div
          v-for="(setting, idx) in technical_config"
          class="flex items-center gap-[5px]"
          :key="idx"
          :class="{
            'pt-[5px]': idx === 0,
            'pt-[20px]': idx !== 0,
          }"
        >
          <VTooltip placement="top">
            <template #contentTemplate>
              {{ (setting.value as any).parameter_name }}
            </template>
            <template #trigger>
              <el-input
                v-model="(setting.value as any).parameter_name"
                class="h-[32px] !w-[30%]"
                size="default"
                :disabled="true"
                :style="'w-[100%]'"
                :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
              />
            </template>
          </VTooltip>
          <VTooltip
            placement="top"
            :content="getParameterTypeLabel((setting.value as any).zalo_data_field)"
            :disabled="props.type !== PageType.Details"
          >
            <template #trigger>
              <el-select-v2
                v-model="(setting.value as any).zalo_data_field"
                :options="PARAMETER_TYPES"
                class="!w-[40%]"
                :disabled="props.type === PageType.Details"
                @change="
                  checkTemplate(
                    idx,
                    getMaxLengthParameterZns((setting.value as any).zalo_data_field),
                  )
                "
              >
                <template #default="{ item }">
                  <el-tooltip placement="left" content>
                    <template #content>{{ item.label }}</template>
                    <span>{{ item.label }}</span>
                  </el-tooltip>
                </template>
              </el-select-v2>
            </template>
          </VTooltip>
          <el-form-item
            class="!w-[30%]"
            v-bind="{
              error: (setting.value as any).error || null,
            }"
          >
            <VTooltip
              placement="top"
              :content="(setting.value as any).default_data_field"
              :disabled="props.type !== PageType.Details"
            >
              <template #trigger>
                <el-input
                  v-model="(setting.value as any).default_data_field"
                  size="default"
                  :maxlength="getMaxLengthParameterZns((setting.value as any).zalo_data_field)"
                  :style="'w-[100%]'"
                  :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
                  :disabled="props.type === PageType.Details"
                  :show-word-limit="true"
                  @input="
                    checkTemplate(
                      idx,
                      getMaxLengthParameterZns((setting.value as any).zalo_data_field),
                    )
                  "
                />
              </template>
            </VTooltip>
          </el-form-item>
        </div>
      </div>

      <!-- Button section -->
      <div class="">
        <div class="text-[14px] text-[#6b7280] font-semibold pt-[15px]">Thêm button</div>
        <!-- Main button section -->
        <div v-show="!znsStore.isAddMainButton" class="pt-[15px]">
          <VElementButton
            bgColor="#4F52FF"
            styleIcon="text-[20px] text-[#fff] mr-[7px]"
            icon="plus"
            styleButton="s"
            label="Thêm button chính"
            :styleButtonClass="'!w-[175px]'"
            :disabled="props.type === PageType.Details"
            @click="znsStore.toggleMainButton(znsStore.isAddMainButton)"
          />
        </div>
        <!-- Main button configuration -->
        <div v-if="znsStore.isAddMainButton" class="pt-[15px] relative">
          <div class="grid gap-[5px] grid-cols-3">
            <div class="text-[13px] text-[#6b7280]">
              Loại button <span class="text-[#F10000]">*</span>
            </div>
            <div class="text-[13px] text-[#6b7280]">
              Nội dung button <span class="text-[#F10000]">*</span>
            </div>
            <div class="text-[13px] text-[#6b7280]">
              {{ values.mainType === ZnsButtonType.Call ? 'Số điện thoại' : 'Đường dẫn liên kết' }}
              <span class="text-[#F10000]">*</span>
            </div>
          </div>
          <el-form class="grid gap-[5px] pt-[5px] grid-cols-3" label-position="top">
            <VElementDropdown
              name="mainType"
              :filterable="false"
              :option="BUTTON_TYPES"
              :style="'w-[100%]'"
              :isUseTooltip="true"
              :disabled="props.type === PageType.Details"
              @change="mainTypeChange"
            />
            <VElementInput
              class="truong-du-lieu-mau"
              size="default"
              name="mainContent"
              :style="'w-[100%]'"
              :required="true"
              :maxlength="30"
              :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
              :disabled="props.type === PageType.Details"
            />
            <VElementInput
              v-if="resetMainLink"
              size="default"
              name="mainLink"
              class="truong-du-lieu-mau"
              :type="values.mainType !== ZnsButtonType.Call ? '' : 'onlyNumber'"
              :style="'w-[100%]'"
              :required="true"
              :maxlength="values.mainType !== ZnsButtonType.Call ? 58 : 15"
              :placeholder="
                type === PageType.Details
                  ? ''
                  : values.mainType === ZnsButtonType.Call
                    ? PLACEHOLDER.TYPE
                    : PLACEHOLDER.TYPE_LINK
              "
              :disabled="props.type === PageType.Details"
              @change="mainPhoneChange"
            />
          </el-form>
          <div class="absolute justify-start flex gap-0.5 pl-1 left-full top-[47px]">
            <RemoveIcon
              v-if="znsStore.isAddMainButton && props.type !== PageType.Details"
              @click="znsStore.toggleMainButton(znsStore.isAddMainButton)"
            />
          </div>
        </div>

        <!-- Sub button section -->
        <div v-show="!znsStore.isAddSubButton" class="pt-[15px]">
          <VElementButton
            text="black"
            bgColor="#DDDEE3"
            styleIcon="text-[20px] mr-[7px]"
            icon="plus"
            styleButton="s"
            label="Thêm button phụ"
            :styleButtonClass="'!w-[175px]'"
            :disabled="props.type === PageType.Details"
            @click="znsStore.toggleSubButton(znsStore.isAddSubButton)"
          />
        </div>
        <!-- Sub button configuration -->
        <div v-if="znsStore.isAddSubButton" class="pt-[15px] relative">
          <div class="grid gap-[5px] grid-cols-3">
            <div class="text-[13px] text-[#6b7280]">
              Loại button <span class="text-[#F10000]">*</span>
            </div>
            <div class="text-[13px] text-[#6b7280]">
              Nội dung button <span class="text-[#F10000]">*</span>
            </div>
            <div class="text-[13px] text-[#6b7280]">
              {{ values.subType === ZnsButtonType.Call ? 'Số điện thoại' : 'Đường dẫn liên kết' }}
              <span class="text-[#F10000]">*</span>
            </div>
          </div>
          <el-form class="grid gap-[5px] pt-[5px] grid-cols-3" label-position="top">
            <VElementDropdown
              name="subType"
              :filterable="false"
              :option="BUTTON_TYPES"
              :style="'w-[100%]'"
              :isUseTooltip="true"
              :disabled="props.type === PageType.Details"
              @change="subTypeChange"
            />
            <VElementInput
              size="default"
              name="subContent"
              :style="'w-[100%]'"
              :required="true"
              :maxlength="30"
              :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
              :disabled="props.type === PageType.Details"
            />
            <VElementInput
              v-if="resetSubLink"
              size="default"
              name="subLink"
              :type="values.subType !== ZnsButtonType.Call ? '' : 'onlyNumber'"
              :style="'w-[100%]'"
              :required="true"
              :maxlength="
                values.subType !== ZnsButtonType.Call
                  ? MAX_LENGTH_BUTTON_LINK
                  : MAX_LENGTH_BUTTON_PHONE
              "
              :placeholder="
                type === PageType.Details
                  ? ''
                  : values.subType === ZnsButtonType.Call
                    ? PLACEHOLDER.TYPE
                    : PLACEHOLDER.TYPE_LINK
              "
              :disabled="props.type === PageType.Details"
              @change="subPhoneChange"
            />
          </el-form>
          <div class="absolute justify-start flex gap-0.5 pl-1 left-full top-[47px]">
            <RemoveIcon
              v-if="znsStore.isAddSubButton && props.type !== PageType.Details"
              @click="znsStore.toggleSubButton(znsStore.isAddSubButton)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Preview section -->
    <ZnsWrapper>
      <PaymentRequestType
        :type="ZnsPageType.Form"
        :mode="!znsStore.darkMode"
        :items="
          {
            title: values.title,
            body: values.body,
            table: values.table,
            bank_content: values.bank_content,
            primary_button: !znsStore.isAddMainButton ? false : { content: values.mainContent },
            secondary_button: !znsStore.isAddSubButton ? false : { content: values.subContent },
          } as any
        "
      >
        <!-- Image preview section -->
        <template #image>
          <div class="overflow-hidden">
            <div class="flex gap-x-2">
              <img
                v-show="!znsStore.darkMode && previewLogo.light"
                :src="previewLogo.light"
                alt=""
                class="max-w-full max-h-14 w-fit h-full mb-2.5 object-contain"
              />
              <img
                v-show="znsStore.darkMode && previewLogo.dark"
                :src="previewLogo.dark"
                alt=""
                class="max-w-full max-h-14 w-fit h-full mb-2.5 object-contain"
              />
            </div>
          </div>
        </template>
      </PaymentRequestType>
    </ZnsWrapper>
  </div>
</template>

<style scoped>
:deep(.truong-du-lieu-mau .el-form-item__error) {
  position: unset;
}
</style>
