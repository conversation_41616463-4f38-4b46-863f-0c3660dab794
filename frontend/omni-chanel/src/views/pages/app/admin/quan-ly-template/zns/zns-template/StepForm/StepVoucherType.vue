<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import * as yup from 'yup';
import { useForm, useFieldArray } from 'vee-validate';
import { useRouter } from 'vue-router';
import { watchDebounced } from '@vueuse/core';
import { PLACEHOLDER, ROUTE_NAME, REGEX_CHECK_PARAMETER } from '@/shared';
import { useZNSStep } from '@/store/useZNSStep';
import { FileSize, FileSizeLabel, PageType } from '@/enums/common';
import {
  ZnsPageType,
  ZnsTextConfigLogoType,
  ZnsType,
  ZnsTypeImg,
  ZnsButtonType,
  ZnsConfigImgType,
  ZnsConfigImg,
} from '@/enums/zns';
import { ERROR_MESSAGE } from '@/constants/templateText';
import RemoveIcon from '@/components/icon/RemoveIcon.vue';
import AddIcon from '@/components/icon/AddIcon.vue';
import VUploadImageNew from '@/components/base/OtherComponent/VUploadImageNew.vue';
import VoucherType from '../info/VoucherType.vue';
import type { IRow, ISetting } from '../index.type';
import {
  getString,
  removeDuplicates,
  isOnlyParameter,
  getParameterTypeLabel,
} from '../index.utils';
import {
  ACCEPT_FILE_TYPE_LOGO,
  BUTTON_TYPES,
  GUIDE_TEXT,
  LOGO_PX_HEIGHT,
  LOGO_PX_WIDTH,
  REGEX_FILE_TYPE_LOGO,
  PARAMETER_TYPES,
  DEBOUNCE_TIME,
  REGEX_PARAMETER,
  MAX_LENGTH_BUTTON_LINK,
  MAX_LENGTH_BUTTON_PHONE,
  MAX_FILE_MULTIPLE,
  ACCEPT_FILE_TYPE_IMAGE,
  VOUCHER_LENGTH,
  VOUCHER_SHOW_CODE_OPTIONS,
  INITIAL_VALUE_VOUCHER,
} from '../index.constants';
import { contentDefaultButton, getMaxLengthParameterZns } from '../index.utils';
import ZnsWrapper from '../info/ZnsWrapper.vue';
import HintUploadImg from '../info/HintUploadImg.vue';
import VUploadMultipleImage from '@/components/base/OtherComponent/VUploadMultipleImage.vue';
import SlideImage from '@/components/base/common/SlideImage.vue';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { getPlaceholder } from '@/utils';
import VTooltip from '@/components/base/ElementComponent/VTooltip.vue';

const props = defineProps<{
  id?: number;
  type: PageType;
}>();

const router = useRouter();
const znsStore = useZNSStep();

const previewLogo = ref<{
  light: string;
  dark: string;
}>({
  light: '',
  dark: '',
});
const uploadedImages = ref<string[]>([]);
const imgRadioValue = ref<ZnsConfigImg>();
const checkValidateSubmit = ref<boolean[]>([]);
const arrayParamTag = ref<string[]>([]);
const arrayParamDataNew = ref<string[]>([]);
const arrayParamCurrentData = ref<ISetting[]>([]);
const resetSubLink = ref(true);
const resetMainLink = ref(true);
const fileUploadLogoLightRef = ref();
const fileUploadLogoDarkRef = ref();
const fileUploadImagesRef = ref();

const valueTexts = computed(() => [
  values.title,
  ...values.body,
  ...values.table.map((item: any) => item.param),
  values.voucher_content?.price_voucher,
  values.voucher_content?.condition,
  values.voucher_content?.voucher_code,
  values.voucher_content?.start_date,
  values.voucher_content?.end_date,
]);

const technicalSchema = yup.object({
  default_data_field: yup
    .string()
    .required(ERROR_MESSAGE.REQUIRED_FIELD('Nội dung mẫu'))
    .trim()
    .test('check-maxlength', 'error', function (value, context) {
      const maxlength = context.options.context?.maxLength;
      if (value.length > maxlength) {
        return this.createError({
          message: ERROR_MESSAGE.MAX_LENGTH_FIELD('Nội dung mẫu', maxlength),
        });
      }
      return true;
    })
    .test(
      'submit',
      '',
      () => !checkValidateSubmit.value.includes(false) || checkValidateSubmit.value.length === 0,
    ),
});

const tableRowSchema = yup.object({
  name: yup
    .string()
    .notRequired()
    .min(3, ERROR_MESSAGE.MIN_LENGTH_FIELD_ZNS(3))
    .trim()
    .test('check-params-valid', ERROR_MESSAGE.INVALID_NO_PARAM_ZNS, (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      if (matches) {
        return false;
      }
      return true;
    }),
  param: yup
    .string()
    .notRequired()
    .min(3, ERROR_MESSAGE.MIN_LENGTH_FIELD_ZNS(3))
    .trim()
    .test('check-params-valid', ERROR_MESSAGE.INVALID_FIELD('Tên tham số'), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      let isValid = true;
      if (matches) {
        matches?.forEach((item) => {
          if (!REGEX_CHECK_PARAMETER.test(item.slice(1, -1)) || getString(item) === null) {
            isValid = false;
            return false;
          }
          return true;
        });
      }
      if (isValid) {
        return true;
      }
      return false;
    })
    .test('check-max-params', ERROR_MESSAGE.MAX_LENGTH_PARAMETER_ZNS('Nội dung', 1), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      if (matches) {
        if (removeDuplicates(matches).length > 1) {
          return false;
        }
      }
      return true;
    }),
});

const logoSchema = {
  fileLogoLight: yup.mixed().required(ERROR_MESSAGE.REQUIRED_ZNS_FILE),
  fileLogoDark: yup.mixed().required(ERROR_MESSAGE.REQUIRED_ZNS_FILE),
  fileSizeLogoLight: yup
    .number()
    .max(FileSize._15Mb, ERROR_MESSAGE.LIMIT_SIZE_FILE(FileSizeLabel._15Mb))
    .notRequired(),
  fileSizeLogoDark: yup
    .number()
    .max(FileSize._15Mb, ERROR_MESSAGE.LIMIT_SIZE_FILE(FileSizeLabel._15Mb))
    .notRequired(),
  fileTypeLogoLight: yup
    .string()
    .matches(
      REGEX_FILE_TYPE_LOGO,
      ERROR_MESSAGE.INVALID_FILE_ZNS_LOGO(LOGO_PX_WIDTH, LOGO_PX_HEIGHT),
    )
    .notRequired(),
  fileTypeLogoDark: yup
    .string()
    .matches(
      REGEX_FILE_TYPE_LOGO,
      ERROR_MESSAGE.INVALID_FILE_ZNS_LOGO(LOGO_PX_WIDTH, LOGO_PX_HEIGHT),
    )
    .notRequired(),
  fileWidthLogoLight: yup
    .number()
    .equals([LOGO_PX_WIDTH], ERROR_MESSAGE.INVALID_FILE_ZNS_LOGO(LOGO_PX_WIDTH, LOGO_PX_HEIGHT))
    .notRequired(),
  fileHeightLogoLight: yup
    .number()
    .equals([LOGO_PX_HEIGHT], ERROR_MESSAGE.INVALID_FILE_ZNS_LOGO(LOGO_PX_WIDTH, LOGO_PX_HEIGHT))
    .notRequired(),
  fileWidthLogoDark: yup
    .number()
    .equals([LOGO_PX_WIDTH], ERROR_MESSAGE.INVALID_FILE_ZNS_LOGO(LOGO_PX_WIDTH, LOGO_PX_HEIGHT))
    .notRequired(),
  fileHeightLogoDark: yup
    .number()
    .equals([LOGO_PX_HEIGHT], ERROR_MESSAGE.INVALID_FILE_ZNS_LOGO(LOGO_PX_WIDTH, LOGO_PX_HEIGHT))
    .notRequired(),
};

const uploadImageSchema = {
  isHasImage: yup.lazy(() => {
    return yup.string().required(ERROR_MESSAGE.REQUIRED_ZNS_FILE);
  }),
};

const voucherSchema = yup.object({
  price_voucher: yup
    .string()
    .notRequired()
    .min(3, ERROR_MESSAGE.MIN_LENGTH_FIELD_ZNS(3))
    .trim()
    .test('check-params-valid', ERROR_MESSAGE.INVALID_FIELD('Tên tham số'), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      let isValid = true;
      if (matches) {
        matches?.forEach((item) => {
          if (!REGEX_CHECK_PARAMETER.test(item.slice(1, -1)) || getString(item) === null) {
            isValid = false;
            return false;
          }
          return true;
        });
      }
      if (isValid) {
        return true;
      }
      return false;
    })
    .test('check-params-only', ERROR_MESSAGE.INVALID_PARAM, (value) => {
      if (value) {
        const isOnlyParams = isOnlyParameter(value);
        if (!isOnlyParams) {
          return false;
        }
      }
      return true;
    })
    .test(
      'check-max-params',
      ERROR_MESSAGE.MAX_LENGTH_PARAMETER_ZNS('Giá trị voucher', 1),
      (value) => {
        const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
        if (matches) {
          if (removeDuplicates(matches).length > 1) {
            return false;
          }
        }
        return true;
      },
    ),
  condition: yup
    .string()
    .notRequired()
    .min(3, ERROR_MESSAGE.MIN_LENGTH_FIELD_ZNS(3))
    .trim()
    .test('check-params-valid', ERROR_MESSAGE.INVALID_FIELD('Tên tham số'), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      let isValid = true;
      if (matches) {
        matches?.forEach((item) => {
          if (!REGEX_CHECK_PARAMETER.test(item.slice(1, -1)) || getString(item) === null) {
            isValid = false;
            return false;
          }
          return true;
        });
      }
      if (isValid) {
        return true;
      }
      return false;
    })
    .test('check-params-only', ERROR_MESSAGE.INVALID_PARAM, (value) => {
      if (value) {
        const isOnlyParams = isOnlyParameter(value);
        if (!isOnlyParams) {
          return false;
        }
      }
      return true;
    })
    .test(
      'check-max-params',
      ERROR_MESSAGE.MAX_LENGTH_PARAMETER_ZNS('Điều kiện áp dụng', 1),
      (value) => {
        const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
        if (matches) {
          if (removeDuplicates(matches).length > 1) {
            return false;
          }
        }
        return true;
      },
    ),
  start_date: yup
    .string()
    .notRequired()
    .trim()
    .test('check-params-valid', ERROR_MESSAGE.INVALID_FIELD('Tên tham số'), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      let isValid = true;
      if (matches) {
        matches?.forEach((item) => {
          if (!REGEX_CHECK_PARAMETER.test(item.slice(1, -1)) || getString(item) === null) {
            isValid = false;
            return false;
          }
          return true;
        });
      }
      if (isValid) {
        return true;
      }
      return false;
    })
    .test('check-params-only', ERROR_MESSAGE.INVALID_PARAM, (value) => {
      if (value) {
        const isOnlyParams = isOnlyParameter(value);
        if (!isOnlyParams) {
          return false;
        }
      }
      return true;
    })
    .test(
      'check-max-params',
      ERROR_MESSAGE.MAX_LENGTH_PARAMETER_ZNS('Ngày bắt đầu voucher', 1),
      (value) => {
        const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
        if (matches) {
          if (removeDuplicates(matches).length > 1) {
            return false;
          }
        }
        return true;
      },
    ),
  end_date: yup
    .string()
    .required(ERROR_MESSAGE.REQUIRED_FIELD('Hạn sử dụng'))
    .test('check-params-valid', ERROR_MESSAGE.INVALID_FIELD('Tên tham số'), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      let isValid = true;
      if (matches) {
        matches?.forEach((item) => {
          if (!REGEX_CHECK_PARAMETER.test(item.slice(1, -1)) || getString(item) === null) {
            isValid = false;
            return false;
          }
          return true;
        });
      }
      if (isValid) {
        return true;
      }
      return false;
    })
    .test('check-params-only', ERROR_MESSAGE.INVALID_PARAM, (value) => {
      if (value) {
        const isOnlyParams = isOnlyParameter(value);
        if (!isOnlyParams) {
          return false;
        }
      }
      return true;
    })
    .test('check-max-params', ERROR_MESSAGE.MAX_LENGTH_PARAMETER_ZNS('Hạn sử dụng', 1), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      if (matches) {
        if (removeDuplicates(matches).length > 1) {
          return false;
        }
      }
      return true;
    }),
  voucher_code: yup
    .string()
    .required(ERROR_MESSAGE.REQUIRED_FIELD('Mã voucher'))
    .trim()
    .test('check-params-valid', ERROR_MESSAGE.INVALID_FIELD('Tên tham số'), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      let isValid = true;
      if (matches) {
        matches?.forEach((item) => {
          if (!REGEX_CHECK_PARAMETER.test(item.slice(1, -1)) || getString(item) === null) {
            isValid = false;
            return false;
          }
          return true;
        });
      }
      if (isValid) {
        return true;
      }
      return false;
    })
    .test('check-params-only', ERROR_MESSAGE.INVALID_PARAM, (value) => {
      if (value) {
        const isOnlyParams = isOnlyParameter(value);
        if (!isOnlyParams) {
          return false;
        }
      }
      return true;
    })
    .test('check-max-params', ERROR_MESSAGE.MAX_LENGTH_PARAMETER_ZNS('Mã voucher', 1), (value) => {
      const matches: string[] | null | undefined = value?.match(REGEX_PARAMETER);
      if (matches) {
        if (removeDuplicates(matches).length > 1) {
          return false;
        }
      }
      return true;
    }),
});

const baseSchema = {
  title: yup
    .string()
    .required(ERROR_MESSAGE.REQUIRED_FIELD('Tiêu đề'))
    .trim()
    .test('check-params-valid', ERROR_MESSAGE.INVALID_FIELD('Tên tham số'), (value) => {
      const matches: string[] | null = value.match(REGEX_PARAMETER);
      let isValid = true;
      if (matches) {
        matches?.forEach((item) => {
          if (!REGEX_CHECK_PARAMETER.test(item.slice(1, -1)) || getString(item) === null) {
            isValid = false;
            return false;
          }
          return true;
        });
      }
      if (isValid) {
        return true;
      }
      return false;
    }),
  body: yup.array().of(
    yup
      .string()
      .required(ERROR_MESSAGE.REQUIRED_FIELD('Nội dung'))
      .trim()
      .test('check-params-valid', ERROR_MESSAGE.INVALID_FIELD('Tên tham số'), (value) => {
        const matches: string[] | null = value.match(REGEX_PARAMETER);
        let isValid = true;
        if (matches) {
          matches?.forEach((item) => {
            if (!REGEX_CHECK_PARAMETER.test(item.slice(1, -1)) || getString(item) === null) {
              isValid = false;
              return false;
            }
            return true;
          });
        }
        if (isValid) {
          return true;
        }
        return false;
      }),
  ),
  technical_config: yup.array().of(technicalSchema),
  table: yup.array().of(tableRowSchema),
  voucher_content: voucherSchema,
  mainContent: yup
    .string()
    .test('mainContent', '', function (value) {
      if (znsStore.isAddMainButton) {
        if (!value) {
          return this.createError({
            message: ERROR_MESSAGE.REQUIRED_FIELD('Nội dung button'),
          });
        }
        if (value.length < 5) {
          return this.createError({
            message: ERROR_MESSAGE.MIN_LENGTH_FIELD_ZNS(5),
          });
        }
      }
      return true;
    })
    .trim(),
  subContent: yup
    .string()
    .test('subContent', '', function (value) {
      if (znsStore.isAddSubButton) {
        if (!value) {
          return this.createError({
            message: ERROR_MESSAGE.REQUIRED_FIELD('Nội dung button'),
          });
        }
        if (value.length < 5) {
          return this.createError({
            message: ERROR_MESSAGE.MIN_LENGTH_FIELD_ZNS(5),
          });
        }
      }
      return true;
    })
    .trim(),
  subType: yup.number(),
  mainType: yup.number(),
  subLink: yup.string().when('subType', (subType, schema) => {
    if (subType[0] !== 2 && znsStore.isAddSubButton) {
      return schema.required(ERROR_MESSAGE.REQUIRED_FIELD('Đường dẫn liên kết')).trim();
    }
    if (subType[0] === 2 && znsStore.isAddSubButton) {
      return schema.required(ERROR_MESSAGE.REQUIRED_FIELD('Số điện thoại')).trim();
    }
    return schema;
  }),
  mainLink: yup.string().when('mainType', (mainType, schema) => {
    if (mainType[0] !== 2 && znsStore.isAddMainButton) {
      return schema.required(ERROR_MESSAGE.REQUIRED_FIELD('Đường dẫn liên kết')).trim();
    }
    if (mainType[0] === 2 && znsStore.isAddMainButton) {
      return schema.required(ERROR_MESSAGE.REQUIRED_FIELD('Số điện thoại')).trim();
    }
    return schema;
  }),
};

const createValidationSchema = (imgOption: ZnsConfigImg) => {
  return imgOption === ZnsConfigImg.Logo
    ? yup.object({ ...baseSchema, ...logoSchema })
    : yup.object({ ...baseSchema, ...uploadImageSchema });
};

const validationSchema = ref(createValidationSchema(imgRadioValue.value as ZnsConfigImg));

const { values, handleSubmit, validateField, errors, setFieldValue } = useForm<any>({
  validationSchema,
  initialValues: INITIAL_VALUE_VOUCHER,
});

const {
  remove: removeBody,
  replace: replaceBody,
  push: pushBody,
  fields: body,
} = useFieldArray('body');

const {
  remove: removeSetting,
  push: pushSetting,
  fields: technical_config,
} = useFieldArray<ISetting[]>('technical_config');

const {
  remove: removeRow,
  replace: replaceTable,
  push: pushRow,
  fields: table,
} = useFieldArray<IRow[]>('table');

const handleChangeImgOption = () => {
  previewLogo.value.light = '';
  previewLogo.value.dark = '';
  uploadedImages.value = [];
};

const handleUploadLogo = (fileData: any, typeLogo: ZnsTextConfigLogoType) => {
  if (fileData) {
    if (typeLogo === ZnsTextConfigLogoType.Light) {
      previewLogo.value.light = fileData.link_url;
      setFieldValue('fileLogoLight' as any, fileData.link_url);
    } else if (typeLogo === ZnsTextConfigLogoType.Dark) {
      previewLogo.value.dark = fileData.link_url;
      setFieldValue('fileLogoDark' as any, fileData.link_url);
    }
  }
};

const watchFileWidthLogo = (
  newValue: { width: number; height: number },
  type: ZnsConfigImgType,
) => {
  if (newValue.width !== null) {
    if (type === ZnsConfigImgType.LogoLight) {
      setFieldValue(`fileWidthLogoLight` as any, newValue.width);
      setFieldValue(`fileHeightLogoLight` as any, newValue.height);
    } else {
      setFieldValue(`fileWidthLogoDark` as any, newValue.width);
      setFieldValue(`fileHeightLogoDark` as any, newValue.height);
    }
  }
};

const beforeUploadLogo = (newValue: { width: number; height: number }, type: ZnsConfigImgType) => {
  watchFileWidthLogo(newValue, type);
};

const handleRemoveLogo = (target: ZnsTextConfigLogoType) => {
  if (target === ZnsTextConfigLogoType.Light) {
    previewLogo.value.light = '';
    setFieldValue('fileLogoLight', undefined);
  } else if (target === ZnsTextConfigLogoType.Dark) {
    previewLogo.value.dark = '';
    setFieldValue('fileLogoDark', undefined);
  }
};

//  Handle upload images success
const handleUploadImages = (fileData: any) => {
  if (fileData && Array.isArray(fileData)) {
    const newImageUrls = fileData.map((item: any) => item.link_url);

    const uniqueNewUrls = newImageUrls.filter((url: string) => !uploadedImages.value.includes(url));

    if (uniqueNewUrls.length > 0) {
      uploadedImages.value = [...uploadedImages.value, ...uniqueNewUrls];
    }
  }
};

const handleRemoveImage = (removedFile: any) => {
  const indexToRemove = uploadedImages.value.findIndex((url) => url === removedFile?.link_url);
  if (indexToRemove !== -1) {
    uploadedImages.value.splice(indexToRemove, 1);
  }
};

const checkTemplate = function (index: number, max: number, isCheckAll: boolean = false) {
  if (isCheckAll) {
    technicalSchema
      .validateAt(
        'default_data_field',
        { default_data_field: values.technical_config[index].default_data_field },
        { context: { maxLength: max } },
      )
      .then(() => {
        checkValidateSubmit.value.push(true);
        (technical_config.value[index].value as any).error = '';
      })
      .catch((err) => {
        if (err.message !== 'Nội dung mẫu không được để trống') {
          checkValidateSubmit.value.push(false);
        }
        (technical_config.value[index].value as any).error = err.message;
      });
  } else {
    setTimeout(() => {
      technicalSchema
        .validateAt(
          'default_data_field',
          { default_data_field: values.technical_config[index].default_data_field },
          { context: { maxLength: max } },
        )
        .then(() => {
          (technical_config.value[index].value as any).error = '';
        })
        .catch((err) => {
          (technical_config.value[index].value as any).error = err.message;
        });
    }, 10);
  }
};

const getParamTag = (arrStrings: string[]) => {
  try {
    if (values?.technical_config) {
      arrayParamCurrentData.value = [...values.technical_config];
    }
    arrayParamDataNew.value = [];
    arrStrings.forEach((str) => {
      const matches: string[] | null = str?.match(REGEX_PARAMETER);
      if (matches) {
        arrayParamDataNew.value.push(...matches);
        matches.forEach((item) => {
          if (REGEX_CHECK_PARAMETER.test(item.slice(1, -1))) {
            if (!arrayParamTag.value.includes(item)) {
              arrayParamTag.value.push(item);
            }
          }
        });
      }
    });
    arrayParamTag.value.forEach((item) => {
      const index = arrayParamCurrentData.value.findIndex(
        (element) => element.parameter_name === item,
      );
      if (index === -1) {
        pushSetting({
          parameter_name: item,
          zalo_data_field: 1,
          default_data_field: '',
          error: '',
        } as any);
        arrayParamCurrentData.value = [...values.technical_config];
        const indexRemove = arrayParamCurrentData.value
          .filter((itemRemove) => !arrayParamDataNew.value.includes(itemRemove.parameter_name))
          .map((itemRemove) => arrayParamCurrentData.value.indexOf(itemRemove));
        indexRemove.forEach((element) => {
          if (arrayParamCurrentData.value.length === 1) {
            removeSetting(0);
          } else {
            removeSetting(element);
          }
          arrayParamCurrentData.value = [...values.technical_config];
        });
      } else {
        const indexRemove = arrayParamCurrentData.value
          .filter((itemRemove) => !arrayParamDataNew.value.includes(itemRemove.parameter_name))
          .map((itemRemove) => arrayParamCurrentData.value.indexOf(itemRemove));
        indexRemove.forEach((element) => {
          if (arrayParamCurrentData.value.length === 1) {
            removeSetting(0);
          } else {
            removeSetting(element);
          }
          arrayParamCurrentData.value = [...values.technical_config];
        });
      }
    });
  } catch (err: any) {
    console.error(err);
  }
};

const mainTypeChange = () => {
  setFieldValue('mainContent', contentDefaultButton(values.mainType));
  resetMainLink.value = false;
  setTimeout(() => {
    resetMainLink.value = true;
    validateField('mainContent');
  }, 10);
};

const subTypeChange = () => {
  setFieldValue('subContent', contentDefaultButton(values.subType));
  resetSubLink.value = false;
  setTimeout(() => {
    resetSubLink.value = true;
    validateField('subContent');
  }, 10);
};

const mainPhoneChange = (value: any) => {
  setFieldValue('mainLink', value);
};

const subPhoneChange = (value: any) => {
  setFieldValue('subLink', value);
};

const saveValues = () => {
  let fileUploads = [];
  // if (approved) {
  //   fileUploads = znsStore.step2Data.file;
  // } else {
  if (imgRadioValue.value === ZnsConfigImg.Logo) {
    fileUploads = [
      fileUploadLogoLightRef.value?.rawFile
        ? {
            ...fileUploadLogoLightRef.value.rawFile,
            template_type_id: ZnsTypeImg.LogoLight,
          }
        : null,
      fileUploadLogoDarkRef.value?.rawFile
        ? {
            ...fileUploadLogoDarkRef.value.rawFile,
            template_type_id: ZnsTypeImg.LogoDark,
          }
        : null,
    ].filter(Boolean);
  } else if (
    imgRadioValue.value === ZnsConfigImg.UploadImage &&
    fileUploadImagesRef.value?.rawFiles
  ) {
    fileUploads = fileUploadImagesRef.value.rawFiles.map((theFile: any) => {
      return {
        ...theFile,
        template_type_id: ZnsTypeImg.Image,
      };
    });
  }
  if (!fileUploads) {
    fileUploads = [];
  }
  znsStore.setValueStep2({
    ...values,
    previewLogo: previewLogo.value,
    uploadedImages: uploadedImages.value,
    file_type: imgRadioValue.value,
    file: fileUploads,
  });
};

const setValue = () => {
  if (znsStore.step2Data) {
    setFieldValue('title', znsStore.step2Data?.title);
    if (znsStore.step2Data?.body) {
      replaceBody(znsStore.step2Data?.body);
    }
    if (znsStore.step2Data?.table) {
      replaceTable(znsStore.step2Data?.table);
    }
    if (znsStore.step2Data?.voucher_content) {
      setFieldValue('voucher_content', znsStore.step2Data.voucher_content);
    }
    setFieldValue('technical_config', znsStore.step2Data?.technical_config);
    if (znsStore.step2Data?.mainLink) {
      setFieldValue('mainLink', znsStore.step2Data?.mainLink);
    }
    if (znsStore.step2Data?.subLink) {
      setFieldValue('subLink', znsStore.step2Data?.subLink);
    }
    if (znsStore.step2Data?.mainType) {
      setFieldValue('mainType', znsStore.step2Data?.mainType);
    }
    if (znsStore.step2Data?.subType) {
      setFieldValue('subType', znsStore.step2Data?.subType);
    }
    if (znsStore.step2Data?.subContent) {
      setFieldValue('subContent', znsStore.step2Data?.subContent);
    }
    if (znsStore.step2Data?.mainContent) {
      setFieldValue('mainContent', znsStore.step2Data?.mainContent);
    }

    // Restore logo
    if (znsStore.step2Data?.previewLogo && znsStore.step2Data?.file_type === ZnsConfigImg.Logo) {
      previewLogo.value = znsStore.step2Data.previewLogo;

      setTimeout(() => {
        if (fileUploadLogoLightRef.value && previewLogo.value.light) {
          fileUploadLogoLightRef.value.imagePreviewUrl = previewLogo.value.light;
          fileUploadLogoLightRef.value.rawFile = znsStore.step2Data.file.find(
            (item: any) => item.template_type_id === ZnsTypeImg.LogoLight,
          );
        }
        if (fileUploadLogoDarkRef.value && previewLogo.value.dark) {
          fileUploadLogoDarkRef.value.imagePreviewUrl = previewLogo.value.dark;
          fileUploadLogoDarkRef.value.rawFile = znsStore.step2Data.file.find(
            (item: any) => item.template_type_id === ZnsTypeImg.LogoDark,
          );
        }

        if (fileUploadLogoLightRef.value.rawFile) {
          setFieldValue('fileLogoLight' as any, fileUploadLogoLightRef.value.rawFile);
        }
        if (fileUploadLogoDarkRef.value.rawFile) {
          setFieldValue('fileLogoDark' as any, fileUploadLogoDarkRef.value.rawFile);
        }
        if (previewLogo.value.light) {
          setFieldValue('logoLightPath' as any, previewLogo.value.light);
        }
        if (previewLogo.value.dark) {
          setFieldValue('logoDarkPath' as any, previewLogo.value.dark);
        }
      }, 100);
    }

    // Restore uploaded images
    if (
      znsStore.step2Data?.uploadedImages &&
      znsStore.step2Data?.file_type === ZnsConfigImg.UploadImage
    ) {
      uploadedImages.value = znsStore.step2Data.uploadedImages;
      setTimeout(() => {
        if (fileUploadImagesRef.value) {
          fileUploadImagesRef.value.imagePreviewUrl = uploadedImages.value;
          fileUploadImagesRef.value.rawFiles = znsStore.step2Data.file;
        }

        // Restore file uploaded images
        if (znsStore.step2Data?.uploadedImages && znsStore.step2Data?.uploadedImages.length > 0) {
          setFieldValue('isHasImage' as any, 'true');
        }
      }, 100);
    }
  }
};

const handleSubmitData = async (value: any, approved: boolean = false) => {
  const overlayLoading = useOverLayLoadingStore();
  overlayLoading.toggleLoading(true);
  try {
    let fileUploads = [];
    if (approved) {
      fileUploads = znsStore.step2Data.file;
    } else {
      if (imgRadioValue.value === ZnsConfigImg.Logo) {
        fileUploads = [
          fileUploadLogoLightRef.value?.rawFile
            ? {
                ...fileUploadLogoLightRef.value.rawFile,
                template_type_id: ZnsTypeImg.LogoLight,
              }
            : null,
          fileUploadLogoDarkRef.value?.rawFile
            ? {
                ...fileUploadLogoDarkRef.value.rawFile,
                template_type_id: ZnsTypeImg.LogoDark,
              }
            : null,
        ].filter(Boolean);
      } else if (
        imgRadioValue.value === ZnsConfigImg.UploadImage &&
        fileUploadImagesRef.value?.rawFiles
      ) {
        fileUploads = fileUploadImagesRef.value.rawFiles.map((theFile: any) => {
          return {
            ...theFile,
            template_type_id: ZnsTypeImg.Image,
          };
        });
      }
    }

    if (!fileUploads) {
      fileUploads = [];
    }

    await znsStore.setValueStep2({
      ...value,
      file_type: imgRadioValue.value,
      file: fileUploads,
    });

    const id = await znsStore.uploadZNS();

    if (id) {
      if (props.type !== PageType.Details) {
        overlayLoading.toggleLoading(false);
        router.push({
          name: ROUTE_NAME.DETAILS_ZNS,
          params: {
            id,
          },
        });
      }
    }
    overlayLoading.toggleLoading(false);
  } catch (error) {
    overlayLoading.toggleLoading(false);
    console.error('Error in onSubmitAll:', error);
  }
};

const onSubmitAll = handleSubmit(async (value) => {
  handleSubmitData(value);
});

const onSubmit = async () => {
  checkValidateSubmit.value = [];
  for (let i = 0; i < technical_config.value.length; i += 1) {
    checkTemplate(
      i,
      getMaxLengthParameterZns((technical_config.value[i].value as any).zalo_data_field),
      true,
    );
  }

  onSubmitAll();
  fileUploadImagesRef.value?.clearError();
};
//#endregion

//#region Watcher
watch(imgRadioValue, (newValue: any) => {
  if (imgRadioValue.value === ZnsConfigImg.UploadImage) {
    znsStore.toggleMainButton(false);
  }
  validationSchema.value = createValidationSchema(newValue as ZnsConfigImg);
  // validate();
});

watch(
  () => znsStore.isAddMainButton,
  () => {
    setFieldValue('mainType', ZnsButtonType.GoToEnterprisePage);
    setFieldValue('mainContent', 'Tìm hiểu thêm');
  },
);

watch(
  () => znsStore.isAddSubButton,
  () => {
    setFieldValue('subType', ZnsButtonType.GoToEnterprisePage);
    setFieldValue('subContent', 'Tìm hiểu thêm');
  },
);

watch(
  () => znsStore.active,
  (newValue: number) => {
    if (newValue === 2 && znsStore.znsType === ZnsType.Voucher) {
      if (znsStore.step2Data?.file_type) {
        imgRadioValue.value = znsStore.step2Data.file_type;
      } else {
        imgRadioValue.value = ZnsConfigImg.Logo;
      }
      setValue();
      getParamTag(valueTexts.value);
    }
  },
  {
    immediate: true,
  },
);

watch(
  () => values,
  (newValue: any) => {
    if (znsStore.active === 2 && znsStore.znsType === ZnsType.Voucher) {
      znsStore.setValueStep2({
        ...newValue,
      });
    }
  },
  {
    deep: true,
    immediate: true,
  },
);
//#endregion

watchDebounced(
  valueTexts,
  () => {
    getParamTag(valueTexts.value);
  },
  { debounce: DEBOUNCE_TIME },
);

defineExpose({
  setValue,
  onSubmit,
  saveValues,
});
</script>

<template>
  <!-- Main container for the entire form -->
  <div class="w-[100%] px-[4%] flex justify-between pt-[40px] mb-[70px] text-sm">
    <!-- Left side - Form content -->
    <div class="pr-14 w-full">
      <div class="">
        <div class="text-[14px]">
          <span class="font-semibold">Lưu ý:</span>
          {{ GUIDE_TEXT }}
        </div>

        <!-- Main form section -->
        <el-form class="pb-[15px]" label-position="top">
          <!-- Upload section -->
          <div class="my-4">
            <!-- Choose option -->
            <div class="flex gap-x-10">
              <div class="flex items-center">
                <RadioButton
                  v-model="imgRadioValue"
                  id="config-step-radio-bg-img"
                  type="radio"
                  inputId="backgroundImageVoucher"
                  :disabled="props.type === PageType.Details"
                  :value="ZnsConfigImg.Logo"
                  @change="handleChangeImgOption()"
                />
                <label
                  for="backgroundImageVoucher"
                  class="ml-2 select-none"
                  :class="type === PageType.Details ? '' : 'cursor-pointer'"
                  >Dùng Logo</label
                >
              </div>
              <div class="flex items-center">
                <RadioButton
                  v-model="imgRadioValue"
                  id="config-step-radio-bg-color"
                  type="radio"
                  inputId="backgroundColorVoucher"
                  :disabled="props.type === PageType.Details"
                  :value="ZnsConfigImg.UploadImage"
                  @change="handleChangeImgOption()"
                />
                <label
                  for="backgroundColorVoucher"
                  class="ml-2 select-none"
                  :class="type === PageType.Details ? '' : 'cursor-pointer'"
                  >Dùng hình ảnh</label
                >
              </div>
            </div>
            <div class="flex gap-5 my-2">
              <!-- Choose Logo -->
              <div v-if="imgRadioValue === ZnsConfigImg.Logo" class="w-2/3">
                <div>
                  <label for="" class="text-sm inline-block mb-[7px] text-[#6b7280] font-semibold"
                    >Giao diện sáng <span class="text-red-500 ml-0.5">*</span></label
                  >
                  <VUploadImageNew
                    id="zns-logo-light"
                    ref="fileUploadLogoLightRef"
                    fileSize="fileSizeLogoLight"
                    fileType="fileTypeLogoLight"
                    file="fileLogoLight"
                    fileWidth="fileWidthLogoLight"
                    fileHeight="fileHeightLogoLight"
                    :typeToValidate="ACCEPT_FILE_TYPE_LOGO"
                    :sizeToValidate="FileSize._15Mb"
                    :disabled="props.type === PageType.Details"
                    :requiredWidth="LOGO_PX_WIDTH"
                    :requiredHeight="LOGO_PX_HEIGHT"
                    @beforeUpload="
                      (newValue: any) => beforeUploadLogo(newValue, ZnsConfigImgType.LogoLight)
                    "
                    @uploadFile="
                      (files: any) => handleUploadLogo(files, ZnsTextConfigLogoType.Light)
                    "
                    @removeFile="handleRemoveLogo(ZnsTextConfigLogoType.Light)"
                  />
                </div>
                <div>
                  <label for="" class="text-sm inline-block mb-[7px] text-[#6b7280] font-semibold"
                    >Giao diện tối <span class="text-red-500 ml-0.5">*</span></label
                  >
                  <VUploadImageNew
                    isDark
                    id="zns-logo-dark"
                    ref="fileUploadLogoDarkRef"
                    fileSize="fileSizeLogoDark"
                    fileType="fileTypeLogoDark"
                    file="fileLogoDark"
                    fileWidth="fileWidthLogoDark"
                    fileHeight="fileHeightLogoDark"
                    :sizeToValidate="FileSize._15Mb"
                    :requiredWidth="LOGO_PX_WIDTH"
                    :requiredHeight="LOGO_PX_HEIGHT"
                    :typeToValidate="ACCEPT_FILE_TYPE_LOGO"
                    :disabled="props.type === PageType.Details"
                    @beforeUpload="
                      (newValue: any) => beforeUploadLogo(newValue, ZnsConfigImgType.LogoDark)
                    "
                    @uploadFile="
                      (files: any) => handleUploadLogo(files, ZnsTextConfigLogoType.Dark)
                    "
                    @removeFile="handleRemoveLogo(ZnsTextConfigLogoType.Dark)"
                  />
                </div>
              </div>
              <!-- Choose image -->
              <div v-if="imgRadioValue === ZnsConfigImg.UploadImage" class="w-2/3">
                <div class="">
                  <label for="" class="text-sm inline-block mb-[7px] text-[#6b7280] font-semibold"
                    >Upload hình ảnh <span class="text-red-500 ml-0.5">*</span></label
                  >
                  <VUploadMultipleImage
                    ref="fileUploadImagesRef"
                    isHasImage="isHasImage"
                    :disabled="props.type === PageType.Details"
                    :numberOfImage="MAX_FILE_MULTIPLE"
                    :sizeToValidate="FileSize._halfMb"
                    :typeToValidate="ACCEPT_FILE_TYPE_IMAGE"
                    :msgErrSize="ERROR_MESSAGE.LIMIT_SIZE_FILE(FileSizeLabel._halfMb, 'KB')"
                    :msgErrType="ERROR_MESSAGE.INVALID_FILE_ZNS_UPLOAD"
                    @uploadFile="handleUploadImages"
                    @removeFile="handleRemoveImage"
                  />
                </div>
              </div>
              <!-- Design suggestion -->
              <HintUploadImg
                :type="
                  imgRadioValue === ZnsConfigImg.Logo ? ZnsConfigImg.Logo : ZnsConfigImg.UploadImage
                "
              />
            </div>
          </div>

          <!-- Main title section -->
          <VElementInput
            name="title"
            size="default"
            :style="'w-[100%]'"
            :required="true"
            :label="'Tiêu đề'"
            :maxlength="VOUCHER_LENGTH.MAX.TITLE"
            :disabled="props.type === PageType.Details"
            :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
          />

          <!-- Main content section -->
          <el-form-item
            v-for="(noiDung, idx) in body"
            v-bind="{
              error: (errors as any)[`body[${idx}]`] || null,
            }"
            class="mt-[20px] relative"
            required
            :key="idx"
            :label="idx === 0 ? 'Nội dung' : ''"
            :disabled="props.type === PageType.Details"
          >
            <el-input
              v-model="noiDung.value"
              show-word-limit
              size="default"
              type="textarea"
              :maxlength="400"
              :style="'w-[100%]'"
              :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
              :disabled="props.type === PageType.Details"
            />
            <!-- Add/Remove content buttons -->
            <div class="absolute justify-start flex gap-0.5 pl-1 left-full top-[28px]">
              <RemoveIcon
                v-if="body.length > 1 && props.type !== PageType.Details"
                @click="removeBody(idx)"
              />
              <AddIcon
                v-if="body.length === idx + 1 && body.length < 4 && props.type !== PageType.Details"
                @click="pushBody('')"
              />
            </div>
          </el-form-item>
        </el-form>

        <!-- Table content section -->
        <div class="">
          <div class="text-[14px] text-[#6b7280] font-semibold">Nội dung bảng</div>
          <div class="grid grid-cols-2 gap-[5px] pt-[10px]">
            <div class="text-[13px] text-[#6b7280]">Tên hàng</div>
            <div class="text-[13px] text-[#6b7280]">Nội dung</div>
          </div>
          <div
            v-for="(row, idx) in table"
            class="grid grid-cols-2 gap-[5px] relative"
            :key="idx"
            :class="{
              'pt-[5px]': idx === 0,
              'pt-[20px]': idx !== 0,
            }"
          >
            <el-form-item
              v-bind="{
                error: (errors as any)[`table[${idx}].name`] || null,
              }"
              :disabled="props.type === PageType.Details"
            >
              <el-input
                v-model="(row.value as any).name"
                size="default"
                show-word-limit
                :maxlength="35"
                :style="'w-[100%]'"
                :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
                :disabled="props.type === PageType.Details"
              />
            </el-form-item>
            <el-form-item
              v-bind="{
                error: (errors as any)[`table[${idx}].param`] || null,
              }"
              :disabled="props.type === PageType.Details"
            >
              <el-input
                v-model="(row.value as any).param"
                size="default"
                show-word-limit
                :maxlength="90"
                :style="'w-[100%]'"
                :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
                :disabled="props.type === PageType.Details"
              />
            </el-form-item>
            <div
              class="absolute justify-start flex gap-0.5 pl-1 left-full"
              :class="{
                'top-[15px]': idx === 0,
                'top-[30px]': idx !== 0,
              }"
            >
              <RemoveIcon
                v-if="table.length > 1 && props.type !== PageType.Details"
                @click="removeRow(idx)"
              />
              <AddIcon
                v-if="
                  table.length === idx + 1 && table.length < 8 && props.type !== PageType.Details
                "
                @click="pushRow({ param: '', name: '' } as any)"
              />
            </div>
          </div>
        </div>

        <!-- Voucher information section -->
        <div class="">
          <div class="flex flex-wrap items-center text-sm text-[#6b7280] font-semibold pt-[15px]">
            Thông tin voucher <span class="pl-1 text-[#F10000]">*</span>
          </div>
          <div class="">
            <table class="table-auto w-full border-separate space-y-2">
              <tbody class="space-y-3">
                <!-- Voucher information -->
                <tr class="align-middle">
                  <td class="pr-4 py-2 w-1/3">
                    <label class="text-[13px] text-[#6b7280]">Giá trị voucher *</label>
                  </td>
                  <td class="py-2">
                    <div class="grid grid-cols-[60px_6fr] items-center gap-2">
                      <label class="text-[13px] text-[#6b7280]">Ưu đãi: </label>
                      <VElementInput
                        name="voucher_content.price_voucher"
                        :placeholder="getPlaceholder(PLACEHOLDER.ZNS.TYPE_PARAMS, type)"
                        :maxlength="VOUCHER_LENGTH.MAX.PRICE"
                        :style="'w-[100%]'"
                        :disabled="props.type === PageType.Details"
                      />
                    </div>
                  </td>
                </tr>

                <tr class="align-middle">
                  <td class="pr-4 py-2">
                    <label class="text-[13px] text-[#6b7280]">Điều kiện áp dụng *</label>
                  </td>
                  <td class="py-2">
                    <div class="grid grid-cols-[60px_6fr] items-center gap-2">
                      <label class="text-[13px] text-[#6b7280]">Áp dụng: </label>
                      <VElementInput
                        name="voucher_content.condition"
                        :placeholder="getPlaceholder(PLACEHOLDER.ZNS.TYPE_PARAMS, type)"
                        :maxlength="VOUCHER_LENGTH.MAX.CONDITION"
                        :style="'w-[100%]'"
                        :disabled="props.type === PageType.Details"
                      />
                    </div>
                  </td>
                </tr>

                <tr class="align-middle">
                  <td class="pr-4 py-2">
                    <label class="text-[13px] text-[#6b7280]">Ngày bắt đầu voucher</label>
                  </td>
                  <td class="py-2">
                    <VElementInput
                      id="voucher-start_date"
                      class="truong-du-lieu-mau"
                      size="default"
                      name="voucher_content.start_date"
                      :style="'w-[100%]'"
                      :required="true"
                      :maxlength="VOUCHER_LENGTH.MAX.START_DATE"
                      :placeholder="getPlaceholder(PLACEHOLDER.ZNS.TYPE_PARAMS, type)"
                      :disabled="props.type === PageType.Details"
                    />
                  </td>
                </tr>

                <tr class="align-middle">
                  <td class="pr-4 py-2">
                    <label class="flex flex-wrap items-center text-[13px] text-[#6b7280]"
                      >Hạn sử dụng *
                      <VTooltip>
                        <template #contentTemplate>
                          <p>
                            Nếu tham số không truyền cụ thể HH:MM, thời gian bắt đầu/kết thúc của
                            voucher trên ví QR được hiển thị MẶC ĐỊNH là 00:00 dd/mm/yyyy
                          </p>
                        </template>
                      </VTooltip>
                    </label>
                  </td>
                  <td class="py-2">
                    <VElementInput
                      class="truong-du-lieu-mau"
                      size="default"
                      name="voucher_content.end_date"
                      id="voucher-end_date"
                      :style="'w-[100%]'"
                      :required="true"
                      :maxlength="VOUCHER_LENGTH.MAX.END_DATE"
                      :placeholder="getPlaceholder(PLACEHOLDER.ZNS.TYPE_PARAMS, type)"
                      :disabled="props.type === PageType.Details"
                    />
                  </td>
                </tr>

                <tr class="align-middle">
                  <td class="pr-4 py-2">
                    <label class="text-[13px] text-[#6b7280]">Mã voucher *</label>
                  </td>
                  <td class="py-2">
                    <VElementInput
                      class="truong-du-lieu-mau"
                      size="default"
                      name="voucher_content.voucher_code"
                      :style="'w-[100%]'"
                      :required="true"
                      :maxlength="VOUCHER_LENGTH.MAX.CODE"
                      :placeholder="getPlaceholder(PLACEHOLDER.ZNS.TYPE_PARAMS, type)"
                      :disabled="props.type === PageType.Details"
                    />
                  </td>
                </tr>

                <tr class="align-middle">
                  <td class="pr-4 py-2 w-1/3">
                    <label class="text-[13px] text-[#6b7280]">Hiển thị *</label>
                  </td>
                  <td class="py-2">
                    <VElementDropdown
                      name="voucher_content.show_code"
                      :filterable="false"
                      :option="VOUCHER_SHOW_CODE_OPTIONS"
                      :placeholder="getPlaceholder(PLACEHOLDER.SELECT, type)"
                      :style="'w-[100%]'"
                      :disabled="props.type === PageType.Details"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Technical settings section -->
      <div class="">
        <div class="text-[14px] text-[#6b7280] font-semibold pt-[15px]">Cài đặt kỹ thuật</div>
        <div class="flex items-center gap-[5px] pt-[10px]">
          <div class="w-[30%] text-[13px] text-[#6b7280]">Tên tham số</div>
          <div class="w-[40%] text-[13px] text-[#6b7280]">
            Loại tham số và ký tự tối đa <span class="text-[#F10000]">*</span>
          </div>
          <div class="w-[30%] text-[13px] text-[#6b7280]">
            Nội dung mẫu <span class="text-[#F10000]">*</span>
          </div>
        </div>
        <div
          v-for="(setting, idx) in technical_config"
          class="flex items-center gap-[5px]"
          :key="idx"
          :class="{
            'pt-[5px]': idx === 0,
            'pt-[20px]': idx !== 0,
          }"
        >
          <VTooltip placement="top">
            <template #contentTemplate>
              {{ (setting.value as any).parameter_name }}
            </template>
            <template #trigger>
              <el-input
                v-model="(setting.value as any).parameter_name"
                class="h-[32px] !w-[30%]"
                size="default"
                :disabled="true"
                :style="'w-[100%]'"
                :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
              />
            </template>
          </VTooltip>

          <VTooltip
            placement="top"
            :content="getParameterTypeLabel((setting.value as any).zalo_data_field)"
            :disabled="props.type !== PageType.Details"
          >
            <template #trigger>
              <el-select-v2
                v-model="(setting.value as any).zalo_data_field"
                :options="PARAMETER_TYPES"
                class="!w-[40%]"
                :disabled="props.type === PageType.Details"
                @change="
                  checkTemplate(
                    idx,
                    getMaxLengthParameterZns((setting.value as any).zalo_data_field),
                  )
                "
              >
                <template #default="{ item }">
                  <el-tooltip placement="left" content>
                    <template #content>{{ item.label }}</template>
                    <span>{{ item.label }}</span>
                  </el-tooltip>
                </template>
              </el-select-v2>
            </template>
          </VTooltip>

          <el-form-item
            class="!w-[30%]"
            v-bind="{
              error: (setting.value as any).error || null,
            }"
          >
            <VTooltip
              placement="top"
              :content="(setting.value as any).default_data_field"
              :disabled="props.type !== PageType.Details"
            >
              <template #contentTemplate>
                {{ (setting.value as any).default_data_field }}
              </template>
              <template #trigger>
                <el-input
                  v-model="(setting.value as any).default_data_field"
                  size="default"
                  :maxlength="getMaxLengthParameterZns((setting.value as any).zalo_data_field)"
                  :style="'w-[100%]'"
                  :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
                  :disabled="props.type === PageType.Details"
                  :show-word-limit="true"
                  @input="
                    checkTemplate(
                      idx,
                      getMaxLengthParameterZns((setting.value as any).zalo_data_field),
                    )
                  "
                />
              </template>
            </VTooltip>
          </el-form-item>
        </div>
      </div>

      <!-- Button section -->
      <div class="">
        <div class="text-[14px] text-[#6b7280] font-semibold pt-[15px]">Thêm button</div>
        <!-- Main button section -->
        <div v-show="!znsStore.isAddMainButton" class="pt-[15px]">
          <VElementButton
            bgColor="#4F52FF"
            styleIcon="text-[20px] text-[#fff] mr-[7px]"
            icon="plus"
            styleButton="s"
            label="Thêm button chính"
            :styleButtonClass="'!w-[175px]'"
            :disabled="props.type === PageType.Details"
            @click="znsStore.toggleMainButton(znsStore.isAddMainButton)"
          />
        </div>
        <!-- Main button configuration -->
        <div v-if="znsStore.isAddMainButton" class="pt-[15px] relative">
          <div class="grid gap-[5px] grid-cols-3">
            <div class="text-[13px] text-[#6b7280]">
              Loại button <span class="text-[#F10000]">*</span>
            </div>
            <div class="text-[13px] text-[#6b7280]">
              Nội dung button <span class="text-[#F10000]">*</span>
            </div>
            <div class="text-[13px] text-[#6b7280]">
              {{ values.mainType === ZnsButtonType.Call ? 'Số điện thoại' : 'Đường dẫn liên kết' }}
              <span class="text-[#F10000]">*</span>
            </div>
          </div>
          <el-form class="grid gap-[5px] pt-[5px] grid-cols-3" label-position="top">
            <VElementDropdown
              name="mainType"
              :filterable="false"
              :option="BUTTON_TYPES"
              :style="'w-[100%]'"
              :isUseTooltip="true"
              :disabled="props.type === PageType.Details"
              @change="mainTypeChange"
            />
            <VElementInput
              class="truong-du-lieu-mau"
              size="default"
              name="mainContent"
              :style="'w-[100%]'"
              :required="true"
              :maxlength="30"
              :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
              :disabled="props.type === PageType.Details"
            />
            <VElementInput
              v-if="resetMainLink"
              size="default"
              name="mainLink"
              class="truong-du-lieu-mau"
              :type="values.mainType !== ZnsButtonType.Call ? '' : 'onlyNumber'"
              :style="'w-[100%]'"
              :required="true"
              :maxlength="values.mainType !== ZnsButtonType.Call ? 58 : 15"
              :placeholder="
                type === PageType.Details
                  ? ''
                  : values.mainType === ZnsButtonType.Call
                    ? PLACEHOLDER.TYPE
                    : PLACEHOLDER.TYPE_LINK
              "
              :disabled="props.type === PageType.Details"
              @change="mainPhoneChange"
            />
          </el-form>
          <div class="absolute justify-start flex gap-0.5 pl-1 left-full top-[47px]">
            <RemoveIcon
              v-if="
                znsStore.isAddMainButton &&
                imgRadioValue !== ZnsConfigImg.UploadImage &&
                props.type !== PageType.Details
              "
              @click="znsStore.toggleMainButton(znsStore.isAddMainButton)"
            />
          </div>
        </div>

        <!-- Sub button section -->
        <div v-show="!znsStore.isAddSubButton" class="pt-[15px]">
          <VElementButton
            text="black"
            bgColor="#DDDEE3"
            styleIcon="text-[20px] mr-[7px]"
            icon="plus"
            styleButton="s"
            label="Thêm button phụ"
            :styleButtonClass="'!w-[175px]'"
            :disabled="props.type === PageType.Details"
            @click="znsStore.toggleSubButton(znsStore.isAddSubButton)"
          />
        </div>
        <!-- Sub button configuration -->
        <div v-if="znsStore.isAddSubButton" class="pt-[15px] relative">
          <div class="grid gap-[5px] grid-cols-3">
            <div class="text-[13px] text-[#6b7280]">
              Loại button <span class="text-[#F10000]">*</span>
            </div>
            <div class="text-[13px] text-[#6b7280]">
              Nội dung button <span class="text-[#F10000]">*</span>
            </div>
            <div class="text-[13px] text-[#6b7280]">
              {{ values.subType === ZnsButtonType.Call ? 'Số điện thoại' : 'Đường dẫn liên kết' }}
              <span class="text-[#F10000]">*</span>
            </div>
          </div>
          <el-form class="grid gap-[5px] pt-[5px] grid-cols-3" label-position="top">
            <VElementDropdown
              name="subType"
              :filterable="false"
              :option="BUTTON_TYPES"
              :style="'w-[100%]'"
              :isUseTooltip="true"
              :disabled="props.type === PageType.Details"
              @change="subTypeChange"
            />
            <VElementInput
              size="default"
              name="subContent"
              :style="'w-[100%]'"
              :required="true"
              :maxlength="30"
              :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
              :disabled="props.type === PageType.Details"
            />
            <VElementInput
              v-if="resetSubLink"
              size="default"
              name="subLink"
              :type="values.subType !== ZnsButtonType.Call ? '' : 'onlyNumber'"
              :style="'w-[100%]'"
              :required="true"
              :maxlength="
                values.subType !== ZnsButtonType.Call
                  ? MAX_LENGTH_BUTTON_LINK
                  : MAX_LENGTH_BUTTON_PHONE
              "
              :placeholder="
                type === PageType.Details
                  ? ''
                  : values.subType === ZnsButtonType.Call
                    ? PLACEHOLDER.TYPE
                    : PLACEHOLDER.TYPE_LINK
              "
              :disabled="props.type === PageType.Details"
              @change="subPhoneChange"
            />
          </el-form>
          <div class="absolute justify-start flex gap-0.5 pl-1 left-full top-[47px]">
            <RemoveIcon
              v-if="znsStore.isAddSubButton && props.type !== PageType.Details"
              @click="znsStore.toggleSubButton(znsStore.isAddSubButton)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Preview section -->
    <ZnsWrapper>
      <VoucherType
        :type="ZnsPageType.Form"
        :mode="!znsStore.darkMode"
        :items="
          {
            title: values.title,
            body: values.body,
            table: values.table,
            voucher_content: values.voucher_content,
            show_code: values.show_code,
            primary_button: !znsStore.isAddMainButton ? false : { content: values.mainContent },
            secondary_button: !znsStore.isAddSubButton ? false : { content: values.subContent },
          } as any
        "
      >
        <template #image>
          <div v-if="imgRadioValue === ZnsConfigImg.Logo" class="pt-4 px-4 overflow-hidden">
            <div class="flex gap-x-2">
              <img
                v-show="!znsStore.darkMode && previewLogo.light"
                alt=""
                class="max-w-full max-h-14 w-fit h-full mb-2.5 object-contain"
                :src="previewLogo.light"
              />
              <img
                v-show="znsStore.darkMode && previewLogo.dark"
                alt=""
                class="max-w-full max-h-14 w-fit h-full mb-2.5 object-contain"
                :src="previewLogo.dark"
              />
            </div>
          </div>
          <div
            v-show="imgRadioValue === ZnsConfigImg.UploadImage"
            class="overflow-hidden"
            :class="[
              {
                'pt-4': uploadedImages.length === 0,
              },
              {
                'pb-4': uploadedImages.length === 1,
              },
            ]"
          >
            <SlideImage
              v-if="uploadedImages.length > 0"
              autoplay
              showPagination
              :images="uploadedImages"
            />
          </div>
        </template>
      </VoucherType>
    </ZnsWrapper>
  </div>
</template>

<style scoped>
:deep(.truong-du-lieu-mau .el-form-item__error) {
  position: unset;
}
</style>
