<script setup lang="ts">
import { ZnsConfigImg } from '@/enums/zns';
import { SUGGEST_IMAGE_LINK } from '../index.constants';

withDefaults(
  defineProps<{
    type?: ZnsConfigImg;
  }>(),
  {
    type: ZnsConfigImg.Logo,
  },
);
</script>

<template>
  <div class="">
    <div class="text-xs max-h-fit flex-1 h-full bg-[rgba(255,251,235,1)] p-3">
      <p class="font-semibold">Gợi ý thiết kế</p>
      <!-- Hint logo -->
      <div v-if="type === ZnsConfigImg.Logo" class="">
        <ul class="my-4 space-y-1">
          <li class="flex items-start gap-x-1">
            <img src="@/assets/icons/check.svg" alt="" />
            <span> Upload logo theo định dạng PNG</span>
          </li>
          <li class="flex items-start gap-x-1">
            <img src="@/assets/icons/check.svg" alt="" />
            <span> Kích thước 400x96 pixel </span>
          </li>
          <li class="flex items-start gap-x-1">
            <img src="@/assets/icons/check.svg" alt="" />
            <span> Logo nên được căn lề trái </span>
          </li>
          <li class="flex items-start gap-x-1">
            <img src="@/assets/icons/check.svg" alt="" />
            <span> Tiêu chuẩn kích thước logo </span>
          </li>
        </ul>
        <div class="">
          <img src="@/assets/images/logo-example.png" alt="" />
          <div class="">
            <div class="">
              <span class="inline-block w-3 h-3 mr-2 bg-[rgba(92,125,255,1)]"> </span>
              <span>Phạm vi logo</span>
            </div>
            <div class="">
              <span class="inline-block w-3 h-3 mr-2 bg-[rgba(255,54,74,1)]"> </span>
              <span>Phạm vi an toàn</span>
            </div>
          </div>
        </div>
      </div>
      <!-- Hint image -->
      <div v-if="type === ZnsConfigImg.UploadImage" class="">
        <ul class="my-4 space-y-1">
          <li class="flex items-start gap-x-1">
            <img src="@/assets/icons/check.svg" alt="" />
            <span> Upload hình ảnh theo định dạng PNG, JPG</span>
          </li>
          <li class="flex items-start gap-x-1">
            <img src="@/assets/icons/check.svg" alt="" /> <span> Tỷ lệ 16:9 </span>
          </li>
          <li class="flex items-start gap-x-1">
            <img src="@/assets/icons/check.svg" alt="" />
            <span> Dung lượng dưới 500KB </span>
          </li>
        </ul>
        <p>
          Hướng dẫn các quy định xét duyệt hình ảnh
          <a target="_blank" class="text-blue-500 hover:underline" :href="SUGGEST_IMAGE_LINK"
            >tại đây</a
          >
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.disabled-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 10;
  pointer-events: all;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}
</style>
