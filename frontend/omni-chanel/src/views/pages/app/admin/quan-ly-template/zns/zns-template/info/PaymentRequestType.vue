<template>
  <ZnsCard
    :mode="mode"
    :type="type"
    :typeStep="ZnsType.Payment"
    :data="items"
  >
    <div class="text-[13px]" :class="props.type === ZnsPageType.Index ? 'overflow-auto' : ''">
      <slot name="image"></slot>

      <div class="">
        <div class="font-bold leading-[26px]">
          {{ props.items.title }}
        </div>
        <!-- :class="[props.type === ZnsPageType.Index  ? 'max-h-[280px]' : '']" -->
        <div
          class="flex flex-col leading-[24px]"
        >
          <div
            v-for="(item, index) in props.items.body"
            :key="index"
            v-html="getItemHTML(item)"
            class="mb-[12px]"
          ></div>
          <div v-for="(item, index) in props.items.table" :key="index" class="flex gap-[8px]">
            <div class="w-[86px] break-words overflow-wrap">
              {{ item.name }}
            </div>
            <div class="max-w-[70%] break-words overflow-wrap font-bold">
              {{ item.param }}
            </div>
          </div>
        </div>
        <div class="mt-3">
          <TransactionCard
            :isDarkMode="!mode"
            :data="{
              titleHeader: CONTENT_PREVIEW_PAYMENT.TITLE_HEADER,
              tableData: [
                {
                  label: CONTENT_PREVIEW_PAYMENT.BANK.BANK_NAME,
                  value: props.items?.bank_content?.bank_name,
                },
                {
                  label: CONTENT_PREVIEW_PAYMENT.BANK.ACCOUNT_NAME,
                  value: props.items?.bank_content?.account_name,
                  isUnColor: true,
                },
                {
                  label: CONTENT_PREVIEW_PAYMENT.BANK.ACCOUNT_NUMBER,
                  value: props.items?.bank_content?.account_number,
                },
                {
                  label: CONTENT_PREVIEW_PAYMENT.BANK.PRICE,
                  value: props.items?.bank_content?.price,
                },
                {
                  label: CONTENT_PREVIEW_PAYMENT.BANK.CONTENT,
                  value: props.items?.bank_content?.content,
                },
              ],
            }"
          >
          </TransactionCard>
        </div>
        <div
          v-if="props.items.primary_button || props.items.secondary_button"
          class="w-full flex flex-col space-y-3 mt-3 rounded-b-[6px]"
        >
          <VElementButton
            v-if="props.items.primary_button"
            :label="props.items.primary_button.content || ''"
            bgColor="#4F52FF"
          />
          <VElementButton
            v-if="props.items.secondary_button"
            :label="props.items.secondary_button.content || ''"
            text="black"
            bgColor="#DDDEE3"
          />
        </div>
      </div>
    </div>
  </ZnsCard>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { ZnsPageType, ZnsType } from '@/enums/zns';
import VElementButton from '@/components/base/ElementComponent/VElementButton.vue';
import { getItemHTML } from '../index.utils';
import ZnsCard from './ZnsCard.vue';
import TransactionCard from './TransactionCard.vue';
import { CONTENT_PREVIEW_PAYMENT } from '../index.constants';

const props = withDefaults(
  defineProps<{
    type?: ZnsPageType;
    items: any;
    mode?: boolean;
  }>(),
  {
    mode: true,
  },
);
const countButton = ref(0);

onMounted(() => {
  if (props.items.secondary_button || props.items.primary_button) {
    countButton.value = 1;
  }
  if (props.items.primary_button && props.items.secondary_button) {
    countButton.value = 2;
  }
});
</script>

<style lang="scss" scoped>
.card-info {
  padding: 15px 0 0 18px;
}

.el-button + .el-button {
  margin-left: 0px;
}
</style>
