<script setup lang="ts">
import { ZnsType, ZnsPageType, ZnsVoucherShowCode } from '@/enums/zns';
import { CONTENT_PREVIEW_VOUCHER } from '../index.constants';

const props = withDefaults(
  defineProps<{
    type?: ZnsPageType;
    typeStep?: ZnsType;
    logo?: string;
    isDarkMode?: boolean;
    customButton?: string;
    data?: {
      iconHeader?: any;
      titleHeader?: string;
      tableData?: {
        label: string;
        value: string | number;
        isBold?: boolean;
        isUnColor?: boolean;
      }[];
      voucherCode?: string;
      showCode?: ZnsVoucherShowCode;
    };
  }>(),
  {
    type: ZnsPageType.Form,
    typeStep: ZnsType.Payment,
    customButton: '',
    data: () => ({
      iconHeader: '@/assets/icons/logo-template.svg',
      titleHeader: 'Thanh toán ngay',
      tableData: [],
    }),
  },
);
</script>

<template>
  <div
    id="transactionCard"
    class="transaction-card"
    :style="{
      '--card-bg': isDarkMode ? '#17252E' : '#f1fbfe ',
      '--card-border': isDarkMode ? '#2d2d2d' : '#e5f0ff',
      '--header-bg': isDarkMode ? '#171717' : '#ffffff',
      '--text-color-first': isDarkMode ? '#B9B9C3' : '#69686d',
      '--text-color': isDarkMode ? '#3758f9' : '#3758f9',
      '--text-unset-color': isDarkMode ? '#ffffff' : 'unset',
      '--text-unset-color-voucher': isDarkMode ? '#ffffff' : '#000',
    }"
  >
    <div class="transaction-card__header">
      <div class="transaction-card__icon-wrapper">
        <!-- Payment -->
        <img
          v-if="typeStep === ZnsType.Payment"
          src="@/assets/icons/payment.svg"
          alt="Icon"
          class="transaction-card__icon"
        />

        <!-- Voucher -->
        <img
          v-if="typeStep === ZnsType.Voucher"
          src="@/assets/icons/wallet-money.svg"
          alt="Icon"
          class="transaction-card__icon"
        />
        <h4 href="#" class="transaction-card__title">{{ data?.titleHeader }}</h4>
      </div>
      <img
        src="@/assets/icons/arrow-right-simple.svg"
        alt="Icon"
        class="transaction-card__icon-arrow"
      />
    </div>

    <div
      class="transaction-card__body"
      :class="{ 'transaction-card__body--voucher': typeStep === ZnsType.Voucher }"
    >
      <!-- Payment -->
      <div
        v-if="typeStep === ZnsType.Payment && data?.tableData"
        class="transaction-card__table-wrapper"
      >
        <table class="transaction-card__table">
          <tr v-for="(row, index) in data?.tableData" :key="index" class="transaction-card__row">
            <td
              :class="{ 'transaction-card__cell--bold': row.isBold }"
              class="transaction-card__cell"
            >
              {{ row.label }}
            </td>
            <td
              :class="{
                'transaction-card__cell--bold': row.isBold,
                'transaction-card__cell--uncolor': row.isUnColor,
              }"
              class="transaction-card__cell"
            >
              {{ row.value }}
            </td>
          </tr>
        </table>
      </div>

      <!-- Voucher -->
      <div
        v-if="typeStep === ZnsType.Voucher && data?.tableData"
        class="transaction-card__table-wrapper transaction-card__table-wrapper--voucher"
      >
        <div class="transaction-card__table">
          <div v-for="(row, index) in data?.tableData" :key="index" class="transaction-card__row">
            <span
              class="transaction-card__cell"
              :class="{
                'transaction-card__cell--bold': row.isBold,
                'transaction-card__cell--uncolor': row.isUnColor,
              }"
              >{{ row.label }}</span
            >
            <span
              class="transaction-card__cell"
              :class="{
                'transaction-card__cell--bold': row.isBold,
                'transaction-card__cell--uncolor': row.isUnColor,
              }"
              >{{ row.value }}</span
            >
          </div>
        </div>

        <div class="transaction-card__voucher-code">
          <img
            v-if="data?.showCode === ZnsVoucherShowCode.Barcode"
            src="@/assets/icons/barcode.svg"
            alt="voucher-code-type-img"
            class="transaction-card__voucher-code-img"
          />
          <img
            v-if="data?.showCode === ZnsVoucherShowCode.QR"
            src="@/assets/images/qr-code.jpg"
            alt="voucher-code-type-img"
            class="transaction-card__voucher-code-img"
          />
          <span class="transaction-card__voucher-code-text"
            >{{ CONTENT_PREVIEW_VOUCHER.CONTENT.VOUCHER_CODE }}
            <span class="transaction-card__voucher-code-text-highlight">{{
              data?.voucherCode
            }}</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.transaction-card {
  background: var(--card-bg);
  border-radius: 8px;
  border: 1px solid var(--card-border);
  overflow: hidden;
}

.transaction-card__header {
  background-color: var(--header-bg);
  padding: 8px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  border-bottom: 1px solid var(--card-border);
}

.transaction-card__icon-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.transaction-card__icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.transaction-card__icon-arrow {
  width: 16px;
  height: 17px;
  object-fit: contain;
}

.transaction-card__title {
  font-size: 16px;
  font-weight: 600;
  color: #3758f9;
  text-decoration: none;
}

.transaction-card__body {
  padding: 12px;
}

.transaction-card__subtitle {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin-bottom: 12px;
}

.transaction-card__table {
  width: 100%;
  border-collapse: collapse;
}

.transaction-card__row {
  padding: 4px 0;
}

.transaction-card__cell:first-child {
  width: 40%;
  vertical-align: top;
  color: var(--text-color-first);
}

.transaction-card__cell:nth-child(2) {
  color: var(--text-color);
}

.transaction-card__cell {
  padding: 4px 0;
  font-size: 14px;
  line-height: 20px;
}

.transaction-card__cell--bold {
  font-weight: 600;
  font-size: 18px;
}

.transaction-card__cell--uncolor {
  color: var(--text-unset-color) !important;
}

.transaction-card__custom-content {
  margin-top: 16px;
}

.transaction-card__body--voucher {
  padding: 0;
}

.transaction-card__table-wrapper--voucher {
  .transaction-card__table {
    border-bottom: 1px dashed var(--card-border);
    padding: 12px;
  }

  .transaction-card__row {
    width: 100%;
    display: flex;
    justify-self: start;
    flex-wrap: wrap;
    gap: 4px;
    color: var(--text-color-first);
  }

  .transaction-card__cell:nth-child(2) {
    color: var(--text-color-first);
  }

  .transaction-card__cell {
    width: fit-content;
    padding: 0;
  }

  .transaction-card__cell--uncolor {
    color: var(--text-unset-color-voucher) !important;
  }

  .transaction-card__voucher-code {
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    border-top: 1px dashed var(--card-border);
  }

  .transaction-card__voucher-code-img {
    max-height: 150px;
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .transaction-card__voucher-code-text-highlight {
    color: var(--text-color);
    font-weight: bold;
  }
}
</style>
