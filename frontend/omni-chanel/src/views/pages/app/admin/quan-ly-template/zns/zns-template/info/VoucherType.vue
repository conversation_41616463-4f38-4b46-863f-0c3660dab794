<template>
  <ZnsCard :mode="mode" :type="type" :typeStep="ZnsType.Voucher" :data="items">
    <div class="text-[13px]" :class="props.type === ZnsPageType.Index ? 'overflow-auto' : ''">
      <slot name="image"></slot>

      <div class="px-4 pb-4">
        <div class="leading-[26px] font-bold">{{ items.title }}</div>
        <!-- :class="[props.type === ZnsPageType.Index ? 'max-h-[280px]' : '']" -->
        <div class="flex flex-col leading-[24px]">
          <div
            v-for="(item, index) in props.items.body"
            :key="index"
            v-html="getItemHTML(item)"
            class="mb-[12px]"
          ></div>
          <div v-for="(item, index) in props.items.table" :key="index" class="flex gap-[8px]">
            <div class="w-[86px] break-words overflow-wrap">
              {{ item.name }}
            </div>
            <div class="max-w-[70%] break-words overflow-wrap font-bold">
              {{ item.param }}
            </div>
          </div>
        </div>
        <div class="mt-3">
          <TransactionCard
            :isDarkMode="!mode"
            :typeStep="ZnsType.Voucher"
            :showCode="props.items?.voucher_content?.show_code"
            :data="{
              titleHeader: CONTENT_PREVIEW_VOUCHER.TITLE_HEADER,
              tableData: [
                {
                  label: CONTENT_PREVIEW_VOUCHER.CONTENT.PRICE,
                  value: props.items?.voucher_content?.price_voucher,
                  isBold: true,
                  isUnColor: true,
                },
                {
                  label: CONTENT_PREVIEW_VOUCHER.CONTENT.CONDITION,
                  value: props.items?.voucher_content?.condition,
                },
                {
                  label: CONTENT_PREVIEW_VOUCHER.CONTENT.EXPIRED_DATE,
                  value: `${props.items?.voucher_content?.start_date ? props.items?.voucher_content?.start_date + ' - ' : ''}${props.items?.voucher_content?.end_date ? props.items?.voucher_content?.end_date : ''}`,
                },
              ],
              voucherCode: props.items?.voucher_content?.voucher_code,
              showCode: props.items?.voucher_content?.show_code,
            }"
          >
          </TransactionCard>
        </div>
        <div class="w-full flex flex-col space-y-3 mt-3 rounded-b-[6px]">
          <VElementButton
            v-if="props.items.primary_button"
            :label="props.items.primary_button.content || ''"
            bgColor="#4F52FF"
          />
          <VElementButton
            v-if="props.items.secondary_button"
            :label="props.items.secondary_button.content || ''"
            text="black"
            bgColor="#DDDEE3"
          />
        </div>
      </div>
    </div>
  </ZnsCard>
</template>

<script setup lang="ts">
import VElementButton from '@/components/base/ElementComponent/VElementButton.vue';
import { onMounted, ref, watchEffect } from 'vue';
import ZnsCard from './ZnsCard.vue';
import { ZnsPageType, ZnsType } from '@/enums/zns';
import { getItemHTML } from '../index.utils';
import { CONTENT_PREVIEW_VOUCHER } from '../index.constants';
import TransactionCard from './TransactionCard.vue';
import type { ZNSTemplate } from '@/models/zns';

const props = withDefaults(
  defineProps<{
    type?: ZnsPageType;
    items: ZNSTemplate;
    mode?: boolean;
  }>(),
  {
    mode: true,
  },
);

const countButton = ref(0);

onMounted(() => {
  if (props.items.secondary_button || props.items.primary_button) {
    countButton.value = 1;
  }
  if (props.items.primary_button && props.items.secondary_button) {
    countButton.value = 2;
  }
});

watchEffect(() => {
  if (props.items.secondary_button || props.items.primary_button) {
    countButton.value = 1;
  }
  if (props.items.primary_button && props.items.secondary_button) {
    countButton.value = 2;
  }
});
</script>

<style lang="scss" scoped>
.card-info {
  padding: 15px 0 0 18px;
}

.el-button + .el-button {
  margin-left: 0px;
}
</style>
