<template style="position: relative">
  <!-- Header -->
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:building-warehouse" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item id="agent-breadcrumb" :to="{ path: ROUTE_PATH.USER_INFO_AGENT }"
          >Thông tin đại lý</el-breadcrumb-item
        >
        <el-breadcrumb-item>{{ getLabelForm(type) }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>

  <!-- Tabs -->
  <BaseTabs :tabs :active-tab="activeTab" tabClass="w-[94%]" @click-tab="handleTabChanged">
    <template #tab-1>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <div class="mx-[10px] flex justify-center h-[calc(100vh-322px)] mb-[50px] overflow-auto">
          <Form :type="props.type" :values="values" />
        </div>
      </div>
    </template>
    <template #tab-2>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <FormDetailsAgent
          ref="priceBoardRef"
          :customer-id="user.user?.id_agent ?? 0"
          :type
          :router-details="ROUTE_NAME.USER_INFO_AGENT"
          @onConfirmCancel="back"
          @on-saved="back"
        />
      </div>
    </template>
    <template #tab-3>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <ContractTab ref="contractTabRef" />
      </div>
    </template>
  </BaseTabs>

  <!-- Footer -->
  <div
    class="w-[100%] flex justify-end px-[15px] py-[9px] border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] absolute bottom-0 rounded-b-[14px]"
  >
    <VElementButton
      v-if="type !== PageType.Details"
      styleButton="s"
      label="Hủy"
      id="agent-cancel-button"
      :bgColor="color.closeButton"
      @click="handleClickCancel"
    />
    <VElementButton
      v-if="type !== PageType.Details"
      label="Lưu"
      styleButton="s"
      id="agent-save-button"
      :bgColor="color.main"
      @click="onSubmit"
    />
    <VElementButton
      v-else-if="activeTab !== TabValue.Tab3"
      label="Cập nhật"
      styleButton="s"
      id="agent-update-button"
      :bgColor="color.main"
      @click="openUpdate"
    />
  </div>

  <PopupCancelConfirm
    v-model:popupVisible="isShowConfirmPopup"
    @onClose="isShowConfirmPopup = false"
    @onConfirm="handleConfirmCancel"
  />
</template>

<script setup lang="ts">
import { onMounted, ref, watch, inject, nextTick, onBeforeUnmount } from 'vue';
import * as yup from 'yup';
import { useForm } from 'vee-validate';
import { useRoute, useRouter } from 'vue-router';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import PopupCancelConfirm from '@/components/base/common/PopupCancelConfirm.vue';
import { ROUTE_NAME, TEXT, ROUTE_PATH, REGEX, TabValue } from '@/shared';
import { getLabelForm, getNullableString } from '@/utils';
import { PageType } from '@/enums/common';
import { cloneDeep } from 'lodash';
import type { TTab } from '../../../admin/cai-dat/bang-gia/index.type';
import { useUserSession } from '@/store/userSession';
import ContractTab from './components/ContractTab.vue';
import Form from '../../../admin/dai-ly/components/Form.vue';
import BaseTabs from '../../../admin/cai-dat/bang-gia/components/BaseTabs.vue';
import FormDetailsAgent from '../../../admin/cai-dat/bang-gia/bang-gia-ban/FormDetailsAgent.vue';
import { useTab } from '@/store/useTab';

const props = defineProps<{
  type: PageType;
}>();

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const contractTabRef = ref();
const tabStore = useTab();
const user: any = useUserSession();

//#region Tabs
let oldData: any;
const activeTab = ref<TabValue>(TabValue.Tab1);
const selectedTab = ref<TabValue>(TabValue.Tab1);
let isClickedCancelButton = false;
const priceBoardRef = ref();

const tabs: TTab[] = [
  {
    label: 'Thông tin chung',
    value: TabValue.Tab1,
  },
  {
    label: 'Bảng giá',
    value: TabValue.Tab2,
  },
  {
    label: 'Hợp đồng',
    value: TabValue.Tab3,
  },
];

const resetFormValue = () => {
  setFieldValue('agent_name', oldData.agent_name);
  setFieldValue('agent_code', oldData.agent_code);
  setFieldValue('address', oldData.address);
  setFieldValue('tax_code', oldData.tax_code);
  setFieldValue('agent_phone', oldData.agent_phone);
  setFieldValue('agent_email', oldData.agent_email);
  setFieldValue('contact_name', oldData.contact_name);
  setFieldValue('contact_phone', oldData.contact_phone);
  setFieldValue('contact_email', oldData.contact_email);
  setFieldValue('agent_type', oldData.agent_type);
  setFieldValue('account_number', oldData.account_number);
  setFieldValue('status', oldData.status);
  setFieldValue('message_usage_limit', oldData.message_usage_limit);
  setFieldValue('contract_deposit', oldData.contract_deposit);
  setFieldValue('referral_code', oldData.referral_code);
};

const triggerTabApi = async (tabValue: TabValue) => {
  await nextTick();
  if (tabValue === TabValue.Tab1) {
    await getDetail();
  } else if (tabValue === TabValue.Tab2) {
    priceBoardRef.value?.getDetail();
    priceBoardRef.value?.resetErrors();
  } else if (tabValue === TabValue.Tab3 && contractTabRef.value) {
    contractTabRef.value.getContracts();
  }
};

const handleTabChanged = async (tabValue: TabValue) => {
  isClickedCancelButton = false;
  await triggerTabApi(tabValue);
  const isChangedTab2 = await priceBoardRef.value.isChangedValue();
  if (activeTab.value === TabValue.Tab1) {
    priceBoardRef.value?.resetOldValue();
  }
  if (activeTab.value !== TabValue.Tab2) {
    priceBoardRef.value?.clearOldTab();
  }
  if (
    (activeTab.value === TabValue.Tab1 && JSON.stringify(oldData) !== JSON.stringify(values)) ||
    (activeTab.value === TabValue.Tab2 && isChangedTab2)
  ) {
    isShowConfirmPopup.value = true;
    selectedTab.value = tabValue;
  } else {
    activeTab.value = tabValue;
    router.push({
      name: ROUTE_NAME.USER_INFO_AGENT,
      params: {
        id: user.user?.id_agent,
      },
    });
    tabStore.setAgentActiveTab(tabValue);
  }
};

const handleClickCancel = async () => {
  isClickedCancelButton = true;
  const isChangedTab2 = await priceBoardRef.value?.isChangedValue();
  if (
    (activeTab.value === TabValue.Tab1 && JSON.stringify(oldData) !== JSON.stringify(values)) ||
    (activeTab.value === TabValue.Tab2 && isChangedTab2)
  ) {
    isShowConfirmPopup.value = true;
  } else {
    back();
  }
};

const handleConfirmCancel = () => {
  isShowConfirmPopup.value = false;

  if (selectedTab.value !== activeTab.value && !isClickedCancelButton) {
    if (activeTab.value === TabValue.Tab1) {
      resetFormValue();
    } else {
      priceBoardRef.value.handleConfirmCancelParent(selectedTab.value !== activeTab.value);
    }
    activeTab.value = selectedTab.value;
    router.push({
      name: ROUTE_NAME.USER_INFO_AGENT,
      params: {
        id: user.user?.id_agent,
      },
    });
    tabStore.setAgentActiveTab(+selectedTab.value);
  } else {
    back();
  }
};
//#endregion

//#region Router
const route = useRoute();
const router = useRouter();

const back = () => {
  router.push({
    name: ROUTE_NAME.USER_INFO_AGENT,
  });
};

const openUpdate = () => {
  router.push({
    name: ROUTE_NAME.USER_INFO_AGENT_UPDATE,
    params: {
      id: user.user?.id_agent,
    },
  });
};
//#endregion

//#region Confirm
const isShowConfirmPopup = ref(false);
//#endregion

//#region Form

const validationSchema = yup.object({
  agent_name: yup.string().required('Tên đại lý không được để trống').trim(),
  address: yup.string().nullable(),
  tax_code: yup.string().nullable(),
  agent_phone: yup.string().nullable(),
  agent_email: yup
    .string()
    .email(TEXT.INVALID_EMAIL)
    .notRequired()
    .test('valid-email', TEXT.INVALID_EMAIL, (value) => {
      if (value === undefined || value === null || value === '') {
        return true;
      }
      const emailRegex = REGEX.EMAIL;
      return emailRegex.test(value);
    })
    .when((email, schema) => {
      if (!email[0]) {
        return schema;
      }
      return schema.matches(REGEX.EMAIL, TEXT.INVALID_EMAIL);
    }),
  contact_name: yup.string().required('Người liên hệ không được để trống').trim(),
  contact_phone: yup.string().required('SĐT người liên hệ không được để trống').trim(),
  contact_email: yup
    .string()
    .email(TEXT.INVALID_EMAIL)
    .required('Email người liên hệ không được để trống')
    .test('valid-email', TEXT.INVALID_EMAIL, (value) => {
      if (value === undefined || value === null || value === '') {
        return true;
      }
      const emailRegex = REGEX.EMAIL;
      return emailRegex.test(value);
    })
    .when((email, schema) => {
      if (!email[0]) {
        return schema;
      }
      return schema.matches(REGEX.EMAIL, TEXT.INVALID_EMAIL);
    }),
  agent_type: yup.number().nullable(),
  message_usage_limit: yup
    .string()
    .trim()
    .required('Hạn mức sử dụng dịch vụ gửi tin không được để trống'),
  contract_deposit: yup.string().trim().nullable(),
  referral_code: yup
    .string()
    .trim()
    .nullable()
    .test('alphanumeric', 'Mã giới thiệu chỉ được chứa chữ và số', (value) => {
      if (value === undefined || value === null || value === '') {
        return true;
      }
      return /^[a-zA-Z0-9]+$/.test(value);
    }),
});

const { values, handleSubmit, setFieldValue } = useForm({
  validationSchema,
});

const submitAgentForm = async () => {
  overlayLoading.toggleLoading(true);
  const params = {
    agent_name: getNullableString(values.agent_name),
    address: getNullableString(values.address),
    tax_code: getNullableString(values.tax_code),
    agent_phone: getNullableString(values.agent_phone),
    agent_email: getNullableString(values.agent_email),
    contact_name: getNullableString(values.contact_name),
    contact_phone: getNullableString(values.contact_phone),
    contact_email: getNullableString(values.contact_email),
    agent_type: values.agent_type,
    status: values.status,
    message_usage_limit: getNullableString(values.message_usage_limit),
    contract_deposit: getNullableString(values.contract_deposit),
    referral_code: getNullableString(values.referral_code),
  };

  await api
    .put(`/business/v1/api/admin/agent/${user.user?.id_agent}`, params)
    .then((response) => {
      if (response.data.code === 0) {
        overlayLoading.toggleLoading(false);
        toast('success', response.data.message);
        back();
      } else {
        overlayLoading.toggleLoading(false);
        toast('error', response.data.message);
      }
    })
    .catch(() => {
      overlayLoading.toggleLoading(false);
      toast('error', TEXT.ERROR_OCCURRED);
    });
};

const onSubmitForm = handleSubmit(async () => {
  submitAgentForm();
});

const onSubmit = async () => {
  if (props.type === PageType.Update && activeTab.value === TabValue.Tab1) {
    onSubmitForm();
  } else {
    await priceBoardRef.value?.updateDatasource();
    const isValidAll = priceBoardRef.value?.isValidAll();
    if (isValidAll) {
      await priceBoardRef.value.onSubmit();
    }
  }
};

const getDetail = function () {
  overlayLoading.toggleLoading(true);
  api
    .get(`/business/v1/api/admin/agent/${user.user?.id_agent}`)
    .then((response) => {
      if (response.data.code === 0) {
        const res = response.data.data;
        setFieldValue('agent_name', res.agent_name);
        setFieldValue('agent_code', res.agent_code);
        setFieldValue('address', res.address);
        setFieldValue('tax_code', res.tax_code);
        setFieldValue('agent_phone', res.agent_phone);
        setFieldValue('agent_email', res.agent_email);
        setFieldValue('contact_name', res.contact_name);
        setFieldValue('contact_phone', res.contact_phone);
        setFieldValue('contact_email', res.contact_email);
        setFieldValue('agent_type', res.agent_type);
        setFieldValue('account_number', res.account_number);
        setFieldValue('status', res.status);
        setFieldValue('message_usage_limit', res.message_usage_limit);
        setFieldValue('contract_deposit', res.contract_deposit);
        setFieldValue('referral_code', res.referral_code);
        oldData = cloneDeep(values);
        overlayLoading.toggleLoading(false);
      } else {
        overlayLoading.toggleLoading(false);
        toast('error', response.data.message);
      }
    })
    .catch(() => {
      overlayLoading.toggleLoading(false);
      toast('error', TEXT.ERROR_OCCURRED);
    });
};
//#endregion

watch(
  () => props.type,
  (newValue) => {
    if (newValue === PageType.Details) {
      getDetail();
    }
  },
);

watch(
  () => route.query.tab,
  async (newTab) => {
    if (newTab && typeof newTab === 'string') {
      const tabValue = +newTab as TabValue;
      activeTab.value = tabValue;
      await triggerTabApi(tabValue);
      router.replace({
        path: route.path,
        query: { ...route.query, tab: undefined },
      });
    }
  },
  { immediate: true },
);

onBeforeUnmount(() => {
  tabStore.clear();
});

onMounted(async () => {
  activeTab.value = tabStore.agentActiveTab;
  if (!route.query.tab) {
    await triggerTabApi(activeTab.value);
  }
});
</script>

<style scoped>
:deep(.p-tag) {
  width: 120px;
}
.view-height {
  height: calc(100vh - 265px);
}
</style>
