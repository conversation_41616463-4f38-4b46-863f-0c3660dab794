<template>
  <div>
    <!-- Search Section -->
    <div class="">
      <div class="relative filter-container">
        <div class="flex gap-[10px]">
          <VElementInput
            size="default"
            name="keyword"
            prefix="search"
            :placeholder="contractConfig.searchConfig?.placeholder"
            :style="'!w-[480px]'"
            @keyup.enter="onSubmit"
          />
          <VElementButton
            :bgColor="color.main"
            label="Tìm kiếm"
            styleButton="s"
            @click="onSubmit"
          />
        </div>
      </div>

      <!-- Contract Table -->
      <ContractTable
        ref="contractTableRef"
        classTableContainer="!max-h-[calc(100vh-430px)]"
        :dataContract="dataContract"
        :dataConfig="contractConfig"
        :params="params"
        @pageChanged="onPageChange"
        @perPageChange="onPerPageChange"
        @rowClick="handleViewDetail"
        @action="handleAction"
      />
    </div>

    <!-- Add Appendix Popup -->
    <AddAppendixPopup
      v-if="showAppendixPopup"
      v-model:visible="showAppendixPopup"
      :contractId="selectedContractId"
      type="add"
      @success="handleAppendixSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, inject, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useForm } from 'vee-validate';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useContractStore } from '@/store/contract';
import { useUserSession } from '@/store/userSession';
import { ROUTE_NAME } from '@/shared';
import { ContractListPayloadType } from '@/enums/contract';
import { color } from '@/constants/statusColor';
import agentAgentConfig from '@/components/pages/Contract/agentAgent.constant';
import ContractTable from '@/components/pages/Contract/components/ContractTable.vue';
import AddAppendixPopup from '@/components/pages/Contract/detail/components/popup/AddAppendixPopup.vue';
import { handleApiError } from '@/utils/useErrorHandler';

const api = useApi();
const router = useRouter();
const route = useRoute();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const contractStore = useContractStore();
const user = useUserSession() as any;

// Form handling
const { values, handleSubmit } = useForm();

// Refs
const contractTableRef = ref();

// State
const dataContract = ref<any>({});
const params = ref({
  page_index: 1,
  page_size: 20,
});

const contractConfig = computed(() => {
  const baseConfig = agentAgentConfig;
  return {
    ...baseConfig,
    dataFilter: undefined,
  };
});

const showAppendixPopup = ref(false);
const selectedContractId = ref<number | null>(null);

// Methods
const getContracts = async () => {
  try {
    overlayLoading.toggleLoading(true);

    const requestData = {
      page_index: params.value.page_index,
      page_size: params.value.page_size,
      agentId: user.user?.id_agent,
      contractTypeId: ContractListPayloadType.AgentAgent,
      searchValue: values.keyword ? values.keyword.trim() : undefined,
    };

    const response = await api.get('/business/v1/api/admin/contract', {
      params: requestData,
    });

    if (response.data.code === 0) {
      dataContract.value = {
        items: response.data.data.items || [],
        paging: {
          total_pages: response.data.data.paging?.total_pages || 0,
          total_records: response.data.data.paging?.total_records || 0,
        },
      };
    } else {
      toast('error', response.data.message);
    }
  } catch (error: any) {
    console.error('getContracts error:', error);
    handleApiError(error);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

const onSubmit = handleSubmit(() => {
  params.value.page_index = 1;
  getContracts();
});

// Event handlers
const onPageChange = (value: number) => {
  params.value.page_index = value;
  getContracts();
};

const onPerPageChange = (value: number) => {
  params.value.page_size = value;
  getContracts();
};

const handleViewDetail = (row: any) => {
  contractStore.setListContractPath(route.fullPath);

  router.push({
    name: ROUTE_NAME.USER_INFO_DETAILS_CONTRACT,
    params: {
      id: row.id,
    },
    query: {
      contractTypeId: row.contractTypeId,
    },
  });
};

// Popup handlers
const handleAppendixSuccess = () => {
  showAppendixPopup.value = false;
  selectedContractId.value = null;
  getContracts(); // Refresh data
};

const handleAction = ({ actionKey, row }: { actionKey: string; row: any }) => {
  const actions = {
    view: () => handleViewDetail(row),
  };

  const actionFn = actions[actionKey as keyof typeof actions];
  if (actionFn) {
    actionFn();
  } else {
    console.warn('Unknown action:', actionKey);
  }
};

defineExpose({
  getContracts,
});
</script>
