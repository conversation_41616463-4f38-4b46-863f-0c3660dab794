<template>
  <el-form-item :required="required" :label="label">
    <div class="max-w-full flex flex-col gap-2">
      <div class="" v-if="listFileUpload.length > 0">
        <div
          v-for="(item, index) in listFileUpload"
          :key="index"
          class="flex items-center gap-2 relative"
        >
          <div
            class="underline italic text-[#1A34B5] max-w-[500px] overflow-hidden text-ellipsis whitespace-nowrap cursor-pointer pr-[4px]"
            :title="item.file_name"
            @click="clickFileName(item)"
          >
            {{ item.file_name }}
          </div>
          <RemoveIcon
            v-if="formType !== FORM_TYPE.DETAILS"
            class="cursor-pointer"
            @click="removeDoc(index)"
            @keydown="removeDoc(index)"
          />
        </div>
      </div>
      <div
        v-if="listFileUpload.length < 1 && (disabled || formType === FORM_TYPE.DETAILS)"
        class="italic text-[#292D32]"
      >
        <PERSON><PERSON>ông có dữ liệu
      </div>
      <div v-if="!disabled && formType !== FORM_TYPE.DETAILS" class="w-fit">
        <div
          v-show="listFileUpload.length < maxFile"
          class="relative upload border border-dashed"
          :class="[
            formType === FORM_TYPE.DETAILS
              ? 'pointer-events-none cursor-not-allowed'
              : 'cursor-pointer',
            fileErr || msgError ? 'border-red-500' : 'border-[#8ba6ff]',
          ]"
          @click="clickInputFile"
          @keydown="clickInputFile"
        >
          <div class="text-[#000]">Upload file</div>
          <Icon @click="clickIconInputFile" icon="tabler:upload" class="text-[17px] text-[#000]" />
        </div>
        <div class="leading-none mt-1">
          <small v-if="msgError || fileErr" class="p-error" :id="id + '-text-error'">{{
            msgError || fileErr
          }}</small>
        </div>
      </div>
    </div>
    <input ref="inputFile" class="hidden" type="file" @change="uploadFile" :id="id + '-field'" />
  </el-form-item>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, watch } from 'vue';
import { useField } from 'vee-validate';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useApi } from '@/store/useApi';
import RemoveIcon from '@/components/icon/RemoveIcon.vue';
import { FORM_TYPE } from '@/shared';
import { FileSize, FileUploadApiType } from '@/enums/common';

const props = withDefaults(
  defineProps<{
    fileSize?: string;
    fileType?: string;
    file: string;
    listFile: any;
    disabled?: boolean;
    required?: boolean;
    formType: string;
    fileTypes?: any;
    sizeToValidate?: number;
    label?: string;
    id?: string;
    typeApi?: number;
    isSubmit?: boolean;
    msgErrType?: string;
    msgErrSize?: string;
    maxFile?: number;
  }>(),
  {
    maxFile: 5,
    fileTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
      'image/jpeg',
      'image/png',
    ],
    sizeToValidate: FileSize._5Mb,
    typeApi: FileUploadApiType.File,
  },
);

const api = useApi();
const toast = inject('toast') as any;

const overlayLoading = useOverLayLoadingStore();
const fileUrl = ref();
const listFileUpload = ref<any>([]);
const fileUrlError = ref();
const inputFile = ref();
const isUpload = ref(false);
const baseURL = ref();

const { value: file, errorMessage: fileErr, resetField: setRequire } = useField<File>(props.file);
const { value: listFile } = useField<any>(props.listFile);
const msgError = ref<string>();

const clickInputFile = (ev: Event) => {
  if (props.formType === FORM_TYPE.DETAILS) return;
  const target = ev.target as Element;
  if (target.classList.contains('iconify')) return;
  if (target.classList.contains('image-close-button')) return;
  if (target.tagName === 'path') return;
  inputFile.value.click();
};

const clickIconInputFile = () => {
  if (props.formType === FORM_TYPE.DETAILS) return;
  inputFile.value.click();
};

const uploadFile = async (event: any) => {
  const fileUpload = event.target.files[0];
  const hasFileSizeError = fileUpload?.size > props.sizeToValidate;
  const fileExtension = fileUpload?.name.split('.').pop()?.toLowerCase();
  const hasFileTypeError = !props.fileTypes.includes(fileExtension);

  if (hasFileTypeError) {
    event.target.value = '';
    msgError.value = props.msgErrType;
    return;
  } else {
    msgError.value = '';
  }

  if (hasFileSizeError) {
    event.target.value = '';
    msgError.value = props.msgErrSize;
    return;
  } else {
    msgError.value = '';
  }

  const formData = new FormData();
  formData.append('file', fileUpload);
  overlayLoading.toggleLoading(true);

  try {
    const res = await api.post(
      `/business/v1/api/common/file/upload?type=${props.typeApi}`,
      formData,
    );
    if (res.data.code === 0) {
      overlayLoading.toggleLoading(false);
      listFileUpload.value.push(res.data.data);
      listFile.value = listFileUpload.value;
      file.value = fileUpload;
    } else {
      overlayLoading.toggleLoading(false);
      toast.error(res.data.message, {
        timeout: 5000,
      });
    }
  } catch (error) {
    overlayLoading.toggleLoading(false);
    toast('error', 'Có lỗi xảy ra khi upload file, vui lòng thử lại');
  } finally {
    // Reset input file để người dùng có thể chọn lại cùng một file nếu muốn
    event.target.value = '';
    fileUrlError.value = fileUpload.name;
  }
};

// const viewFile = async (fileUploadId: number) => {
//   const response = await api.get(
//     `/business/v1/api/common/file/event/file/${props.typeApi}/${fileUploadId}`,
//     {
//       responseType: 'blob',
//     },
//   );

//   const url = window.URL.createObjectURL(new Blob([response.data]));

//   const a = document.createElement('a');
//   a.href = url;
//   a.download = 'file.pdf';
//   document.body.appendChild(a);
//   a.click();
//   a.remove();
//   window.URL.revokeObjectURL(url);
// };

function removeDoc(index: number) {
  listFileUpload.value.splice(index, 1);
  listFile.value = listFileUpload.value;
  if (listFileUpload.value.length === 0) {
    setRequire({
      value: undefined,
      touched: false,
    });
  }
}

const clickFileName = (fileUpload: any) => {
  // Detect Microsoft Edge
  const isEdge = /Edg/i.test(navigator.userAgent);
  const isDocxFile = ['.doc', '.docx'].some((ext) =>
    fileUpload.file_name?.toLowerCase().endsWith(ext),
  );

  if (isEdge && isDocxFile) {
    // For Edge + DOCX files: Use location.href to avoid opening 2 tabs
    window.location.href = fileUpload.link_url;
  } else {
    // For other browsers or non-DOCX files: Normal behavior
    window.open(fileUpload.link_url, '_blank');
  }
};

watch(
  listFile,
  (newValue) => {
    listFileUpload.value = newValue || [];
  },
  { deep: true, immediate: true },
);

onMounted(() => {
  baseURL.value = import.meta.env.VITE_BASE_API_URL;
});

defineExpose({
  fileUrl,
  listFileUpload,
  isUpload,
});
</script>

<style scoped>
.upload {
  display: flex;
  gap: 10px;
  color: #000;
  justify-content: center;
  border-radius: 6px;
  width: 122px;
  align-items: center;
  height: 34px;
  cursor: pointer;
  background-color: #e1e8ff;
}
.uploaded {
  display: flex;
  gap: 10px;
  border: solid 1px #4042e2;
  width: 160px;
  color: #4042e2;
  justify-content: center;
  border-radius: 6px;
  align-items: center;
  height: 34px;
  background-color: #fff;
}
.image-close-button {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  height: 15px;
  width: 15px;
  background-color: #e24c4c;
  cursor: pointer;
  position: absolute;
  top: -7px;
  right: -8px;
}
</style>
