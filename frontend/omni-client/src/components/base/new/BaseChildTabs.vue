<script setup lang="ts">
import { TabValue } from '@/shared';

export type TTab = {
  label: string;
  value: TabValue;
};

const props = defineProps<{
  tabs: TTab[];
}>();

const emit = defineEmits<(e: 'clickTab', tabValue: TabValue) => void>();

const activeTab = defineModel<TabValue>('activeTab');
</script>

<template>
  <div>
    <div class="flex items-center gap-x-[8px] w-full mb-[10px]">
      <div
        v-for="tab of props.tabs"
        :key="tab.value"
        class="flex items-center justify-center min-w-[100px] px-[14px] h-[34px] font-semibold cursor-pointer text-[#A3A3A3] hover:text-[white] hover:bg-main text-[14px] border border-[#DFE4EA] rounded-sm"
        :class="`${activeTab === tab.value ? '!text-[white] bg-main !border-main' : ''}`"
        @click="emit('clickTab', tab.value)"
      >
        {{ tab.label }}
      </div>
    </div>
    <div class="w-full flex items-center justify-center">
      <div
        v-for="tab of props.tabs"
        :key="tab.value"
        v-show="activeTab === tab.value"
        class="w-full"
      >
        <slot :name="`tab-${tab.value}`" />
      </div>
    </div>
  </div>
</template>
