import { ServiceType, ServiceTypeLabel } from '@/enums/common';
import type { TDropdownItem } from '@/shared';

export const DATE_FORMAT = {
  FULL: 'HH:MM:SS DD/MM/YYYY',
  DATE: 'DD/MM/YYYY',
  TIME: 'HH:MM:SS',
};

export const MAX_COLLAPSE_SERVICE = 2;

export const SERVICE_TYPE_MAPPING = {
  [ServiceType.SMS]: ServiceTypeLabel.SMS,
  [ServiceType.ZNS]: ServiceTypeLabel.ZNS,
} as const;

export const SERVICE_OPTIONS: TDropdownItem[] = [
  { value: ServiceType.SMS, label: ServiceTypeLabel.SMS },
  { value: ServiceType.ZNS, label: ServiceTypeLabel.ZNS },
];
