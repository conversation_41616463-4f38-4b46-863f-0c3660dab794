import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useEpointStep = defineStore('epoint', () => {
  const active = ref(1);
  const step1Data = ref();
  const paymentType = ref(1);
  const businessId = ref();
  const userId = ref();
  const businessName = ref();
  const step2Data = ref();
  const epoint_amount = ref();
  const payment_method = ref();

  const setValueStep1 = (value: any) => {
    step1Data.value = { ...value };
  };
  const setValueStep2 = (value: any) => {
    step2Data.value = { ...value };
  };

  const setValuePaymentType = (value: any) => {
    paymentType.value = value;
  };

  const setPaymentMethod = (value: number) => {
    payment_method.value = value;
  };

  const next = () => {
    active.value += 1;
  };

  const back = () => {
    active.value -= 1;
  };

  return {
    active,
    paymentType,
    step1Data,
    step2Data,
    businessId,
    userId,
    businessName,
    next,
    back,
    setValueStep1,
    setValueStep2,
    setValuePaymentType,
    setPaymentMethod,
    epoint_amount,
    payment_method,
  } as const;
});
