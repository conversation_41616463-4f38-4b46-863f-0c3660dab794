/**
 * Utility function to handle click outside for FilterCard components
 * Supports both Element Plus 2.3.x and 2.5.x class names
 */
export const createFilterCardClickOutsideHandler = (closeCallback: () => void) => {
  return (ev: Event) => {
    const target = ev.target as Element;
    
    // Element Plus 2.3.x class names (legacy support)
    if (target.classList.contains('el-select-dropdown__option-item')) return;
    if (target.closest('.el-select-dropdown__option-item')) return;
    
    // Element Plus 2.5.x class names (new structure)
    if (target.classList.contains('el-select-v2__option')) return;
    if (target.closest('.el-select-v2__option')) return;
    if (target.closest('.el-select-v2__dropdown')) return;
    if (target.closest('.el-select-dropdown')) return;
    
    // Element Plus dropdown and popper containers
    if (target.closest('.el-popper')) return;
    if (target.closest('.el-dropdown-menu')) return;
    if (target.closest('.el-select-dropdown__wrap')) return;
    
    // Date picker and other Element Plus components
    if (target.closest('.el-picker-panel__body')) return;
    if (target.closest('.el-picker-panel')) return;
    if (target.closest('.el-date-picker')) return;
    if (target.closest('.el-time-picker')) return;
    
    // Filter card itself and related elements
    if (target.closest('.filter')) return;
    if (target.closest('[data-filter-card]')) return;
    if (target.closest('.filter-container')) return;
    
    // Tooltip and other overlay elements
    if (target.closest('.el-tooltip__popper')) return;
    if (target.closest('.el-overlay')) return;
    
    // Call the close callback
    closeCallback();
  };
};

/**
 * Common Element Plus dropdown selectors for click outside detection
 */
export const ELEMENT_PLUS_DROPDOWN_SELECTORS = [
  // Element Plus 2.3.x (legacy)
  '.el-select-dropdown__option-item',
  '.el-select-dropdown',
  
  // Element Plus 2.5.x (new)
  '.el-select-v2__option',
  '.el-select-v2__dropdown',
  '.el-select-v2__popper',
  
  // Common Element Plus components
  '.el-popper',
  '.el-dropdown-menu',
  '.el-picker-panel',
  '.el-tooltip__popper',
  '.el-overlay',
  
  // Filter card specific
  '.filter',
  '[data-filter-card]',
  '.filter-container',
];

/**
 * Check if target is inside any Element Plus dropdown or overlay
 */
export const isInsideElementPlusDropdown = (target: Element): boolean => {
  return ELEMENT_PLUS_DROPDOWN_SELECTORS.some(selector => {
    if (selector.startsWith('[') && selector.endsWith(']')) {
      // Handle attribute selectors
      return target.closest(selector) !== null;
    }
    if (selector.startsWith('.')) {
      // Handle class selectors
      const className = selector.substring(1);
      return target.classList.contains(className) || target.closest(selector) !== null;
    }
    return false;
  });
};
