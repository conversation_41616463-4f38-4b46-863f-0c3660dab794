<script setup lang="ts">
import { inject, onMounted, ref, watch } from 'vue';
import * as yup from 'yup';
import { useApi } from '@/store/useApi';
import { useForm } from 'vee-validate';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import PopupCancelConfirm from '@/components/base/common/PopupCancelConfirm.vue';
import { TabValue } from '@/shared';
import { initSellingPriceValueSMS, initSellingPriceValueZNS } from './index.constants';
import SMSBoard from '../components/SMSBoard.vue';
import ZNSBoard from '../components/ZNSBoard.vue';
import { type TSMSWrapper, type TTab, type TZNSWrapper } from '../index.type';
import { cloneDeep } from 'lodash';
import { getSellingPriceSMSValue, getSellingPriceZNSValue } from './index.utils';
import { PriceType } from '../index.constants';
import { PageType } from '@/enums/common';
import BaseChildTabs from '@/components/base/new/BaseChildTabs.vue';

const props = defineProps<{
  type: PageType;
}>();

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();

//#region Datasource
const smsDataSource = ref<TSMSWrapper>(cloneDeep(initSellingPriceValueSMS));
const znsDataSource = ref<TZNSWrapper>(cloneDeep(initSellingPriceValueZNS));

const mapDataSMS = (responseData: any) => {
  smsDataSource.value = cloneDeep(getSellingPriceSMSValue(responseData.capital));
  setTimeout(() => {
    smsBoardRef.value?.resetFields();
  }, 100);
};

const mapDataZNS = (responseData: any) => {
  znsDataSource.value = cloneDeep(getSellingPriceZNSValue(responseData.capital));
  setTimeout(() => {
    znsBoardRef.value?.resetFields();
  }, 100);
};

const mapDataDetails = (tab: TabValue, responseData: any) => {
  if (tab === TabValue.Tab1) {
    mapDataSMS(responseData);
  } else if (tab === TabValue.Tab2) {
    mapDataZNS(responseData);
  }
};

const getDetail = async function (tab: TabValue = activeTab.value) {
  if (tab === TabValue.Tab3 || tab === TabValue.Tab4) return;

  overlayLoading.toggleLoading(true);
  let url = `business/v1/api/price-list/sms`;
  if (tab === TabValue.Tab2) {
    url = `business/v1/api/price-list/zns`;
  }
  const { data } = await api.get(url);
  if (data.code === 0) {
    mapDataDetails(tab, data.data);
  } else {
    toast('error', data.message);
  }
  overlayLoading.toggleLoading(false);
};
//#endregion

//#region Form
const smsBoardRef = ref();
const znsBoardRef = ref();
const validationSchema = yup.object().shape({});

const { handleSubmit } = useForm({
  validationSchema,
});

const handleSubmitSMS = async () => {
  await smsBoardRef.value.onSubmit();
};

const handleSubmitZNS = async () => {
  await znsBoardRef.value.onSubmit();
};

const handleSubmitFormSMS = async (payload: any) => {
  overlayLoading.toggleLoading(true);
  const { data } = await api.put(`business/v1/api/price-list/sms`, payload);
  if (data.code === 0) {
    toast('success', data.message);
  } else {
    toast('error', data.message);
  }
  overlayLoading.toggleLoading(false);
};

const handleSubmitFormZNS = async (payload: any) => {
  overlayLoading.toggleLoading(true);
  const { data } = await api.put(`business/v1/api/price-list/zns`, payload);
  if (data.code === 0) {
    toast('success', data.message);
  } else {
    toast('error', data.message);
  }
  overlayLoading.toggleLoading(false);
};

const onSubmit = handleSubmit(() => {
  if (activeTab.value === TabValue.Tab1) {
    handleSubmitSMS();
  } else if (activeTab.value === TabValue.Tab2) {
    handleSubmitZNS();
  }
});
//#endregion

//#region Cancel validation
const emit = defineEmits(['onConfirmCancel']);
const isVisibleCancelPopup = ref(false);

const handleCloseCancel = () => {
  isVisibleCancelPopup.value = false;
};

const handleConfirmCancel = () => {
  isVisibleCancelPopup.value = false;
  if (activeTab.value === TabValue.Tab1) {
    smsDataSource.value = cloneDeep(initSellingPriceValueSMS);
    setTimeout(() => {
      smsBoardRef.value?.resetFields();
    }, 100);
  } else if (activeTab.value === TabValue.Tab2) {
    znsDataSource.value = cloneDeep(initSellingPriceValueZNS);
    setTimeout(() => {
      znsBoardRef.value?.resetFields();
    }, 100);
  }
  if (selectedTab.value !== activeTab.value) {
    activeTab.value = selectedTab.value;
    oldTab.value = selectedTab.value;
  } else {
    emit('onConfirmCancel');
  }
};

const handleConfirmCancelParent = (isChangedTab: boolean) => {
  if (isChangedTab && activeTab.value === TabValue.Tab1) {
    smsDataSource.value = cloneDeep(initSellingPriceValueSMS);
    setTimeout(() => {
      smsBoardRef.value?.resetFields();
    }, 100);
  } else if (isChangedTab && activeTab.value === TabValue.Tab2) {
    znsDataSource.value = cloneDeep(initSellingPriceValueZNS);
    setTimeout(() => {
      znsBoardRef.value?.resetFields();
    }, 100);
  }
  if (isChangedTab || selectedTab.value !== activeTab.value) {
    activeTab.value = selectedTab.value;
  } else {
    emit('onConfirmCancel');
  }
};
//#endregion

//#region Tab
const oldTab = ref<TabValue>(TabValue.Tab1);
const activeTab = ref<TabValue>(TabValue.Tab1);
const selectedTab = ref<TabValue>(TabValue.Tab1);

const tabs: TTab[] = [
  {
    label: 'SMS',
    value: TabValue.Tab1,
  },
  {
    label: 'ZNS',
    value: TabValue.Tab2,
  },
];

const handleTabChanged = async (tabValue: TabValue) => {
  if (tabValue === oldTab.value) return;
  if (props.type !== PageType.Details) {
    isVisibleCancelPopup.value = true;
    selectedTab.value = tabValue;
  } else {
    activeTab.value = tabValue;
    oldTab.value = tabValue;
    getDetail(tabValue);
  }
};
//#endregion

//#region Watcher
watch(
  () => props.type,
  (value) => {
    if (value === PageType.Details) {
      getDetail();
    }
  },
);
//#endregion

onMounted(() => {
  getDetail(TabValue.Tab1);
});

defineExpose({
  activeTab,
  onSubmit,
  getDetail,
  handleConfirmCancel,
  handleConfirmCancelParent,
});
</script>

<template>
  <div class="overflow-y-auto w-full h-[calc(100%-40px)]">
    <BaseChildTabs :tabs :active-tab="activeTab" tabClass="w-[90%]" @click-tab="handleTabChanged">
      <template #tab-1>
        <SMSBoard
          v-model:datasource="smsDataSource"
          ref="smsBoardRef"
          note="Lưu ý: Giá khai báo phải bao gồm VAT"
          :type
          :activeTab
          :price-type="PriceType.CostPrice"
          @onSubmitForm="handleSubmitFormSMS"
        />
      </template>
      <template #tab-2>
        <ZNSBoard
          v-model:datasource="znsDataSource"
          ref="znsBoardRef"
          note="Lưu ý: Giá khai báo phải bao gồm VAT"
          :type
          :activeTab
          :price-type="PriceType.CostPrice"
          @onSubmitForm="handleSubmitFormZNS"
        />
      </template>
    </BaseChildTabs>
  </div>
  <PopupCancelConfirm
    v-model:popupVisible="isVisibleCancelPopup"
    @onClose="handleCloseCancel"
    @onConfirm="handleConfirmCancel"
  />
</template>
