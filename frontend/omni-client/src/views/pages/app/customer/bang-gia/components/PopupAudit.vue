<script setup lang="ts">
import { color } from '@/constants/statusColor';
import { reactive, ref, watch } from 'vue';
import VOldTable from '@/components/base/VOldTable.vue';
import moment from 'moment';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useApi } from '@/store/useApi';
import {
  popupAuditLogPt,
  popupAuditLogStyle,
  auditLogHeaders,
  auditLogStyle,
  ServiceType,
} from '../index.constants';
import VDownLoadFilePrice from './VDownLoadFilePrice.vue';
import type { CustomerType } from '../index.type';

const props = defineProps<{
  visible: boolean;
  customerType?: CustomerType;
  serviceType: ServiceType;
}>();

const emit = defineEmits(['onClose']);

//#region Table
const params = reactive({
  page: 1,
  size: 20,
});

const pageInfo = reactive<any>({
  currentPage: null,
  total_pages: null,
  total_records: null,
});

const tableRef = ref();
const auditDatas = ref<any[]>([]);
const overlayLoading = useOverLayLoadingStore();

const onPageChange = (page: number) => {
  params.page = page;
  getList();
};

const onPerPageChange = (size: number) => {
  params.size = size;
};

const sortTable = () => {
  getList();
};
//#endregion

//#region Datasource
const getList = async () => {
  overlayLoading.toggleLoading(true);
  const api = useApi();
  const { data } = await api.get(`business/v1/api/price-list/admin-changes`, {
    params: {
      page: params.page - 1,
      size: params.size,
      serviceType: props.serviceType,
    },
  });
  if (data.code === 0) {
    params.page = 1;
    params.size = 20;
    auditDatas.value = data.data?.content;
    pageInfo.total_pages = data?.data?.totalPages ?? 0;
    pageInfo.total_records = data.data?.totalElements ?? 0;
  }
  overlayLoading.toggleLoading(false);
};
//#endregion

watch(
  () => props.visible,
  async (newValue) => {
    if (newValue) {
      await getList();
    }
  },
);
</script>

<template>
  <VDialog
    modal
    ref="refDialog"
    header="Lịch sử chỉnh sửa"
    :visible="visible"
    :draggable="false"
    :pt="popupAuditLogPt"
    :style="popupAuditLogStyle"
    @update:visible="emit('onClose')"
  >
    <div class="mt-[20px]">
      <VOldTable
        ref="tableRef"
        idTable="audit-log-table"
        class="!p-0 mb-[40px]"
        :useOrder="false"
        :rows="auditDatas"
        :isCursorRow="false"
        :headers="auditLogHeaders"
        :styleHeaders="auditLogStyle"
        :showAction="false"
        :pagination="{
          totalPage: pageInfo.total_pages ?? 0,
          total: pageInfo.total_records ?? 0,
          perPage: params.size ?? 0,
        }"
        @pageChanged="onPageChange"
        @perPageChange="onPerPageChange"
        @sort-table="sortTable"
      >
        <template v-slot:items="{ row }">
          <td class="text-primaryText px-2">
            <div class="column-container text-center">
              {{ moment(row?.createdAt).format('DD/MM/YYYY HH:mm') }}
            </div>
          </td>
          <td class="text-primaryText px-2">
            <div
              :id="`campaign-link-${row.accountId}`"
              :title="row.account"
              class="column-container max-w-[150px]"
            >
              {{ row.account }}
            </div>
          </td>
          <td class="text-primaryText px-2">
            <div class="column-container text-center">
              {{ row?.channel }}
            </div>
          </td>
          <td class="text-primaryText px-2">
            <div class="column-container flex items-center justify-center">
              <VDownLoadFilePrice :label="''" :url="row?.fileViewUrl" />
            </div>
          </td>
        </template>
      </VOldTable>
      <div class="save-container flex justify-center items-center gap-5">
        <VElementButton label="Đóng" :bgColor="color.closeButton" @click="emit('onClose')" />
      </div>
    </div>
  </VDialog>
</template>

<style scoped>
:deep(.v-old-table-container) {
  height: 350px;
}
</style>
