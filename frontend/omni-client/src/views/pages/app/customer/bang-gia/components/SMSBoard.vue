<script setup lang="ts">
import type { <PERSON><PERSON><PERSON><PERSON>, TSMSWrapper } from '../index.type';
import { REGEX, TabValue } from '@/shared';
import { Icon } from '@iconify/vue';
import { ElInput, ElTooltip } from 'element-plus';
import { ref, watch } from 'vue';
import { PriceType } from '../index.constants';
import { AccountType, PageType } from '@/enums/common';

const props = defineProps<{
  type: PageType;
  note?: string;
  activeTab: TabValue;
  priceType: PriceType;
  capitalData?: TSMSWrapper;
  fixedData?: TSMSWrapper;
  accountType?: AccountType;
}>();

const datasource = defineModel<TSMSWrapper>('datasource');
const platformValue = ref(datasource.value?.platformCost);
const platformError = ref('');
const accType = props.accountType || localStorage.getItem('accountType') || AccountType.Admin;

const isShowPriceField = (item: TSMSBlock, indexField: number) => {
  return props.priceType !== PriceType.CostPrice && item.hasField && indexField === 1;
};

const inputValues = ref(
  datasource.value?.data.map((item) =>
    item.rows.map((row) => row.fields.map((field) => field.value)),
  ) ?? [],
);
const inputErrors = ref(
  datasource.value?.data.map((item) =>
    item.rows.map((row) => row.fields.map((field) => field.value)),
  ) ?? [],
);

const resetFields = () => {
  platformValue.value = datasource.value?.platformCost;

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((fieldItem, indexField) => {
        inputValues.value[indexData][indexRow][indexField] = fieldItem.value;
      });
    });
  });
};

const resetErrors = () => {
  platformError.value = '';

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((_, indexField) => {
        inputErrors.value[indexData][indexRow][indexField] = '';
      });
    });
  });
};

const updateDatasource = () => {
  if (datasource.value) datasource.value.platformCost = platformValue.value;

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((fieldItem, indexField) => {
        fieldItem.value = inputValues.value[indexData][indexRow][indexField];
      });
    });
  });
};

const handleChangeValue = (value: any, indexData: number, indexRow: number, indexField: number) => {
  const positiveAndZeroRegex = REGEX.POSITIVE_NUMBER_OR_ZERO;
  const isValid = positiveAndZeroRegex.test(value);
  if (isValid) {
    inputErrors.value[indexData][indexRow][indexField] = '';
  } else if (value === '') {
    inputErrors.value[indexData][indexRow][indexField] = 'Không được để trống';
  } else {
    inputErrors.value[indexData][indexRow][indexField] = 'Chỉ nhập số';
  }
};

watch(
  () => props.activeTab,
  (newValue: TabValue) => {
    if (newValue === TabValue.Tab1) {
      resetErrors();
    }
  },
  {
    immediate: true,
  },
);

watch(
  () => props.type,
  () => {
    resetErrors();
  },
);

defineExpose({
  updateDatasource,
  resetFields,
});
</script>

<template>
  <div class="text-[#292D32] text-[14px]">
    <div class="mb-[16px]">
      <p class="italic">{{ props.note }}</p>
    </div>
    <div
      v-if="props.type === PageType.Details && accType != AccountType.Agent"
      class="mb-[20px] flex items-center"
    >
      <p class="font-bold mr-[8px]">Phí nền tảng</p>
      <div class="flex items-center gap-[4px]">
        <p
          v-if="datasource?.platformCost || datasource?.platformCost == '0'"
          :class="`${props.priceType === PriceType.SellingPrice && fixedData?.platformCost != platformValue ? 'bg-[#6CFFFF]' : ''}`"
        >
          {{
            `${datasource?.platformCost == '0' ? 'Miễn phí' : datasource?.platformCost?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.')}`
          }}
        </p>
        <span>(E-Point/ bản tin MT)</span>
      </div>
    </div>
    <div class="">
      <div
        v-for="(item, indexData) of datasource?.data"
        :key="`${item.label}-${indexData}`"
        class="mb-[20px]"
      >
        <div class="w-full flex items-center justify-between mb-[6px]">
          <p class="font-bold">{{ item.label }}</p>
          <p>{{ item.unit }}</p>
        </div>
        <div class="w-full overflow-x-auto">
          <div
            v-for="(rowItem, indexRow) of item.rows"
            :key="`row-${rowItem}-${indexRow}`"
            class="w-full"
            :class="props.priceType !== PriceType.CostPrice && item.hasField ? 'relative' : ''"
          >
            <div
              v-if="rowItem.isHeader"
              class="flex items-center font-bold rounded-t-[6px] shadow gap-x-[4px] bg-[#F6F8FB]"
            >
              <div
                v-if="props.priceType !== PriceType.CostPrice && !item.hasField"
                class="flex-1 flex items-center justify-center h-[36px] min-w-[120px] bg-[#F6F8FB] rounded-tl-[6px]"
              >
                Giá
              </div>
              <div
                v-for="(fieldItem, indexField) of rowItem.fields"
                :key="`header-${fieldItem.value}-${indexField}`"
                class="flex-1 flex items-center justify-center h-[36px] bg-[#F6F8FB]"
                :class="`${indexField === 0 ? 'rounded-tl-[6px]' : ''} ${indexField === rowItem.fields.length - 1 ? 'rounded-tr-[6px]' : ''} ${isShowPriceField(item, indexField) ? 'min-w-[240px]' : 'min-w-[120px]'}`"
              >
                <p
                  v-if="isShowPriceField(item, indexField)"
                  class="flex items-center justify-center h-[36px] min-w-[120px] bg-[#F6F8FB]"
                >
                  Giá
                </p>
                <p
                  class="text-center"
                  :class="`${isShowPriceField(item, indexField) ? 'flex-1' : ''}`"
                >
                  {{ fieldItem.value }}
                </p>
              </div>
            </div>
            <div v-if="!rowItem.isHeader" class="flex items-stretch gap-x-[4px] min-h-[40px]">
              <div
                v-for="(fieldItem, indexField) of rowItem.fields"
                :key="`field-${fieldItem.value}-${indexField}`"
                class="flex-1 flex py-[4px] items-start justify-center border-b border-b-[#DFE4EA] relative"
                :class="isShowPriceField(item, indexField) ? 'min-w-[240px]' : 'min-w-[120px]'"
              >
                <div
                  v-if="fieldItem.field"
                  class="flex items-center justify-center"
                  :class="
                    props.priceType !== PriceType.CostPrice && item.hasField && indexField === 0
                      ? 'absolute top-0 translate-y-[-50%] h-[190%] border-r w-full px-[10px] bg-white border-r-[#DFE4EA]'
                      : ''
                  "
                >
                  <p class="text-center overflow-hidden !line-clamp-2" :title="fieldItem.field">
                    {{ fieldItem.field }}
                  </p>
                  <el-tooltip v-if="fieldItem.description" placement="right">
                    <template #content>
                      <p class="max-w-[320px]">
                        {{ fieldItem.description }}
                      </p>
                    </template>
                    <Icon
                      icon="mingcute:information-fill"
                      class="text-[#354052] ml-[8px] cursor-pointer"
                    />
                  </el-tooltip>
                </div>
                <div v-if="!fieldItem.field && type !== PageType.Details" class="w-full">
                  <el-input
                    v-model="inputValues[indexData][indexRow][indexField]"
                    class="min-w-[120px]"
                    :min="0"
                    :maxlength="5"
                    :placeholder="props.type === PageType.Details ? '' : fieldItem.placeholder"
                    :disabled="props.type === PageType.Details"
                    :show-word-limit="false"
                    @change="
                      (value: any) => handleChangeValue(value, indexData, indexRow, indexField)
                    "
                  />
                  <span class="text-[12px] text-[#f56c6c]">{{
                    inputErrors[indexData][indexRow][indexField]
                  }}</span>
                </div>
                <div
                  v-else-if="!fieldItem.field"
                  class="min-w-[120px] w-full text-center h-full"
                  :class="`${
                    props.priceType !== PriceType.CostPrice && item.hasField
                      ? 'border-r border-r-[#DFE4EA]'
                      : ''
                  } ${props.priceType === PriceType.SellingPrice && fixedData?.data[indexData].rows[indexRow].fields[indexField].value !== fieldItem.value ? 'bg-[#6CFFFF]' : ''}`"
                >
                  {{
                    fieldItem.value?.toString() == '0'
                      ? 'Miễn phí'
                      : fieldItem.value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.')
                  }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-input.invalid .el-input__wrapper) {
  box-shadow: 0 0 0 1px #f56c6c inset;
}
</style>
