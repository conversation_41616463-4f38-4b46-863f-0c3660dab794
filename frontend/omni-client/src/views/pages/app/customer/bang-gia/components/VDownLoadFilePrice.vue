<script setup lang="ts">
const props = defineProps<{
  url: string;
  required?: boolean;
}>();

const clickDownload = () => {
  window.open(props.url, '_blank');
};
</script>

<template>
  <el-form-item :required="required">
    <div class="relative w-[100%] flex items-center justify-center">
      <div class="relative uploaded cursor-pointer" @click="clickDownload" @keydown="clickDownload">
        <Icon icon="tabler:download" class="text-[20px] text-[#354052]" />
      </div>
    </div>
  </el-form-item>
</template>

<style scoped>
.uploaded {
  display: flex;
  color: #354052;
  justify-content: center;
  align-items: center;
  height: 34px;
}
</style>
