<script setup lang="ts">
import type { TZNS<PERSON>rapper } from '../index.type';
import { REGEX, TabValue } from '@/shared';
import { Icon } from '@iconify/vue';
import { ElInput, ElTooltip } from 'element-plus';
import { ref, watch } from 'vue';
import { PriceType } from '../index.constants';
import { AccountType, PageType } from '@/enums/common';

const props = defineProps<{
  type: PageType;
  note?: string;
  activeTab: TabValue;
  priceType: PriceType;
  capitalData?: TZNSWrapper;
  fixedData?: TZNSWrapper;
  accountType?: AccountType;
}>();

const datasource = defineModel<TZNSWrapper>('datasource');
const platformValue = ref(datasource.value?.platformCost);
const platformError = ref('');
const accType = props.accountType || localStorage.getItem('accountType') || AccountType.Admin;

const inputValues = ref(
  datasource.value?.data.map((item) =>
    item.rows.map((row) => row.fields.map((field) => field.value)),
  ) ?? [],
);
const inputErrors = ref(
  datasource.value?.data.map((item) =>
    item.rows.map((row) => row.fields.map((field) => field.value)),
  ) ?? [],
);

const resetFields = () => {
  platformValue.value = datasource.value?.platformCost;

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((fieldItem, indexField) => {
        inputValues.value[indexData][indexRow][indexField] = fieldItem.value;
      });
    });
  });
};

const resetErrors = () => {
  platformError.value = '';

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((_, indexField) => {
        inputErrors.value[indexData][indexRow][indexField] = '';
      });
    });
  });
};

const updateDatasource = () => {
  if (datasource.value) datasource.value.platformCost = platformValue.value;

  datasource.value?.data.forEach((item, indexData) => {
    item.rows.forEach((rowItem, indexRow) => {
      rowItem.fields.forEach((fieldItem, indexField) => {
        fieldItem.value = inputValues.value[indexData][indexRow][indexField];
      });
    });
  });
};

const handleChangeValue = (value: any, indexData: number, indexRow: number, indexField: number) => {
  const positiveAndZeroRegex = REGEX.POSITIVE_NUMBER_OR_ZERO;
  const isValid = positiveAndZeroRegex.test(value);
  if (isValid) {
    inputErrors.value[indexData][indexRow][indexField] = '';
  } else if (value === '') {
    inputErrors.value[indexData][indexRow][indexField] = 'Không được để trống';
  } else {
    inputErrors.value[indexData][indexRow][indexField] = 'Chỉ nhập số';
  }
};

watch(
  () => props.activeTab,
  (newValue: TabValue) => {
    if (newValue === TabValue.Tab1) {
      resetErrors();
    }
  },
  {
    immediate: true,
  },
);

watch(
  () => props.type,
  () => {
    resetErrors();
  },
);

defineExpose({
  updateDatasource,
  resetFields,
});
</script>

<template>
  <div class="text-[#292D32] text-[14px]">
    <div class="mb-[16px]">
      <p class="italic">{{ props.note }}</p>
    </div>
    <div
      v-if="props.type === PageType.Details && accType != AccountType.Agent"
      class="mb-[20px] flex items-center"
    >
      <p class="font-bold mr-[8px]">Phí nền tảng</p>
      <div class="flex items-center gap-x-[6px]">
        <p
          v-if="datasource?.platformCost || datasource?.platformCost == '0'"
          :class="`${props.priceType === PriceType.SellingPrice && fixedData?.platformCost != platformValue ? 'bg-[#6CFFFF]' : ''}`"
        >
          {{
            `${datasource?.platformCost?.toString() == '0' ? 'Miễn phí' : datasource?.platformCost?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.')}`
          }}
        </p>
        <span>(E-Point/ ZNS)</span>
      </div>
    </div>
    <div class="">
      <div
        v-for="(item, indexData) of datasource?.data"
        :key="`${item.label}-${indexData}`"
        class="mb-[20px]"
      >
        <div class="w-full flex items-center justify-between mb-[6px]">
          <p class="font-bold">{{ item.label }}</p>
        </div>
        <div class="w-full overflow-x-auto overflow-y-hidden">
          <div
            v-for="(rowItem, indexRow) of item.rows"
            :key="`row-${rowItem}-${indexRow}`"
            class="w-full"
            :class="props.priceType !== PriceType.CostPrice ? 'relative' : ''"
          >
            <div
              v-if="rowItem.isHeader"
              class="flex items-center font-bold rounded-t-[6px] shadow gap-x-[4px] bg-[#F6F8FB]"
            >
              <div
                v-for="(fieldItem, indexField) of rowItem.fields"
                :key="`header-${fieldItem.value}-${indexField}`"
                class="px-[10px] flex items-center justify-start h-[36px] min-w-[120px] bg-[#F6F8FB]"
                :class="`${indexField === 0 ? 'rounded-tl-[6px] w-[30%]' : indexField === rowItem.fields.length - 1 ? 'w-[20%] rounded-tr-[6px]' : 'w-[50%]'}`"
              >
                <p
                  :class="`${props.priceType !== PriceType.CostPrice && indexField === 1 ? 'flex items-center justify-center w-full' : ''}`"
                >
                  {{ fieldItem.value }}
                </p>
              </div>
            </div>
            <div v-if="!rowItem.isHeader" class="flex items-stretch gap-x-[4px] min-h-[40px]">
              <div
                v-for="(fieldItem, indexField) of rowItem.fields"
                :key="`field-${fieldItem.value}-${indexField}`"
                class="flex items-start justify-start py-[4px] border-b border-b-[#DFE4EA]"
                :class="`${fieldItem.field ? 'w-[30%]' : fieldItem.unit ? 'w-[20%]' : 'w-[50%]'} ${props.priceType !== PriceType.CostPrice ? 'px-[2px]' : 'px-[10px]'}`"
              >
                <div v-if="fieldItem.field" class="flex items-center justify-center">
                  <div
                    class="overflow-hidden !line-clamp-2"
                    :class="`${
                      props.priceType !== PriceType.CostPrice && indexField === 0
                        ? 'absolute top-0 left-0 h-full border-r min-w-[120px] w-[30%] px-[10px] border-r-[#DFE4EA]'
                        : ''
                    }`"
                    :title="fieldItem.field"
                  >
                    <p
                      :class="`${props.priceType !== PriceType.CostPrice && indexField === 0 ? 'flex items-center h-full' : ''}`"
                    >
                      {{ fieldItem.field }}
                    </p>
                  </div>
                  <el-tooltip v-if="fieldItem.description" placement="right">
                    <template #content>
                      <p class="max-w-[320px]">
                        {{ fieldItem.description }}
                      </p>
                    </template>
                    <Icon
                      icon="mingcute:information-fill"
                      class="text-[#354052] ml-[8px] cursor-pointer"
                    />
                  </el-tooltip>
                </div>
                <div
                  v-if="!fieldItem.field && !fieldItem.unit"
                  :class="`${props.priceType !== PriceType.CostPrice ? 'size-full' : ''} ${props.type === PageType.Details ? '' : 'w-full'}`"
                >
                  <div v-if="type !== PageType.Details" class="w-full">
                    <el-input
                      v-model="inputValues[indexData][indexRow][indexField]"
                      class="min-w-[120px]"
                      :min="0"
                      :maxlength="5"
                      :placeholder="props.type === PageType.Details ? '' : fieldItem.placeholder"
                      :disabled="props.type === PageType.Details"
                      :show-word-limit="false"
                      @change="
                        (value: any) => handleChangeValue(value, indexData, indexRow, indexField)
                      "
                    />
                    <span class="text-[12px] text-[#f56c6c]">{{
                      inputErrors[indexData][indexRow][indexField]
                    }}</span>
                  </div>
                  <div
                    v-else
                    class="text-center h-full"
                    :class="`${props.priceType === PriceType.SellingPrice && fixedData?.data[indexData].rows[indexRow].fields[indexField].value !== fieldItem.value ? 'bg-[#6CFFFF]' : ''}`"
                  >
                    {{
                      fieldItem.value?.toString() == '0'
                        ? 'Miễn phí'
                        : fieldItem.value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.')
                    }}
                  </div>
                </div>
                <div
                  v-else
                  :class="
                    props.priceType !== PriceType.CostPrice
                      ? 'absolute h-[90%] top-1/2 right-0 pl-[10px] translate-y-[-50%] bg-white w-[20%] border-l border-[#DFE4EA]'
                      : ''
                  "
                >
                  <p
                    :class="
                      props.priceType !== PriceType.CostPrice ? 'h-full flex items-center' : ''
                    "
                  >
                    {{ fieldItem.unit }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-input.invalid .el-input__wrapper) {
  box-shadow: 0 0 0 1px #f56c6c inset;
}
:deep(.input-normal .el-input__wrapper) {
  background-color: white;
}
</style>
