import type { TabValue } from '@/shared';

export type TSMSWrapper = {
  platformCost?: string | null;
  data: TSMSBlock[];
};

export type TSMSBlock = {
  label: string;
  unit: string;
  rows: TSMSRow[];
  hasField?: boolean;
};

export type TSMSRow = {
  isHeader?: boolean;
  fields: TSMSField[];
};

export type TSMSField = {
  value: string;
  field?: string;
  description?: string;
  placeholder?: string;
};

export type TZNSWrapper = {
  platformCost?: string | null;
  data: TZNSBlock[];
};

export type TZNSBlock = {
  label: string;
  rows: TZNSRow[];
  hasField?: boolean;
};

export type TZNSRow = {
  isHeader?: boolean;
  fields: TZNSField[];
};

export type TZNSField = {
  field?: string;
  value: string;
  unit?: string;
  description?: string;
  placeholder?: string;
};

export type TTab = {
  label: string;
  value: TabValue;
};

export enum CustomerType {
  Agent = 1,
  Enterprise = 2,
}
