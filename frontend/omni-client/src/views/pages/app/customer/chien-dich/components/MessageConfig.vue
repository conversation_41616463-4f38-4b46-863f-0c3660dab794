<script setup lang="ts">
import { FORM_TYPE, PLACEHOLDER, TEXT, type TDropdownItem } from '@/shared';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useApi } from '@/store/useApi';
import { useUserSession } from '@/store/userSession';
import { computed, inject, ref, watch } from 'vue';
import { ChannelValue, CUSTOM_FIELD_TYPE, CUSTOM_FIELD_VALUE, StepValue } from '../index.constants';
import { useCampaign } from '@/store/useCampaign';
import TextType from '../../quan-ly-kenh/zns/zns-template/info/TextType.vue';
import OtpType from '../../quan-ly-kenh/zns/zns-template/info/OtpType.vue';
import TableType from '../../quan-ly-kenh/zns/zns-template/info/table-type/TableType.vue';
import RateType from '../../quan-ly-kenh/zns/zns-template/info/RateType.vue';
import PaymentRequestType from '../../quan-ly-kenh/zns/zns-template/info/PaymentRequestType.vue';
import VoucherType from '../../quan-ly-kenh/zns/zns-template/info/VoucherType.vue';
import { ZnsConfigImg, ZnsPageType, ZnsType, ZnsTypeImg } from '@/enums/zns';
import { useFieldArray, useForm } from 'vee-validate';
import * as yup from 'yup';
import { ElTooltip, ElSelectV2, ElInput } from 'element-plus';
import ZnsWrapper from '../../quan-ly-kenh/zns/zns-template/info/ZnsWrapper.vue';
import type { TParameter, TTemplateParams } from '../index.type';
import PopupKeywordBlock from '../popup/PopupKeywordBlock.vue';
import { getRawParam } from '@/utils/formatText';
import { stringGetter } from '@/utils/getter.utils';

const props = defineProps<{
  type: FORM_TYPE;
  is_failover: boolean;
  channel_type?: ChannelValue;
  customClass?: string;
  isPreview?: boolean;
  parameters?: TParameter[];
}>();

const emit = defineEmits(['onSubmitForm', 'onSubmitFormFailover']);

const api = useApi();
const toast = inject('toast') as any;
const user = useUserSession() as any;
const overlayLoading = useOverLayLoadingStore();
const campaignStore = useCampaign();
const isVisibleBlockPopup = ref(false);
const blockedKeywords = ref<any>({});

const parameterOptions = computed<any>(() => {
  return props.parameters?.map((item: TParameter) => {
    return {
      label: item.field_name,
      value: item.field_name,
      type: item.field_type,
    };
  });
});

//#region Datasource
const oaList = ref<any[]>([]);
const brandnameList = ref<any[]>([]);
const templateList = ref<any[]>([]);
const listBlockedKeyword = ref([]);

const filteredBlockedKeywords = computed(() => {
  if (values.template_id) {
    return listBlockedKeyword.value?.filter((i: any) => i.sms_type === values.template_id);
  }
  return listBlockedKeyword.value;
});

const getListActiveOa = async () => {
  if (!props.channel_type) return;
  overlayLoading.toggleLoading(true);
  api
    .get(`campaign/v1/api/campaign/label/${props.channel_type}?userId=${user.user.userId}`)
    .then((response) => {
      if (response.data.code === 0) {
        oaList.value = response.data.data?.map((item: any) => ({
          value: item.id,
          label: item.name,
        }));
        overlayLoading.toggleLoading(false);
      } else {
        overlayLoading.toggleLoading(false);
        toast('error', response.data.message);
      }
    })
    .catch((err) => {
      overlayLoading.toggleLoading(false);
      console.error(err);
      toast('error', TEXT.ERROR_OCCURRED);
    });
};

const getListActiveBrandname = async () => {
  if (!props.channel_type) return;
  overlayLoading.toggleLoading(true);
  api
    .get(`campaign/v1/api/campaign/label/${props.channel_type}?userId=${user.user.userId}`)
    .then((response) => {
      if (response.data.code === 0) {
        brandnameList.value = response.data.data?.map((item: any) => ({
          value: item.id,
          label: item.name,
        }));
        overlayLoading.toggleLoading(false);
      } else {
        overlayLoading.toggleLoading(false);
        toast('error', response.data.message);
      }
    })
    .catch((err) => {
      overlayLoading.toggleLoading(false);
      console.error(err);
      toast('error', TEXT.ERROR_OCCURRED);
    });
};

const getListTemplate = async (sourceId: number) => {
  setTimeout(() => {
    if (props.channel_type === ChannelValue.ZNS) {
      campaignStore.setOA(
        oaList.value?.filter((item: TDropdownItem) => item.value === sourceId)[0]?.label,
      );
    } else if (props.channel_type === ChannelValue.SMS) {
      campaignStore.setBrandname(
        brandnameList.value?.filter((item: TDropdownItem) => item.value === sourceId)[0]?.label,
      );
    }
  }, 100);

  if (!sourceId) {
    templateList.value = [];
    return;
  }
  overlayLoading.toggleLoading(true);
  api
    .get(`campaign/v1/api/campaign/template/${sourceId}`)
    .then((response) => {
      if (response.data.code === 0) {
        templateList.value = response.data.data.map((item: any) => ({
          value: item.id,
          label: item.name ?? item.id,
        }));
        overlayLoading.toggleLoading(false);
      } else {
        overlayLoading.toggleLoading(false);
        toast('error', response.data.message);
      }
    })
    .catch((err) => {
      overlayLoading.toggleLoading(false);
      console.error(err);
      toast('error', TEXT.ERROR_OCCURRED);
    });
};
//#endregion

//#region Form

const validationSchema: any = yup.object({
  label_id: yup.lazy(() => {
    if (props.channel_type === ChannelValue.ZNS) {
      return yup.number().required('Vui lòng chọn OA');
    } else return yup.mixed().notRequired();
  }),
  brandname_id: yup.lazy(() => {
    if (props.channel_type === ChannelValue.SMS) {
      return yup.number().required('Vui lòng chọn Brandname');
    } else return yup.mixed().notRequired();
  }),
  template_id: yup.number().required('Vui lòng chọn Template'),
  parameters: yup.array().of(
    yup.object({
      param_value: yup.string().trim().required('Giá trị tham số không được để trống'),
      param_custom: yup.lazy((_, context) => {
        if (context.parent.param_type === CUSTOM_FIELD_TYPE) {
          return yup.string().required('Nội dung không được để trống');
        }
        return yup.mixed().notRequired();
      }),
    }),
  ),
});

const { values, setValues, handleSubmit, errors, setFieldError, setErrors } = useForm<any>({
  validationSchema,
});

const {
  fields: parameterDatas,
  replace: replaceParameterDatas,
  update: updateParameterDatas,
} = useFieldArray<TTemplateParams>('parameters');

const channelTypeName = computed(() => (props.channel_type === ChannelValue.SMS ? 'SMS' : 'ZNS'));

const templateData = ref<any>();

const resetFieldErrors = (data: any[] = parameterDatas.value) => {
  setTimeout(() => {
    data.forEach((_, idx: number) => {
      setFieldError(`parameters[${idx}].param_value`, '');
      setFieldError(`parameters[${idx}].param_custom`, '');
    });
  }, 10);
};

const mapParameterData = (params: any) => {
  const temp: TTemplateParams[] = params.map((item: any) => {
    return {
      param_name:
        props.channel_type === ChannelValue.SMS && !`${item.name}`.includes('<')
          ? `<${item.name}>`
          : item.name,
      param_value: '',
      param_custom: '',
      param_type: '',
    } as TTemplateParams;
  });

  replaceParameterDatas(temp);

  resetFieldErrors(temp);
};

const mapDataDetails = (znsData: any, isRealSelect: boolean) => {
  const znsType = znsData.template_type;
  const fileType =
    znsData.file_upload[0]?.template_type_id === ZnsTypeImg.Image
      ? ZnsConfigImg.UploadImage
      : ZnsConfigImg.Logo;
  const step2Data: any = {
    file_type: fileType,
    file: znsData.file_upload,
    title: znsData.template_title,
    body: znsData.template_content?.split('<br>'),
    extra_content: znsData.note?.split('<br>'),
    technical_config: znsData.param_dto?.map((param: any) => {
      return {
        parameter_name: param.name,
        zalo_data_field: param.field_zalo_id,
        default_data_field: param.sample_data,
      };
    }),
  };
  if (isRealSelect) mapParameterData(znsData.param_dto);
  if (znsType === ZnsType.Table && znsData.table_data) {
    const responseTable = JSON.parse(znsData.table_data);
    step2Data.table = [];
    for (const property in responseTable) {
      step2Data.table.push({
        name: property,
        param: responseTable[property],
      });
    }
  } else if (znsType === ZnsType.Rate) {
    step2Data.rate_content = znsData.rating;
  } else if (znsType === ZnsType.Otp) {
    step2Data.otp_content = znsData.template_content;
  } else if (znsType === ZnsType.Payment && znsData.table_data) {
    const responseTable = JSON.parse(znsData.table_data);
    step2Data.table = [];
    for (const property in responseTable) {
      step2Data.table.push({
        name: property,
        param: responseTable[property],
      });
    }
    step2Data.bank_content = znsData.bank_dto;
  } else if (znsType === ZnsType.Voucher && znsData.table_data) {
    const responseTable = JSON.parse(znsData.table_data);
    step2Data.table = [];
    for (const property in responseTable) {
      step2Data.table.push({
        name: property,
        param: responseTable[property],
      });
    }
    step2Data.voucher_content = { ...znsData.voucher_dto };
  }
  if (znsData?.main_button) {
    step2Data.primary_button = {};
    const primaryButton = JSON.parse(znsData.main_button);
    step2Data.primary_button.content = primaryButton.name;
  }
  if (znsData?.extra_button) {
    step2Data.secondary_button = {};
    const extraButton = JSON.parse(znsData.extra_button);
    step2Data.secondary_button.content = extraButton.name;
  }
  templateData.value = {
    content: step2Data,
    OA: znsData.label_name,
    znsType: znsData.template_type,
    znsName: znsData.template_name,
    status: znsData.status,
    reason: znsData.reason ?? '',
    templateId: znsData.template_id,
    zalo_id: znsData.zalo_id,
  };
  if (props.is_failover) {
    campaignStore.setTemplateFailoverData(templateData.value);
  } else {
    campaignStore.setTemplateData(templateData.value);
  }
};

const handleSelectParamValue = (paramName: string, value: any) => {
  resetAllParamErrors();
  const paramType = parameterOptions.value?.filter((item: any) => item.value === value);
  if (paramType) {
    const paramIndex = parameterDatas.value.findIndex(
      (item: any) => item.value.param_name === paramName,
    );

    if (paramIndex !== -1) {
      setTimeout(() => {
        updateParameterDatas(paramIndex, {
          ...parameterDatas.value[paramIndex].value,
          param_type: paramType[0].type,
          param_custom: '',
        });
      }, 10);
    }
  }
};

const resetAllParamErrors = () => {
  blockedKeywords.value = [];
  resetFieldErrors();
};

const handleSelectTemplate = async (
  templateId: number,
  isRealSelect: boolean = true,
  isFailover: boolean = false,
) => {
  try {
    resetAllParamErrors();
    overlayLoading.toggleLoading(true);
    const res = await api.get(`/business/v1/api/client/zns-template/${templateId}`);
    if (res.data.code === 0) {
      const ZNS = res.data.data;
      mapDataDetails(ZNS, isRealSelect);
      if (!isFailover) {
        campaignStore.setTemplateName(ZNS.template_name);
      } else {
        campaignStore.setTemplateFailoverName(ZNS.template_name);
      }
    } else {
      await toast('error', res.data.message);
    }
  } catch {
    await toast('error', TEXT.ERROR_OCCURRED);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

const setValue = () => {
  try {
    if (!props.is_failover) {
      templateData.value = campaignStore.templateData;
      replaceParameterDatas(
        campaignStore.parameters?.map((item: TTemplateParams) => {
          return {
            param_name: item.param_name,
            param_type: item.param_type,
            param_value:
              item.param_type === CUSTOM_FIELD_TYPE ? CUSTOM_FIELD_VALUE : item.param_value,
            param_custom: item.param_type === CUSTOM_FIELD_TYPE ? item.param_value : '',
          };
        }),
      );
      if (props.channel_type === ChannelValue.ZNS) {
        setValues({
          template_id: campaignStore.step3Data?.content?.template_id,
          label_id: campaignStore.step3Data?.content?.label_id,
        });
      } else if (props.channel_type === ChannelValue.SMS) {
        setValues({
          template_id: campaignStore.step3Data?.content?.template_id,
          brandname_id: campaignStore.step3Data?.content?.label_id,
        });
      }
      getListTemplate(campaignStore.step3Data?.content?.label_id);
    } else {
      templateData.value = campaignStore.templateFailoverData;
      replaceParameterDatas(
        campaignStore.parameterFailovers?.map((item: TTemplateParams) => {
          return {
            param_name: item.param_name,
            param_type: item.param_type,
            param_value:
              item.param_type === CUSTOM_FIELD_TYPE ? CUSTOM_FIELD_VALUE : item.param_value,
            param_custom: item.param_type === CUSTOM_FIELD_TYPE ? item.param_value : '',
          };
        }),
      );
      if (props.channel_type === ChannelValue.ZNS) {
        setValues({
          template_id: campaignStore.step3Data?.content?.template_id_failover,
          label_id: campaignStore.step3Data?.content?.label_id_failover,
        });
      } else if (props.channel_type === ChannelValue.SMS) {
        setValues({
          template_id: campaignStore.step3Data?.content?.template_id_failover,
          brandname_id: campaignStore.step3Data?.content?.label_id_failover,
        });
      }
      getListTemplate(campaignStore.step3Data?.content?.label_id_failover);
    }
  } catch (err: any) {
    console.error(err);
  }
};

const setValuePreview = () => {
  try {
    if (!props.is_failover) {
      templateData.value = campaignStore.templateData;
      if (props.type !== FORM_TYPE.DETAILS) {
        replaceParameterDatas(campaignStore.parameters);
      } else {
        replaceParameterDatas(
          campaignStore.parameters?.map((item: TTemplateParams) => {
            return {
              param_name: item.param_name,
              param_type: item.param_type,
              param_value:
                item.param_type === CUSTOM_FIELD_TYPE ? CUSTOM_FIELD_VALUE : item.param_value,
              param_custom: item.param_type === CUSTOM_FIELD_TYPE ? item.param_value : '',
            };
          }),
        );
      }
      if (props.channel_type === ChannelValue.ZNS) {
        setValues({
          template_id: campaignStore.step3Data?.content?.template_id,
          label_id: campaignStore.step3Data?.content?.label_id,
        });
      } else if (props.channel_type === ChannelValue.SMS) {
        setValues({
          template_id: campaignStore.step3Data?.content?.template_id,
          brandname_id: campaignStore.step3Data?.content?.label_id,
        });
      }
    } else {
      templateData.value = campaignStore.templateFailoverData;
      if (props.type !== FORM_TYPE.DETAILS) {
        replaceParameterDatas(campaignStore.parameterFailovers);
      } else {
        replaceParameterDatas(
          campaignStore.parameterFailovers?.map((item: TTemplateParams) => {
            return {
              param_name: item.param_name,
              param_type: item.param_type,
              param_value:
                item.param_type === CUSTOM_FIELD_TYPE ? CUSTOM_FIELD_VALUE : item.param_value,
              param_custom: item.param_type === CUSTOM_FIELD_TYPE ? item.param_value : '',
            };
          }),
        );
      }
      if (props.channel_type === ChannelValue.ZNS) {
        setValues({
          template_id: campaignStore.step3Data?.content?.template_id_failover,
          label_id: campaignStore.step3Data?.content?.label_id_failover,
        });
      } else if (props.channel_type === ChannelValue.SMS) {
        setValues({
          template_id: campaignStore.step3Data?.content?.template_id_failover,
          brandname_id: campaignStore.step3Data?.content?.label_id_failover,
        });
      }
    }
  } catch (err: any) {
    console.error(err);
  }
};

const validateBlocked = async () => {
  try {
    if (props.channel_type === ChannelValue.ZNS || !parameterDatas.value?.length) return true;
    const payload: any = {};
    parameterDatas.value?.forEach((item: any) => {
      payload[`${getRawParam(item.value.param_name)}`] = item.value.param_custom;
    });
    overlayLoading.toggleLoading(true);
    const res = await api.post(
      `/business/v1/api/admin/message-policy/check-blocked-keywords-template`,
      {
        templateId: values.template_id,
        keywords: payload,
      },
    );
    if (res.data.code === 0) {
      const resData = res.data.data;
      blockedKeywords.value = resData.blocked_keywords;
      overlayLoading.toggleLoading(false);
      return resData.is_valid;
    } else {
      overlayLoading.toggleLoading(false);
      toast('error', res.data.message);
      return false;
    }
  } catch (error) {
    console.error(error);
    overlayLoading.toggleLoading(false);
    toast('error', TEXT.ERROR_OCCURRED);
    return false;
  }
};

const onSubmitVee = handleSubmit(async (value: any) => {
  const isValid = await validateBlocked();
  if (!isValid) return;
  if (!props.is_failover) {
    campaignStore.setParameters(value.parameters);
    emit('onSubmitForm', value);
  } else {
    campaignStore.setParameterFailovers(value.parameters);
    emit('onSubmitFormFailover', value);
  }
});

const onDraft = () => {
  if (!props.is_failover) {
    campaignStore.setParameters(values.parameters);
  } else {
    campaignStore.setParameterFailovers(values.parameters);
  }
  return values;
};

const onSubmit = () => {
  onSubmitVee();
};
//#endregion

watch(
  () => campaignStore.active,
  async (newValue: StepValue) => {
    if (newValue === StepValue.StepThree && !props.isPreview) {
      if (props.channel_type === ChannelValue.SMS) {
        await getListActiveBrandname();
      } else {
        await getListActiveOa();
      }
      blockedKeywords.value = [];
      setErrors({
        label_id: '',
        brandname_id: '',
        template_id: '',
        parameters: '',
      });
    }
  },
  {
    immediate: true,
  },
);

const resetData = (newValue: ChannelValue) => {
  if (newValue === ChannelValue.SMS) {
    brandnameList.value = [];
    setValues({
      template_id: null,
      brandname_id: null,
    });
  } else if (newValue === ChannelValue.ZNS) {
    oaList.value = [];
    setValues({
      template_id: null,
      label_id: null,
    });
  }
  templateList.value = [];
  templateData.value = undefined;
  replaceParameterDatas([]);
};

const resetParams = () => {
  setTimeout(() => {
    setValues({
      template_id: null,
    });
    templateData.value = undefined;
    replaceParameterDatas([]);
  }, 200);
};

watch(
  () => campaignStore.step2Data?.target_group?.target_group_id,
  () => {
    if (props.type !== FORM_TYPE.DETAILS) resetParams();
  },
);

watch(
  () => campaignStore.step2Data?.target_group?.target_group_type,
  () => {
    if (props.type !== FORM_TYPE.DETAILS) resetParams();
  },
);

watch(
  () => campaignStore.step2Data?.target_group?.subscriber_upload_file,
  () => {
    if (props.type !== FORM_TYPE.DETAILS) resetParams();
  },
);

watch(
  () => campaignStore.step1Data?.use_failover,
  (newValue) => {
    if (!newValue && props.is_failover) {
      if (props.channel_type === ChannelValue.SMS) {
        brandnameList.value = [];
        setValues({
          template_id: null,
          brandname_id: null,
        });
      } else if (props.channel_type === ChannelValue.ZNS) {
        oaList.value = [];
        setValues({
          template_id: null,
          label_id: null,
        });
      }
      templateList.value = [];
      templateData.value = undefined;
      replaceParameterDatas([]);
    }
  },
);

watch(
  () => campaignStore.step1Data?.channel_type,
  (newValue: ChannelValue) => {
    if (!props.is_failover) {
      resetData(newValue);
    }
  },
);

watch(
  () => campaignStore.step1Data?.channel_type_failover,
  (newValue) => {
    if (props.is_failover) {
      resetData(newValue);
    }
  },
);

const handleChangeLabelId = (value: any) => {
  getListTemplate(value);

  setValues({
    template_id: null,
  });
  templateList.value = [];
  templateData.value = undefined;
  replaceParameterDatas([]);
  resetAllParamErrors();
};

defineExpose({
  onSubmit,
  onSubmitVee,
  setValue,
  setValuePreview,
  handleSelectTemplate,
  onDraft,
});
</script>

<template>
  <div
    :class="
      is_failover
        ? `bg-[#FBFAFF] border-y-[1px] border-[#DFE4EA] py-[30px] mx-[5%] px-[4%] mt-8 flex justify-between mb-[50px] gap-[40px]  ${stringGetter(customClass)} ${isPreview && 'mr-0'}`
        : `pb-[30px] mx-[5%] px-[4%] mt-8 flex items-start justify-between gap-[40px]  ${stringGetter(customClass)}  ${isPreview ? 'mr-0' : ''}`
    "
  >
    <div class="w-1/2">
      <div class="text-[14px] text-[#212121] font-semibold mb-[10px]">
        {{ `Thiết lập nội dung tin ${channelTypeName}${is_failover ? ' khi có Failover' : ''}` }}
      </div>
      <div class="flex flex-col gap-y-[8px]">
        <div>
          <div class="text-[14px] text-[#6b7280] font-semibold mb-[2px]">
            {{ props.channel_type === ChannelValue.ZNS ? 'OA' : 'Brandname' }}
            <span v-if="!isPreview" class="text-[#F10000]">*</span>
          </div>
          <div v-if="!isPreview">
            <VElementDropdown
              v-if="props.channel_type === ChannelValue.ZNS"
              name="label_id"
              label=""
              :disabled="type === FORM_TYPE.DETAILS || props.isPreview"
              :placeholder="PLACEHOLDER.SELECT"
              :required="true"
              :filterable="true"
              :option="oaList"
              :style="'w-[100%]'"
              @change="handleChangeLabelId"
            />
            <VElementDropdown
              v-else
              name="brandname_id"
              label=""
              :disabled="type === FORM_TYPE.DETAILS || props.isPreview"
              :placeholder="PLACEHOLDER.SELECT"
              :required="true"
              :filterable="true"
              :option="brandnameList"
              :style="'w-[100%]'"
              @change="handleChangeLabelId"
            />
          </div>
          <div v-else>
            <p
              v-if="props.channel_type === ChannelValue.ZNS"
              class="font-semibold text-[14px] mt-[5px]"
            >
              {{ campaignStore.oa }}
            </p>
            <p v-else class="font-semibold text-[14px] mt-[5px]">{{ campaignStore.brandname }}</p>
          </div>
        </div>
        <div>
          <div class="text-[14px] text-[#6b7280] font-semibold mb-[2px]">
            Template <span v-if="!isPreview" class="text-[#F10000]">*</span>
          </div>
          <VElementDropdown
            v-if="!isPreview"
            name="template_id"
            label=""
            :disabled="type === FORM_TYPE.DETAILS || props.isPreview"
            :placeholder="PLACEHOLDER.SELECT"
            :required="true"
            :filterable="true"
            :option="templateList"
            :style="'w-[100%]'"
            @change="($event: any) => handleSelectTemplate($event, true, props.is_failover)"
          />
          <p v-else class="font-semibold text-[14px] mt-[5px]">
            {{
              props.is_failover ? campaignStore.templateFailoverName : campaignStore.templateName
            }}
          </p>
        </div>
        <div v-show="values.template_id && parameterDatas.length">
          <div class="text-[14px] text-[#6b7280] font-semibold mb-[2px] flex items-center">
            <p class="mr-[4px]">Cài đặt giá trị tham số</p>
            <span v-if="!isPreview" class="text-[#F10000]">*</span>
            <div v-if="!isPreview" class="cursor-pointer">
              <el-tooltip placement="right">
                <template #content>
                  <p class="max-w-[320px]">
                    Bạn có thể chọn các Giá trị tham số là tên các trường trong danh sách đối tượng
                    bạn sử dụng hoặc dùng giá trị tùy chỉnh để thiết lập một nội dung cố định cho
                    tham số
                  </p>
                </template>
                <Icon icon="mingcute:information-fill" class="text-[#5a5e65] ml-[6px]" />
              </el-tooltip>
            </div>
          </div>
        </div>
        <div v-show="values.template_id && parameterDatas.length">
          <div class="flex items-center gap-[6px] pt-[10px]">
            <div class="w-[40%] text-[13px] text-[#6b7280]">Tên tham số</div>
            <div class="flex-1 text-[13px] text-[#6b7280]">Giá trị tham số</div>
          </div>
          <div v-for="(setting, idx) in parameterDatas" class="flex gap-[6px] pt-[5px]" :key="idx">
            <el-input
              v-model="setting.value.param_name"
              class="h-[32px] !w-[40%]"
              size="default"
              :disabled="true"
            />
            <div class="flex flex-col flex-1">
              <div class="flex flex-col">
                <el-form-item
                  v-bind="{
                    error: errors[`parameters[${idx}].param_value`],
                  }"
                  :disabled="props.type === FORM_TYPE.DETAILS || props.isPreview"
                >
                  <el-select-v2
                    v-model="setting.value.param_value"
                    class="!w-full"
                    no-data-text="Không có dữ liệu để hiển thị"
                    no-match-text="Không có kết quả tìm kiếm"
                    :filterable="true"
                    :options="parameterOptions"
                    :placeholder="PLACEHOLDER.SELECT"
                    :disabled="type === FORM_TYPE.DETAILS || props.isPreview"
                    @change="
                      ($event: any) => handleSelectParamValue(setting.value.param_name, $event)
                    "
                  />
                </el-form-item>
              </div>
              <el-form-item
                :class="
                  errors[`parameters[${idx}].param_custom`] ||
                  blockedKeywords[getRawParam(setting.value.param_name)]
                    ? 'is-error'
                    : ''
                "
                :disabled="props.type === FORM_TYPE.DETAILS || props.isPreview"
              >
                <el-input
                  v-if="setting.value.param_type === CUSTOM_FIELD_TYPE"
                  v-model="setting.value.param_custom"
                  class="h-[32px] !w-full mt-[4px]"
                  size="default"
                  :placeholder="PLACEHOLDER.TYPE"
                  :disabled="type === FORM_TYPE.DETAILS || props.isPreview"
                  :maxlength="200"
                  @blur="
                    () => {
                      setting.value.param_custom = setting.value.param_custom?.trim();
                    }
                  "
                />
                <div
                  v-if="
                    errors[`parameters[${idx}].param_custom`] ||
                    blockedKeywords[getRawParam(setting.value.param_name)]
                  "
                  class="space-x-[6px] text-[12px] mt-[1px]"
                >
                  <p class="text-[#f56c6c] leading-[18px]">
                    {{
                      errors[`parameters[${idx}].param_custom`] ||
                      `Nội dung không được phép chứa từ khóa chặn "${blockedKeywords[getRawParam(setting.value.param_name)]}".`
                    }}
                  </p>
                  <p
                    v-if="
                      setting.value.param_type === CUSTOM_FIELD_TYPE &&
                      channel_type === ChannelValue.SMS &&
                      blockedKeywords[getRawParam(setting.value.param_name)]
                    "
                    class="cursor-pointer underline text-blue-600 hover:text-blue-800 leading-[18px]"
                    @click="isVisibleBlockPopup = true"
                  >
                    Xem danh sách từ khóa bị chặn
                  </p>
                </div>
              </el-form-item>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="w-1/2 mt-[31px]">
      <div v-if="props.channel_type === ChannelValue.ZNS" class="flex justify-end w-full">
        <ZnsWrapper
          v-if="templateData"
          title="Xem trước Template"
          titleClass="mb-[10px]"
          classCustom="!w-[380px] max-w-full"
          :type="FORM_TYPE.CAMPAIGN"
        >
          <TextType
            v-if="templateData?.znsType === ZnsType.Text"
            :type="ZnsPageType.Index"
            :items="templateData.content"
            :typeStep="ZnsType.Text"
          />
          <OtpType
            v-if="templateData?.znsType === ZnsType.Otp"
            :type="ZnsPageType.Index"
            :items="templateData.content"
            :typeStep="ZnsType.Otp"
          />
          <TableType
            v-if="templateData?.znsType === ZnsType.Table"
            :type="ZnsPageType.Index"
            :items="templateData.content"
            :typeStep="ZnsType.Table"
          />
          <RateType
            v-if="templateData?.znsType === ZnsType.Rate"
            :type="ZnsPageType.Index"
            :items="templateData.content"
            :rate-star="5"
            :typeStep="ZnsType.Rate"
          />
          <PaymentRequestType
            v-if="templateData?.znsType === ZnsType.Payment"
            :type="ZnsPageType.Index"
            :items="templateData.content"
            :typeStep="ZnsType.Payment"
          />
          <VoucherType
            v-if="templateData?.znsType === ZnsType.Voucher"
            :type="ZnsPageType.Index"
            :items="templateData.content"
            :typeStep="ZnsType.Voucher"
          />
        </ZnsWrapper>
      </div>
      <div v-else class="flex justify-end w-full">
        <ZnsWrapper
          v-if="templateData"
          title="Xem trước Template"
          titleClass="mb-[10px]"
          classCustom="!w-[380px] max-w-full"
          :type="FORM_TYPE.CAMPAIGN"
        >
          <TextType
            :type="ZnsPageType.Index"
            :items="templateData.content"
            :typeStep="ZnsType.Text"
          />
        </ZnsWrapper>
      </div>
    </div>
    <PopupKeywordBlock
      :templateId="values.template_id"
      :visible="isVisibleBlockPopup"
      :listBlockedKeyword="filteredBlockedKeywords"
      @onClose="isVisibleBlockPopup = false"
    />
  </div>
</template>

<style scoped lang="scss">
.el-form-item.is-error {
  margin-bottom: 14px;
}
</style>
