<script setup lang="ts">
//#region Imports
import { onMounted, ref, reactive, inject } from 'vue';
import { vOnClickOutside } from '@vueuse/components';
import { useForm } from 'vee-validate';
import moment from 'moment';
import * as yup from 'yup';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useApi } from '@/store/useApi';
import FilterCard from '@/components/base/container/FilterCard.vue';
import VOldTable from '@/components/base/VOldTable.vue';
import { color } from '@/constants/statusColor';
import { PLACEHOLDER, TEXT } from '@/shared';
import { FORMAT } from '@/shared/text.shared';
import { formatDateTime } from '@/utils/getter.utils';
import type { IParamsSearch, IDataHistorySent } from '../index.type';
import {
  headersHistorySended,
  channels,
  styleHeadersHistorySended,
  TAB_HISTORY_STATUS_FILTER_OPTIONS,
  TAB_HISTORY_NETWORK_FILTER_OPTIONS,
  EMPTY_STRING,
} from '../index.constants';
import {
  getOperatorLabel,
  getCampaignChanelTypeTable,
  getCampaignHistorySentColorStatus,
  getCampaignHistorySentLabelStatus,
} from '../index.utils';
import { createFilterCardClickOutsideHandler } from '@/utils/clickOutside';
//#endregion

//#region Props & Setup
const props = defineProps<{
  id?: number;
}>();

// Core services
const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();

// Form validation
const { values } = useForm({
  validationSchema: yup.object({
    org_id: yup.mixed(),
  }),
});
//#endregion

//#region Reactive Data
const tableRef = ref();
const filterCount = ref('');
const isShowFilterCard = ref(false);
const filterRef = ref<InstanceType<typeof FilterCard>>();
const tableWidth = ref();
const selectedStartDate = ref<Date | null>(null);
const isStopDisable = ref(true);
const params = ref<IParamsSearch>({
  page_index: 1,
  page_size: 20,
});
const dataHistorySent = ref<IDataHistorySent>();
//#endregion

//#region
const getDefaultDateRange = () => {
  const endDate = moment().toDate();
  const startDate = moment().subtract(30, 'days').toDate();
  return [startDate, endDate];
};

const disabledDatesForRange = (date: Date) => {
  if (!selectedStartDate.value || isStopDisable.value) {
    return false;
  }

  const dateToCheck = moment(date);
  const startDate = moment(selectedStartDate.value);

  // Disable dates before start date - 30 days and after start date + 30 days
  const thirtyDaysBeforeStart = startDate.clone().subtract(30, 'days');
  const thirtyDaysAfterStart = startDate.clone().add(30, 'days');

  return (
    dateToCheck.isBefore(thirtyDaysBeforeStart, 'day') ||
    dateToCheck.isAfter(thirtyDaysAfterStart, 'day')
  );
};

const dataFilter = reactive([
  {
    label: 'Thời gian',
    valueName: 'time_date',
    type: 'calendar_range',
    clearable: false,
    defaultValue: getDefaultDateRange(),
    disabledDates: disabledDatesForRange,
  },
  {
    label: 'Thuê bao',
    valueName: 'phone_number',
    type: 'input',
    placeholder: PLACEHOLDER.TYPE,
  },
  {
    label: 'Kênh gửi tin',
    valueName: 'channel_type',
    type: 'dropdown',
    filterable: true,
    placeholder: PLACEHOLDER.SELECT,
    dropdownConfig: {
      option: channels,
    },
  },
  {
    label: 'Trạng thái',
    type: 'dropdown',
    filterable: true,
    valueName: 'status',
    placeholder: PLACEHOLDER.SELECT,
    dropdownConfig: {
      option: TAB_HISTORY_STATUS_FILTER_OPTIONS,
    },
  },
  {
    label: 'Nhà mạng',
    type: 'dropdown',
    filterable: true,
    valueName: 'operator_id',
    placeholder: PLACEHOLDER.SELECT,
    dropdownConfig: {
      option: TAB_HISTORY_NETWORK_FILTER_OPTIONS,
    },
  },
]);

const onFilterChange = (data: any) => {
  if (data.field === 'daterange' && data.name === 'time_date') {
    if (data.value && data.value.length > 0) {
      selectedStartDate.value = data.value[0];
    } else {
      selectedStartDate.value = null;
    }
  }
};

const onFilterDateChange = (data: any) => {
  if (data.field === 'daterange' && data.name === 'time_date') {
    if (!data.value || data.value.length < 2 || !data.value[0] || !data.value[1]) {
      selectedStartDate.value = data.value?.[0] || null;
      isStopDisable.value = false;
    } else {
      isStopDisable.value = true;
    }
  }
};

const handleResetForm = () => {
  filterRef.value?.setFieldValue('time_date', getDefaultDateRange());
  const [fromDate, toDate] = getDefaultDateRange();
  params.value.date_from = moment(fromDate).format(FORMAT.INVERSE_DATE);
  params.value.date_to = moment(toDate).format(FORMAT.INVERSE_DATE);
  params.value.page_index = 1;
};

const filterData = (value: any) => {
  isShowFilterCard.value = false;
  params.value.phone_number = value.phone_number;
  params.value.channel_type = value.channel_type;
  params.value.status = value.status;
  params.value.operator_id = value.operator_id;
  if (value.time_date) {
    params.value.date_from = formatDateTime(value.time_date[0], FORMAT.INVERSE_DATE).toString();
    params.value.date_to = formatDateTime(value.time_date[1], FORMAT.INVERSE_DATE).toString();
  } else {
    params.value.date_from = '';
    params.value.date_to = '';
  }

  if (filterRef.value) {
    if (filterRef.value.count && filterRef.value.count > 0) {
      filterCount.value = `(${filterRef.value.count})`;
    } else {
      filterCount.value = '';
    }
  }

  dataFilter.forEach((item) => {
    if (value.hasOwnProperty(item.valueName)) {
      item.defaultValue = value[item.valueName] ?? '';
    }
  });
  tableRef.value.filterData();
};

const onFilter = () => {
  if (filterRef.value) {
    filterRef.value.onSubmit();
    if (filterRef.value.count && filterRef.value.count > 0) {
      filterCount.value = `(${filterRef.value.count})`;
    } else {
      filterCount.value = '';
    }
    return;
  }
  tableRef.value.filterData();
};

const onClickOutsideHandler = createFilterCardClickOutsideHandler(() => {
  isShowFilterCard.value = false;
});

const onSetWidth = (value: number) => {
  tableWidth.value = value;
};

const onPageChange = (value: number) => {
  params.value.page_index = value;
  getDataHistorySent();
};

const onPerPageChange = (value: number) => {
  params.value.page_size = value;
};

const getDataHistorySent = async () => {
  overlayLoading.toggleLoading(true);
  try {
    let url = `/campaign/v1/api/campaign/sending-history?campaign_id=${props.id}&page_size=${params.value.page_size}&page_index=${params.value.page_index}`;

    if (values.searchKey) {
      url += `&keyword=${values.searchKey.trim()}`;
    }
    if (params.value.phone_number) {
      url += `&phone_number=${params.value.phone_number.toString().trim()}`;
    }
    if (params.value.channel_type) {
      url += `&channel_type=${params.value.channel_type}`;
    }
    if (params.value.status || params.value.status === 0) {
      url += `&status=${params.value.status}`;
    }
    if (params.value.operator_id) {
      url += `&operator_id=${params.value.operator_id}`;
    }
    if (params.value.date_from) {
      url += `&date_from=${params.value.date_from}`;
    }
    if (params.value.date_to) {
      url += `&date_to=${params.value.date_to}`;
    }

    const res = await api.get(url);
    if (res.data.code === 0) {
      dataHistorySent.value = res.data.data;
    }
  } catch (error: any) {
    if (!error?.response?.status) {
      toast('error', TEXT.ERROR_OCCURRED);
    }
  } finally {
    overlayLoading.toggleLoading(false);
  }
};
//#endregion

//#region Lifecycle
onMounted(async () => {
  await getDataHistorySent();
  const [fromDate, toDate] = getDefaultDateRange();
  params.value.date_from = moment(fromDate).format(FORMAT.INVERSE_DATE);
  params.value.date_to = moment(toDate).format(FORMAT.INVERSE_DATE);
});
//#endregion
</script>

<template>
  <div class="">
    <el-form class="relative filter-container" @submit.prevent="onFilter">
      <div class="flex gap-[12px]">
        <VElementButton
          styleIcon="text-[20px] text-[#fff] mr-[7px]"
          icon="filter-plus"
          class="filter"
          styleButton="s"
          :bgColor="color.tertiary"
          :label="`Bộ lọc ${filterCount}`"
          @click="isShowFilterCard = !isShowFilterCard"
        />
        <VElementInput
          size="default"
          name="searchKey"
          prefix="search"
          placeholder="Tìm kiếm theo Thuê bao, Nội dung,..."
          :style="'!w-[480px]'"
          :maxlength="100"
          :showLimit="false"
          @keyup.enter="getDataHistorySent()"
        />
        <VElementButton label="Tìm kiếm" styleButton="s" :bgColor="color.main" @click="onFilter" />
        <Transition>
          <FilterCard
            v-if="isShowFilterCard"
            ref="filterRef"
            v-on-click-outside="onClickOutsideHandler"
            filterHeight="h-[230px]"
            width="w-[360px]"
            width-input="!w-[215px]"
            :dataFilter="dataFilter"
            @filter="filterData"
            @onChange="onFilterChange"
            @onDateChange="onFilterDateChange"
            @onResetForm="handleResetForm"
          />
        </Transition>
      </div>
    </el-form>
    <VOldTable
      idTable="tai-khoan-table"
      ref="tableRef"
      classContainer="!max-h-[calc(100vh-435px)]"
      :rows="dataHistorySent?.items ?? []"
      :headers="headersHistorySended"
      :styleHeaders="styleHeadersHistorySended"
      :showAction="false"
      :isCursorRow="false"
      :keySortTable="[]"
      :pagination="{
        totalPage: dataHistorySent?.paging?.total_pages ?? 0,
        total: dataHistorySent?.paging?.total_records ?? 0,
        perPage: params.page_size ?? 0,
      }"
      @setWidth="onSetWidth"
      @pageChanged="onPageChange"
      @perPageChange="onPerPageChange"
    >
      <template v-slot:items="{ row }">
        <td class="text-primaryText px-2">
          <div
            class="w-full line-clamp-1 text-ellipsis break-all text-center"
            :title="formatDateTime(row?.sending_time)"
          >
            {{ formatDateTime(row?.sending_time) }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div
            class="column-container line-clamp-1 text-ellipsis break-all text-center"
            :title="row?.phone_number"
          >
            {{ row?.phone_number }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div
            class="column-container line-clamp-1 text-ellipsis break-all"
            :title="
              getCampaignChanelTypeTable(
                row?.channel_type,
                row?.use_failover,
                row?.channel_type_failover,
              )
            "
          >
            {{
              getCampaignChanelTypeTable(
                row?.channel_type,
                row?.use_failover,
                row?.channel_type_failover,
              )
            }}
          </div>
        </td>
        <td class="text-primaryText p-2">
          <div
            class="break-normal !text-wrap h-max max-h-20 overflow-y-auto rounded-md bg-info p-2"
            :title="row?.message_content"
          >
            {{ row?.message_content }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div
            class="column-container text-center"
            :title="getCampaignHistorySentLabelStatus(row?.status)"
          >
            <Tag
              :class="getCampaignHistorySentColorStatus(row?.status)"
              :value="getCampaignHistorySentLabelStatus(row?.status)"
            />
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div
            class="column-container line-clamp-1 text-ellipsis break-all"
            :title="getOperatorLabel(row?.operator_id)"
          >
            {{ getOperatorLabel(row?.operator_id) }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container" :title="row?.notes ?? EMPTY_STRING">
            {{ row?.notes ?? EMPTY_STRING }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container text-center" :title="row?.mt_number ?? EMPTY_STRING">
            {{ row?.mt_number ?? EMPTY_STRING }}
          </div>
        </td>
      </template>
    </VOldTable>
  </div>
</template>

<style lang="scss" scoped>
:deep(.p-tag) {
  max-width: 120px;
  width: 100%;
}
</style>
