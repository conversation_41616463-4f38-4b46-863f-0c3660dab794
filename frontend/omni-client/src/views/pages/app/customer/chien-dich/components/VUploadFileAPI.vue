<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useField } from 'vee-validate';
import { useToast } from 'vue-toastification';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';

const overlayLoading = useOverLayLoadingStore();
const api = useApi();
const toast = useToast();
const props = withDefaults(
  defineProps<{
    fileSize: string;
    fileType: string;
    file: string;
    disabled?: boolean;
    required?: boolean;
    formType: string;
    fileTypes?: any;
    sizeToValidate?: number;
    label?: string;
    id?: string;
  }>(),
  {
    fileTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
      'image/jpeg',
      'image/png',
    ],
    sizeToValidate: 209715200,
  },
);

const fileUrl = ref();
const fileUrlError = ref();
const inputFile = ref();

const {
  value: fileSize,
  errorMessage: fileSizeErr,
  resetField: setSize,
} = useField<string | null>(props.fileSize);

const {
  value: fileType,
  errorMessage: fileTypeErr,
  resetField: setType,
} = useField<string | null>(props.fileType);

const { value: file, errorMessage: fileErr, resetField: setRequire } = useField<File>(props.file);

const isUpload = ref(false);

const clickInputFile = (ev: Event) => {
  const target = ev.target as Element;
  if (isUpload.value) {
    return;
  }
  if (target.classList.contains('iconify')) return;
  if (target.classList.contains('image-close-button')) return;
  if (target.tagName === 'path') return;
  inputFile.value.click();
};

const clickIconInputFile = () => {
  inputFile.value.click();
};

const uploadFile = async (event: any) => {
  const fileUpload = event.target.files[0];
  fileSize.value = fileUpload?.size;
  fileType.value = '';
  if (props.fileTypes.includes(fileUpload?.type)) {
    fileType.value = 'file';
  }
  if (props.fileTypes.includes(fileUpload?.type) && fileUpload?.size <= props.sizeToValidate) {
    fileUrl.value = fileUpload.name;
    file.value = fileUpload;
    const params = new FormData();
    params.append('type', '1');
    params.append('file', file.value);
    overlayLoading.toggleLoading(true);
    const res = await api.post('/portal/v1/upload/upload_file', params);
    if (res.data.status === 0) {
      overlayLoading.toggleLoading(false);
      fileUrl.value = res.data.data.url_path;
      isUpload.value = true;
    } else {
      overlayLoading.toggleLoading(false);

      toast.error(res.data.msg, {
        timeout: 3000,
      });
    }
  } else {
    isUpload.value = true;
    fileUrlError.value = fileUpload.name;
  }
};

const customFileName = (name: string) => {
  let str = '';
  if (name?.length > 15) {
    str += name.substr(0, 7);
    str += '...';
    str += name.substr(name.length - 5, name.length);
    return str;
  }
  return name;
};

const convertUrlName = (url: string) => {
  if (url) {
    const urlParts = url.split('/');
    const fileName = urlParts[urlParts.length - 1];
    return customFileName(fileName);
  }
  return '';
};

const baseURL = ref();

onMounted(() => {
  baseURL.value = import.meta.env.VITE_BASE_API_URL;
});

const removeImage = () => {
  fileUrl.value = '';
  fileUrlError.value = '';
  isUpload.value = false;
  setType({
    value: null,
    touched: false,
  });
  setSize({
    value: null,
    touched: false,
  });
  setRequire({
    value: undefined,
    touched: false,
  });
  if (props.formType === 'sua') {
    fileType.value = 'file';
  }
  inputFile.value.value = null;
};

defineExpose({
  fileUrl,
  isUpload,
});
</script>

<template>
  <el-form-item :required="required" :label="label">
    <div class="w-fit">
      <div
        @click="clickInputFile"
        @keydown="clickInputFile"
        class="relative"
        :class="isUpload ? 'uploaded' : 'upload'"
      >
        <div
          v-if="(fileUrl || fileUrlError) && formType !== 'xem'"
          class="image-close-button"
          @click="removeImage()"
          @keydown="removeImage()"
        >
          <Icon icon="tabler:x" class="text-[12px] text-[#fff]" />
        </div>
        <Icon
          v-if="isUpload && fileUrl?.includes(baseURL)"
          icon="tabler:download"
          class="text-[16px] text-[#4042E2]"
        />
        <div v-if="!isUpload" class="text-[#000]">Upload file</div>
        <a
          :href="fileUrl"
          target="_blank"
          v-else-if="fileUrl?.includes(baseURL)"
          class="text-[#4042E2] underline cursor-pointer"
          >{{ convertUrlName(fileUrl) }}</a
        >
        <div v-else-if="fileUrl" class="text-[#4042E2]">{{ customFileName(fileUrl) }}</div>
        <div v-else-if="fileUrlError" class="text-[#4042E2]">
          {{ customFileName(fileUrlError) }}
        </div>
        <Icon
          @click="clickIconInputFile"
          v-if="!isUpload"
          icon="tabler:upload"
          class="text-[17px] text-[#000]"
        />
      </div>
      <div class="leading-none">
        <small v-if="fileTypeErr" class="text-[#f56c6c] text-[12px]" :id="id + '-text-error'">{{
          fileTypeErr
        }}</small>
        <small
          v-else-if="fileSizeErr"
          class="text-[#f56c6c] text-[12px]"
          :id="id + '-text-error'"
          >{{ fileSizeErr }}</small
        >
        <small v-else-if="fileErr" class="text-[#f56c6c] text-[12px]" :id="id + '-text-error'">{{
          fileErr
        }}</small>
      </div>
    </div>
    <input ref="inputFile" class="hidden" type="file" @change="uploadFile" :id="id + '-field'" />
  </el-form-item>
</template>

<style scoped>
.upload {
  display: flex;
  gap: 10px;
  border: solid 1px #8ba6ff;
  color: #000;
  justify-content: center;
  border-radius: 6px;
  width: 122px;
  align-items: center;
  height: 34px;
  cursor: pointer;
  background-color: #e1e8ff;
}

.uploaded {
  display: flex;
  gap: 10px;
  border: solid 1px #4042e2;
  width: 160px;
  color: #4042e2;
  justify-content: center;
  border-radius: 6px;
  align-items: center;
  height: 34px;
  background-color: #fff;
}
.image-close-button {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  height: 15px;
  width: 15px;
  background-color: #e24c4c;
  cursor: pointer;
  position: absolute;
  top: -7px;
  right: -8px;
}
</style>
