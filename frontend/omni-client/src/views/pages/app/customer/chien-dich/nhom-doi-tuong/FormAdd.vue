<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:users-group" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item @click="handleCheckCancel" to="#">Nhóm đối tượng</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: route.path }">{{
          props.type === PageType.Add
            ? 'Thêm mới'
            : props.type === PageType.Update
              ? 'Cập nhật'
              : 'Xem chi tiết'
        }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
  <div class="view-height">
    <div class="justify-center flex">
      <el-steps
        align-center
        class="pt-[40px] w-[500px]"
        finish-status="success"
        :active="activeTab"
      >
        <el-step
          title="Thiết lập chung"
          class="cursor-pointer"
          :icon="Step1"
          @click="activeTab === 2 && turnBack()"
        />
        <el-step
          title="Thiết lập lọc đối tượng"
          class="cursor-pointer"
          :icon="activeTab === 2 ? Step2Done : Step2"
          @click="clickStep2()"
        />
      </el-steps>
    </div>
    <StepOne
      v-show="activeTab === 0"
      :data="dataStep1"
      @saveDataStep1="saveDataStep1"
      ref="stepOneRef"
      :type="props.type"
    />
    <div v-show="activeTab === 2">
      <StepTwo :data="dataStep2" @saveDataStep2="submitData" ref="stepTwoRef" :type="props.type" />
    </div>
    <div
      class="flex z-[999] py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke items-center"
      :class="
        props.type === PageType.Details || activeTab !== 0 ? 'justify-between' : 'justify-end'
      "
    >
      <VElementButton
        v-if="props.type === PageType.Details || activeTab !== 0"
        icon="arrow-narrow-left"
        text="black"
        bgColor="#EBEBEB"
        label="Quay lại"
        @click="turnBack"
      />
      <div>
        <VElementButton
          v-if="props.type !== PageType.Details"
          text="black"
          bgColor="#EBEBEB"
          label="Hủy"
          @click="handleCheckCancel()"
        />
        <VElementButton
          v-if="type !== PageType.Details && activeTab === 2"
          label="Lưu"
          :bgColor="color.main"
          @click="onSubmit"
        />
        <VElementButton
          v-if="activeTab === 0 && type !== PageType.Details"
          label="Tiếp tục"
          :bgColor="color.main"
          @click="clickStep2"
        />
      </div>
    </div>
  </div>
  <PopupCancelConfirm
    v-model:popupVisible="isShowConfirmPopup"
    @onClose="isShowConfirmPopup = false"
    @onConfirm="moveToList"
  />
</template>

<script setup lang="ts">
import { inject, onMounted, ref } from 'vue';
import Step1 from '@/components/icon/Step1.vue';
import Step2Done from '@/components/icon/Step2Done.vue';
import Step2 from '@/components/icon/Step2.vue';
import { useRouter, useRoute } from 'vue-router';
import { color } from '@/constants/statusColor';
import StepOne from './StepForm/StepOne.vue';
import StepTwo from './StepForm/StepTwo.vue';
import { ROUTE_NAME, TEXT } from '@/shared';
import { PageType } from '@/enums/common';
import PopupCancelConfirm from '@/components/base/common/PopupCancelConfirm.vue';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';

const props = defineProps<{
  id?: number;
  type: PageType;
}>();

const router = useRouter();
const route = useRoute();
const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();

const stepOneRef = ref();
const stepTwoRef = ref();
const isShowConfirmPopup = ref(false);
const activeTab = ref(0);
const dataStep1 = ref();
const dataStep2 = ref();

const getDetail = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const res = await api.get(`/cdp/v1/api/client/target-group/${route.params.id}`);
    if (res.data.code === 0) {
      dataStep1.value = res.data.data;
      dataStep2.value = res.data.data;
    } else {
      toast('error', res.data.message);
    }
    overlayLoading.toggleLoading(false);
  } catch (error: any) {
    overlayLoading.toggleLoading(false);
    if (!error?.response?.status) {
      toast('error', TEXT.ERROR_OCCURRED);
    }
  }
};

const openDetail = (id: number) => {
  router.push({
    name: ROUTE_NAME.DETAILS_NHOM_DOI_TUONG,
    params: {
      id: id,
    },
  });
};

const moveToList = () => {
  router.push({
    name: ROUTE_NAME.NHOM_DOI_TUONG,
  });
};

const handleCheckCancel = () => {
  isShowConfirmPopup.value = true;
};

const onSubmit = () => {
  stepTwoRef.value.onSubmit();
};

const clickStep2 = function () {
  if (activeTab.value === 0) {
    stepOneRef.value.onSubmit();
  }
};

const saveDataStep1 = (tab: number, data: any, linkFileMau: string) => {
  activeTab.value = tab;
  dataStep1.value = data;
  stepTwoRef.value.linkFileMau = linkFileMau;
};

const submitData = async (data: any) => {
  const body = {
    name: dataStep1.value.target_group_name,
    description: dataStep1.value.description,
    identification_method: dataStep1.value.identification_method,
    criteria: data.criteria,
    criteria_type: data.criteria_type,
    file_path: data.file ? data.file.file_path : null,
    error_file_path: data.file ? data.file.error_file_path : null,
    file_upload: data.file ? data.file : null,
  };
  try {
    let res;
    if (props.type === PageType.Update) {
      res = await api.put(`/cdp/v1/api/client/target-group/${route.params.id}`, body);
    } else {
      res = await api.post('/cdp/v1/api/client/target-group', body);
    }
    if (res.data.code === 0) {
      toast('success', res.data.message);
      openDetail(res.data.data.id);
    } else {
      toast('error', res.data.message);
    }
  } catch (error) {
    console.error(error);
  }
};

const turnBack = () => {
  if (activeTab.value === 0) {
    router.push({
      name: ROUTE_NAME.NHOM_DOI_TUONG,
    });
  } else {
    activeTab.value = 0;
  }
};

onMounted(() => {
  if (props.type === PageType.Update) {
    getDetail();
  }
});
</script>

<style lang="scss" scoped>
.view-height {
  height: calc(100vh - 172px);
  overflow: auto;
}
</style>
