<script setup lang="ts">
import { inject, onMounted, ref } from 'vue';
import { ROUTE_NAME, ROUTE_PATH, TabValue, TEXT, PLACEHOLDER } from '@/shared';
import { useRoute, useRouter } from 'vue-router';
import { historyHeaders, phuongThucOptions, styleHistoryHeaders } from './index.constant';
import { getCampaignLabelStatus, getCampaignColorStatus } from './index.utils';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useForm } from 'vee-validate';
import { color } from '@/constants/statusColor';
import type { IParamsGetList } from './index.type';
import VOldTable from '@/components/base/VOldTable.vue';
import moment from 'moment';
import Dialog from 'primevue/dialog';
import PopupInfo from '@/components/base/common/PopupInfo.vue';
import BaseTabs from '@/components/base/new/BaseTabs.vue';
import type { TTab } from '../../bang-gia/index.type';

const route = useRoute();
const router = useRouter();
const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const criteria = ref<any[]>([]);
const fileName = ref('');
const srcFileUrl = ref('');
const fileUpload = ref<any>();
const popupTargetFile = ref(false);
const status = ref('');
const urlFileFilter = ref('');
const criteria_type = ref('');
const popupIsUpdate = ref(false);
const popupIsValidateUpdate = ref(false);
const activeTab = ref<TabValue>(TabValue.Tab1);

const tabs: TTab[] = [
  {
    label: 'Thông tin nhóm đối tượng',
    value: TabValue.Tab1,
  },
  {
    label: 'Báo cáo nhóm đối tượng',
    value: TabValue.Tab2,
  },
];

const { setFieldValue } = useForm({});

const getDetail = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const res = await api.get(`/cdp/v1/api/client/target-group/${route.params.id}`);
    if (res.data.code === 0) {
      setFieldValue('target_group_name', res.data.data.target_group_name);
      setFieldValue('identification_method', res.data.data.identification_method);
      setFieldValue('description', res.data.data.description);
      setFieldValue('created_by', res.data.data.created_by);
      setFieldValue('created_at', res.data.data.created_at);
      setFieldValue('updated_at', res.data.data.updated_at);
      criteria.value = res.data.data.criteria;
      fileName.value = res.data.data.source_file_name;
      srcFileUrl.value = res.data.data.source_file_url;
      fileUpload.value = res.data.data.file_upload_response;
      status.value = res.data.data.status;
      urlFileFilter.value = res.data.data.filter_file_url;
      criteria_type.value = res.data.data.criteria_type;
    } else {
      toast('error', res.data.message);
    }
    overlayLoading.toggleLoading(false);
  } catch (error: any) {
    overlayLoading.toggleLoading(false);
    if (!error?.response?.status) {
      toast('error', TEXT.ERROR_OCCURRED);
    }
  }
};

const openUpdate = async () => {
  if (status.value === 'Đang xử lý') {
    popupIsUpdate.value = true;
    return;
  }
  try {
    overlayLoading.toggleLoading(true);
    const res = await api.get(
      `/cdp/v1/api/client/target-group/check-in-campaign/${route.params.id}`,
    );
    if (res.data.code === 0) {
      router.push({
        name: ROUTE_NAME.UPDATE_NHOM_DOI_TUONG,
        params: {
          id: route.params.id,
        },
      });
    } else {
      popupIsValidateUpdate.value = true;
      overlayLoading.toggleLoading(false);
    }
  } catch (error: any) {
    overlayLoading.toggleLoading(false);
    if (!error?.response?.status) {
      toast('error', TEXT.ERROR_OCCURRED);
    }
  }
};

const params = ref<IParamsGetList>({
  page_index: 1,
  page_size: 20,
});

const listObject = ref<any>();
const listObjectPaging = ref<any>();
const objectHeaders = ref<any[]>([]);
const styleObjectHeaders = ref<any[]>([]);

const getListObject = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const res = await api.get(`/cdp/v1/api/client/target-object/list`, {
      params: {
        page_index: params.value.page_index,
        page_size: params.value.page_size,
        target_group_id: route.params.id,
      },
    });
    if (res.data.code === 0) {
      if (res.data.data.items.length > 0) {
        listObject.value = res.data.data.items.map((item: any) => JSON.parse(item.additional_data));
        listObjectPaging.value = res.data.data.paging;
        objectHeaders.value = Object.keys(JSON.parse(res.data.data.items[0].additional_data)).map(
          (item: any) => ({
            name: item,
            visible: true,
            pin: false,
          }),
        );

        // Tạo styleObjectHeaders động theo số phần tử của objectHeaders
        styleObjectHeaders.value = objectHeaders.value.map((_, idx) => ({
          idx: idx,
          class: 'w-[200px] min-w-[200px]',
        }));
      }
    } else {
      toast('error', res.data.message);
    }
    overlayLoading.toggleLoading(false);
  } catch (error: any) {
    overlayLoading.toggleLoading(false);
    if (!error?.response?.status) {
      toast('error', TEXT.ERROR_OCCURRED);
    }
  }
};

const listHistory = ref<any>();

const getListHistory = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const res = await api.get(
      `/cdp/v1/api/client/target-group/target-group-history/${route.params.id}`,
    );
    if (res.data.code === 0) {
      listHistory.value = res.data.data;
    } else {
      toast('error', res.data.message);
    }
    overlayLoading.toggleLoading(false);
  } catch (error: any) {
    overlayLoading.toggleLoading(false);
    if (!error?.response?.status) {
      toast('error', TEXT.ERROR_OCCURRED);
    }
  }
};

const onPageChange = (value: number) => {
  params.value.page_index = value;
  getListObject();
};

const onPerPageChange = (value: number) => {
  params.value.page_size = value;
};

const tableRef = ref<any>();

const onFilter = () => {
  tableRef.value.filterData();
};

const handleShowResult = () => {
  popupTargetFile.value = true;
};

const handleDownloadFileFilter = () => {
  window.open(urlFileFilter.value);
};

const handleTabChanged = async (tabValue: TabValue) => {
  activeTab.value = tabValue;
};

onMounted(async () => {
  await getDetail();
  getListHistory();
  await getListObject();
});
</script>

<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:users-group" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="ROUTE_PATH.NHOM_DOI_TUONG">Nhóm đối tượng</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: route.path }">{{ 'Chi tiết' }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div>
      <el-dropdown>
        <el-button type="primary" class="!bg-[#E7E7FF] !text-black !border-[#8ba6ff]">
          Tuỳ chọn<el-icon class="el-icon--right"
            ><Icon icon="tabler:chevron-down" class="text-[25px] text-primaryText cursor-pointer"
          /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleDownloadFileFilter" :disabled="status !== 'Thành công'"
              >Xuất danh sách đối tượng</el-dropdown-item
            >
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
  <div class="view-height">
    <BaseTabs :tabs :active-tab="activeTab" tabClass="w-[94%]" @click-tab="handleTabChanged">
      <template #tab-1>
        <div class="flex justify-center">
          <div class="flex flex-col gap-3 w-full max-w-[1200px]">
            <!-- Form thông tin nhóm đối tượng -->
            <div class="grid grid-cols-2 gap-[100px]">
              <div class="flex flex-col gap-4">
                <el-form class="flex flex-col gap-4 w-full" label-position="top">
                  <VElementInput
                    :style="'w-[100%]'"
                    :required="true"
                    label="Tên nhóm đối tượng"
                    :maxlength="200"
                    name="target_group_name"
                    :placeholder="PLACEHOLDER.TYPE"
                    disabled
                    :popperClass="'!max-w-[400px]'"
                  />
                  <VElementDropdown
                    :style="'w-[100%]'"
                    :required="true"
                    label="Phương thức xác định"
                    :option="phuongThucOptions"
                    name="identification_method"
                    :placeholder="PLACEHOLDER.SELECT"
                    disabled
                  />
                  <VElementInput
                    :style="'w-[100%]'"
                    type="textarea"
                    label="Mô tả"
                    :maxlength="1000"
                    name="description"
                    :placeholder="PLACEHOLDER.TYPE"
                    disabled
                  />
                </el-form>
              </div>
              <div class="flex flex-col gap-4">
                <el-form class="flex flex-col gap-4 w-full" label-position="top">
                  <VElementInput
                    :style="'w-[100%]'"
                    label="Người tạo"
                    :maxlength="200"
                    name="created_by"
                    :placeholder="PLACEHOLDER.TYPE"
                    disabled
                    popper-class="!max-w-[400px]"
                  />
                  <VElementDateTimePicker
                    :style="'!w-[100%]'"
                    type="date"
                    format="HH:mm:ss DD/MM/YYYY"
                    label="Ngày tạo"
                    name="created_at"
                    disabled
                  />
                  <VElementDateTimePicker
                    :style="'!w-[100%]'"
                    type="date"
                    format="HH:mm:ss DD/MM/YYYY"
                    label="Ngày cập nhật"
                    name="updated_at"
                    disabled
                  />
                </el-form>
              </div>
            </div>
            <div>
              <div class="font-semibold text-[#6B7280] text-[14px]">
                Danh sách đối tượng nguồn <span class="text-[#f56c6c]">*</span>
              </div>
              <div class="flex gap-3 items-start mt-2">
                <VDownLoadFile :label="fileName" :url="srcFileUrl" />
                <Icon
                  icon="tabler:eye"
                  class="text-[25px] text-primaryText mt-[5px] cursor-pointer"
                  @click="handleShowResult"
                />
              </div>
            </div>
            <div>
              <div class="font-semibold text-[#6B7280] text-[14px]">Trạng thái xử lý</div>
              <div class="flex gap-3 items-start mt-2">
                <Tag
                  :class="
                    status === 'Thành công'
                      ? color.Green
                      : status === 'Thất bại'
                        ? color.Red
                        : color.Yellow
                  "
                  :value="status"
                />
              </div>
            </div>
            <!-- Box chi tiết tiêu chí -->
            <div
              class="bg-[#f6f8fb] border border-[#e5e7eb] border-l-4 target-container rounded-lg p-6 mt-6"
              v-if="criteria.length > 0"
            >
              <div class="font-semibold mb-2">Chi tiết tiêu chí</div>
              <div class="text-[14px] mb-3">{{ criteria_type }}:</div>
              <div class="flex flex-col gap-2">
                <div
                  class="inline-flex items-center gap-2 bg-[#E7E7FF] rounded-full px-4 py-1 w-fit"
                  v-for="item in criteria"
                  :key="item.id"
                >
                  <Icon icon="tabler:circle-check" class="text-[20px] text-[#6160DC] w-[30px]" />
                  <div class="flex-1">
                    {{ item.field_name }} {{ item.operator }} {{ item.value }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #tab-2>
        <div class="flex justify-center mt-5">
          <div class="flex flex-col gap-6 w-full max-w-[1200px]">
            <div>
              <div class="px-3 flex justify-between mb-3">
                <div
                  class="border-1 border-solid px-[15px] py-[6px] rounded-[30px] h-[34px] w-fit text-[14px] font-semibold flex items-center"
                  style="border-color: var(--main-color); color: var(--main-color)"
                >
                  Lịch sử tương tác nhóm đối tượng
                </div>
              </div>
              <VOldTable
                idTable="history-table"
                ref="tableRefHistory"
                :tableName="'Lịch sử tương tác nhóm đối tượng'"
                :showAction="false"
                :headers="historyHeaders"
                :styleHeaders="styleHistoryHeaders"
                :rows="listHistory ?? []"
                :pagination="{
                  totalPage: 0,
                  total: 0,
                  perPage: 0,
                }"
                :showPagination="false"
              >
                <template v-slot:items="{ row }">
                  <td class="text-primaryText px-2">
                    <div>
                      {{ row.campaign_name }}
                    </div>
                  </td>
                  <td class="text-primaryText px-2 text-center">
                    <Tag
                      :class="getCampaignColorStatus(row.status)"
                      :value="getCampaignLabelStatus(row.status)"
                    />
                  </td>
                  <td class="text-primaryText px-2 text-center">
                    <div v-if="row.time">
                      {{ row.time }}
                    </div>
                    <div v-else>-</div>
                  </td>
                </template>
              </VOldTable>
            </div>
            <div class="mt-4 bg-[#FBFAFF] py-3 px-2">
              <div class="px-1 flex justify-between mb-3">
                <div
                  class="border-1 border-solid px-[15px] py-[6px] rounded-[30px] h-[34px] w-fit text-[14px] font-semibold flex items-center"
                  style="border-color: var(--main-color); color: var(--main-color)"
                >
                  Danh sách đối tượng
                </div>

                <el-button
                  class="border-[1px] border-[#8BA6FF] border-solid !bg-[#E1E8FF] !text-black mr-3"
                  @click="onFilter"
                >
                  <Icon :icon="`tabler:rotate-2`" class="mr-2" />Cập nhật quy mô nhóm
                </el-button>
              </div>
              <VOldTable
                idTable="object-table"
                ref="tableRef"
                :tableName="'Danh sách đối tượng'"
                :showAction="false"
                :headers="objectHeaders"
                :styleHeaders="styleObjectHeaders"
                :rows="listObject ?? []"
                :pagination="{
                  totalPage: listObjectPaging?.total_pages ?? 0,
                  total: listObjectPaging?.total_records ?? 0,
                  perPage: params.page_size ?? 0,
                }"
                @pageChanged="onPageChange"
                @perPageChange="onPerPageChange"
              >
                <template v-slot:items="{ row }">
                  <td
                    v-for="header in objectHeaders"
                    :key="header.name"
                    class="text-primaryText px-2"
                  >
                    <div class="column-container">
                      {{ row[header.name] }}
                    </div>
                  </td>
                </template>
              </VOldTable>
            </div>
          </div>
        </div>
      </template>
    </BaseTabs>
  </div>
  <div
    class="flex z-[999] py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke items-center"
    :class="'justify-end'"
  >
    <VElementButton label="Cập nhật" :bgColor="color.main" @click="openUpdate" />
  </div>

  <Dialog
    modal
    header="Kết quả xử lý file"
    ref="refDialog"
    v-model:visible="popupTargetFile"
    :draggable="false"
    :class="'h-240px'"
    :pt="{
      content: { class: 'mb-[60px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: '!pl-[190px] !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
      },
    }"
    :style="{
      width: '550px',
      backgroundColor: '#fff',
      maxHeight: '100%',
    }"
  >
    <div class="pt-[15px]">
      <div class="flex flex-col mt-[20px]" id="popup-result">
        <div class="flex flex-col gap-3 text-black pl-[20px]">
          <div>
            <span class="font-semibold">File dữ liệu:</span>
            {{ fileUpload?.file_name }}
          </div>
          <div>
            <span class="font-semibold">Trạng thái upload:</span>
            {{ fileUpload?.validation_status }}
          </div>
          <div>
            <span class="font-semibold">Tổng số bản ghi:</span>
            {{ fileUpload?.total_records }}
          </div>
          <div>
            <span class="font-semibold">Số bản ghi hợp lệ:</span>
            {{ fileUpload?.valid_records }}
          </div>
          <div class="flex gap-2 items-center">
            <span class="font-semibold">Số bản ghi không hợp lệ:</span>
            {{ fileUpload?.invalid_records }}
            <VDownLoadFile
              v-if="fileUpload?.invalid_records > 0"
              label="File kết quả"
              :url="fileUpload?.error_file_url"
            />
          </div>
          <div>
            <span class="font-semibold">Ngày tạo:</span>
            {{ moment(fileUpload?.upload_date).format('HH:mm:ss DD/MM/YYYY') }}
          </div>
        </div>
      </div>
    </div>
    <div class="save-container flex justify-center items-center gap-5 mt-5">
      <VElementButton label="Đóng" :bgColor="color.closeButton" @click="popupTargetFile = false" />
    </div>
  </Dialog>
  <PopupInfo
    :show="popupIsUpdate"
    message="Không thể cập nhật nhóm đối tượng do hệ thống đang xử lý nhóm đối tượng"
    @close="popupIsUpdate = false"
  />
  <PopupInfo
    :show="popupIsValidateUpdate"
    message="Không thể cập nhật nhóm đối tượng do nhóm đối tượng này đang được sử dụng trong chiến dịch gửi tin"
    @close="popupIsValidateUpdate = false"
  />
</template>

<style scoped>
.view-height {
  height: calc(100vh - 172px);
  overflow: auto;
}

/* Tăng kích thước el-tabs */
.demo-tabs {
  font-size: 16px;
  margin-bottom: 100px;
}

.demo-tabs :deep(.el-tabs__item) {
  font-size: 16px;
}

:deep(.uploaded) {
  width: 100%;
  padding: 0 10px;
  max-width: 500px;
}
:deep(.uploaded-text) {
  max-width: 500px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: bottom;
}

.target-container {
  border-left: 4px solid var(--main-color);
}
:deep(.widthSTT) {
  width: 50px !important;
}
</style>
