<script setup lang="ts">
import { phuongThucOptions } from '../index.constant';
import * as yup from 'yup';
import { PLACEHOLDER } from '@/shared';
import { useForm } from 'vee-validate';
import { useApi } from '@/store/useApi';
import { inject, watch } from 'vue';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { PageType } from '@/enums/common';
import { useRoute } from 'vue-router';

const emit = defineEmits(['saveDataStep1']);
const props = defineProps<{
  data?: any;
  type: PageType;
}>();
const toast = inject('toast') as any;
const api = useApi();
const overlayLoading = useOverLayLoadingStore();
const route = useRoute();

const validationSchema = yup.object({
  target_group_name: yup.string().required('Tên nhóm đối tượng không được để trống').trim(),
  description: yup.string().trim().nullable(),
  identification_method: yup.string().required('<PERSON>ui lòng chọn phương thức xác định'),
});

const { values, handleSubmit, setFieldValue, setFieldError } = useForm({
  validationSchema,
});

const onSubmit = handleSubmit(async () => {
  try {
    overlayLoading.toggleLoading(true);
    const body = {
      target_group_name: values.target_group_name,
      description: values.description,
      identification_method: values.identification_method,
    };
    let res;
    if (props.type === PageType.Update) {
      res = await api.post(`/cdp/v1/api/client/target-group/validate/${route.params.id}`, body);
    } else {
      res = await api.post('/cdp/v1/api/client/target-group/validate', body);
    }
    if (res.data.code === 0) {
      emit('saveDataStep1', 2, values, res.data.data);
      overlayLoading.toggleLoading(false);
    } else {
      if (res.data.code === 200001) {
        setFieldError('target_group_name', res.data.message);
      } else {
        toast('error', res.data.message);
      }
      overlayLoading.toggleLoading(false);
    }
  } catch (error) {
    overlayLoading.toggleLoading(false);
    console.error(error);
  }
});

watch(
  () => props.data,
  (newData) => {
    if (newData) {
      setFieldValue('target_group_name', newData.target_group_name);
      setFieldValue('description', newData.description);
      setFieldValue('identification_method', newData.identification_method);
    }
  },
  { immediate: true },
);

defineExpose({
  onSubmit,
});
</script>
<template>
  <div class="flex justify-center mt-[50px] mb-[100px]">
    <el-form class="flex flex-col gap-4 w-full max-w-[500px]" label-position="top">
      <VElementInput
        :style="'w-[100%]'"
        :required="true"
        label="Tên nhóm đối tượng"
        :maxlength="200"
        name="target_group_name"
        :placeholder="PLACEHOLDER.TYPE"
      />
      <VElementInput
        :style="'w-[100%]'"
        type="textarea"
        label="Mô tả"
        :maxlength="1000"
        name="description"
        :placeholder="PLACEHOLDER.TYPE"
      />
      <VElementDropdown
        :style="'w-[100%]'"
        :required="true"
        label="Phương thức xác định"
        :option="phuongThucOptions"
        name="identification_method"
        :placeholder="PLACEHOLDER.SELECT"
      />
    </el-form>
  </div>
</template>

<style scoped></style>
