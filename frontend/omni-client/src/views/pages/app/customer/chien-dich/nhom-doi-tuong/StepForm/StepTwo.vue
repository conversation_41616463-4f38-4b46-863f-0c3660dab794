<script setup lang="ts">
import * as yup from 'yup';
import { useForm, useFieldArray } from 'vee-validate';
import VDownLoadFile from '@/components/base/common/VDownLoadFile.vue';
import VElementInput from '@/components/base/ElementComponent/VElementInput.vue';
import { dieuKienOptions, typeOptions, operatorOptions } from '../index.constant';
import { onMounted, ref, watch } from 'vue';
import RemoveIcon from '@/components/icon/RemoveIcon.vue';
import AddIcon from '@/components/icon/AddIcon.vue';
import VUploadFileTarget from '@/components/base/common/VUploadFileTarget.vue';
import Dialog from 'primevue/dialog';
import { color } from '@/constants/statusColor';
import moment from 'moment';
import type { PageType } from '@/enums/common';
import InfoCircle from '@/components/icon/InfoCircle.vue';

const emit = defineEmits(['saveDataStep2']);
const props = defineProps<{
  data?: any;
  type: PageType;
}>();
// Các option mẫu cho dropdown
const fieldOptions = ref([]);
const uploadFileTarget = ref();
const popupTargetFile = ref(false);
const linkFileMau = ref('');
const forceUpdateKey = ref(0); // Simple key to force re-render

const validationSchema = yup.object({
  file: yup.mixed().required('Danh sách đối tượng nguồn không được để trống'),
  fileSize: yup
    .number()
    .max(25 * 1024 * 1024, 'Vui lòng chỉ upload file có dung lượng tối đa là 25MB')
    .nullable(),
  fileType: yup
    .string()
    .matches(
      /^file$/,
      'Định dạng file chưa được hỗ trợ, vui lòng upload file có định dạng đuôi xlsx',
    )
    .nullable(),
  criteria_type: yup.string().nullable(),
  criteria: yup.array().of(
    yup.object().shape({
      field_name: yup.string().nullable(),
      data_type: yup.string().when('field_name', {
        is: (value: string) => value && value.length > 0,
        then: (schema) =>
          schema.required('Vui lòng chọn loại dữ liệu phù hợp với trường dữ liệu đã chọn'),
        otherwise: (schema) => schema.nullable(),
      }),
      operator: yup.string().when('field_name', {
        is: (value: string) => value && value.length > 0,
        then: (schema) =>
          schema.required('Vui lòng chọn toán tử phù hợp với trường dữ liệu đã chọn'),
        otherwise: (schema) => schema.nullable(),
      }),
      value: yup
        .string()
        .trim()
        .when('field_name', {
          is: (value: string) => value && value.length > 0,
          then: (schema) =>
            schema.required('Vui lòng nhập giá trị phù hợp với trường dữ liệu đã chọn'),
          otherwise: (schema) => schema.nullable(),
        }),
    }),
  ),
});
const { values, handleSubmit, setFieldValue, setFieldError } = useForm({
  validationSchema,
});

const { fields: criteria, remove, replace } = useFieldArray<any>('criteria');

const onSubmit = handleSubmit((values) => {
  // Manual validation for criteria
  let hasErrors = false;

  // Check for duplicate criteria
  const activeCriteria = values.criteria.filter(
    (item: any) => item.field_name && item.field_name.length > 0,
  );

  for (let i = 0; i < activeCriteria.length; i++) {
    for (let j = i + 1; j < activeCriteria.length; j++) {
      const item1 = activeCriteria[i];
      const item2 = activeCriteria[j];

      // Check if all 4 fields are identical
      if (
        item1.field_name === item2.field_name &&
        item1.data_type === item2.data_type &&
        item1.operator === item2.operator &&
        item1.value &&
        item2.value &&
        item1.value.trim() === item2.value.trim()
      ) {
        // Find original indexes in values.criteria
        const originalIndex1 = values.criteria.findIndex((criteria: any) => criteria === item1);
        const originalIndex2 = values.criteria.findIndex((criteria: any) => criteria === item2);

        setFieldError(
          `criteria[${originalIndex1}].value`,
          'Tiêu chí thiết lập đã tồn tại, vui lòng chọn lại!',
        );
        setFieldError(
          `criteria[${originalIndex2}].value`,
          'Tiêu chí thiết lập đã tồn tại, vui lòng chọn lại!',
        );
        hasErrors = true;
      }
    }
  }

  if (!hasErrors) {
    const data = {
      ...values,
      criteria: values.criteria
        .filter((item: any) => item.field_name !== null && item.field_name !== '')
        .map((item: any) => ({
          ...item,
        })),
      file: uploadFileTarget.value?.dataFile,
    };
    emit('saveDataStep2', data);
  }
});

const handleShowResult = () => {
  popupTargetFile.value = true;
};

onMounted(() => {
  setFieldValue('criteria_type', 'Thoả mãn tất cả các điều kiện');
  if (!values.criteria || values.criteria.length === 0) {
    setFieldValue('criteria', [
      { field_name: null, data_type: null, operator: null, value: '' },
      { field_name: null, data_type: 'Văn bản', operator: 'Chứa ký tự', value: '' },
      { field_name: null, data_type: 'Số', operator: 'Bằng', value: '' },
      { field_name: null, data_type: 'Văn bản', operator: 'Là', value: '' },
    ]);
  }
});

watch(
  () => props.data,
  (newData) => {
    if (newData) {
      setFieldValue('criteria', newData.criteria);
      fieldOptions.value = newData.file_upload_response.header.map((item: any) => ({
        label: item,
        value: item,
      }));
      if (newData.criteria.length === 0) {
        setFieldValue('criteria', [
          { field_name: null, data_type: null, operator: null, value: '' },
          { field_name: null, data_type: 'Văn bản', operator: 'Chứa ký tự', value: '' },
          { field_name: null, data_type: 'Số', operator: 'Bằng', value: '' },
          { field_name: null, data_type: 'Văn bản', operator: 'Là', value: '' },
        ]);
      }
      setFieldValue('criteria_type', newData.criteria_type);
      uploadFileTarget.value.dataFile = newData.file_upload_response;
      setTimeout(() => {
        uploadFileTarget.value.fileUrl = newData.source_file_name;
        setFieldValue('file', newData.source_file_name);
        uploadFileTarget.value.isUpload = true;
      }, 500);
    }
  },
  { immediate: true },
);

function addCriteria(idx: number) {
  const arr = criteria.value.map((item: any) => ({
    field_name: item.value.field_name,
    data_type: item.value.data_type,
    operator: item.value.operator,
    value: item.value.value,
  }));
  arr.splice(idx + 1, 0, { field_name: null, data_type: null, operator: null, value: '' });
  replace(arr);
  // Force re-render and clear validation errors
  forceUpdateKey.value++;
}

function removeCriteria(idx: number) {
  if (values.criteria.length > 1) {
    remove(idx);
    // Force re-render and clear validation errors
    forceUpdateKey.value++;
  }
}

const handleUploadFile = () => {
  fieldOptions.value = uploadFileTarget.value?.dataFile?.header.map((item: any) => ({
    label: item,
    value: item,
  }));
  setFieldValue('criteria', [
    { field_name: null, data_type: null, operator: null, value: '' },
    { field_name: null, data_type: 'Văn bản', operator: 'Chứa ký tự', value: '' },
    { field_name: null, data_type: 'Số', operator: 'Bằng', value: '' },
    { field_name: null, data_type: 'Văn bản', operator: 'Là', value: '' },
  ]);
};

defineExpose({
  onSubmit,
  linkFileMau,
});
</script>
<template>
  <div class="flex justify-center mt-[50px] px-3">
    <div class="flex flex-col gap-4 w-full max-w-[1032px]">
      <div class="font-semibold text-[#6B7280] text-[14px]">
        Danh sách đối tượng nguồn <span class="text-[#f56c6c]">*</span>
      </div>
      <div class="flex gap-3 items-start">
        <VDownLoadFile label="Tải file mẫu" :url="linkFileMau" />
        <VUploadFileTarget
          ref="uploadFileTarget"
          fileSize="fileSize"
          fileType="fileType"
          file="file"
          formType="them"
          :fileTypes="['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']"
          :sizeToValidate="25 * 1024 * 1024"
          @handleUploadFile="handleUploadFile"
        />
        <Icon
          v-show="uploadFileTarget?.isUpload"
          icon="tabler:eye"
          class="text-[25px] text-primaryText mt-[5px] cursor-pointer"
          @click="handleShowResult"
        />
      </div>

      <div class="tieu-chi mt-[20px]">
        <div class="font-semibold text-black flex items-center gap-2">
          Thiết lập tiêu chí
          <el-tooltip
            class="box-item"
            effect="dark"
            placement="top-start"
            content="Nếu bạn muốn sử dụng danh sách đối tượng mà không cần lọc, bạn có thể bỏ qua bước thiết lập tiêu chí này"
          >
            <InfoCircle class="cursor-pointer inline" />
          </el-tooltip>
        </div>
        <VElementDropdown
          class="mt-2"
          :style="'w-[300px]'"
          :required="true"
          :option="dieuKienOptions"
          name="criteria_type"
        />
        <!-- Form động tiêu chí -->
        <div class="mt-4 flex flex-col gap-3">
          <div
            v-for="(item, idx) in criteria"
            :key="`${forceUpdateKey}-${idx}`"
            class="flex gap-3 items-start"
          >
            <VElementDropdown
              class="w-[200px]"
              :option="fieldOptions"
              :placeholder="'Chọn trường dữ liệu'"
              :name="`criteria[${idx}].field_name`"
            />
            <VElementDropdown
              class="w-[120px]"
              :option="typeOptions"
              :placeholder="'Chọn loại dữ liệu'"
              :name="`criteria[${idx}].data_type`"
            />
            <VElementDropdown
              class="w-[200px]"
              :option="operatorOptions[String(item.value.data_type)] || []"
              :placeholder="'Chọn toán tử'"
              :name="`criteria[${idx}].operator`"
            />
            <VElementInput
              class="flex-1"
              :placeholder="'Nhập giá trị'"
              :name="`criteria[${idx}].value`"
              :maxlength="200"
            />

            <RemoveIcon class="mt-[5px]" @click="removeCriteria(idx)" v-if="criteria.length > 1" />
            <AddIcon class="mt-[5px]" @click="addCriteria(idx)" />
          </div>
        </div>
      </div>
    </div>
  </div>

  <Dialog
    modal
    header="Kết quả xử lý file"
    ref="refDialog"
    v-model:visible="popupTargetFile"
    :draggable="false"
    :class="'h-240px'"
    :pt="{
      content: { class: 'mb-[60px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: '!pl-[190px] !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
      },
    }"
    :style="{
      width: '550px',
      backgroundColor: '#fff',
      maxHeight: '100%',
    }"
  >
    <div class="pt-[15px]">
      <div class="flex flex-col mt-[20px]" id="popup-result">
        <div class="flex flex-col gap-3 text-black pl-[20px]">
          <div>
            <span class="font-semibold">File dữ liệu:</span>
            {{ uploadFileTarget?.dataFile?.file_name }}
          </div>
          <div>
            <span class="font-semibold">Trạng thái upload:</span>
            {{ uploadFileTarget?.dataFile?.validation_status }}
          </div>
          <div>
            <span class="font-semibold">Tổng số bản ghi:</span>
            {{ uploadFileTarget?.dataFile?.total_records }}
          </div>
          <div>
            <span class="font-semibold">Số bản ghi hợp lệ:</span>
            {{ uploadFileTarget?.dataFile?.valid_records }}
          </div>
          <div class="flex gap-2 items-center">
            <span class="font-semibold">Số bản ghi không hợp lệ:</span>
            {{ uploadFileTarget?.dataFile?.invalid_records }}
            <VDownLoadFile
              v-if="uploadFileTarget?.dataFile?.invalid_records > 0"
              label="File kết quả"
              :url="uploadFileTarget?.dataFile?.error_file_url"
            />
          </div>
          <div>
            <span class="font-semibold">Ngày tạo:</span>
            {{ moment(uploadFileTarget?.dataFile?.upload_date).format('HH:mm:ss DD/MM/YYYY') }}
          </div>
        </div>
      </div>
    </div>
    <div class="save-container flex justify-center items-center gap-5 mt-5">
      <VElementButton label="Đóng" :bgColor="color.closeButton" @click="popupTargetFile = false" />
    </div>
  </Dialog>
</template>

<style scoped>
.tieu-chi {
  border: 1px solid #e5e7eb;
  border-left: 3px solid #1a34b5;
  box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08);
  padding: 15px 20px 30px;
  border-radius: 0 8px 8px 0;
  background-color: #f6f8fb;
  margin-bottom: 100px;
}
.el-form-item.is-error {
  margin-bottom: 16px;
}
</style>
