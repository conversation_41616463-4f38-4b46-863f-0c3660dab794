<script setup lang="ts">
import { color } from '@/constants/statusColor';
import moment from 'moment';

defineProps<{
  historyData: any;
}>();

const emit = defineEmits(['onClose']);
</script>

<template>
  <div>
    <div class="pt-[15px] text-[14px]">
      <div class="w-full">
        <div class="bg-[#F6F8FB] flex items-center h-[36px] border-b border-[#e0dddd]">
          <div class="py-[4px] px-[10px] w-[160px] font-bold text-center">Thời gian chỉnh sửa</div>
          <div class="py-[4px] px-[10px] w-[160px] font-bold">Thông tin chỉnh sửa</div>
          <div class="py-[4px] px-[10px] w-[160px] font-bold">Tài <PERSON><PERSON><PERSON>n chỉnh sửa</div>
          <div class="py-[4px] px-[10px] w-[160px] font-bold text-center"><PERSON><PERSON> chú</div>
        </div>
        <div class="w-full h-[calc(300px)] overflow-y-auto">
          <div
            v-for="(item, index) of historyData"
            :key="index"
            class="border-b border-[#e0dddd] flex items-center h-[38px] hover:bg-[#F6F8FB]"
          >
            <div class="py-[4px] px-[10px] text-center w-[160px]">
              {{ moment(item.created_at).format('DD/MM/YYYY HH:mm:ss') }}
            </div>
            <div
              class="py-[4px] px-[10px] w-[160px] overflow-hidden line-clamp-1 text-ellipsis break-all"
              :title="item.action_message"
            >
              {{ item.action_message }}
            </div>
            <div
              class="py-[4px] px-[10px] w-[160px] overflow-hidden line-clamp-1 text-ellipsis break-all"
              :title="item.created_by_username"
            >
              {{ item.created_by_username }}
            </div>
            <div
              class="py-[4px] px-[10px] text-center w-[160px] overflow-hidden line-clamp-1 text-ellipsis break-all"
              :title="item.notes"
            >
              {{ item.notes }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="save-container flex justify-center items-center gap-5 mt-5">
      <VElementButton label="Đóng" :bgColor="color.closeButton" @click="emit('onClose')" />
    </div>
  </div>
</template>
