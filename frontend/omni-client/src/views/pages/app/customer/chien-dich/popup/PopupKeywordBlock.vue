<script setup lang="ts">
import { color } from '@/constants/statusColor';
import { reactive, ref, watch } from 'vue';
import VOldTable from '@/components/base/VOldTable.vue';
import { useForm } from 'vee-validate';
import moment from 'moment';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import {
  blockedKeywordsHeaders,
  blockedKeywordsStyle,
  popupBlockPt,
  popupBlockStyle,
} from '../index.constants';
import type { IKeyword } from '../index.type';
import { useApi } from '@/store/useApi';

const props = defineProps<{
  visible: boolean;
  templateId?: number;
}>();

const emit = defineEmits(['onClose']);

const { values } = useForm({
  initialValues: {
    keyword: '',
  },
});

const params = reactive({
  pageIndex: 1,
  pageSize: 20,
});

const pageInfo = reactive<any>({
  currentPage: null,
  total_pages: null,
  total_records: null,
});

const listBlockedKeyword = ref<IKeyword[]>([]);
const tableRef = ref();
const overlayLoading = useOverLayLoadingStore();

const onFilter = () => {
  tableRef.value.filterData();
};

const onPageChange = (page: number) => {
  params.pageIndex = page;
  getList();
};

const getList = async () => {
  if (!props.templateId) return;
  const api = useApi();
  const { data } = await api.get(
    `business/v1/api/admin/message-policy/blocked-keywords/template/${props.templateId}`,
    {
      params: {
        ...params,
        ...values,
      },
    },
  );
  if (data.code === 0) {
    listBlockedKeyword.value = data.data?.items;
    pageInfo.total_pages = data?.data?.paging?.total_pages;
    pageInfo.total_records = data.data?.paging?.total_records;
  }
};

const onPerPageChange = (size: number) => {
  params.pageSize = size;
};

const sortTable = () => {
  getList();
};

watch(
  () => props.visible,
  async (newValue) => {
    if (newValue) {
      overlayLoading.toggleLoading(true);
      await getList();
      overlayLoading.toggleLoading(false);
    }
  },
);
</script>

<template>
  <VDialog
    modal
    ref="refDialog"
    header="Danh sách từ khóa bị chặn"
    :visible="visible"
    :draggable="false"
    :pt="popupBlockPt"
    :style="popupBlockStyle"
    @update:visible="emit('onClose')"
  >
    <div>
      <div
        class="relative w-full bg-[#f7f7f7] rounded-[10px] border border-[#e3e2ea] p-[16px] mb-[10px]"
      >
        <el-form class="flex gap-[12px]" @submit.prevent>
          <VElementInput
            size="small"
            name="keyword"
            type="text"
            placeholder="Tìm kiếm từ khóa"
            id="keyword-search-input"
            class="grow"
            @keyup.enter="onFilter"
          />
          <VElementButton
            label="Tìm kiếm"
            styleButton="s"
            id="keyword-search-button"
            :bgColor="color.main"
            @click="onFilter"
          />
        </el-form>
      </div>
      <VOldTable
        ref="tableRef"
        idTable="sms-template-table"
        class="!p-0 mb-[40px]"
        :rows="listBlockedKeyword"
        :headers="blockedKeywordsHeaders"
        :styleHeaders="blockedKeywordsStyle"
        :showAction="false"
        :pagination="{
          totalPage: pageInfo.total_pages ?? 0,
          total: pageInfo.total_records ?? 0,
          perPage: params.pageSize ?? 0,
        }"
        @pageChanged="onPageChange"
        @perPageChange="onPerPageChange"
        @sort-table="sortTable"
      >
        <template v-slot:items="{ row }">
          <td class="text-primaryText px-2">
            <div class="column-container">
              {{ row?.keyword }}
            </div>
          </td>
          <td class="text-primaryText px-2">
            <div class="column-container">
              {{ row?.description }}
            </div>
          </td>
          <td class="text-primaryText px-2">
            <div class="column-container">
              {{ moment(row?.created_at).format('DD/MM/YYYY HH:mm') }}
            </div>
          </td>
        </template>
      </VOldTable>
      <div class="save-container flex justify-center items-center gap-5">
        <VElementButton label="Đóng" :bgColor="color.closeButton" @click="emit('onClose')" />
      </div>
    </div>
  </VDialog>
</template>

<style scoped>
:deep(.v-old-table-container) {
  height: 350px;
}
</style>
