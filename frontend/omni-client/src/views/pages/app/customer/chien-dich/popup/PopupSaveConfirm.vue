<script setup lang="ts">
import { color } from '@/constants/statusColor';
import { ElTooltip } from 'element-plus';

defineProps<{
  costEstimateData: any;
}>();

const emit = defineEmits(['onConfirm']);
</script>

<template>
  <div>
    <div class="pt-[15px] text-[14px] text-[black]">
      <div class="flex items-center gap-x-[4px]">
        <p class="font-bold">Chi phí ước tính dự kiến:</p>
        <p class="text-[#3D4FF4]">
          {{ `${(costEstimateData?.estimated_cost as number)?.toLocaleString('de-DE')} E-Point` }}
        </p>
      </div>
      <div class="flex items-center gap-x-[4px]">
        <div class="cursor-pointer">
          <el-tooltip v-if="false" placement="top">
            <template #content>
              <p class="max-w-[320px]"></p>
            </template>
            <Icon icon="mingcute:information-fill" class="text-[#495057]" />
          </el-tooltip>
          <Icon v-else icon="mingcute:information-fill" class="text-[#495057]" />
        </div>
        <p>Lưu ý:</p>
      </div>
      <ul class="ml-[40px] list-disc">
        <li>
          Chi phí ước tính dự kiến cho chiến dịch có thể thay đổi tùy theo độ dài tin nhắn, kênh gửi
          tin, giá nhà mạng gửi tin,...
        </li>
        <li>
          Chi phí dự kiến cũng có thể thay đổi so với hệ thống đã ước tính vì chiến dịch này có sử
          dụng tính năng Failover
        </li>
        <li>Chi phí ước tính đã dự kiến đã bao gồm VAT (10%)</li>
      </ul>
      <div class="flex items-center gap-x-[4px] mt-[8px]">
        <p class="font-bold mb-[4px]">Phân bổ số dư ví dự kiến:</p>
      </div>
      <table class="w-full text-[#292D32]">
        <thead class="bg-[#F6F8FB] h-[36px] border-b border-[#DFE4EA]">
          <th class="py-[4px] px-[10px]">Hạng mục</th>
          <th class="py-[4px] px-[10px] text-right">Số E-Point</th>
        </thead>
        <tbody>
          <tr class="border-b border-[#DFE4EA] h-[38px]">
            <td class="py-[4px] px-[10px]">Số dư ví hiện tại</td>
            <td class="py-[4px] px-[10px] text-right">
              {{ (costEstimateData?.current_balance as number)?.toLocaleString('de-DE') }}
            </td>
          </tr>
          <tr class="border-b border-[#DFE4EA] h-[38px]">
            <td class="py-[4px] px-[10px]">Dùng cho các chiến dịch hiện có</td>
            <td class="py-[4px] px-[10px] text-right">
              {{
                (costEstimateData?.used_for_existing_campaigns as number)?.toLocaleString('de-DE')
              }}
            </td>
          </tr>
          <tr class="border-b border-[#DFE4EA] h-[38px]">
            <td class="py-[4px] px-[10px]">Dự kiến dùng cho chiến dịch này</td>
            <td class="py-[4px] px-[10px] text-right">
              {{
                (costEstimateData?.estimated_for_this_campaign as number)?.toLocaleString('de-DE')
              }}
            </td>
          </tr>
          <tr class="border-b border-[#DFE4EA] h-[38px] bg-[#F6F8FB]">
            <td
              class="py-[4px] px-[10px]"
              :class="`${costEstimateData?.remaining_balance >= 0 ? 'text-[#3D4FF4]' : 'text-[#F20000]'} `"
            >
              Số dư còn lại dự kiến
            </td>
            <td
              class="py-[4px] px-[10px] text-right"
              :class="`${costEstimateData?.remaining_balance >= 0 ? 'text-[#3D4FF4]' : 'text-[#F20000]'} `"
            >
              {{ (costEstimateData?.remaining_balance as number)?.toLocaleString('de-DE') }}
            </td>
          </tr>
        </tbody>
      </table>
      <p
        v-if="costEstimateData?.remaining_balance < 0"
        class="text-center mt-[10px] text-[#F20000]"
      >
        Số dư ví của bạn không đủ!
      </p>
      <p
        class="text-center"
        :class="costEstimateData?.remaining_balance < 0 ? 'mt-[2px]' : 'mt-[10px]'"
      >
        Vui lòng đảm bảo số dư ví doanh nghiệp của bạn luôn đủ trong suốt quá trình chiến dịch hoạt
        động, để chiến dịch gửi tin không bị gián đoạn!
      </p>
    </div>
    <div class="save-container flex justify-center items-center gap-2 mt-5">
      <VElementButton
        v-if="costEstimateData?.remaining_balance <= 0"
        label="Xác nhận & Nạp tiền"
        :bgColor="'#3D4FF4'"
        @click="emit('onConfirm')"
      />
      <VElementButton label="Xác nhận" :bgColor="color.secondary" @click="emit('onConfirm')" />
    </div>
  </div>
</template>
