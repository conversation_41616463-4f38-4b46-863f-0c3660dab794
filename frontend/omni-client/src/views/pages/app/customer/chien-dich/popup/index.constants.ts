import * as yup from 'yup';

export const DATE = {
  MONDAY: 1,
  TUESDAY: 2,
  WEDNESDAY: 3,
  THURSDAY: 4,
  FRIDAY: 5,
  SATURDAY: 6,
  SUNDAY: 7,
} as const;

export const PROVIDER = {
  VINAPHONE: 8,
  MOBIFONE: 7,
  VIETTEL: 6,
  VIETNAMOBILE: 4,
  GTEL: 5,
  SFONE: 3,
  ITEL: 2,
  REDDI: 1,
};

export const POLICY_SETTING_TYPE = {
  SMS_TIME_LIMIT: 1,
  SMS_QUANTITY_LIMIT: 2,
  SMS_CHARACTER_LIMIT: 3,
  ZNS_TIME_LIMIT: 4,
};

export const SMS_TYPE = {
  QC: 2,
  CSKH: 1,
  // CSKH_UNICODE: 3,
  // CSKH_NO_UNICODE: 4,
};

export const INIT_SMS_TIME_LIMIT_QC = {
  sms_type: SMS_TYPE.QC,
  policy_setting_type: POLICY_SETTING_TYPE.SMS_TIME_LIMIT,
  start_days_of_week_id: DATE.MONDAY,
  end_days_of_week_id: DATE.MONDAY,
  network_operator_id: PROVIDER.VINAPHONE,
  start_time: null,
  end_time: null,
};

export const INIT_SMS_TIME_LIMIT_CSKH = {
  sms_type: SMS_TYPE.CSKH,
  policy_setting_type: POLICY_SETTING_TYPE.SMS_TIME_LIMIT,
  start_days_of_week_id: DATE.MONDAY,
  network_operator_id: PROVIDER.VINAPHONE,
  end_days_of_week_id: DATE.MONDAY,
  start_time: null,
  end_time: null,
};

export const INIT_SMS_QUANTITY_LIMIT_QC = {
  policy_setting_type: POLICY_SETTING_TYPE.SMS_QUANTITY_LIMIT,
  network_operator_id: PROVIDER.VINAPHONE,
  sms_type: SMS_TYPE.QC,
  limit_send_date: null,
  limit_send_month: null,
};

export const INIT_SMS_QUANTITY_LIMIT_CSKH = {
  policy_setting_type: POLICY_SETTING_TYPE.SMS_QUANTITY_LIMIT,
  network_operator_id: PROVIDER.VINAPHONE,
  sms_type: SMS_TYPE.CSKH,
  limit_send_date: null,
  limit_send_month: null,
};

export const INIT_SMS_CHARACTER_QC = {
  policy_setting_type: POLICY_SETTING_TYPE.SMS_CHARACTER_LIMIT,
  network_operator_id: PROVIDER.VINAPHONE,
  sms_type: SMS_TYPE.QC,
  char_limit: null,
};

export const INIT_SMS_CHARACTER_CSKH_UNICODE = {
  policy_setting_type: POLICY_SETTING_TYPE.SMS_CHARACTER_LIMIT,
  network_operator_id: PROVIDER.VINAPHONE,
  sms_type: SMS_TYPE.CSKH,
  is_unicode: true,
  char_limit: null,
};

export const INIT_SMS_CHARACTER_CSKH = {
  policy_setting_type: POLICY_SETTING_TYPE.SMS_CHARACTER_LIMIT,
  network_operator_id: PROVIDER.VINAPHONE,
  is_unicode: false,
  sms_type: SMS_TYPE.CSKH,
  char_limit: null,
};

export const INIT_ZNS_LIMIT = {
  policy_setting_type: POLICY_SETTING_TYPE.ZNS_TIME_LIMIT,
  start_time: '',
  end_time: '',
};

export const timeSchema = yup.array().of(
  yup.object().shape({
    start_time: yup
      .string()
      .nullable()
      .test('required-if-end', 'Vui lòng chọn giờ bắt đầu', function (value) {
        return !this.parent.end_time || !!value;
      }),
    end_time: yup
      .string()
      .nullable()
      .test('required-if-start', 'Vui lòng chọn giờ kết thúc', function (value) {
        return !this.parent.start_time || !!value;
      }),
    network_operator_id: yup.number().nullable().required('Vui lòng chọn nhà mạng'),
  }),
);

export const quantitySchema = yup.array().of(
  yup.object().shape({
    network_operator_id: yup.number().nullable().required('Vui lòng chọn nhà mạng'),
  }),
);

export const characterSchema = yup.array().of(
  yup.object().shape({
    network_operator_id: yup.number().nullable().required('Vui lòng chọn nhà mạng'),
  }),
);

export const znsTimeSchema = yup.object().shape({
  start_time: yup
    .string()
    .nullable()
    .test('required-if-end', 'Khoảng thời gian không hợp lệ', function (value) {
      return !this.parent.end_time || !!value;
    }),
  end_time: yup
    .string()
    .nullable()
    .test('required-if-start', 'Khoảng thời gian không hợp lệ', function (value) {
      return !this.parent.start_time || !!value;
    }),
});
