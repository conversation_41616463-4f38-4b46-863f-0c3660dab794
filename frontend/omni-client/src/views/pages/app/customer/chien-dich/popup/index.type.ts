export interface IRequest {
  network_operator_id?: number | null;
  policy_setting_type?: number | null;
  start_days_of_week_id?: number | null;
  end_days_of_week_id?: number | null;
  start_time?: string | null;
  end_time?: string | null;
  char_limit?: number | null;
  sms_type?: number | null;
  limit_send_date?: number | null;
  limit_send_month?: number | null;
  is_unicode?: boolean;
}

export interface IResponse extends IRequest {
  id?: number;
}

export interface sms_char_limit {
  policy_setting_type: number;
  char_limit: string;
  network_operator_id: number | null;
  sms_type: number;
  is_unicode: boolean;
}

export interface sms_quantity_limit {
  limit_send_date: string;
  limit_send_month: string;
  network_operator_id: number | null;
  sms_type: number;
  policy_setting_type: number;
}

export interface sms_time_limit {
  policy_setting_type: number;
  start_days_of_week_id: number;
  start_time: string | null | number;
  network_operator_id?: number | null;
  sms_type: number;
  end_days_of_week_id: number;
  end_time: string | null | number;
}
