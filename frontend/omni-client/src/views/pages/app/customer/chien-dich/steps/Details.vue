<script setup lang="ts">
import { ref, onMounted, inject } from 'vue';
import { useCampaign } from '@/store/useCampaign';
import { onBeforeRouteLeave, useRouter, useRoute } from 'vue-router';
import StepFour from './Step4.vue';
import { FORM_TYPE, ROUTE_NAME, TabValue, TEXT } from '@/shared';
import { ElPopover } from 'element-plus';
import { color } from '@/constants/statusColor';
import PopupHistories from '../popup/PopupHistories.vue';
import {
  CampaignStatusValue,
  PopupConfirmType,
  popupHistoryPt,
  popupHistoryStyle,
} from '../index.constants';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import TabHistorySent from '../components/TabHistorySent.vue';
import type { IUpdateStatus } from '../index.type';
import BaseTabs from '@/components/base/new/BaseTabs.vue';
import type { TTab } from '../../bang-gia/index.type';

const props = defineProps<{
  id?: number;
  type: FORM_TYPE;
}>();

const campaignStore = useCampaign();
const stepFourRef = ref();
const route = useRoute();
const router = useRouter();
const isVisibleHistoriesPopup = ref(false);
const historyData = ref<any>();

const moveToList = () => {
  router.push({
    name: ROUTE_NAME.CAMPAIGN,
  });
};

const moveToUpdate = () => {
  router.push({
    name: ROUTE_NAME.UPDATE_CAMPAIGN,
    params: {
      id: props.id,
    },
  });
};

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();

const clickViewEditLog = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const res = await api.get(`/campaign/v1/api/campaign/audit-history/${props.id}`, {
      skipToastErr: true,
    });
    if (res.data.code === 0) {
      historyData.value = res.data.data;
      isVisibleHistoriesPopup.value = true;
      overlayLoading.toggleLoading(false);
    } else {
      overlayLoading.toggleLoading(false);
      toast('error', res.data.message);
    }
  } catch (error) {
    console.error(error);
    overlayLoading.toggleLoading(false);
    toast('error', TEXT.ERROR_OCCURRED);
  }
};

const handleUpdateStatus = async (type: PopupConfirmType) => {
  overlayLoading.toggleLoading(true);
  let status = CampaignStatusValue.Ended;
  if (type === PopupConfirmType.Paused) {
    status = CampaignStatusValue.Paused;
  } else if (type === PopupConfirmType.Active) {
    status = CampaignStatusValue.Active;
  }
  const params: IUpdateStatus = {
    id: props.id!,
    status,
  };
  try {
    let url = '';
    if (type === PopupConfirmType.Paused) {
      url = `/campaign/v1/api/campaign/stop/${props.id}`;
    } else if (type === PopupConfirmType.Active) {
      url = `/campaign/v1/api/campaign/reactivate/${props.id}`;
    } else if (type === PopupConfirmType.Ended) {
      url = `/campaign/v1/api/campaign/end/${props.id}`;
    }
    const res = await api.put(url, params);
    if (res.data.code === 0) {
      toast('success', res.data.message);
      setTimeout(() => {
        overlayLoading.toggleLoading(false);
        window.location.reload();
      }, 1000);
    } else {
      toast('error', res.data.message);
      overlayLoading.toggleLoading(false);
    }
  } catch (error: any) {
    overlayLoading.toggleLoading(false);
    if (!error?.response?.status) {
      toast('error', TEXT.ERROR_OCCURRED);
    }
  }
};

//#region Tabs
const activeTab = ref<TabValue>(TabValue.Tab1);

const tabs: TTab[] = [
  {
    label: 'Thông tin chung',
    value: TabValue.Tab1,
  },
  {
    label: 'Lịch sử gửi tin',
    value: TabValue.Tab2,
  },
];

const handleTabChanged = async (tabValue: TabValue) => {
  activeTab.value = tabValue;
};
//#endregion

onBeforeRouteLeave(() => {
  campaignStore.$reset();
});

onMounted(async () => {
  campaignStore.setFormType({ id: props.id });
  if (props.id) {
    await campaignStore.getDetailCampain(props.id);
  }
});
</script>

<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:confetti" class="text-[30px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: route.path }" @click="moveToList()"
          >Chiến dịch</el-breadcrumb-item
        >
        <el-breadcrumb-item :to="{ path: route.path }">Xem chiến dịch</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <VElementButton
      icon="history"
      label="Xem lịch sử chỉnh sửa"
      :bgColor="color.main"
      @click="clickViewEditLog"
    />
  </div>
  <BaseTabs :tabs :active-tab="activeTab" tabClass="w-[98%]" @click-tab="handleTabChanged">
    <template #tab-1>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden">
        <StepFour :type="FORM_TYPE.DETAILS" :id="props.id" ref="stepFourRef" />
      </div>
    </template>
    <template #tab-2>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden">
        <TabHistorySent :id="props.id" />
      </div>
    </template>
  </BaseTabs>
  <div
    class="flex z-[999] py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke items-center justify-between"
  >
    <VElementButton
      icon="arrow-narrow-left"
      text="black"
      bgColor="#EBEBEB"
      label="Quay lại"
      @click="moveToList"
    />
    <div class="flex items-center gap-x-[10px]">
      <VElementButton
        v-if="
          campaignStore.status === CampaignStatusValue.Draft ||
          campaignStore.status === CampaignStatusValue.NotStart
        "
        :bgColor="color.main"
        label="Cập nhật"
        @click="moveToUpdate"
      />
      <div class="cursor-pointer">
        <el-popover placement="top-start" trigger="click">
          <template #reference>
            <div>
              <Icon
                v-if="
                  campaignStore.status === CampaignStatusValue.Active ||
                  campaignStore.status === CampaignStatusValue.OutOfEPoint ||
                  campaignStore.status === CampaignStatusValue.Paused
                "
                icon="pepicons-pencil:dots-y"
                class="text-[28px] text-[#5d6066] hover:text-[#1a34b5]"
              />
            </div>
          </template>
          <!-- Content -->
          <ul>
            <li
              v-if="
                campaignStore.status === CampaignStatusValue.Active ||
                campaignStore.status === CampaignStatusValue.OutOfEPoint
              "
              class="item min-w-[100px]"
              @click="handleUpdateStatus(PopupConfirmType.Paused)"
              @keydown="handleUpdateStatus(PopupConfirmType.Paused)"
            >
              Tạm dừng
            </li>
            <li
              v-if="campaignStore.status === CampaignStatusValue.Paused"
              class="item min-w-[100px]"
              @click="handleUpdateStatus(PopupConfirmType.Active)"
              @keydown="handleUpdateStatus(PopupConfirmType.Active)"
            >
              Tiếp tục
            </li>
            <li
              v-if="
                campaignStore.status === CampaignStatusValue.Active ||
                campaignStore.status === CampaignStatusValue.OutOfEPoint ||
                campaignStore.status === CampaignStatusValue.Paused
              "
              class="item min-w-[100px]"
              @click="handleUpdateStatus(PopupConfirmType.Ended)"
              @keydown="handleUpdateStatus(PopupConfirmType.Ended)"
            >
              Kết thúc
            </li>
          </ul>
        </el-popover>
      </div>
    </div>
  </div>
  <VDialog
    v-model:visible="isVisibleHistoriesPopup"
    modal
    ref="refDialog"
    header="Lịch sử chỉnh sửa"
    :draggable="false"
    :pt="popupHistoryPt"
    :class="'h-285px'"
    :style="popupHistoryStyle"
  >
    <PopupHistories :historyData="historyData" @onClose="isVisibleHistoriesPopup = false" />
  </VDialog>
</template>

<style lang="scss" scoped>
.view-height {
  height: calc(100vh - 172px);
  overflow: auto;
}

.item {
  padding: 6px 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  border-radius: 6px;
  &:hover {
    background-color: #f2f2f7;
  }
}
</style>
