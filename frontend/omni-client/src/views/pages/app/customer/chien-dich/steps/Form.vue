<script setup lang="ts">
import { ref, onMounted, inject } from 'vue';
import Step1 from '@/components/icon/Step1.vue';
import Step2 from '@/components/icon/Step2.vue';
import Step2Done from '@/components/icon/Step2Done.vue';
import Step3 from '@/components/icon/Step3.vue';
import Step3Done from '@/components/icon/Step3Done.vue';
import Step4 from '@/components/icon/Step4.vue';
import Step4Done from '@/components/icon/Step4Done.vue';
import { useCampaign } from '@/store/useCampaign';
import { onBeforeRouteLeave, useRouter, useRoute } from 'vue-router';
import { color } from '@/constants/statusColor';
import StepOne from './Step1.vue';
import StepTwo from './Step2.vue';
import StepThree from './Step3.vue';
import StepFour from './Step4.vue';
import { popupSaveConfirmPt, popupSaveConfirmStyle, StepValue } from '../index.constants';
import { FORM_TYPE, ROUTE_NAME, TEXT } from '@/shared';
import PopupCancelConfirm from '../popup/PopupCancelConfirm.vue';
import { isEmptyObject } from '@/utils/object';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useApi } from '@/store/useApi';
import PopupSaveConfirm from '../popup/PopupSaveConfirm.vue';

const props = defineProps<{
  id?: number;
  type: FORM_TYPE;
}>();

//#region Popup confirm
const isShowConfirmPopup = ref(false);
const isVisibleConfirmSavePopup = ref(false);

const isChangedAddData = () => {
  return (
    campaignStore.step1Data !== undefined &&
    campaignStore.step1Data !== null &&
    !isEmptyObject(campaignStore.step1Data, [])
  );
};

const handleClickCancel = () => {
  const isDataChanged = isChangedAddData();

  if (isDataChanged) {
    isShowConfirmPopup.value = true;
  } else {
    moveToList();
  }
};
//#endregion

//#region Datasource
const campaignStore = useCampaign();
const stepOneRef = ref();
const stepTwoRef = ref();
const stepThreeRef = ref();
const stepFourRef = ref();
const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const theId = ref<number>();

const saveDraft = async (callBack: any) => {
  try {
    const step1Data = await stepOneRef.value?.onDraft();
    const step2Data = await stepTwoRef.value?.onDraft();
    const step3Data = await stepThreeRef.value?.onDraft();
    const payload = {
      ...step1Data,
      ...step2Data,
      ...step3Data,
    };
    overlayLoading.toggleLoading(true);
    const res = await api.post(`/campaign/v1/api/campaign`, payload);
    if (res.data.code === 0) {
      theId.value = res.data.data.id;
      toast('success', res.data.message);
      isShowConfirmPopup.value = false;
      overlayLoading.toggleLoading(false);
      callBack();
    } else {
      overlayLoading.toggleLoading(false);
      toast('error', res.data.message);
      isShowConfirmPopup.value = false;
    }
  } catch {
    isShowConfirmPopup.value = false;
  }
};

const onSubmit = async () => {
  if (campaignStore.active === StepValue.StepOne) {
    stepOneRef.value.onSubmit();
  } else if (campaignStore.active === StepValue.StepTwo) {
    stepTwoRef.value.onSubmit();
  } else if (campaignStore.active === StepValue.StepThree) {
    stepThreeRef.value.onSubmit();
  } else if (campaignStore.active === StepValue.StepFour) {
    await getDataEstimateCost();
  }
};

const createCampaign = async () => {
  if (theId.value) {
    const payload = {
      ...campaignStore.step1Data,
      ...campaignStore.step2Data,
      ...campaignStore.step3Data,
    };
    const validate = await campaignStore.finallize(theId.value, payload);
    if (validate) {
      router.push({
        name: ROUTE_NAME.CAMPAIGN,
      });
    } else {
      isVisibleConfirmSavePopup.value = false;
    }
  }
};

const handleSaveUpdate = async (callBack: any) => {
  try {
    const payload = {
      ...campaignStore.step1Data,
      ...campaignStore.step2Data,
      ...campaignStore.step3Data,
    };
    overlayLoading.toggleLoading(true);
    const res = await api.put(`/campaign/v1/api/campaign/${props.id}`, payload);
    if (res.data.code === 0) {
      theId.value = res.data.data.id;
      toast('success', res.data.message);
      isShowConfirmPopup.value = false;
      overlayLoading.toggleLoading(false);
      callBack();
    } else {
      overlayLoading.toggleLoading(false);
      toast('error', res.data.message);
      isVisibleConfirmSavePopup.value = false;
    }
  } catch (error) {
    console.error(error);
    overlayLoading.toggleLoading(false);
    toast('error', TEXT.ERROR_OCCURRED);
    isVisibleConfirmSavePopup.value = false;
  }
};

const updateCampaign = async () => {
  if (props.id) {
    const payload = {
      ...campaignStore.step1Data,
      ...campaignStore.step2Data,
      ...campaignStore.step3Data,
    };
    const validate = await campaignStore.finallize(props.id, payload);
    if (validate) {
      router.push({
        name: ROUTE_NAME.CAMPAIGN,
      });
    } else {
      isVisibleConfirmSavePopup.value = false;
    }
  }
};

const handleConfirmSave = () => {
  if (props.type === FORM_TYPE.ADD) {
    saveDraft(createCampaign);
  } else if (props.type === FORM_TYPE.UPDATE) {
    handleSaveUpdate(updateCampaign);
  }
};

const costEstimateData = ref<any>();

const getDataEstimateCost = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const payload = {
      campaign_id: props.id ? props.id : null,
      target_group_id: campaignStore.step2Data.target_group.target_group_id,
      target_group_file_path: campaignStore.step2Data?.target_group?.error_subscriber_file_path,
      blacklist_file_path: campaignStore.step2Data?.target_group?.error_blacklist_file_path,
      template_id: campaignStore.step3Data?.content?.template_id,
      label_id: campaignStore.step3Data?.content?.label_id,
      schedule: campaignStore.step1Data?.schedule,
    };
    const res = await api.post('/campaign/v1/api/campaign/cost-estimation', payload, {
      skipToastErr: true,
    });
    if (res.data.code === 0) {
      isVisibleConfirmSavePopup.value = true;
      costEstimateData.value = res.data.data;
    } else {
      toast('error', res.data.message);
    }
  } catch (error) {
    console.error(error);
    toast('error', TEXT.ERROR_OCCURRED);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};
//#endregion

//#region Router
const route = useRoute();
const router = useRouter();

const turnBack = () => {
  campaignStore.back(1);
};

const moveToList = () => {
  router.push({
    name: ROUTE_NAME.CAMPAIGN,
  });
  isShowConfirmPopup.value = false;
};

const handleClickMoveToList = () => {
  isShowConfirmPopup.value = true;
};
//#endregion

onBeforeRouteLeave(() => {
  campaignStore.$reset();
});

const setValueByStep = () => {
  if (campaignStore.active === StepValue.StepOne) {
    stepOneRef.value.setValue();
  } else if (campaignStore.active === StepValue.StepTwo) {
    stepTwoRef.value.setValue();
  } else if (campaignStore.active === StepValue.StepThree) {
    stepThreeRef.value.setValue();
  } else if (campaignStore.active === StepValue.StepFour) {
    stepFourRef.value.setValue();
  }
};

onMounted(async () => {
  campaignStore.setFormType({ type: props.type, id: props.id });
  if (props.type !== FORM_TYPE.ADD && props.id) {
    await campaignStore.getDetailCampain(props.id);
    setValueByStep();
  }
});
</script>

<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:confetti" class="text-[30px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: route.path }" @click="handleClickMoveToList"
          >Chiến dịch</el-breadcrumb-item
        >
        <el-breadcrumb-item :to="{ path: route.path }">{{
          props.type === FORM_TYPE.UPDATE
            ? 'Cập nhật chiến dịch đặt lịch'
            : 'Thêm mới chiến dịch đặt lịch'
        }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
  <div class="h-[calc(100vh-80px-42px-53px)] overflow-hidden">
    <div class="justify-center flex">
      <el-steps
        align-center
        class="pt-[50px] w-[722px]"
        finish-status="success"
        :active="campaignStore.active"
      >
        <el-step title="Thông tin chung" :icon="Step1" />
        <el-step
          title="Nhóm đối tượng"
          :icon="campaignStore.active >= StepValue.StepTwo ? Step2Done : Step2"
        />
        <el-step
          title="Nội dung gửi tin"
          :icon="campaignStore.active >= StepValue.StepThree ? Step3Done : Step3"
        />
        <el-step
          title="Tổng quan"
          :icon="campaignStore.active >= StepValue.StepFour ? Step4Done : Step4"
        />
      </el-steps>
    </div>
    <StepOne
      v-show="campaignStore.active === StepValue.StepOne"
      ref="stepOneRef"
      :id="props.id"
      :type="props.type"
    />
    <StepTwo
      v-show="campaignStore.active === StepValue.StepTwo"
      ref="stepTwoRef"
      :id="props.id"
      :type="props.type"
    />
    <StepThree
      v-show="campaignStore.active === StepValue.StepThree"
      ref="stepThreeRef"
      :id="props.id"
      :type="props.type"
    />
    <StepFour
      v-show="campaignStore.active === StepValue.StepFour"
      ref="stepFourRef"
      :type="props.type"
      :id="props.id"
    />
    <div
      class="flex z-[999] py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke items-center"
      :class="campaignStore.active > StepValue.StepOne ? 'justify-between' : 'justify-end'"
    >
      <VElementButton
        v-if="campaignStore.active > StepValue.StepOne"
        icon="arrow-narrow-left"
        text="black"
        bgColor="#EBEBEB"
        label="Quay lại"
        @click="turnBack"
      />
      <div>
        <VElementButton label="Hủy" :bgColor="color.closeButton" @click="handleClickCancel" />
        <VElementButton
          :bgColor="color.main"
          :label="campaignStore.active < StepValue.StepFour ? 'Tiếp tục' : 'Lưu'"
          @click="onSubmit"
        />
      </div>
    </div>
  </div>
  <PopupCancelConfirm
    v-model:popupVisible="isShowConfirmPopup"
    :type="type"
    @onClose="isShowConfirmPopup = false"
    @onConfirm="moveToList"
    @onSaveDraft="saveDraft(moveToList)"
  />
  <VDialog
    v-model:visible="isVisibleConfirmSavePopup"
    modal
    ref="refDialog"
    header="Xác nhận lưu chiến dịch"
    :draggable="false"
    :pt="popupSaveConfirmPt"
    :class="'h-285px'"
    :style="popupSaveConfirmStyle"
  >
    <PopupSaveConfirm :costEstimateData="costEstimateData" @onConfirm="handleConfirmSave" />
  </VDialog>
</template>

<style lang="scss" scoped>
.view-height {
  height: calc(100vh - 172px);
  overflow: auto;
}
</style>
