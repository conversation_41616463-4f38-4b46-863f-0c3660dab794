<script setup lang="ts">
import { useCampaign } from '@/store/useCampaign';
import { ref, watch } from 'vue';
import * as yup from 'yup';
import { useFieldArray, useForm } from 'vee-validate';
import RadioButton from 'primevue/radiobutton';
import {
  days,
  frequencies,
  channels,
  scheduleTypes,
  StepValue,
  failovers,
  FailoverValue,
  weeklyData,
  TypeMonthlyValue,
  TypeCustomValue,
  popupPolicyPt,
  popupPolicyStyle,
  DayValue,
  FrequencyValue,
  FieldName,
  ScheduleTypeText,
} from '../index.constants';
import { FORM_TYPE, isDateBefore, PLACEHOLDER, SYSTEM_CODE, type TDropdownItem } from '@/shared';
import { ElTooltip, ElTimeSelect, ElFormItem } from 'element-plus';
import VElementMultiCheckBox from '@/components/base/ElementComponent/VElementMultiCheckBox.vue';
import PopupPolicy from '../popup/PopupPolicy.vue';
import RemoveIcon from '@/components/icon/RemoveIcon.vue';
import AddIcon from '@/components/icon/AddIcon.vue';
import type { TTimeRange } from '../index.type';
import { getCampaignTypeLabel } from '../index.utils';
import { timestampGetter } from '@/utils';
import { isDateAfter } from '@/shared/validation.shared';

const props = defineProps<{
  id?: number;
  type: FORM_TYPE;
}>();

//#region Datasource
const campaignStore = useCampaign();
const channelOptions = ref<TDropdownItem[]>(channels);
const typeCustom = ref(TypeCustomValue.Option1);
const typeMonthly = ref(TypeMonthlyValue.Option1);
const isVisiblePolicyPopup = ref(false);
//#endregion


//#region Form
const errorMessageTime = ref('');
const errorMessageTimeFailover = ref('');

const validationSchema: any = yup.object({
  name: yup.string().trim().required('Tên chiến dịch không được để trống'),
  schedule_type: yup.string().required('Vui lòng chọn loại chiến dịch đặt lịch'),
  send_time: yup.lazy((_, context) => {
    if (context.parent.schedule_type === ScheduleTypeText.SendOnce) {
      return yup.mixed().required('Thời gian gửi tin không được để trống');
    }
    return yup.mixed().notRequired();
  }),
  start_date: yup.lazy((_, context) => {
    if (
      context.parent.schedule_type === ScheduleTypeText.SendDaily ||
      context.parent.schedule_type === ScheduleTypeText.SendWeekly ||
      context.parent.schedule_type === ScheduleTypeText.SendMonthly ||
      context.parent.schedule_type === ScheduleTypeText.SendCustom
    ) {
      return yup.mixed().required('Từ ngày không được để trống');
    }
    return yup.mixed().notRequired();
  }),
  end_date: yup.lazy((_, context) => {
    if (
      context.parent.schedule_type === ScheduleTypeText.SendDaily ||
      context.parent.schedule_type === ScheduleTypeText.SendWeekly ||
      context.parent.schedule_type === ScheduleTypeText.SendMonthly ||
      context.parent.schedule_type === ScheduleTypeText.SendCustom
    ) {
      return yup.mixed().required('Đến ngày không được để trống');
    }
    return yup.mixed().notRequired();
  }),
  week_days: yup.lazy((_, context) => {
    if (context.parent.schedule_type === ScheduleTypeText.SendWeekly) {
      return yup
        .array()
        .test('check-empty', 'Vui lòng chọn một hoặc nhiều ngày trong tuần', (item: any) => {
          if (item?.length > 0) {
            return true;
          }
          return false;
        });
    }
    return yup.mixed().notRequired();
  }),
  monthlyNumberDay: yup.lazy((_, context) =>
    context.parent.schedule_type === ScheduleTypeText.SendMonthly &&
    typeMonthly.value === TypeMonthlyValue.Option1
      ? yup
          .number()
          .positive()
          .typeError('Ngày không được để trống')
          .required('Ngày không được để trống')
      : yup.mixed().notRequired(),
  ),
  customNumberDayTimes: yup.lazy((_, context) =>
    context.parent.schedule_type === ScheduleTypeText.SendCustom &&
    typeCustom.value === TypeCustomValue.Option1
      ? yup
          .number()
          .positive()
          .typeError('Ngày không được để trống')
          .required('Ngày không được để trống')
      : yup.mixed().notRequired(),
  ),
  customNumberWeekTimes: yup.lazy((_, context) =>
    context.parent.schedule_type === ScheduleTypeText.SendCustom &&
    typeCustom.value === TypeCustomValue.Option2
      ? yup
          .number()
          .positive()
          .typeError('Tuần không được để trống')
          .required('Tuần không được để trống')
      : yup.mixed().notRequired(),
  ),
  customWeekly: yup.lazy((_, context) =>
    context.parent.schedule_type === ScheduleTypeText.SendCustom &&
    typeCustom.value === TypeCustomValue.Option2
      ? yup.number().typeError(' ').required(' ')
      : yup.mixed().notRequired(),
  ),
  customNumberMonthTimes1: yup.lazy((_, context) =>
    context.parent.schedule_type === ScheduleTypeText.SendCustom &&
    typeCustom.value === TypeCustomValue.Option3
      ? yup
          .number()
          .positive()
          .typeError('Tháng không được để trống')
          .required('Tháng không được để trống')
      : yup.mixed().notRequired(),
  ),
  customMonthlyNumberDay: yup.lazy((_, context) =>
    context.parent.schedule_type === ScheduleTypeText.SendCustom &&
    typeCustom.value === TypeCustomValue.Option3
      ? yup
          .number()
          .positive()
          .typeError('Ngày không được để trống')
          .required('Ngày không được để trống')
      : yup.mixed().notRequired(),
  ),
  customMonthlyDay: yup.lazy((_, context) =>
    context.parent.schedule_type === ScheduleTypeText.SendCustom &&
    typeCustom.value === TypeCustomValue.Option4
      ? yup.number().typeError(' ').required(' ')
      : yup.mixed().notRequired(),
  ),
  customNumberMonthTimes2: yup.lazy((_, context) =>
    context.parent.schedule_type === ScheduleTypeText.SendCustom &&
    typeCustom.value === TypeCustomValue.Option4
      ? yup
          .number()
          .positive()
          .typeError('Tháng không được để trống')
          .required('Tháng không được để trống')
      : yup.mixed().notRequired(),
  ),
  customMultiDate: yup
    .array()
    .typeError('Vui lòng chọn giá trị lịch gửi tin muốn gửi')
    .when('schedule_type', (item, schema) => {
      if (item[0] === ScheduleTypeText.SendCustom && typeCustom.value === TypeCustomValue.Option5) {
        return schema
          .test('check-empty', 'Vui lòng chọn giá trị lịch gửi tin muốn gửi', (item1: any) => {
            if (item1?.length > 0) {
              return true;
            }
            return false;
          })
          .nullable();
      }
      return schema.nullable();
    }),
  channel_type: yup.number().required('Vui lòng chọn kênh gửi tin'),
  use_failover: yup.number().notRequired(),
  channel_type_failover: yup.lazy((_, context) => {
    if (context.parent.use_failover === FailoverValue.Yes) {
      return yup.number().required('Vui lòng chọn kênh gửi tin Failover');
    }
    return yup.mixed().notRequired();
  }),
  sending_time_ranges: yup.lazy((_, context) =>
    context.parent.schedule_type === ScheduleTypeText.SendNow ||
    context.parent.schedule_type === ScheduleTypeText.SendOnce
      ? yup.mixed().notRequired()
      : yup.lazy((_, context) => {
          const isValid = context.parent.sending_time_ranges?.every(
            (item: TTimeRange) =>
              (item.start_time && item.end_time) || (!item.start_time && !item.end_time),
          );
          if (!isValid) {
            return yup.array(
              yup.object({
                start_time: yup.mixed().required('Từ giờ không được để trống'),
                end_time: yup.mixed().required('Đến giờ không được để trống'),
              }),
            );
          }
          return yup.mixed().notRequired();
        }),
  ),
  sending_time_ranges_failover: yup.lazy((_, context) =>
    context.parent.schedule_type === ScheduleTypeText.SendNow ||
    context.parent.schedule_type === ScheduleTypeText.SendOnce
      ? yup.mixed().notRequired()
      : yup.lazy((_, context) => {
          if (context.parent.use_failover === FailoverValue.Yes) {
            const isValid = context.parent.sending_time_ranges?.every(
              (item: TTimeRange) =>
                (item.start_time && item.end_time) || (!item.start_time && !item.end_time),
            );
            if (!isValid) {
              return yup.array(
                yup.object({
                  start_time: yup.mixed().required('Từ giờ không được để trống'),
                  end_time: yup.mixed().required('Đến giờ không được để trống'),
                }),
              );
            }
            return yup.mixed().notRequired();
          }
          return yup.mixed().notRequired();
        }),
  ),
});

const { values, handleSubmit, setFieldValue, setValues, errors } = useForm<any>({
  validationSchema,
  initialValues: {
    monthlyNumberDay: 1,
    customNumberDayTimes: 1,
    customNumberWeekTimes: 1,
    monthlyDay: DayValue.Monday,
    monthlyFrequency: FrequencyValue.First,
    customMonthlyFrequency: FrequencyValue.First,
    customNumberMonthTimes1: 1,
    customMonthlyNumberDay: 1,
    customNumberMonthTimes2: 1,
    sending_time_ranges: [
      { start_time: '', end_time: '' },
      { start_time: '', end_time: '' },
    ],
    sending_time_ranges_failover: [
      { start_time: '', end_time: '' },
      { start_time: '', end_time: '' },
    ],
  },
});

const {
  fields: scheduleConfigTimes,
  remove: removeScheduleConfigTimes,
  insert: insertScheduleConfigTimes,
} = useFieldArray<TTimeRange>('sending_time_ranges');

const {
  fields: failoverConfigTimes,
  remove: removeFailoverConfigTimes,
  insert: insertFailoverConfigTimes,
  replace: replaceFailoverConfigTimes,
} = useFieldArray<TTimeRange>('sending_time_ranges_failover');

const disabledPassDates = (date: Date) => {
  return isDateBefore(date, new Date());
};

const disabledDatesStart = (date: Date) => {
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(now.getDate() + 1);
  if (values.end_date) {
    return isDateBefore(date, tomorrow) || isDateAfter(date, values.end_date);
  } else {
    return isDateBefore(date, tomorrow);
  }
};

const disabledDatesEnd = (date: Date) => {
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(now.getDate() + 1);
  if (values.start_date) {
    return isDateBefore(date, values.start_date);
  } else {
    return isDateBefore(date, tomorrow);
  }
};

const disabledCustomDates = (date: Date) => {
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(now.getDate() + 1);

  if (values.start_date && values.end_date) {
    const afterStartDate = new Date(values.start_date);
    afterStartDate.setDate(afterStartDate.getDate());
    const beforeEndDate = new Date(values.end_date);
    beforeEndDate.setDate(beforeEndDate.getDate());
    return isDateBefore(date, afterStartDate) || isDateAfter(date, beforeEndDate);
  } else if (values.start_date) {
    const afterStartDate = new Date(values.start_date);
    afterStartDate.setDate(afterStartDate.getDate());
    return isDateBefore(date, afterStartDate);
  } else if (values.end_date) {
    const beforeEndDate = new Date(values.end_date);
    beforeEndDate.setDate(beforeEndDate.getDate());
    return isDateBefore(date, tomorrow) || isDateAfter(date, beforeEndDate);
  } else {
    return isDateBefore(date, tomorrow);
  }
};

const resetDataStep3 = () => {
  campaignStore.setTemplateData(null);
  campaignStore.setTemplateFailoverData(null);
  campaignStore.setParameters([]);
  campaignStore.setParameterFailovers([]);
  campaignStore.setOA('');
  campaignStore.setBrandname('');
  campaignStore.setValueStep3({});
};

const handleChangeChannel = (value: any) => {
  channelOptions.value = channelOptions.value.map((item: TDropdownItem) => {
    if (item.value === value) {
      return {
        ...item,
        disabled: true,
      };
    }
    return {
      ...item,
      disabled: false,
    };
  });
  setFieldValue('channel_type_failover', undefined);
  handleChangeField(FieldName.ChannelType, value);

  resetDataStep3();
};

const getConfigData = (value: any) => {
  switch (value.schedule_type) {
    case ScheduleTypeText.SendNow:
    case ScheduleTypeText.SendDaily:
      return {};
    case ScheduleTypeText.SendOnce:
      return {
        send_time: timestampGetter(new Date(value.send_time)),
      };
    case ScheduleTypeText.SendWeekly:
      return {
        week_days: value.week_days,
      };
    case ScheduleTypeText.SendMonthly: {
      if (typeMonthly.value === TypeMonthlyValue.Option1) {
        return {
          is_monthly_by_day: true,
          month_day: value.monthlyNumberDay,
        };
      }
      return {
        is_monthly_by_day: false,
        week_days: [value.monthlyDay],
        week_position: value.monthlyFrequency,
      };
    }
    case ScheduleTypeText.SendCustom:
      if (typeCustom.value === TypeCustomValue.Option1) {
        return {
          repeat_interval: value.customNumberDayTimes,
          repeat_type: ScheduleTypeText.SendDaily,
        };
      } else if (typeCustom.value === TypeCustomValue.Option2) {
        return {
          repeat_interval: value.customNumberWeekTimes,
          week_days: value.customWeekly,
          repeat_type: ScheduleTypeText.SendWeekly,
        };
      } else if (typeCustom.value === TypeCustomValue.Option3) {
        return {
          is_monthly_by_day: true,
          repeat_interval: value.customNumberMonthTimes1,
          month_day: value.customMonthlyNumberDay,
          repeat_type: ScheduleTypeText.SendMonthly,
        };
      } else if (typeCustom.value === TypeCustomValue.Option4) {
        return {
          is_monthly_by_day: false,
          repeat_interval: value.customNumberMonthTimes2,
          week_days: value.customMonthlyDay,
          week_position: value.customMonthlyFrequency,
          repeat_type: ScheduleTypeText.SendMonthly,
        };
      } else if (typeCustom.value === TypeCustomValue.Option5) {
        return {
          specific_dates: value.customMultiDate?.map((item: any) =>
            timestampGetter(new Date(item)),
          ),
          repeat_type: ScheduleTypeText.SendCustom,
        };
      }
  }
};

const getTimeRanges = (ranges: TTimeRange[]) => {
  return ranges?.filter((item: TTimeRange) => item.start_time && item.end_time);
};

const getSendingTimeRanges = (value: any) => {
  return value.schedule_type === ScheduleTypeText.SendNow ||
    value.schedule_type === ScheduleTypeText.SendOnce
    ? null
    : getTimeRanges(value.sending_time_ranges);
};

const getSendingTimeRangesFailover = (value: any) => {
  return value.schedule_type === ScheduleTypeText.SendNow ||
    value.schedule_type === ScheduleTypeText.SendOnce
    ? null
    : getTimeRanges(value.sending_time_ranges_failover);
};

const onSubmitVee = handleSubmit(async (value: any) => {
  if (
    value.schedule_type !== ScheduleTypeText.SendNow &&
    value.schedule_type !== ScheduleTypeText.SendOnce &&
    !validateConfigTimes()
  ) {
    return;
  }
  const payload: any = {
    name: value.name,
    campaign_type: campaignStore.campaign_type,
    use_failover: value.use_failover,
    sending_time_ranges: getSendingTimeRanges(value),
    sending_time_ranges_failover: getSendingTimeRangesFailover(value),
    channel_type: value.channel_type,
    channel_type_failover: value.channel_type_failover,
    schedule: {
      type: value.schedule_type,
      start_date: timestampGetter(new Date(value.start_date)),
      end_date: timestampGetter(new Date(value.end_date)),
      config: getConfigData(value),
    },
  };
  if (props.type !== FORM_TYPE.ADD) {
    payload.campaign_id = props.id;
  }
  campaignStore.setValueStep1(payload);
  const validate = await campaignStore.validateStepData(StepValue.StepOne, payload);
  if (typeof validate === 'object') {
    if (validate.code === SYSTEM_CODE.OVERLAPPING_TIME) {
      errorMessageTime.value = validate.message;
      errorMessageTimeFailover.value = '';
    } else if (validate.code === SYSTEM_CODE.OVERLAPPING_TIME_FAILOVER) {
      errorMessageTimeFailover.value = validate.message;
      errorMessageTime.value = '';
    }
  } else if (validate) {
    errorMessageTime.value = '';
    errorMessageTimeFailover.value = '';
    campaignStore.next(1);
  }
});

const onDraft = () => {
  return {
    name: values.name,
    campaign_type: campaignStore.campaign_type,
    use_failover: values.use_failover,
    sending_time_ranges: getSendingTimeRanges(values),
    sending_time_ranges_failover: getSendingTimeRangesFailover(values),
    channel_type: values.channel_type,
    channel_type_failover: values.channel_type_failover,
    schedule: {
      type: values.schedule_type,
      start_date: timestampGetter(new Date(values.start_date)),
      end_date: timestampGetter(new Date(values.end_date)),
      config: getConfigData(values),
    },
  };
};

const getScheduleConfigTimeError = (fieldName: string, index: number) => {
  if (
    fieldName === 'start_time' &&
    !scheduleConfigTimes.value[index]?.value?.start_time &&
    scheduleConfigTimes.value[index]?.value?.end_time
  ) {
    return 'Từ giờ không được để trống';
  }
  if (
    fieldName === 'end_time' &&
    !scheduleConfigTimes.value[index]?.value?.end_time &&
    scheduleConfigTimes.value[index]?.value?.start_time
  ) {
    return 'Đến giờ không được để trống';
  }
};

const getFailoverConfigTimeError = (fieldName: string, index: number) => {
  if (
    fieldName === 'start_time' &&
    !failoverConfigTimes.value[index]?.value?.start_time &&
    failoverConfigTimes.value[index]?.value?.end_time
  ) {
    return 'Từ giờ không được để trống';
  }
  if (
    fieldName === 'end_time' &&
    !failoverConfigTimes.value[index]?.value?.end_time &&
    failoverConfigTimes.value[index]?.value?.start_time
  ) {
    return 'Đến giờ không được để trống';
  }
};

const validateConfigTimes = () => {
  let isValidBothValueMain = true;
  let isValidBothValueFailover = true;
  scheduleConfigTimes.value?.some((item: { value: TTimeRange }) => {
    if (
      (item.value?.start_time && !item.value?.end_time) ||
      (!item.value?.start_time && item.value?.end_time)
    ) {
      isValidBothValueMain = false;
    }
  });
  failoverConfigTimes.value?.some((item: { value: TTimeRange }) => {
    if (
      (item.value?.start_time && !item.value?.end_time) ||
      (!item.value?.start_time && item.value?.end_time)
    ) {
      isValidBothValueFailover = false;
    }
  });
  if (!isValidBothValueMain || !isValidBothValueFailover) {
    errorMessageTime.value = '';
    errorMessageTimeFailover.value = '';
    return false;
  }

  const isEmptyScheduleConfigTimes = values.sending_time_ranges?.some(
    (item: TTimeRange) => !item.start_time && !item.end_time,
  );
  const isEmptyFailoverConfigTimes = values.sending_time_ranges_failover?.some(
    (item: TTimeRange) => !item.start_time && !item.end_time,
  );

  if (isEmptyScheduleConfigTimes) {
    errorMessageTime.value = 'Khung giờ gửi tin không được để trống';
  } else {
    errorMessageTime.value = '';
  }
  if (values.use_failover && isEmptyFailoverConfigTimes) {
    errorMessageTimeFailover.value = 'Khung giờ gửi tin không được để trống';
  } else if (values.use_failover) {
    errorMessageTimeFailover.value = '';
  }

  if (isEmptyScheduleConfigTimes || (values.use_failover && isEmptyFailoverConfigTimes))
    return false;

  return true;
};

watch(
  () => values.sending_time_ranges,
  () => {
    validateConfigTimes();
  },
  { deep: true },
);

watch(
  () => values.sending_time_ranges_failover,
  () => {
    validateConfigTimes();
  },
  { deep: true },
);

const onSubmit = () => {
  validateConfigTimes();
  onSubmitVee();
};

const isSetValue = ref(0);

const setValue = () => {
  if (isSetValue.value === 2) return;
  isSetValue.value += 1;
  disabledChannelType();
  const data = campaignStore.step1Data;
  if (data) {
    setValues({
      name: data.name,
      schedule_type: data.schedule?.type,
      channel_type: data.channel_type,
      use_failover: data.use_failover ? 1 : 0,
      channel_type_failover: data.channel_type_failover,
      sending_time_ranges: data.sending_time_ranges ?? [
        { start_time: '', end_time: '' },
        { start_time: '', end_time: '' },
      ],
      sending_time_ranges_failover: data.sending_time_ranges_failover ?? [
        { start_time: '', end_time: '' },
        { start_time: '', end_time: '' },
      ],
    });
    if (data.schedule?.config?.is_monthly_by_day && !data.schedule?.config?.repeat_interval) {
      typeMonthly.value = TypeMonthlyValue.Option1;
    } else if (
      !data.schedule?.config?.is_monthly_by_day &&
      !data.schedule?.config?.repeat_interval
    ) {
      typeMonthly.value = TypeMonthlyValue.Option2;
    }
    if (data.schedule?.config?.repeat_interval || data.schedule?.config?.specific_dates) {
      if (data.schedule?.config?.repeat_type === ScheduleTypeText.SendDaily) {
        typeCustom.value = TypeCustomValue.Option1;
      } else if (data.schedule?.config?.repeat_type === ScheduleTypeText.SendWeekly) {
        typeCustom.value = TypeCustomValue.Option2;
      } else if (
        data.schedule?.config?.is_monthly_by_day &&
        data.schedule?.config?.repeat_type === ScheduleTypeText.SendMonthly
      ) {
        typeCustom.value = TypeCustomValue.Option3;
      } else if (
        !data.schedule?.config?.is_monthly_by_day &&
        data.schedule?.config?.repeat_type === ScheduleTypeText.SendMonthly
      ) {
        typeCustom.value = TypeCustomValue.Option4;
      } else if (data.schedule?.config?.specific_dates) {
        typeCustom.value = TypeCustomValue.Option5;
      }
    }
    if (
      data.schedule?.type !== ScheduleTypeText.SendNow &&
      data.schedule?.type !== ScheduleTypeText.SendOnce
    ) {
      setValues({
        start_date:
          typeof data.schedule?.start_date === 'string'
            ? new Date(data.schedule?.start_date)
            : new Date(data.schedule?.start_date * 1000),
        end_date:
          typeof data.schedule?.end_date === 'string'
            ? new Date(data.schedule?.end_date)
            : new Date(data.schedule?.end_date * 1000),
      });
    }
    if (data.schedule?.type === ScheduleTypeText.SendWeekly) {
      setValues({
        week_days: data.schedule?.config?.week_days,
      });
    }
    if (data.schedule?.type === ScheduleTypeText.SendOnce) {
      setValues({
        send_time:
          typeof data.schedule?.config?.send_time === 'string'
            ? data.schedule?.config?.send_time
            : new Date(data.schedule?.config?.send_time * 1000),
      });
    }
    if (typeMonthly.value === TypeMonthlyValue.Option1) {
      setValues({
        monthlyNumberDay: data.schedule?.config?.month_day,
      });
    } else if (typeMonthly.value === TypeMonthlyValue.Option2) {
      setValues({
        monthlyDay: data.schedule?.config?.week_days?.length
          ? data.schedule?.config?.week_days[0]
          : null,
        monthlyFrequency: data.schedule?.config?.week_position,
      });
    }
    if (typeCustom.value === TypeCustomValue.Option1) {
      setValues({
        customNumberDayTimes: data.schedule?.config?.repeat_interval,
      });
    } else if (typeCustom.value === TypeCustomValue.Option2) {
      setValues({
        customNumberWeekTimes: data.schedule?.config?.repeat_interval,
        customWeekly: data.schedule?.config?.week_days,
      });
    } else if (typeCustom.value === TypeCustomValue.Option3) {
      setValues({
        customNumberMonthTimes1: data.schedule?.config?.repeat_interval,
        customMonthlyNumberDay: data.schedule?.config?.month_day,
      });
    } else if (typeCustom.value === TypeCustomValue.Option4) {
      setValues({
        customNumberMonthTimes2: data.schedule?.config?.repeat_interval,
        customMonthlyDay: data.schedule?.config?.week_days,
        customMonthlyFrequency: data.schedule?.config?.week_position,
      });
    } else if (typeCustom.value === TypeCustomValue.Option5) {
      setValues({
        customMultiDate: data.schedule?.config?.specific_dates?.map((item: any) =>
          typeof item === 'string' ? item : new Date(item * 1000),
        ),
      });
    }
  }
  if (campaignStore.campaign_type) {
    setFieldValue('campaign_type', getCampaignTypeLabel(campaignStore.campaign_type));
  }
};
//#endregion

//#region Set data to store for validation cancel button
const resetFailoverData = () => {
  setFieldValue('channel_type_failover', null);
  replaceFailoverConfigTimes([
    { start_time: '', end_time: '' },
    { start_time: '', end_time: '' },
  ]);
};

const handleChangeField = (field: FieldName, value: any) => {
  if (field === FieldName.UseFailover) resetFailoverData();
  if (field === FieldName.StartDate) {
    setFieldValue('customMultiDate', undefined);
  }
  switch (field) {
    case FieldName.Name:
    case FieldName.ChannelType:
    case FieldName.UseFailover:
    case FieldName.ChannelTypeFailover:
      campaignStore.setValueStep1({
        ...campaignStore.step1Data,
        [field]: value,
      });
      break;
    case FieldName.ScheduleType:
      if (value === ScheduleTypeText.SendMonthly) {
        setFieldValue('monthlyNumberDay', 1);
        setFieldValue('monthlyDay', DayValue.Monday);
        setFieldValue('monthlyFrequency', FrequencyValue.First);
      } else if (value === ScheduleTypeText.SendCustom) {
        setFieldValue('customNumberDayTimes', 1);
        setFieldValue('customNumberWeekTimes', 1);
        setFieldValue('customMonthlyFrequency', FrequencyValue.First);
        setFieldValue('customNumberMonthTimes1', 1);
        setFieldValue('customMonthlyNumberDay', 1);
        setFieldValue('customNumberMonthTimes2', 1);
        setFieldValue('customNumberMonthTimes2', 1);
      }
      campaignStore.setValueStep1({
        ...campaignStore.step1Data,
        schedule: {
          ...campaignStore.step1Data?.schedule,
          type: value,
        },
      });
      break;
    case FieldName.StartDate:
    case FieldName.EndDate:
      campaignStore.setValueStep1({
        ...campaignStore.step1Data,
        schedule: {
          ...campaignStore.step1Data?.schedule,
          [field]: value,
        },
      });
      break;
  }
};

const disabledChannelType = () => {
  channelOptions.value = channelOptions.value.map((item: TDropdownItem) => {
    if (item.value === campaignStore.step1Data?.channel_type) {
      return {
        ...item,
        disabled: true,
      };
    }
    return {
      ...item,
      disabled: false,
    };
  });
};
//#endregion

watch(
  () => campaignStore.active,
  (newValue: StepValue) => {
    if (newValue === StepValue.StepOne) {
      if (props.type !== FORM_TYPE.ADD) setValue();
      else {
        if (values.use_failover === null || values.use_failover === undefined) {
          setFieldValue('use_failover', FailoverValue.No, false);
        }
        setFieldValue('campaign_type', getCampaignTypeLabel(campaignStore.campaign_type));
      }
    }
  },
  {
    immediate: true,
  },
);

//#region Setvalue
watch(
  () => values.sending_time_ranges,
  (newValue) => {
    campaignStore.setValueStep1({
      ...campaignStore.step1Data,
      sending_time_ranges: getTimeRanges(newValue),
    });
  },
  {
    deep: true,
  },
);

watch(
  () => values.sending_time_ranges_failover,
  (newValue) => {
    campaignStore.setValueStep1({
      ...campaignStore.step1Data,
      sending_time_ranges_failover: getTimeRanges(newValue),
    });
  },
  {
    deep: true,
  },
);

watch(
  () => values.send_time,
  (newValue) => {
    campaignStore.setValueStep1({
      ...campaignStore.step1Data,
      schedule: {
        ...campaignStore.step1Data?.schedule,
        send_time: timestampGetter(new Date(newValue)),
      },
    });
  },
  {
    deep: true,
  },
);
//#endregion

defineExpose({
  onSubmit,
  setValue,
  onDraft,
});
</script>
<template>
  <div class="h-[calc(100%-115px-50px)] overflow-y-auto">
    <el-form
      label-position="top"
      class="grid grid-cols-2 gap-x-[5%] mx-[3%] px-[4%] pt-8"
      :disabled="props.type === FORM_TYPE.DETAILS"
    >
      <!-- Tên Chiến dịch -->
      <VElementInput
        class="mt-[15px]"
        size="default"
        name="name"
        :placeholder="PLACEHOLDER.TYPE"
        :maxlength="200"
        :showLimit="false"
        :style="'w-[100%]'"
        :required="true"
        :label="'Tên chiến dịch'"
        @change="($event: any) => handleChangeField(FieldName.Name, $event)"
      />
      <!-- Loại Chiến dịch -->
      <VElementInput
        class="mt-[15px]"
        size="default"
        name="campaign_type"
        placeholder=" "
        :style="'w-[100%]'"
        :label="'Loại chiến dịch'"
        :disabled="true"
      />
      <!-- Loại Chiến dịch đặt lịch -->
      <VElementDropdown
        class="mt-[15px] schedule-type"
        name="schedule_type"
        :placeholder="PLACEHOLDER.SELECT"
        :required="true"
        :filterable="true"
        :label="'Loại chiến dịch đặt lịch'"
        :style="'w-[100%]'"
        :option="scheduleTypes"
        @change="($event: any) => handleChangeField(FieldName.ScheduleType, $event)"
      />
      <!-- Thời gian gửi tin -->
      <VElementDateTimePicker
        v-if="values.schedule_type === ScheduleTypeText.SendOnce"
        class="mt-[15px] send-time"
        name="send_time"
        type="datetime"
        format="DD-MM-YYYY HH:mm"
        :placeholder="PLACEHOLDER.SELECT"
        :required="true"
        :label="'Thời gian gửi tin'"
        :clearable="false"
        :style="'!w-[100%]'"
        :disabledDates="disabledPassDates"
      />
      <!-- Hiệu lực Chiến dịch -->
      <div
        v-if="
          values.schedule_type === ScheduleTypeText.SendDaily ||
          values.schedule_type === ScheduleTypeText.SendWeekly ||
          values.schedule_type === ScheduleTypeText.SendMonthly ||
          values.schedule_type === ScheduleTypeText.SendCustom
        "
        class="mt-[15px]"
      >
        <div class="text-[14px] text-[#6b7280] font-semibold">
          Hiệu lực chiến dịch <span class="text-[#F10000]">*</span>
        </div>
        <div class="flex gap-[5px] mt-[10px]">
          <VElementDateTimePicker
            class="flex-1"
            name="start_date"
            type="date"
            format="DD-MM-YYYY"
            placeholder="Từ: -- : --"
            :clearable="false"
            :style="'!w-[100%]'"
            :disabledDates="disabledDatesStart"
            @changeDate="($event: any) => handleChangeField(FieldName.StartDate, $event)"
          />
          <VElementDateTimePicker
            class="flex-1"
            name="end_date"
            type="date"
            format="DD-MM-YYYY"
            placeholder="Đến: --:--"
            :clearable="false"
            :style="'!w-[100%]'"
            :disabledDates="disabledDatesEnd"
            @changeDate="($event: any) => handleChangeField(FieldName.EndDate, $event)"
          />
        </div>
      </div>
      <!-- Lịch gửi tin -->
      <div
        v-if="
          values.schedule_type === ScheduleTypeText.SendWeekly ||
          values.schedule_type === ScheduleTypeText.SendMonthly ||
          values.schedule_type === ScheduleTypeText.SendCustom
        "
        class="mt-[15px] min-w-[170%]"
      >
        <div class="text-[14px] text-[#6b7280] font-semibold">
          Lịch gửi tin <span class="text-[#F10000]">*</span>
        </div>
        <!-- Lịch gửi tin hàng tuần -->
        <VElementMultiCheckBox
          v-show="values.schedule_type === ScheduleTypeText.SendWeekly"
          name="week_days"
          :style="'!grid !grid-cols-2'"
          :required="true"
          :categories="weeklyData"
        />
        <!-- Lịch gửi tin hàng tháng -->
        <div
          v-show="values.schedule_type === ScheduleTypeText.SendMonthly"
          class="text-[14px] pt-[4px]"
        >
          <!--  -->
          <div class="flex items-center gap-[10px]">
            <RadioButton v-model="typeMonthly" :value="TypeMonthlyValue.Option1" />
            <div>Ngày</div>
            <InputPosition
              :max="31"
              :preventZero="true"
              name="monthlyNumberDay"
              controlsPosition="right"
            />
            <div>Hàng tháng</div>
          </div>
          <div class="text-[#f56c6c] text-[12px] pt-[1px]">
            {{ errors.monthlyNumberDay ? 'Vui lòng nhập giá trị lịch gửi tin muốn gửi' : '' }}
          </div>
          <!--  -->
          <div class="pt-[10px] flex items-center gap-[10px]">
            <RadioButton v-model="typeMonthly" :value="TypeMonthlyValue.Option2" />
            <VElementDropdown
              placeholder=" "
              name="monthlyDay"
              class="w-[120px]"
              :option="days"
              :style="'w-[100%]'"
              :filterable="true"
              :required="true"
            />
            <VElementDropdown
              placeholder=" "
              class="w-[120px]"
              name="monthlyFrequency"
              :option="frequencies"
              :style="'w-[100%]'"
              :filterable="true"
              :required="true"
            />
            <div>Hàng tháng</div>
          </div>
        </div>
        <!-- Lịch gửi tin đặt lịch tùy chỉnh -->
        <div
          v-show="values.schedule_type === ScheduleTypeText.SendCustom"
          class="text-[14px] pt-[4px] schedule-custom"
        >
          <!--  -->
          <div class="flex items-center gap-[10px]">
            <RadioButton v-model="typeCustom" :value="TypeCustomValue.Option1" />
            <div>Cứ mỗi</div>
            <InputPosition
              width="80px"
              :preventZero="true"
              :max="2000"
              name="customNumberDayTimes"
            />
            <div>ngày/ lần</div>
          </div>
          <div class="text-[#f56c6c] text-[12px] pt-[1px]">
            {{
              errors.customNumberDayTimes ? 'Vui lòng nhập/chọn giá trị lịch gửi tin muốn gửi' : ''
            }}
          </div>
          <!--  -->
          <div class="flex items-center gap-[10px] mt-[15px]">
            <RadioButton v-model="typeCustom" :value="TypeCustomValue.Option2" />
            <div>Cứ mỗi</div>
            <InputPosition
              width="80px"
              :preventZero="true"
              :max="312"
              name="customNumberWeekTimes"
            />
            <div>tuần/ lần vào</div>
            <VElementDropdown
              name="customWeekly"
              formItemClass="w-[184px]"
              :multiple="true"
              :placeholder="PLACEHOLDER.SELECT"
              :filterable="false"
              :option="days"
              :style="'w-[100%]'"
            />
          </div>
          <div class="text-[#f56c6c] text-[12px] pt-[1px]">
            {{
              errors.customNumberWeekTimes || errors.customWeekly
                ? 'Vui lòng nhập/chọn giá trị lịch gửi tin muốn gửi'
                : ''
            }}
          </div>
          <!--  -->
          <div class="flex items-center gap-[10px] mt-[15px]">
            <RadioButton v-model="typeCustom" :value="TypeCustomValue.Option3" />
            <div>Cứ mỗi</div>
            <InputPosition
              width="80px"
              :preventZero="true"
              :max="75"
              name="customNumberMonthTimes1"
            />
            <div>tháng/ lần vào ngày</div>
            <InputPosition :preventZero="true" :max="31" name="customMonthlyNumberDay" />
            <div>hàng tháng</div>
          </div>
          <div class="text-[#f56c6c] text-[12px] pt-[1px]">
            {{
              errors.customNumberMonthTimes1 || errors.customMonthlyNumberDay
                ? 'Vui lòng nhập/chọn giá trị lịch gửi tin muốn gửi'
                : ''
            }}
          </div>
          <!--  -->
          <div class="flex items-center gap-[10px] mt-[15px]">
            <RadioButton v-model="typeCustom" :value="TypeCustomValue.Option4" />
            <div>Cứ mỗi</div>
            <InputPosition
              :preventZero="true"
              width="80px"
              :max="75"
              name="customNumberMonthTimes2"
            />
            <div>tháng/ lần vào</div>
            <VElementDropdown
              name="customMonthlyDay"
              class="w-[190px]"
              :multiple="true"
              :filterable="true"
              :option="days"
              :style="'w-[100%]'"
              :placeholder="PLACEHOLDER.SELECT"
            />
            <VElementDropdown
              class="w-[190px]"
              name="customMonthlyFrequency"
              :filterable="true"
              :option="frequencies"
              :style="'w-[100%]'"
              :placeholder="PLACEHOLDER.SELECT"
            />
            <div>hàng tháng</div>
          </div>
          <div class="text-[#f56c6c] text-[12px] pt-[1px]">
            {{
              errors.customNumberMonthTimes2 || errors.customMonthlyDay
                ? 'Vui lòng nhập/chọn giá trị lịch gửi tin muốn gửi'
                : ''
            }}
          </div>
          <!--  -->
          <div class="pt-[10px] flex items-center gap-[10px]">
            <RadioButton v-model="typeCustom" :value="TypeCustomValue.Option5" />
            <div>Nhiều ngày cụ thể</div>
            <div class="w-[300px]">
              <VElementDateTimePicker
                name="customMultiDate"
                format="DD-MM-YYYY"
                :type="'dates'"
                :style="'!w-full'"
                :disabledDates="disabledCustomDates"
              />
            </div>
          </div>
        </div>
      </div>
    </el-form>
    <el-form
      label-position="top"
      class="bg-[#FBFAFF] border-y-[1px] border-[#DFE4EA] pb-[30px] mx-[3%] px-[4%] mt-[22px]"
      :disabled="props.type === FORM_TYPE.DETAILS"
    >
      <div class="grid grid-cols-2 gap-x-[5%] mt-[15px]">
        <VElementDropdown
          class=""
          name="channel_type"
          :required="true"
          :filterable="false"
          :label="'Kênh gửi tin'"
          :option="channels"
          :style="'w-[100%]'"
          :placeholder="PLACEHOLDER.SELECT"
          @change="handleChangeChannel"
        />
        <div
          v-show="
            values.schedule_type !== ScheduleTypeText.SendNow &&
            values.schedule_type !== ScheduleTypeText.SendOnce
          "
          class="wrapper-frame"
        >
          <div class="flex items-center justify-between h-[22px]">
            <div class="flex items-center">
              <div class="text-[14px] text-[#6b7280] font-semibold">
                Khung giờ gửi tin <span class="text-[#F10000]">*</span>
              </div>
              <div class="cursor-pointer">
                <el-tooltip placement="top">
                  <template #content>
                    <p class="max-w-[320px]">
                      Khung giờ gửi tin tùy theo từng kênh và nội dung gửi tin cần được tuân theo
                      Chính sách gửi tin của hệ thống.
                    </p>
                  </template>
                  <Icon icon="mingcute:information-fill" class="ml-[6px] text-[#6b7280]" />
                </el-tooltip>
              </div>
            </div>
            <p
              class="text-[#1A34B5] underline cursor-pointer text-[10px]"
              @click="isVisiblePolicyPopup = true"
            >
              Xem Chính sách gửi tin
            </p>
          </div>
          <div
            v-for="(item, index) of scheduleConfigTimes"
            :key="index"
            class="flex gap-[5px] pt-[7px] wrapper-block"
          >
            <el-form-item
              :error="getScheduleConfigTimeError('start_time', index)"
              :disabled="props.type === FORM_TYPE.DETAILS"
              class="w-full"
            >
              <el-time-select
                v-model="item.value.start_time"
                class="w-[100%]"
                placeholder="Từ: -- : --"
                start="00:00"
                step="00:15"
                end="23:45"
                :maxTime="item.value.end_time"
              />
            </el-form-item>
            <el-form-item
              :error="getScheduleConfigTimeError('end_time', index)"
              :disabled="props.type === FORM_TYPE.DETAILS"
              class="w-full"
            >
              <el-time-select
                v-model="item.value.end_time"
                class="w-[100%]"
                placeholder="Đến: --:--"
                start="00:00"
                step="00:15"
                end="23:45"
                :minTime="item.value.start_time"
              />
            </el-form-item>
            <div class="flex items-center w-[65px] gap-1">
              <RemoveIcon
                v-if="scheduleConfigTimes.length > 1"
                @click="removeScheduleConfigTimes(index)"
              />
              <AddIcon
                @click="
                  insertScheduleConfigTimes(index + 1, {
                    start_time: '',
                    end_time: '',
                  })
                "
              />
            </div>
          </div>
          <div v-if="errorMessageTime" class="text-[#f56c6c] text-[12px] pt-[1px]">
            {{ errorMessageTime }}
          </div>
        </div>
      </div>
      <div class="mt-[15px] mr-[5%]">
        <div class="flex items-center gap-[2px] text-[#6b7280]">
          <div class="text-[14px] font-semibold">
            Tính năng Failover <span class="text-[#F10000]">*</span>
          </div>
          <div class="cursor-pointer">
            <el-tooltip placement="top">
              <template #content>
                <p class="max-w-[320px]">
                  Khi gửi tin bằng kênh gửi tin đầu tiên thất bại, tính năng Failover hỗ trợ bạn gửi
                  lại tin bằng kênh thay thế
                </p>
              </template>
              <Icon icon="mingcute:information-fill" class="ml-[6px] text-[#6b7280]" />
            </el-tooltip>
          </div>
        </div>
        <VElementDropdown
          name="use_failover"
          label=" "
          :placeholder="PLACEHOLDER.SELECT"
          :filterable="false"
          :formItemClass="'w-[50%]'"
          :option="failovers"
          @change="($event: any) => handleChangeField(FieldName.UseFailover, $event)"
        />
      </div>
      <div
        v-if="values.use_failover === FailoverValue.Yes"
        class="grid grid-cols-2 gap-x-[5%] mt-[15px]"
      >
        <VElementDropdown
          name="channel_type_failover"
          :required="true"
          :filterable="false"
          :label="'Kênh gửi tin Failover'"
          :style="'w-[100%]'"
          :placeholder="PLACEHOLDER.SELECT"
          :option="channelOptions"
          @change="($event: any) => handleChangeField(FieldName.ChannelTypeFailover, $event)"
        />
        <div
          v-show="
            values.schedule_type !== ScheduleTypeText.SendNow &&
            values.schedule_type !== ScheduleTypeText.SendOnce
          "
          class="wrapper-frame"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="text-[14px] text-[#6b7280] font-semibold">
                Khung giờ gửi tin <span class="text-[#F10000]">*</span>
              </div>
              <div class="cursor-pointer">
                <el-tooltip placement="top">
                  <template #content>
                    <p class="max-w-[320px]">
                      Khung giờ gửi tin tùy theo từng kênh và nội dung gửi tin cần được tuân theo
                      Chính sách gửi tin của hệ thống.
                    </p>
                  </template>
                  <Icon icon="mingcute:information-fill" class="ml-[6px] text-[#6b7280]" />
                </el-tooltip>
              </div>
            </div>
            <p
              class="text-[#1A34B5] text-[10px] underline cursor-pointer"
              @click="isVisiblePolicyPopup = true"
            >
              Xem Chính sách gửi tin
            </p>
          </div>
          <div
            v-for="(item, index) of failoverConfigTimes"
            :key="index"
            class="flex gap-[5px] pt-[7px] wrapper-block"
          >
            <el-form-item
              :error="getFailoverConfigTimeError('start_time', index)"
              :disabled="props.type === FORM_TYPE.DETAILS"
              class="w-full"
            >
              <el-time-select
                v-model="item.value.start_time"
                class="w-[100%]"
                placeholder="Từ: -- : --"
                start="00:00"
                step="00:15"
                end="23:45"
                :maxTime="item.value.end_time"
              />
            </el-form-item>
            <el-form-item
              :error="getFailoverConfigTimeError('end_time', index)"
              :disabled="props.type === FORM_TYPE.DETAILS"
              class="w-full"
            >
              <el-time-select
                v-model="item.value.end_time"
                class="w-[100%]"
                placeholder="Đến: --:--"
                start="00:00"
                step="00:15"
                end="23:45"
                :minTime="item.value.start_time"
              />
            </el-form-item>
            <div class="flex items-center w-[65px] gap-1">
              <RemoveIcon
                v-if="failoverConfigTimes.length > 1"
                @click="removeFailoverConfigTimes(index)"
              />
              <AddIcon
                @click="
                  insertFailoverConfigTimes(index + 1, {
                    start_time: '',
                    end_time: '',
                  })
                "
              />
            </div>
          </div>
          <div v-if="errorMessageTimeFailover" class="text-[#f56c6c] text-[12px] pt-[1px]">
            {{ errorMessageTimeFailover }}
          </div>
        </div>
      </div>
    </el-form>
    <VDialog
      v-model:visible="isVisiblePolicyPopup"
      modal
      ref="refDialog"
      header="Chính sách gửi tin"
      :draggable="false"
      :pt="popupPolicyPt"
      :class="'h-285px'"
      :style="popupPolicyStyle"
    >
      <PopupPolicy :type="FORM_TYPE.DETAILS" @onClose="isVisiblePolicyPopup = false" />
    </VDialog>
  </div>
</template>

<style scoped lang="scss">
.el-form-item.is-error {
  margin-bottom: 14px;
}
.schedule-custom {
  .el-form-item.is-error {
    margin-bottom: 0;
  }
}
:deep(.send-time .el-form-item__label),
:deep(.schedule-type .el-form-item__label) {
  display: inline-block !important;
}
</style>
