<script setup lang="ts">
import { useCampaign } from '@/store/useCampaign';
import { computed, inject, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import * as yup from 'yup';
import { useForm } from 'vee-validate';
import VDownLoadFile from '@/components/base/common/VDownLoadFile.vue';
import {
  targetGroupTypes,
  TargetGroupTypeValue,
  popupFileResultPt,
  popupFileResultStyle,
  StepValue,
  TemplateType,
  FileType,
} from '../index.constants';
import { FORM_TYPE, PLACEHOLDER, ROUTE_NAME, SIZE_LIMIT, TEXT, type TDropdownItem } from '@/shared';
import PopupFileResult from '../popup/PopupFileResult.vue';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import VUploadFileBlacklist from '../components/VUploadFileBlacklist.vue';
import VUploadFileTarget from '../components/VUploadFileTarget.vue';
import { useUserSession } from '@/store/userSession';
import { getNotBlankString, nullGetter, stringGetter } from '@/utils/getter.utils';

const props = defineProps<{
  type: FORM_TYPE;
  id?: number;
}>();

const api = useApi();
const toast = inject('toast') as any;
const user = useUserSession() as any;
const overlayLoading = useOverLayLoadingStore();
const router = useRouter();
const existedGroupUser = ref<TDropdownItem[]>([]);

//#region Popup
const isVisibleFileTargetResultPopup = ref(false);
const isVisibleFileBlacklistResultPopup = ref(false);

const handleShowTargetResult = () => {
  isVisibleFileTargetResultPopup.value = true;
};

const handleShowBlacklistResult = () => {
  isVisibleFileBlacklistResultPopup.value = true;
};

const navigateToAddGroup = () => {
  const url = router.resolve({
    name: ROUTE_NAME.ADD_NHOM_DOI_TUONG,
  }).href;
  window.open(url, '_blank');
};
//#endregion

//#region Datasource
const blacklistUploadRef = ref();
const subscriberUploadUploadRef = ref();
const campaignStore = useCampaign();

const dataStep2 = computed(() => ({
  target_group_type: values.target_group_type,
  subscriber_upload_file: subscriberUploadUploadRef.value?.dataFile,
  error_subscriber_file_path:
    subscriberUploadUploadRef.value?.dataFile?.error_file_url ??
    campaignStore.step2Data?.target_group?.error_subscriber_file_path,
  blacklist_upload_file: blacklistUploadRef.value?.dataFile,
  error_blacklist_file_path:
    blacklistUploadRef.value?.dataFile?.error_file_url ??
    campaignStore.step2Data?.target_group?.error_blacklist_file_path,
}));

const isSetValue = ref(false);

const setValue = () => {
  if (isSetValue.value) return;
  isSetValue.value = true;
  const data = campaignStore.step2Data.target_group;
  if (data) {
    setValues({
      target_group_id: data.target_group_id,
      target_group_type: data.target_group_type,
      subscriber_upload_file: data.subscriber_upload_file,
      blacklist_upload_file: data.blacklist_upload_file,
    });
    if (data.subscriber_upload_file && subscriberUploadUploadRef.value) {
      setTimeout(() => {
        subscriberUploadUploadRef.value.fileUrl = data.subscriber_upload_file;
        subscriberUploadUploadRef.value.isUpload = true;
      }, 10);
    }
    if (data.blacklist_upload_file && blacklistUploadRef.value) {
      setTimeout(() => {
        blacklistUploadRef.value.fileUrl = data.blacklist_upload_file;
        blacklistUploadRef.value.isUpload = true;
      }, 10);
    }
  }
};

const getExistedGroup = async () => {
  try {
    overlayLoading.toggleLoading(true);
    const res = await api.get(
      `/cdp/v1/api/client/target-group/all?businessId=${user.user?.id_business}`,
    );
    if (res.data.code === 0) {
      existedGroupUser.value = res.data.data?.map((item: any) => ({
        value: item.id,
        label: item.targetGroupName,
      }));
      overlayLoading.toggleLoading(false);
    } else {
      overlayLoading.toggleLoading(false);
      toast('error', res.data.message);
    }
  } catch (error) {
    console.error(error);
    overlayLoading.toggleLoading(false);
    toast('error', TEXT.ERROR_OCCURRED);
  }
};

const handleDownloadSubscriberList = async (templateType: TemplateType) => {
  try {
    let urlDownload = '/campaign/v1/api/campaign/subscriber-list-template';
    if (templateType === TemplateType.Blacklist) {
      urlDownload = '/campaign/v1/api/campaign/blacklist-template';
    }
    const response = await api.get(urlDownload, {
      responseType: 'blob',
    });
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const a = document.createElement('a');
    a.href = url;
    let fileName = 'Template mẫu_Danh sách đối tượng.xlsx';
    if (templateType === TemplateType.Blacklist) {
      fileName = 'Template mẫu_Danh sách Black list.xlsx';
    }
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    a.remove();
    window.URL.revokeObjectURL(url);
  } catch (error: any) {
    if (!error?.response?.status) {
      toast('error', TEXT.ERROR_OCCURRED);
    }
  }
};

const handleRemoveBlacklist = () => {
  campaignStore.setBlacklistUploadLink('');
  campaignStore.setBlacklistUploadPath('');
  campaignStore.setBlacklistUploadView('');
  const payload: any = {
    target_group: {
      ...campaignStore.step2Data.target_group,
      blacklist_upload_file: undefined,
      error_blacklist_file_path: undefined,
      blacklist_file_view_name: undefined,
      blacklist_file_info: {
        validation_status: '',
        total_records: '',
        valid_records: '',
        invalid_records: '',
        upload_date: '',
        error_file_view_name: '',
      },
    },
  };
  campaignStore.setValueStep2(payload);
};
//#endregion

//#region Form
const validationSchema = yup.object({
  target_group_id: yup.lazy((_, context) => {
    if (context.parent.target_group_type === TargetGroupTypeValue.UseExisted) {
      return yup.number().required('Vui lòng chọn Nhóm đối tượng có sẵn');
    }
    return yup.mixed().notRequired();
  }),
  subscriberUploadSize: yup.lazy((_, context) => {
    if (context.parent.target_group_type === TargetGroupTypeValue.UseList) {
      return yup
        .number()
        .max(SIZE_LIMIT._25_MB, 'Vui lòng chỉ upload file có dung lượng tối đa là 25MB')
        .nullable();
    }
    return yup.mixed().notRequired();
  }),
  subscriberUploadType: yup.lazy((_, context) => {
    if (context.parent.target_group_type === TargetGroupTypeValue.UseList) {
      return yup
        .string()
        .matches(
          /^file$/,
          'Định dạng file chưa được hỗ trợ, vui lòng upload file có định dạng đuôi xlsx',
        )
        .nullable();
    }
    return yup.mixed().notRequired();
  }),
  subscriber_upload_file: yup.lazy((_, context) => {
    if (context.parent.target_group_type === TargetGroupTypeValue.UseList) {
      return yup.mixed().required('Vui lòng upload file');
    }
    return yup.mixed().notRequired();
  }),
  blacklistUploadSize: yup
    .number()
    .max(
      SIZE_LIMIT._30_MB,
      'Định dạng file chưa được hỗ trợ, vui lòng upload file có định dạng đuôi xlsx và có dung lượng < 30MB',
    )
    .nullable(),
  blacklistUploadType: yup
    .string()
    .matches(
      /^file$/,
      'Định dạng file chưa được hỗ trợ, vui lòng upload file có định dạng đuôi xlsx và có dung lượng < 30MB',
    )
    .nullable(),
  blacklist_upload_file: yup.mixed().nullable(),
});

const { values, setValues, handleSubmit } = useForm<any>({
  validationSchema,
  initialValues: {
    target_group_type: TargetGroupTypeValue.UseExisted,
  },
});

const setPreviewData = (value: any) => {
  campaignStore.setTargetGroupName(
    existedGroupUser.value.find((item: TDropdownItem) => item.value === value.target_group_id)
      ?.label ?? '',
  );

  campaignStore.setSubscriberUploadView(
    dataStep2.value.subscriber_upload_file?.file_view_name || campaignStore.subscriberUploadView,
  );
  campaignStore.setBlacklistUploadView(
    dataStep2.value.blacklist_upload_file?.file_view_name || campaignStore.blacklistUploadView,
  );

  campaignStore.setSubscriberUploadPath(dataStep2.value.subscriber_upload_file?.file_path);
  campaignStore.setBlacklistUploadPath(dataStep2.value.blacklist_upload_file?.file_path);
};

const onSubmitVee = handleSubmit(async (value: any) => {
  setPreviewData(value);
  setTimeout(async () => {
    const payload: any = {
      target_group: {
        target_group_type: value.target_group_type,
        target_group_id: nullGetter(value.target_group_id),
        target_group_name: stringGetter(campaignStore.targetGroupName),
        subscriber_upload_file: nullGetter(
          dataStep2.value.subscriber_upload_file?.file_name ??
            campaignStore.step2Data?.target_group?.subscriber_upload_file,
        ),
        error_subscriber_file_path: nullGetter(
          dataStep2.value.subscriber_upload_file?.error_file_path ??
            campaignStore.step2Data?.target_group?.error_subscriber_file_path,
        ),
        subscriber_file_view_name: nullGetter(
          campaignStore.subscriberUploadView ??
            campaignStore.step2Data?.target_group?.subscriber_file_view_name,
        ),
        blacklist_upload_file: nullGetter(
          dataStep2.value.blacklist_upload_file?.file_name ??
            campaignStore.step2Data?.target_group?.blacklist_upload_file,
        ),
        error_blacklist_file_path: nullGetter(
          dataStep2.value.blacklist_upload_file?.error_file_path ??
            campaignStore.step2Data?.target_group?.error_blacklist_file_path,
        ),
        blacklist_file_view_name: nullGetter(
          campaignStore.blacklistUploadView ??
            campaignStore.step2Data?.target_group?.blacklist_file_view_name,
        ),
      },
    };
    if (value.target_group_type === TargetGroupTypeValue.UseList) {
      payload.target_group.subscriber_file_info = {
        validation_status: nullGetter(
          getNotBlankString(
            subscriberUploadUploadRef.value?.dataFile?.validation_status,
            campaignStore.step2Data?.target_group?.subscriber_file_info?.validation_status,
          ),
        ),
        total_records: nullGetter(
          getNotBlankString(
            subscriberUploadUploadRef.value?.dataFile?.total_records,
            campaignStore.step2Data?.target_group?.subscriber_file_info?.total_records,
          ),
        ),
        valid_records: nullGetter(
          getNotBlankString(
            subscriberUploadUploadRef.value?.dataFile?.valid_records,
            campaignStore.step2Data?.target_group?.subscriber_file_info?.valid_records,
          ),
        ),
        invalid_records: nullGetter(
          getNotBlankString(
            subscriberUploadUploadRef.value?.dataFile?.invalid_records,
            campaignStore.step2Data?.target_group?.subscriber_file_info?.invalid_records,
          ),
        ),
        upload_date: nullGetter(
          getNotBlankString(
            subscriberUploadUploadRef.value?.dataFile?.upload_date,
            campaignStore.step2Data?.target_group?.subscriber_file_info?.upload_date,
          ),
        ),
        error_file_view_name: nullGetter(
          getNotBlankString(
            subscriberUploadUploadRef.value?.dataFile?.error_file_view_name,
            campaignStore.step2Data?.target_group?.subscriber_file_info?.error_file_view_name,
          ),
        ),
      };
    }
    if (
      dataStep2.value.blacklist_upload_file?.file_name ||
      campaignStore.step2Data?.target_group?.blacklist_upload_file
    ) {
      payload.target_group.blacklist_file_info = {
        validation_status: nullGetter(
          getNotBlankString(
            blacklistUploadRef.value?.dataFile?.validation_status,
            campaignStore.step2Data?.target_group?.blacklist_file_info?.validation_status,
          ),
        ),
        total_records: nullGetter(
          getNotBlankString(
            blacklistUploadRef.value?.dataFile?.total_records,
            campaignStore.step2Data?.target_group?.blacklist_file_info?.total_records,
          ),
        ),
        valid_records: nullGetter(
          getNotBlankString(
            blacklistUploadRef.value?.dataFile?.valid_records,
            campaignStore.step2Data?.target_group?.blacklist_file_info?.valid_records,
          ),
        ),
        invalid_records: nullGetter(
          getNotBlankString(
            blacklistUploadRef.value?.dataFile?.invalid_records,
            campaignStore.step2Data?.target_group?.blacklist_file_info?.invalid_records,
          ),
        ),
        upload_date: nullGetter(
          getNotBlankString(
            blacklistUploadRef.value?.dataFile?.upload_date,
            campaignStore.step2Data?.target_group?.blacklist_file_info?.upload_date,
          ),
        ),
        error_file_view_name: nullGetter(
          getNotBlankString(
            blacklistUploadRef.value?.dataFile?.error_file_view_name,
            campaignStore.step2Data?.target_group?.blacklist_file_info?.error_file_view_name,
          ),
        ),
      };
    }
    if (props.type !== FORM_TYPE.ADD) {
      payload.campaign_id = props.id;
    }
    campaignStore.setValueStep2(payload);
    const validate = await campaignStore.validateStepData(StepValue.StepTwo, payload);
    if (validate) {
      campaignStore.next(1);
    }
  }, 0);
});

const onDraft = () => {
  setPreviewData(values);
  const payload: any = {
    target_group: {
      target_group_type: values.target_group_type,
      target_group_id: nullGetter(values.target_group_id),
      target_group_name: stringGetter(campaignStore.targetGroupName),
      subscriber_upload_file: nullGetter(
        dataStep2.value.subscriber_upload_file?.file_name ??
          campaignStore.step2Data?.target_group?.subscriber_upload_file,
      ),
      error_subscriber_file_path: nullGetter(
        dataStep2.value.subscriber_upload_file?.error_file_path ??
          campaignStore.step2Data?.target_group?.error_subscriber_file_path,
      ),
      subscriber_file_view_name: nullGetter(
        campaignStore.subscriberUploadView ??
          campaignStore.step2Data?.target_group?.subscriber_file_view_name,
      ),
      blacklist_upload_file: nullGetter(
        dataStep2.value.blacklist_upload_file?.file_name ??
          campaignStore.step2Data?.target_group?.blacklist_upload_file,
      ),
      error_blacklist_file_path: nullGetter(
        dataStep2.value.blacklist_upload_file?.error_file_path ??
          campaignStore.step2Data?.target_group?.error_blacklist_file_path,
      ),
      blacklist_file_view_name: nullGetter(
        campaignStore.blacklistUploadView ??
          campaignStore.step2Data?.target_group?.blacklist_file_view_name,
      ),
    },
  };
  if (values.target_group_type === TargetGroupTypeValue.UseList) {
    payload.target_group.subscriber_file_info = {
      validation_status: getNotBlankString(
        subscriberUploadUploadRef.value?.dataFile?.validation_status,
        campaignStore.step2Data?.target_group?.subscriber_file_info?.validation_status,
      ),
      total_records: getNotBlankString(
        subscriberUploadUploadRef.value?.dataFile?.total_records,
        campaignStore.step2Data?.target_group?.subscriber_file_info?.total_records,
      ),
      valid_records: getNotBlankString(
        subscriberUploadUploadRef.value?.dataFile?.valid_records,
        campaignStore.step2Data?.target_group?.subscriber_file_info?.valid_records,
      ),
      invalid_records: getNotBlankString(
        subscriberUploadUploadRef.value?.dataFile?.invalid_records,
        campaignStore.step2Data?.target_group?.subscriber_file_info?.invalid_records,
      ),
      upload_date: getNotBlankString(
        subscriberUploadUploadRef.value?.dataFile?.upload_date,
        campaignStore.step2Data?.target_group?.subscriber_file_info?.upload_date,
      ),
      error_file_view_name: getNotBlankString(
        subscriberUploadUploadRef.value?.dataFile?.error_file_view_name,
        campaignStore.step2Data?.target_group?.subscriber_file_info?.error_file_view_name,
      ),
    };
  }
  if (
    dataStep2.value.blacklist_upload_file?.file_name ||
    campaignStore.step2Data?.target_group?.blacklist_upload_file
  ) {
    payload.target_group.blacklist_file_info = {
      validation_status: getNotBlankString(
        blacklistUploadRef.value?.dataFile?.validation_status,
        campaignStore.step2Data?.target_group?.blacklist_file_info?.validation_status,
      ),
      total_records: getNotBlankString(
        blacklistUploadRef.value?.dataFile?.total_records,
        campaignStore.step2Data?.target_group?.blacklist_file_info?.total_records,
      ),
      valid_records: getNotBlankString(
        blacklistUploadRef.value?.dataFile?.valid_records,
        campaignStore.step2Data?.target_group?.blacklist_file_info?.valid_records,
      ),
      invalid_records: getNotBlankString(
        blacklistUploadRef.value?.dataFile?.invalid_records,
        campaignStore.step2Data?.target_group?.blacklist_file_info?.invalid_records,
      ),
      upload_date: getNotBlankString(
        blacklistUploadRef.value?.dataFile?.upload_date,
        campaignStore.step2Data?.target_group?.blacklist_file_info?.upload_date,
      ),
      error_file_view_name: getNotBlankString(
        blacklistUploadRef.value?.dataFile?.error_file_view_name,
        campaignStore.step2Data?.target_group?.blacklist_file_info?.error_file_view_name,
      ),
    };
  }

  return payload;
};

const onSubmit = () => {
  onSubmitVee();
};
//#endregion

watch(
  () => values.target_group_type,
  async (newValue: TargetGroupTypeValue) => {
    if (newValue === TargetGroupTypeValue.UseExisted) {
      await getExistedGroup();
    }
  },
  {
    immediate: true,
  },
);

watch(
  () => campaignStore.active,
  (newValue: StepValue) => {
    if (newValue === StepValue.StepTwo && props.type !== FORM_TYPE.ADD) {
      setValue();
    }
  },
  {
    immediate: true,
  },
);

defineExpose({
  onSubmit,
  onDraft,
});
</script>

<template>
  <el-form label-position="top" class="mx-[20%] pt-8">
    <VElementDropdown
      name="target_group_type"
      :required="true"
      :filterable="false"
      :label="'Nhóm đối tượng gửi tin'"
      :option="targetGroupTypes"
      :style="'w-[100%]'"
      :placeholder="PLACEHOLDER.SELECT"
    />
    <VElementDropdown
      v-if="values.target_group_type === TargetGroupTypeValue.UseExisted"
      class="mt-[15px]"
      name="target_group_id"
      :required="true"
      :label="'Nhóm đối tượng có sẵn'"
      :option="existedGroupUser"
      :style="'w-[100%]'"
      :placeholder="PLACEHOLDER.SELECT"
    >
      <template #footer>
        <div class="px-2 text-main underline cursor-pointer" @click="navigateToAddGroup">
          Thêm nhóm đối tượng
        </div>
      </template>
    </VElementDropdown>
    <div
      v-show="values.target_group_type === TargetGroupTypeValue.UseList"
      class="mt-[15px] gap-[15px]"
    >
      <div class="flex items-center text-[14px] text-[#6b7280] font-semibold gap-x-[4px]">
        Danh sách đối tượng
        <span class="text-[#f35b5b]">*</span>
      </div>
      <div class="flex gap-[15px] mt-[8px]">
        <VDownLoadFile
          url=""
          label="Tải file mẫu"
          :use-hook-click="true"
          @onClick="handleDownloadSubscriberList(TemplateType.TargetList)"
        />
        <VUploadFileTarget
          ref="subscriberUploadUploadRef"
          fileSize="subscriberUploadSize"
          fileType="subscriberUploadType"
          file="subscriber_upload_file"
          :formType="type"
          :sizeToValidate="SIZE_LIMIT._25_MB"
          :fileTypes="[
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          ]"
        />
        <Icon
          v-show="subscriberUploadUploadRef?.isUpload"
          icon="tabler:eye"
          class="text-[24px] text-primaryText mt-[5px] cursor-pointer"
          @click="handleShowTargetResult"
        />
      </div>
    </div>
    <div class="mt-[15px] gap-[15px]">
      <div class="flex items-center text-[14px] text-[#6b7280] font-semibold">
        <p>Blacklist</p>
        <div class="cursor-pointer">
          <el-tooltip placement="top">
            <template #content>
              <p class="max-w-[320px]">
                Blacklist là danh sách các thuê bao không được gửi tin dù đủ điều kiện
              </p>
            </template>
            <Icon icon="mingcute:information-fill" class="ml-[6px] text-[#6b7280]" />
          </el-tooltip>
        </div>
      </div>
      <div class="flex gap-[15px] mt-[8px]">
        <VDownLoadFile
          url=""
          label="Tải file mẫu"
          :use-hook-click="true"
          @onClick="handleDownloadSubscriberList(TemplateType.Blacklist)"
        />
        <VUploadFileBlacklist
          ref="blacklistUploadRef"
          fileSize="blacklistUploadSize"
          fileType="blacklistUploadType"
          file="blacklist_upload_file"
          :formType="type"
          :sizeToValidate="SIZE_LIMIT._30_MB"
          :fileTypes="[
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          ]"
          @on-remove-file="handleRemoveBlacklist"
        />
        <Icon
          v-show="blacklistUploadRef?.isUpload"
          icon="tabler:eye"
          class="text-[24px] text-primaryText mt-[5px] cursor-pointer"
          @click="handleShowBlacklistResult"
        />
      </div>
    </div>
    <VDialog
      v-model:visible="isVisibleFileTargetResultPopup"
      modal
      ref="refDialog"
      header="Kết quả xử lý file"
      :draggable="false"
      :pt="popupFileResultPt"
      :class="'h-240px'"
      :style="popupFileResultStyle"
    >
      <PopupFileResult
        :type="FileType.TargetGroup"
        :uploadFileTarget="subscriberUploadUploadRef"
        @onClose="isVisibleFileTargetResultPopup = false"
      />
    </VDialog>
    <VDialog
      v-model:visible="isVisibleFileBlacklistResultPopup"
      modal
      ref="refDialog"
      header="Kết quả xử lý file"
      :draggable="false"
      :pt="popupFileResultPt"
      :class="'h-240px'"
      :style="popupFileResultStyle"
    >
      <PopupFileResult
        :type="FileType.Blacklist"
        :uploadFileTarget="blacklistUploadRef"
        @onClose="isVisibleFileBlacklistResultPopup = false"
      />
    </VDialog>
  </el-form>
</template>

<style lang="css" scoped>
.el-form-item.is-error {
  margin-bottom: 20px;
}
</style>
