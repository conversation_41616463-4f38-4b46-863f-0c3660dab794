<script setup lang="ts">
import { FORM_TYPE, TEXT } from '@/shared';
import { useCampaign } from '@/store/useCampaign';
import MessageConfig from '../components/MessageConfig.vue';
import { inject, ref, watch, watchEffect } from 'vue';
import {
  ChannelValue,
  CUSTOM_FIELD_TYPE,
  StepValue,
  TargetGroupTypeValue,
} from '../index.constants';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useApi } from '@/store/useApi';
import type { TParameter, TTemplateParams } from '../index.type';
import { cloneDeep } from 'lodash';

const props = defineProps<{
  type: FORM_TYPE;
  id?: number;
}>();

const mainConfigRef = ref();
const subConfigRef = ref();
const campaignStore = useCampaign();
const parameters = ref<TParameter[]>([]);
const isSetValue = ref(false);

const setValue = () => {
  if (!isSetValue.value) {
    isSetValue.value = true;
    if (campaignStore.step3Data) {
      mainConfigRef.value?.setValue();
      if (campaignStore.step1Data?.use_failover) {
        subConfigRef.value?.setValue();
      }
    }
  }
};

const isSubmitMainConfig = ref(false);
const isSubmitFailoverConfig = ref(false);

const handleSubmitConfig = async (data: any) => {
  if (campaignStore.step1Data?.channel_type === ChannelValue.ZNS) {
    campaignStore.setValueStep3({
      content: {
        ...campaignStore.step3Data?.content,
        channel_type: campaignStore.step1Data?.channel_type,
        template_id: data.template_id ?? campaignStore.step3Data?.content?.template_id,
        oa: campaignStore.oa,
        label_id: data.label_id ?? campaignStore.step3Data?.content?.label_id,
        template_params: (
          data.parameters || campaignStore.step3Data?.content?.template_params
        )?.map((item: TTemplateParams) => {
          return {
            param_name: item.param_name,
            param_value:
              item.param_type === CUSTOM_FIELD_TYPE ? item.param_custom : item.param_value,
            param_type: item.param_type,
          };
        }),
      },
    });
  } else if (campaignStore.step1Data?.channel_type === ChannelValue.SMS) {
    campaignStore.setValueStep3({
      content: {
        ...campaignStore.step3Data?.content,
        channel_type: campaignStore.step1Data?.channel_type,
        template_id: data.template_id ?? campaignStore.step3Data?.content?.template_id,
        brand_name: campaignStore.brandname,
        label_id: data.brandname_id ?? campaignStore.step3Data?.content?.label_id,
        template_params: (
          data.parameters || campaignStore.step3Data?.content?.template_params
        )?.map((item: TTemplateParams) => {
          return {
            param_name: item.param_name,
            param_value:
              item.param_type === CUSTOM_FIELD_TYPE ? item.param_custom : item.param_value,
            param_type: item.param_type,
          };
        }),
      },
    });
  }

  isSubmitMainConfig.value = true;
};

const handleSubmitConfigFailover = async (data: any) => {
  if (campaignStore.step1Data?.channel_type_failover === ChannelValue.ZNS) {
    campaignStore.setValueStep3({
      content: {
        ...campaignStore.step3Data?.content,
        channel_type_failover: campaignStore.step1Data?.channel_type_failover,
        template_id_failover:
          data.template_id ?? campaignStore.step3Data?.content?.template_id_failover,
        oa: campaignStore.oa,
        label_id_failover: data.label_id ?? campaignStore.step3Data?.content?.label_id_failover,
        template_params_failover: (
          data.parameters || campaignStore.step3Data?.content?.template_params_failover
        )?.map((item: TTemplateParams) => {
          return {
            param_name: item.param_name,
            param_value:
              item.param_type === CUSTOM_FIELD_TYPE ? item.param_custom : item.param_value,
            param_type: item.param_type,
          };
        }),
      },
    });
  } else if (campaignStore.step1Data?.channel_type_failover === ChannelValue.SMS) {
    campaignStore.setValueStep3({
      content: {
        ...campaignStore.step3Data?.content,
        channel_type_failover: campaignStore.step1Data?.channel_type_failover,
        template_id_failover:
          data.template_id ?? campaignStore.step3Data?.content?.template_id_failover,
        brand_name: campaignStore.brandname,
        label_id_failover: data.brandname_id ?? campaignStore.step3Data?.content?.label_id_failover,
        template_params_failover: (
          data.parameters || campaignStore.step3Data?.content?.template_params_failover
        )?.map((item: TTemplateParams) => {
          return {
            param_name: item.param_name,
            param_value:
              item.param_type === CUSTOM_FIELD_TYPE ? item.param_custom : item.param_value,
            param_type: item.param_type,
          };
        }),
      },
    });
  }

  isSubmitFailoverConfig.value = true;
};

const getMainPayload = (data: any): any => {
  if (campaignStore.step1Data?.channel_type === ChannelValue.ZNS) {
    return {
      content: {
        channel_type: campaignStore.step1Data?.channel_type,
        template_id: data.template_id ?? campaignStore.step3Data?.content?.template_id,
        oa: campaignStore.oa,
        label_id: data.label_id ?? campaignStore.step3Data?.content?.label_id,
        template_params: (
          data.parameters || campaignStore.step3Data?.content?.template_params
        )?.map((item: TTemplateParams) => {
          return {
            param_name: item.param_name,
            param_value:
              item.param_type === CUSTOM_FIELD_TYPE ? item.param_custom : item.param_value,
            param_type: item.param_type,
          };
        }),
      },
    };
  } else if (campaignStore.step1Data?.channel_type === ChannelValue.SMS) {
    return {
      content: {
        channel_type: campaignStore.step1Data?.channel_type,
        template_id: data.template_id ?? campaignStore.step3Data?.content?.template_id,
        brand_name: campaignStore.brandname,
        label_id: data.brandname_id ?? campaignStore.step3Data?.content?.label_id,
        template_params: (
          data.parameters || campaignStore.step3Data?.content?.template_params
        )?.map((item: TTemplateParams) => {
          return {
            param_name: item.param_name,
            param_value:
              item.param_type === CUSTOM_FIELD_TYPE ? item.param_custom : item.param_value,
            param_type: item.param_type,
          };
        }),
      },
    };
  }
};

const getFailoverPayload = (mainData: any, data: any): any => {
  if (campaignStore.step1Data?.channel_type_failover === ChannelValue.ZNS) {
    return {
      content: {
        ...mainData?.content,
        channel_type_failover: campaignStore.step1Data?.channel_type_failover,
        template_id_failover:
          data.template_id ?? campaignStore.step3Data?.content?.template_id_failover,
        oa: campaignStore.oa,
        label_id_failover: data.label_id ?? campaignStore.step3Data?.content?.label_id_failover,
        template_params_failover: (
          data.parameters || campaignStore.step3Data?.content?.template_params_failover
        )?.map((item: TTemplateParams) => {
          return {
            param_name: item.param_name,
            param_value:
              item.param_type === CUSTOM_FIELD_TYPE ? item.param_custom : item.param_value,
            param_type: item.param_type,
          };
        }),
      },
    };
  } else if (campaignStore.step1Data?.channel_type_failover === ChannelValue.SMS) {
    return {
      content: {
        ...mainData?.content,
        channel_type_failover: campaignStore.step1Data?.channel_type_failover,
        template_id_failover:
          data.template_id ?? campaignStore.step3Data?.content?.template_id_failover,
        brand_name: campaignStore.brandname,
        label_id_failover: data.brandname_id ?? campaignStore.step3Data?.content?.label_id_failover,
        template_params_failover: (
          data.parameters || campaignStore.step3Data?.content?.template_params_failover
        )?.map((item: TTemplateParams) => {
          return {
            param_name: item.param_name,
            param_value:
              item.param_type === CUSTOM_FIELD_TYPE ? item.param_custom : item.param_value,
            param_type: item.param_type,
          };
        }),
      },
    };
  }
};

const onDraft = async () => {
  const mainData = getMainPayload(await mainConfigRef.value.onDraft());
  const failoverData = getFailoverPayload(mainData, await subConfigRef.value?.onDraft());
  return {
    ...mainData,
    ...failoverData,
  };
};

const onSubmit = () => {
  mainConfigRef.value.onSubmit();
  subConfigRef.value?.onSubmit();
};
//#endregion

//#region Parameters
const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();

const getParameters = async () => {
  try {
    if (!campaignStore.step2Data?.target_group?.target_group_type) return;
    overlayLoading.toggleLoading(true);

    let url = `/campaign/v1/api/campaign/parameter-fields?target_group_type=${campaignStore.step2Data.target_group.target_group_type}`;
    if (
      campaignStore.step2Data.target_group.target_group_type === TargetGroupTypeValue.UseExisted
    ) {
      url += `&target_group_id=${campaignStore.step2Data.target_group.target_group_id}`;
    }
    if (campaignStore.step2Data.target_group.target_group_type === TargetGroupTypeValue.UseList) {
      url += `&subscriber_upload_file=${campaignStore.subscriberUploadPath ?? campaignStore.step2Data?.target_group?.error_subscriber_file_path}`;
    }

    const res = await api.get(url);
    if (res.data.code === 0) {
      parameters.value = res.data.data;
      campaignStore.setParameterOptions(res.data.data);
      overlayLoading.toggleLoading(false);
    } else {
      overlayLoading.toggleLoading(false);
      toast('error', res.data.message);
    }
  } catch (error) {
    console.error(error);
    overlayLoading.toggleLoading(false);
    toast('error', TEXT.ERROR_OCCURRED);
  }
};

watch(
  () => campaignStore.active,
  async (newValue: StepValue) => {
    if (newValue === StepValue.StepThree) {
      isSubmitMainConfig.value = false;
      isSubmitFailoverConfig.value = false;
      getParameters();
      if (props.type !== FORM_TYPE.ADD) {
        if (campaignStore.step3Data?.content?.template_id) {
          mainConfigRef.value?.handleSelectTemplate(
            campaignStore.step3Data?.content?.template_id,
            false,
            false,
          );
        }
        if (
          campaignStore.step1Data?.use_failover &&
          campaignStore.step3Data?.content?.template_id_failover
        ) {
          subConfigRef.value?.handleSelectTemplate(
            campaignStore.step3Data?.content?.template_id_failover,
            false,
            true,
          );
        }
        setTimeout(() => {
          setValue();
        }, 10);
      }
    }
  },
  {
    immediate: true,
  },
);

watchEffect(async () => {
  if (
    (campaignStore.step1Data?.use_failover &&
      isSubmitMainConfig.value &&
      isSubmitFailoverConfig.value) ||
    (!campaignStore.step1Data?.use_failover && isSubmitMainConfig.value)
  ) {
    const payload = cloneDeep(campaignStore.step3Data ?? {});
    if (props.type !== FORM_TYPE.ADD && props.id) {
      payload.campaign_id = props.id;
    }
    if (campaignStore.active === StepValue.StepThree) {
      const validate = await campaignStore.validateStepData(StepValue.StepThree, payload);
      if (validate) {
        campaignStore.next(1);
      }
    }
  }
});
//#endregion

defineExpose({
  onSubmit,
  setValue,
  onDraft,
});
</script>

<template>
  <div class="h-[calc(100vh-80px-30px-53px-115px-52px)] overflow-y-auto mt-8">
    <MessageConfig
      ref="mainConfigRef"
      :type="type"
      :is_failover="false"
      :parameters="parameters"
      :channel_type="campaignStore.step1Data?.channel_type"
      @onSubmitForm="handleSubmitConfig"
    />
    <MessageConfig
      ref="subConfigRef"
      v-if="campaignStore.step1Data?.use_failover"
      :type="type"
      :parameters="parameters"
      :is_failover="true"
      :channel_type="campaignStore.step1Data?.channel_type_failover"
      @onSubmitFormFailover="handleSubmitConfigFailover"
    />
  </div>
</template>
