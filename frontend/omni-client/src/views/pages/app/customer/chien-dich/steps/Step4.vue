<script setup lang="ts">
import { useCampaign } from '@/store/useCampaign';
import { computed, inject, ref, watch } from 'vue';
import moment from 'moment';
import {
  convertUrlName,
  getCampaignColorStatus,
  getCampaignLabelStatus,
  getCampaignTypeLabel,
  getCampaignTypeScheduleLabel,
} from '../index.utils';
import {
  CampaignStatusValue,
  ChannelLabel,
  ChannelValue,
  FailoverLabel,
  FileType,
  ScheduleTypeText,
  StepValue,
  TargetGroupTypeLabel,
  TargetGroupTypeValue,
} from '../index.constants';
import { FORM_TYPE, type TDropdownItem } from '@/shared';
import type { TTimeRange } from '../index.type';
import MessageConfig from '../components/MessageConfig.vue';
import { FORMAT, TEXT } from '@/shared/text.shared';
import { useApi } from '@/store/useApi';

const props = defineProps<{
  type: FORM_TYPE;
  id?: number;
}>();

const campaignStore = useCampaign();
const mainConfigRef = ref();
const subConfigRef = ref();

//#region Expand
const isExpandBlock1 = ref(true);
const isExpandBlock2 = ref(false);
const isExpandBlock3 = ref(false);
//#endregion

//#region Form
const campaignName = computed(() => {
  return campaignStore.step1Data?.name;
});
const campaignStatus = computed(() => campaignStore.status);
const campaignType = computed(() => getCampaignTypeLabel(campaignStore.campaign_type));
const campaignScheduleType = computed(() => {
  return getCampaignTypeScheduleLabel(campaignStore.step1Data?.schedule?.type);
});
const channelMessage = computed(() =>
  campaignStore.step1Data?.channel_type === ChannelValue.SMS ? ChannelLabel.SMS : ChannelLabel.ZNS,
);
const channelMessageFailover = computed(() => {
  if (campaignStore.step1Data?.channel_type_failover === ChannelValue.SMS) return ChannelLabel.SMS;
  else if (campaignStore.step1Data?.channel_type_failover === ChannelValue.ZNS)
    return ChannelLabel.ZNS;
  return '';
});
const labelChannel = computed(() =>
  campaignStore.step1Data?.schedule?.type === ScheduleTypeText.SendOnce ||
  campaignStore.step1Data?.schedule?.type === ScheduleTypeText.SendNow
    ? 'Thời gian gửi tin'
    : 'Hiệu lực chiến dịch',
);
const getSendTime = () => {
  return typeof campaignStore.step1Data?.schedule?.config?.send_time === 'string'
    ? moment(campaignStore.step1Data?.schedule?.config?.send_time).format(FORMAT.DATE_TIME)
    : moment(campaignStore.step1Data?.schedule?.config?.send_time * 1000).format(FORMAT.DATE_TIME);
};
const getSendNowDate = () => {
  if (props.type === FORM_TYPE.DETAILS)
    return moment(campaignStore.finalizedAt).format(FORMAT.DATE_TIME);
  return '';
};
const getStartTime = () => {
  return typeof campaignStore.step1Data?.schedule?.start_date === 'string'
    ? moment(campaignStore.step1Data?.schedule?.start_date).format(FORMAT.DATE)
    : moment(campaignStore.step1Data?.schedule?.start_date * 1000).format(FORMAT.DATE);
};
const getEndTime = () => {
  return typeof campaignStore.step1Data?.schedule?.end_date === 'string'
    ? moment(campaignStore.step1Data?.schedule?.end_date).format(FORMAT.DATE)
    : moment(campaignStore.step1Data?.schedule?.end_date * 1000).format(FORMAT.DATE);
};
const timeSchedule = computed(() => {
  if (campaignStore.step1Data?.schedule?.type === ScheduleTypeText.SendNow) {
    return getSendNowDate();
  }
  if (campaignStore.step1Data?.schedule?.type === ScheduleTypeText.SendOnce) {
    return getSendTime();
  }
  return `Từ ${getStartTime()} đến ${getEndTime()}`;
});
const sendTime = computed(() => {
  let result = '';
  campaignStore.step1Data?.sending_time_ranges?.forEach((item: TTimeRange) => {
    if (item.start_time && item.end_time)
      result += `Từ ${item.start_time} đến ${item.end_time}<br>`;
  });
  return result;
});
const useFailover = computed(() =>
  campaignStore.step1Data?.use_failover ? FailoverLabel.Yes : FailoverLabel.No,
);

const detailsData = ref<TDropdownItem[]>([]);

const api = useApi();
const toast = inject('toast') as any;

const handleDownloadFile = async (fileType: FileType) => {
  try {
    if (fileType === FileType.TargetGroup && !campaignStore.subscriberUploadView) return;
    if (fileType === FileType.Blacklist && !campaignStore.blacklistUploadView) return;
    let urlDownload = `/campaign/v1/api/common/file/view/${fileType}/${campaignStore.subscriberUploadView}`;
    if (fileType === FileType.Blacklist) {
      urlDownload = `/campaign/v1/api/common/file/view/${fileType}/${campaignStore.blacklistUploadView}`;
    }
    const response = await api.get(urlDownload, {
      responseType: 'blob',
      skipToastErr: true,
    });
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const a = document.createElement('a');
    a.href = url;
    a.download =
      fileType === FileType.TargetGroup
        ? campaignStore.step2Data?.target_group?.subscriber_upload_file
        : campaignStore.step2Data?.target_group?.blacklist_upload_file;
    document.body.appendChild(a);
    a.click();
    a.remove();
    window.URL.revokeObjectURL(url);
  } catch {
    toast('error', TEXT.ERROR_OCCURRED);
  }
};
//#endregion

watch(
  () => campaignStore.active,
  (newValue: StepValue) => {
    if (newValue === StepValue.StepFour) {
      mainConfigRef.value.setValuePreview();
      subConfigRef.value?.setValuePreview();
    }
  },
  {
    immediate: true,
  },
);

watch(
  () => campaignStore.step1Data,
  () => {
    if (props.type === FORM_TYPE.DETAILS) {
      detailsData.value = [
        { label: 'Tên chiến dịch', value: campaignName.value },
        { label: 'Trạng thái', value: campaignStatus.value },
        { label: 'Loại chiến dịch', value: campaignType.value },
        {
          label: 'Loại chiến dịch đặt lịch',
          value: campaignScheduleType.value,
        },
        {
          label: labelChannel.value,
          value: timeSchedule.value,
        },
        {
          label: 'Kênh gửi tin',
          value: channelMessage.value,
        },
        { label: 'Khung giờ gửi tin', value: sendTime.value },
        {
          label: 'Sử dụng tính năng Failover',
          value: useFailover.value,
        },
        { label: 'Kênh gửi tin Failover', value: channelMessageFailover.value },
      ];
    } else {
      detailsData.value = [
        { label: 'Tên chiến dịch', value: campaignName.value },
        { label: 'Loại chiến dịch', value: campaignType.value },
        {
          label: 'Loại chiến dịch đặt lịch',
          value: campaignScheduleType.value,
        },
        {
          label: labelChannel.value,
          value: timeSchedule.value,
        },
        {
          label: 'Kênh gửi tin',
          value: channelMessage.value,
        },
        { label: 'Khung giờ gửi tin', value: sendTime.value },
        {
          label: 'Sử dụng tính năng Failover',
          value: useFailover.value,
        },
        { label: 'Kênh gửi tin Failover', value: channelMessageFailover.value },
      ];
    }
  },
  {
    immediate: true,
  },
);

watch(
  () => campaignStore.step3Data,
  () => {
    if (props.type === FORM_TYPE.DETAILS) {
      if (campaignStore.step3Data?.content?.template_id) {
        mainConfigRef.value?.handleSelectTemplate(
          campaignStore.step3Data?.content?.template_id,
          false,
          false,
        );
      }
      if (
        campaignStore.step1Data?.use_failover &&
        campaignStore.step3Data?.content?.template_id_failover
      ) {
        subConfigRef.value?.handleSelectTemplate(
          campaignStore.step3Data?.content?.template_id_failover,
          false,
          true,
        );
      }
      setTimeout(() => {
        mainConfigRef.value?.setValuePreview();
        subConfigRef.value?.setValuePreview();
      }, 10);
    }
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <div
    class="h-[calc(100vh-80px-40px-52px-115px-42px)] overflow-y-auto"
    :class="type === FORM_TYPE.DETAILS ? 'mt-[38px]' : 'mt-8'"
  >
    <div class="mx-[10%] px-[3%]">
      <div class="flex items-center justify-between">
        <div
          class="text-main font-semibold text-[14px] leading-[34px] h-[34px] px-[15px] border-[1px] border-main rounded-[30px] w-fit"
        >
          Thông tin chung
        </div>
        <div
          class="cursor-pointer hover:bg-[#e9e9e9] rounded-full"
          @click="isExpandBlock1 = !isExpandBlock1"
        >
          <Icon
            :icon="isExpandBlock1 ? 'iconamoon:arrow-up-2-light' : 'iconamoon:arrow-down-2-light'"
            class="text-[30px] text-[#A3A3A3]"
          />
        </div>
      </div>
      <div
        v-show="isExpandBlock1"
        class="gap-y-[25px] mt-[25px] grid grid-cols-2 px-[10px] gap-x-[10px]"
      >
        <div v-for="(item, idx) in detailsData" :key="idx">
          <div
            v-if="
              item.label !== 'Kênh gửi tin Failover' ||
              (item.label === 'Kênh gửi tin Failover' &&
                item.value &&
                campaignStore.step1Data?.use_failover)
            "
            class="text-[12px]"
          >
            {{ item.label }}
          </div>
          <div v-if="item.label === 'Trạng thái'">
            <Tag
              :class="getCampaignColorStatus(item.value as CampaignStatusValue)"
              :value="getCampaignLabelStatus(item.value as CampaignStatusValue)"
            />
          </div>
          <div
            v-else-if="
              item.label === 'Kênh gửi tin Failover' &&
              item.value &&
              campaignStore.step1Data?.use_failover
            "
          >
            <div class="font-semibold text-[14px] mt-[5px]" v-html="item.value"></div>
          </div>
          <div v-else-if="item.label !== 'Kênh gửi tin Failover'">
            <div
              v-if="item.value"
              class="font-semibold text-[14px] mt-[5px]"
              v-html="item.value"
            ></div>
            <p v-else>...</p>
          </div>
        </div>
      </div>
    </div>
    <div class="mx-[10%] px-[3%] mt-[38px] border-y-[1px] py-[20px] bg-[#FBFAFF] border-[#DFE4EA]">
      <div class="flex items-center justify-between">
        <div
          class="text-main font-semibold bg-[#fff] text-[14px] leading-[34px] h-[34px] px-[15px] border-[1px] border-main rounded-[30px] w-fit"
        >
          Nhóm đối tượng
        </div>
        <div
          class="cursor-pointer hover:bg-[#e9e9e9] rounded-full"
          @click="isExpandBlock2 = !isExpandBlock2"
        >
          <Icon
            :icon="isExpandBlock2 ? 'iconamoon:arrow-up-2-light' : 'iconamoon:arrow-down-2-light'"
            class="text-[30px] text-[#A3A3A3]"
          />
        </div>
      </div>
      <div
        v-show="isExpandBlock2"
        class="space-y-[25px] mt-[25px] px-[10px] gap-x-[10px] max-h-[252px] overflow-y-auto"
      >
        <div>
          <div class="text-[12px]">Nhóm đối tượng gửi tin</div>
          <div class="font-semibold text-[14px] mt-[5px]">
            {{
              campaignStore.step2Data?.target_group?.target_group_type ===
              TargetGroupTypeValue.UseExisted
                ? TargetGroupTypeLabel.UseExisted
                : TargetGroupTypeLabel.UseList
            }}
          </div>
        </div>
        <div
          v-if="
            campaignStore.step2Data?.target_group?.target_group_type ===
            TargetGroupTypeValue.UseExisted
          "
        >
          <div class="text-[12px]">Nhóm đối tượng có sẵn</div>
          <div class="font-semibold text-[14px] mt-[5px]">
            {{ campaignStore.targetGroupName }}
          </div>
        </div>
        <div v-else>
          <div class="text-[12px]">Danh sách đối tượng</div>
          <p
            class="font-semibold text-[14px] mt-[5px]"
            :class="campaignStore.subscriberUploadView ? 'cursor-pointer' : ''"
            @click="handleDownloadFile(FileType.TargetGroup)"
          >
            {{
              convertUrlName(campaignStore.step2Data?.target_group?.subscriber_upload_file) || '...'
            }}
          </p>
        </div>
        <div>
          <div class="text-[12px]">Blacklist</div>
          <p
            class="font-semibold text-[14px] mt-[5px]"
            :class="campaignStore.blacklistUploadView ? 'cursor-pointer' : ''"
            @click="handleDownloadFile(FileType.Blacklist)"
          >
            {{
              convertUrlName(campaignStore.step2Data?.target_group?.blacklist_upload_file) || '...'
            }}
          </p>
        </div>
      </div>
    </div>
    <div class="mx-[10%] px-[3%] mt-[38px] mb-[100px]">
      <div class="flex items-center justify-between">
        <div
          class="text-main font-semibold text-[14px] leading-[34px] h-[34px] px-[15px] border-[1px] border-main rounded-[30px] w-fit"
        >
          Nội dung gửi tin
        </div>
        <div
          class="cursor-pointer hover:bg-[#e9e9e9] rounded-full"
          @click="isExpandBlock3 = !isExpandBlock3"
        >
          <Icon
            :icon="isExpandBlock3 ? 'iconamoon:arrow-up-2-light' : 'iconamoon:arrow-down-2-light'"
            class="text-[30px] text-[#A3A3A3]"
          />
        </div>
      </div>
      <div v-show="isExpandBlock3">
        <MessageConfig
          ref="mainConfigRef"
          :type="type"
          :is_failover="false"
          :channel_type="campaignStore.step1Data?.channel_type"
          :parameters="campaignStore.parameterOptions"
          :isPreview="true"
          :customClass="'pl-[0px] pr-[0px] !pb-[0px] ml-[0px]'"
        />
        <MessageConfig
          v-show="campaignStore.step1Data?.use_failover"
          ref="subConfigRef"
          :type="type"
          :is_failover="true"
          :channel_type="campaignStore.step1Data?.channel_type_failover"
          :parameters="campaignStore.parameterOptions"
          :isPreview="true"
          :customClass="'pl-[0px] pr-[0px] ml-[0px] bg-transparent border-none'"
        />
      </div>
    </div>
  </div>
</template>
