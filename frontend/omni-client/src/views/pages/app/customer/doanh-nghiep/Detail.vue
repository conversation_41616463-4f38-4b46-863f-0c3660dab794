<template>
  <!-- Header -->
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:building-skyscraper" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          :to="{
            name: ROUTE_NAME.DETAILS_ENTERPRISE,
            params: { id: user.userId },
          }"
          >Thông tin doanh nghiệp</el-breadcrumb-item
        >
        <el-breadcrumb-item>{{
          type === PageType.Details ? 'Xem chi tiết' : 'Cập nhật'
        }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>

  <!-- Tabs -->
  <BaseTabs :tabs :active-tab="activeTab" tabClass="w-[94%]" @click-tab="handleTabChanged">
    <template #tab-1>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <GeneralInfoTab :type="type" :values="values" />
      </div>
    </template>
    <template #tab-2>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <FormDetailsEnterprise ref="priceBoardRef" :type="PageType.Details" />
      </div>
    </template>
    <template #tab-3>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <ContractTab ref="contractTabRef" />
      </div>
    </template>
  </BaseTabs>

  <!-- Footer -->
  <div
    class="w-[100%] flex justify-end px-[15px] py-[9px] border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] absolute bottom-0 rounded-b-[14px]"
  >
    <VElementButton
      v-if="activeTab === TabValue.Tab2"
      icon="history"
      label="Xem lịch sử chỉnh sửa"
      :bgColor="color.secondary"
      @click="clickViewAuditLog"
    />
    <VElementButton
      v-if="type === PageType.Update"
      styleButton="s"
      label="Huỷ"
      id="enterprise-cancel-button"
      :bgColor="color.closeButton"
      @click="isShowConfirmPopup = true"
    />
    <VElementButton
      v-if="type === PageType.Update"
      label="Lưu"
      styleButton="s"
      id="enterprise-save-button"
      :bgColor="color.main"
      @click="onSubmit"
    />
    <VElementButton
      v-if="type === PageType.Details && activeTab === TabValue.Tab1"
      label="Cập nhật"
      :disabled="!user.id_business"
      :bgColor="user.id_business ? color.main : color.info"
      styleButton="s"
      id="enterprise-update-button"
      @click="openUpdate"
    />

    <PopupAudit
      :serviceType="priceBoardRef?.activeTab === TabValue.Tab1 ? ServiceType.SMS : ServiceType.ZNS"
      :visible="isVisibleHistoriesPopup"
      @onClose="isVisibleHistoriesPopup = false"
    />
  </div>

  <PopupCancelConfirm v-model:popupVisible="isShowConfirmPopup" @onConfirm="handleCancelConfirm" />
</template>

<script setup lang="ts">
import { onMounted, ref, watch, inject, nextTick } from 'vue';
import * as yup from 'yup';
import { useForm } from 'vee-validate';
import { useRoute, useRouter } from 'vue-router';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import PopupCancelConfirm from '@/components/base/common/PopupCancelConfirm.vue';
import { REGEX, ROUTE_NAME, STATUS_VALUE, TabValue, TEXT } from '@/shared';
import { getNullableString } from '@/utils';
import { useUserSession, type UserData } from '@/store/userSession';
import { PageType } from '@/enums/common';
import GeneralInfoTab from './components/GeneralInfoTab.vue';
import ContractTab from './components/ContractTab.vue';
import { Icon } from '@iconify/vue';
import BaseTabs from '@/components/base/new/BaseTabs.vue';
import type { TTab } from '../bang-gia/index.type';
import FormDetailsEnterprise from '../bang-gia/bang-gia-ban/FormDetailsEnterprise.vue';
import PopupAudit from '../bang-gia/components/PopupAudit.vue';
import { ServiceType } from '../bang-gia/index.constants';

const props = defineProps<{
  id?: number;
  type: PageType;
}>();

const tabs: TTab[] = [
  {
    label: 'Thông tin chung',
    value: TabValue.Tab1,
  },
  {
    label: 'Bảng giá',
    value: TabValue.Tab2,
  },
  {
    label: 'Hợp đồng',
    value: TabValue.Tab3,
  },
];

const api = useApi();
const route = useRoute();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const user = useUserSession().user as UserData;
const router = useRouter();

const activeTab = ref<TabValue>(TabValue.Tab1);
const priceBoardRef = ref();
const contractTabRef = ref();
const isShowConfirmPopup = ref(false);

//#region Audit logs
const isVisibleHistoriesPopup = ref(false);

const clickViewAuditLog = async () => {
  isVisibleHistoriesPopup.value = true;
};
//#endregion

const handleTabChanged = async (tabValue: TabValue) => {
  activeTab.value = tabValue;

  router.replace({
    path: route.path,
    query: { ...route.query, activeTab: tabValue.toString() },
  });

  await triggerTabApi(tabValue);
};

const triggerTabApi = async (tabValue: TabValue) => {
  await nextTick();
  if (tabValue === TabValue.Tab1) {
    getDetail();
  }
  if (tabValue === TabValue.Tab2 && priceBoardRef.value) {
    priceBoardRef.value.getDetail();
  }
  if (tabValue === TabValue.Tab3 && contractTabRef.value) {
    contractTabRef.value?.getList();
  }
};

const handleCancelConfirm = () => {
  isShowConfirmPopup.value = false;
  openDetail();
};

const openUpdate = () => {
  router.push({
    name: ROUTE_NAME.UPDATE_ENTERPRISE,
    params: {
      id: props.id,
    },
  });
};

const openDetail = (id?: number) => {
  router.push({
    name: ROUTE_NAME.DETAILS_ENTERPRISE,
    params: {
      id: id ?? props.id,
    },
  });
};
//#endregion

//#region Form
const validationSchema = yup.object({
  business_name: yup.string().required('Tên doanh nghiệp không được để trống').trim(),
  address: yup.string().nullable(),
  tax_code: yup.string().nullable(),
  business_phone: yup.string().nullable(),
  business_email: yup
    .string()
    .email(TEXT.INVALID_EMAIL)
    .notRequired()
    .test('valid-email', TEXT.INVALID_EMAIL, (value) => {
      if (value === undefined || value === null || value === '') {
        return true;
      }
      const emailRegex = REGEX.EMAIL;
      return emailRegex.test(value);
    })
    .when((email, schema) => {
      if (!email[0]) {
        return schema;
      }
      return schema.matches(REGEX.EMAIL, TEXT.INVALID_EMAIL);
    }),
  contact_name: yup.string().required('Người liên hệ không được để trống').trim(),
  contact_phone: yup.string().required('SĐT người liên hệ không được để trống').trim(),
  contact_email: yup
    .string()
    .email(TEXT.INVALID_EMAIL)
    .required('Email người liên hệ không được để trống')
    .test('valid-email', TEXT.INVALID_EMAIL, (value) => {
      if (value === undefined || value === null || value === '') {
        return true;
      }
      const emailRegex = REGEX.EMAIL;
      return emailRegex.test(value);
    })
    .when((email, schema) => {
      if (!email[0]) {
        return schema;
      }
      return schema.matches(REGEX.EMAIL, TEXT.INVALID_EMAIL);
    }),
});

const { values, handleSubmit, setFieldValue } = useForm({
  validationSchema,
});

const onSubmit = handleSubmit(async () => {
  overlayLoading.toggleLoading(true);
  await api
    .put(`/business/v1/api/client/business/${user.id_business}`, {
      business_name: getNullableString(values.business_name),
      address: getNullableString(values.address),
      tax_code: getNullableString(values.tax_code),
      business_phone: getNullableString(values.business_phone),
      business_email: getNullableString(values.business_email),
      contact_name: getNullableString(values.contact_name),
      contact_phone: getNullableString(values.contact_phone),
      contact_email: getNullableString(values.contact_email),
      customer_source: values.customer_source,
      agent_id: values.agent_id,
      status: values.status,
      business_label_type: values.business_label_type,
      channel_types: values.channel_types,
      referral_code: values.referral_code,
    })
    .then((response) => {
      if (response.data.code === 0) {
        overlayLoading.toggleLoading(false);
        toast('success', response.data.message);
        openDetail();
      } else {
        overlayLoading.toggleLoading(false);
        toast('error', response.data.message);
      }
    })
    .catch(() => {
      overlayLoading.toggleLoading(false);
    });
});

const getDetail = function () {
  // FE set tạm
  if (!user.id_business) {
    return toast('error', 'Doanh nghiệp chưa xác định');
  }
  overlayLoading.toggleLoading(true);
  api
    .get(`/business/v1/api/client/business/${user.id_business}`)
    .then((response) => {
      if (response.data.code === 0) {
        const res = response.data.data;
        setFieldValue('tax_code', res.tax_code);
        setFieldValue('business_name', res.business_name);
        setFieldValue('business_phone', res.business_phone);
        setFieldValue('business_email', res.business_email);
        setFieldValue('contact_name', res.contact_name);
        setFieldValue('contact_phone', res.contact_phone);
        setFieldValue('contact_email', res.contact_email);
        setFieldValue('agent_id', res.agent_id);
        setFieldValue('address', res.address);
        setFieldValue('account_number', res.account_number);
        setFieldValue('customer_source', res.customer_source);
        setFieldValue('status', res.status);
        setFieldValue('business_label_type', res.business_label_type);
        setFieldValue('channel_types', res.channel_types);
        setFieldValue('referral_code', res.referral_code);
        overlayLoading.toggleLoading(false);
      } else {
        overlayLoading.toggleLoading(false);
        toast('error', response.data.message);
      }
    })
    .catch((err) => {
      overlayLoading.toggleLoading(false);
      console.error(err);
    });
};
//#endregion

watch(
  () => props.type,
  (value) => {
    if (value === PageType.Details) {
      getDetail();
    }
  },
);

watch(
  () => route.query.tab,
  async (newTab) => {
    if (newTab && typeof newTab === 'string') {
      const tabValue = +newTab as TabValue;
      activeTab.value = tabValue;
      await triggerTabApi(tabValue);
      router.replace({
        path: route.path,
        query: { ...route.query, tab: undefined, activeTab: tabValue.toString() },
      });
    }
  },
  { immediate: true },
);

watch(
  () => route.query.activeTab,
  async (newActiveTab) => {
    if (newActiveTab && typeof newActiveTab === 'string') {
      const tabValue = +newActiveTab as TabValue;
      if (tabValue !== activeTab.value) {
        activeTab.value = tabValue;
        await triggerTabApi(tabValue);
      }
    }
  },
  { immediate: true },
);

onMounted(async () => {
  if (props.type !== PageType.Add) {
    getDetail();
  } else {
    setFieldValue('status', STATUS_VALUE.ACTIVE);
  }

  if (!route.query.tab) {
    await triggerTabApi(activeTab.value);
  }
});
</script>

<style scoped>
:deep(.p-tag) {
  width: 120px;
}
.view-height {
  height: calc(100vh - 265px);
}
.enterprise-tabs :deep(.el-tabs__content) {
  padding: 0;
}
</style>
