<template>
  <div class="">
    <el-form class="relative filter-container" @submit.prevent="onFilter">
      <div class="flex gap-3 item-center">
        <div class="flex items-center gap-3">
          <VElementInput
            id="search-input"
            name="searchKey"
            prefix="search"
            placeholder="Tì<PERSON> kiếm theo số hợp đồng, tên hợp đồng"
            :style="'!w-[480px]'"
            @keyup.enter="onFilter"
          />
        </div>
        <VElementButton id="search-btn" label="Tìm kiếm" :bgColor="color.main" @click="onFilter" />
      </div>
    </el-form>
    <VOldTable
      idTable="contract-table"
      ref="tableRef"
      classContainer="!max-h-[calc(100vh-430px)]"
      :tableName="'Hợp đồng'"
      :showAction="true"
      :headers="contractHeaders"
      :styleHeaders="contractStyleHeaders"
      :rows="dataContract?.items ?? []"
      :pagination="{
        totalPage: dataContract?.paging?.total_pages ?? 0,
        total: dataContract?.paging?.total_records ?? 0,
        perPage: params.page_size ?? 0,
      }"
      @pageChanged="onPageChange"
      @perPageChange="onPerPageChange"
      @rowClick="handleViewDetail"
    >
      <template v-slot:items="{ row }">
        <td class="text-primaryText px-2">
          <div class="column-container" :title="row.contract_number">
            {{ row.contract_number }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container" :title="row.contract_name">
            {{ row.contract_name }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container" :title="getServiceNamesFromIds(row.service)">
            {{ getServiceNamesFromIds(row.service) }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="flex justify-center" :title="getContractTypeLabel(row.contract_type)">
            {{ getContractTypeLabel(row.contract_type) }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div
            class="flex justify-center"
            :title="row.effective_date ? formatDateTime(row.effective_date, DATE_FORMAT.DATE) : ''"
          >
            {{ row.effective_date ? formatDateTime(row.effective_date, DATE_FORMAT.DATE) : '' }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div
            class="flex justify-center"
            :title="row.expiry_date ? formatDateTime(row.expiry_date, DATE_FORMAT.DATE) : ''"
          >
            {{ row.expiry_date ? formatDateTime(row.expiry_date, DATE_FORMAT.DATE) : '' }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="flex justify-center" :title="getContractStatusLabel(row.status)">
            <Tag
              :class="getContractStatusColor(row.status)"
              :value="getContractStatusLabel(row.status)"
            />
          </div>
        </td>
      </template>
      <template v-slot:actions="{ row }">
        <li
          class="item min-w-[100px]"
          @click="handleViewDetail(row)"
          @keydown="handleViewDetail(row)"
        >
          <i class="pi pi-eye text-xs mr-3"></i>
          <span>Xem</span>
        </li>
      </template>
    </VOldTable>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import * as yup from 'yup';
import { useForm } from 'vee-validate';
import { useRouter } from 'vue-router';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import VOldTable from '@/components/base/VOldTable.vue';
import { ROUTE_NAME } from '@/shared';
import { contractHeaders, contractStyleHeaders } from '../index.constant';
import {
  getContractStatusColor,
  getContractStatusLabel,
  getContractTypeLabel,
  getServiceNamesFromIds,
} from '../index.utils';
import { formatDateTime } from '@/utils/getter.utils';
import { DATE_FORMAT } from '@/constants/common';
import { useUserSession, type UserData } from '@/store/userSession';

const api = useApi();
const router = useRouter();
const overlayLoading = useOverLayLoadingStore();
const user = useUserSession().user as UserData;

//#region Table
const tableRef = ref();
const dataContract = ref();

const { values, handleSubmit } = useForm({
  validationSchema: yup.object({
    searchKey: yup.string(),
  }),
});

const handleViewDetail = (row: any) => {
  router.push({
    name: ROUTE_NAME.DETAILS_CONTRACT,
    params: {
      contractId: row.id,
    },
    query: {
      contractTypeId: row.contractTypeId,
    },
  });
};

const getList = async () => {
  overlayLoading.toggleLoading(true);

  try {
    const apiUrl = '/business/v1/api/admin/contract';

    const requestData = {
      page_index: params.value?.page_index,
      page_size: params.value?.page_size,
      businessId: user.id_business,
      searchValue: values.searchKey ? values.searchKey.trim() : undefined,
    };

    if (!requestData.searchValue) {
      delete requestData.searchValue;
    }

    const payload = new URLSearchParams(requestData);

    const res = await api.get(apiUrl, { params: payload });
    if (res.data.code === 0) {
      dataContract.value = {
        items: res.data.data.items.map((item: any) => ({
          ...item,
          contract_name: item.contractName,
          contract_number: item.contractNumber,
          service: item.contractChannelIds,
          contract_type: item?.isTimeLimited,
          effective_date: item.contractStartDate,
          expiry_date: item.contractEndDate,
          status: item.contractStatusId,
        })),
        paging: {
          total_pages: res.data.data.paging?.total_pages,
          total_records: res.data.data.paging?.total_records,
        },
      };

      if (res.data.data?.items?.length === 0 && params.value.page_index !== 1) {
        tableRef.value.onPageChange(1);
        tableRef.value.filterData();
        return;
      }
    } else {
      console.error('API error:', res.data.message);
    }
  } catch (error) {
    console.error('getList error:', error);
  }

  overlayLoading.toggleLoading(false);
};
//#endregion

//#region Filter
interface IParamsGetList {
  page_index: number;
  page_size: number;
  contractType?: string;
  status?: string;
  effectiveDateFrom?: string;
  effectiveDateTo?: string;
}

const params = ref<IParamsGetList>({
  page_index: 1,
  page_size: 20,
});

const onPageChange = (value: number) => {
  params.value.page_index = value;
  getList();
};

const onPerPageChange = (value: number) => {
  params.value.page_size = value;
};

const onFilter = handleSubmit(() => {
  tableRef.value.filterData();
});

defineExpose({
  getList,
});
</script>

<style lang="scss" scoped>
.item {
  padding: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 131px;
  font-size: 14px;
  font-weight: 400;

  &:hover {
    color: var(--main-color);
  }
}

:deep(.p-tag) {
  width: 120px;
}

.view-height {
  height: calc(100vh - 265px);
}
</style>
