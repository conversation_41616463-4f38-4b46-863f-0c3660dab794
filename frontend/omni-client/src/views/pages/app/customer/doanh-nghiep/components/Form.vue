<template>
  <el-form label-position="top" class="w-[70%] flex justify-center gap-[75px] pb-[52px]">
    <div class="flex flex-col gap-[15px] w-[50%]">
      <VElementInput
        name="business_name"
        size="default"
        label="Tên doanh nghiệp"
        id="enterprise-business_name-input"
        :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
        :disabled="props.type === PageType.Details"
        :maxlength="200"
        :showLimit="false"
        :required="true"
      />
      <VElementInput
        name="address"
        size="default"
        label="Địa chỉ"
        :maxlength="200"
        id="enterprise-address-input"
        :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
        :disabled="props.type === PageType.Details"
        :showLimit="false"
      />
      <VElementInput
        name="tax_code"
        size="default"
        label="<PERSON><PERSON> số thuế"
        :maxlength="50"
        id="enterprise-tax_code-input"
        :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
        :disabled="props.type === PageType.Details"
        :showLimit="false"
      />
      <VElementInput
        name="business_phone"
        size="default"
        label="SĐT doanh nghiệp"
        :maxlength="50"
        id="enterprise-business_phone-input"
        :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
        :disabled="props.type === PageType.Details"
        :showLimit="false"
      />
      <VElementInput
        size="default"
        name="business_email"
        label="Email doanh nghiệp"
        :maxlength="50"
        id="enterprise-business_email-input"
        :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
        :disabled="props.type === PageType.Details"
        :showLimit="false"
      />
      <VElementInput
        size="default"
        name="referral_code"
        label="Mã giới thiệu"
        :maxlength="50"
        id="enterprise-referral_code-input"
        :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
        :disabled="props.type === PageType.Details"
        :showLimit="false"
      />
      <el-form-item
        v-if="props.type !== PageType.Add"
        id="enterprise-account_number-input"
        label="Đã tạo"
      >
        <div
          class="h-[33px] w-[100%] flex gap-[5px] items-center bg-[#f4f7fa] border-[1px] border-[#e4e7ec] rounded-[6px] px-[10px] select-none"
        >
          <div class="text-main">{{ values.account_number }}</div>
          <div>Tài khoản</div>
        </div>
      </el-form-item>
    </div>
    <div class="flex flex-col gap-[15px] w-[50%]">
      <VElementInput
        name="contact_name"
        size="default"
        label="Người liên hệ"
        :maxlength="50"
        id="enterprise-contact_name-input"
        :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
        :disabled="props.type === PageType.Details"
        :required="true"
        :showLimit="false"
      />
      <VElementInput
        name="contact_phone"
        size="default"
        label="SĐT người liên hệ"
        id="enterprise-contact_phone-input"
        :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
        :disabled="props.type === PageType.Details"
        :maxlength="50"
        :required="true"
        :showLimit="false"
      />
      <VElementInput
        name="contact_email"
        size="default"
        label="Email người liên hệ"
        id="enterprise-contact_email-input"
        :placeholder="getPlaceholder(PLACEHOLDER.TYPE, type)"
        :disabled="props.type === PageType.Details"
        :maxlength="50"
        :required="true"
        :showLimit="false"
      />
      <VElementDropdown
        name="business_label_type"
        label="Lĩnh vực hoạt động"
        :formType="type"
        :filterable="false"
        id="business_label_type-dropdown"
        :placeholder="getPlaceholder(PLACEHOLDER.SELECT, type)"
        :disabled="props.type === PageType.Details"
        :option="listLabelType"
        multiple
        :style="'w-[100%]'"
      />
      <VElementDropdown
        name="channel_types"
        label="Dịch vụ sử dụng"
        id="channel_type-dropdown"
        :placeholder="getPlaceholder(PLACEHOLDER.SELECT, type)"
        disabled
        :formType="type"
        :filterable="false"
        :option="channelTypes"
        multiple
        :style="'w-[100%]'"
      />
      <VElementDropdown
        name="status"
        label="Trạng thái"
        id="enterprise-status-dropdown"
        :placeholder="getPlaceholder(PLACEHOLDER.SELECT, type)"
        :disabled="true"
        :formType="type"
        :filterable="false"
        :option="statusValueOptions"
        :style="'w-[100%]'"
      />
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { PLACEHOLDER, statusOptions, type TDropdownItem } from '@/shared';
import { getPlaceholder } from '@/utils';
import { PageType } from '@/enums/common';
import { channelTypes } from '../index.constant';
import { onMounted, ref } from 'vue';
import { useApi } from '@/store/useApi.ts';

const props = defineProps<{
  type: PageType;
  values: any;
}>();

const api = useApi();

const statusValueOptions: TDropdownItem[] = statusOptions.filter((_, index: number) => index !== 0);

const listLabelType = ref<any[]>([]);
const getListLabelType = async () => {
  try {
    const res = await api.get(`/business/v1/api/client/business/label-types`);
    if (res.data.code === 0 && res.data.data) {
      listLabelType.value = res.data.data;
    }
  } catch (error: any) {
    console.log(error);
  }
};

onMounted(() => {
  getListLabelType();
});
</script>
