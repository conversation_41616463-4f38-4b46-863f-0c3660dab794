<script setup lang="ts">
import { ref, computed } from 'vue';
import * as yup from 'yup';
import { useForm } from 'vee-validate';

import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { handleApiError } from '@/utils/useErrorHandler';
import VOldTable from '@/components/base/VOldTable.vue';
import { APPENDIX_HEADERS, APPENDIX_STYLE_HEADERS } from '../../../index.constant';
import { formatDateTime } from '@/utils/getter.utils';
import { DATE_FORMAT } from '@/constants/common';
import FormAppendixPopup from './popup/FormAppendixPopup.vue';

export interface ParamsSearch {
  page_index: number;
  page_size: number;
  searchValue?: string;
}

// Đ<PERSON>nh nghĩa props cho component
interface Props {
  id: number;
  type: any;
  contractStatus: number;
}

// <PERSON>hai báo props với giá trị mặc định nếu cần
const props = defineProps<Props>();

const overlayLoading = useOverLayLoadingStore();
const api = useApi();

const tableRef = ref();
const tableWidth = ref();
const params = ref<ParamsSearch>({
  page_index: 1,
  page_size: 20,
});
const dataAppendix = ref([]) as any;
const { values, handleSubmit } = useForm({
  validationSchema: yup.object({
    org_id: yup.mixed(),
  }),
});

const getList = async () => {
  overlayLoading.toggleLoading(true);

  try {
    const requestData: any = {
      page_index: params.value?.page_index,
      page_size: params.value?.page_size,
      searchValue: values.keyword ? values.keyword.trim() : undefined,
    };

    if (!requestData.searchValue && requestData.searchValue !== '') {
      delete requestData.searchValue;
    }

    const requestEncoded = new URLSearchParams(requestData);

    const res = await api.get(`/business/v1/api/admin/contract/${props.id}/addendum`, {
      params: requestEncoded,
    });

    if (res.data.code === 0) {
      dataAppendix.value = {
        items: res.data.data.items || [],
        paging: {
          total_pages: res.data.data.paging?.total_pages,
          total_records: res.data.data.paging?.total_records,
        },
      };

      if (res.data.data?.items?.length === 0 && params.value.page_index !== 1) {
        tableRef.value.onPageChange(1);
        tableRef.value.filterData();
        return;
      }
    } else {
      console.error('API error:', res.data.message);
    }
  } catch (error: any) {
    console.error('getList error:', error);
    handleApiError(error);
  }

  overlayLoading.toggleLoading(false);
};

const onPageChange = (value: number) => {
  params.value.page_index = value;
  getList();
};

const onPerPageChange = (value: number) => {
  params.value.page_size = value;
};

const onSubmit = handleSubmit(() => {
  tableRef.value.filterData();
});

const onSetWidth = (value: number) => {
  tableWidth.value = value;
};

// State cho popup thêm phụ lục
const showAppendixPopup = ref(false);
const selectedAddendumId = ref<number | null>(null);
const typeAppendixPopup = ref<'add' | 'view'>('add');

const handleViewDetail = (row: any) => {
  typeAppendixPopup.value = 'view';
  selectedAddendumId.value = row.contractAddendumId;
  showAppendixPopup.value = true;
};

defineExpose({
  getList,
});
</script>

<template>
  <div>
    <div class="mb-4">
      <div class="relative filter-container flex justify-between">
        <div class="flex gap-[10px]">
          <VElementInput
            size="default"
            name="keyword"
            prefix="search"
            :placeholder="'Tìm kiếm theo số phụ lục, tên phụ lục'"
            :style="'!w-[480px]'"
            @keyup.enter="onSubmit"
          />
          <VElementButton
            :bgColor="color.main"
            label="Tìm kiếm"
            styleButton="s"
            @click="onSubmit"
          />
        </div>
      </div>
    </div>
    <VOldTable
      classContainer="!max-h-[calc(100vh-430px)]"
      idTable="contract-table"
      ref="tableRef"
      :tableName="'Hợp đồng'"
      :rows="dataAppendix?.items ?? []"
      :styleHeaders="APPENDIX_STYLE_HEADERS"
      :headers="APPENDIX_HEADERS"
      :pagination="{
        totalPage: dataAppendix?.paging?.total_pages ?? 0,
        total: dataAppendix?.paging?.total_records ?? 0,
        perPage: params.page_size ?? 0,
      }"
      :actionLength="3"
      :showAction="true"
      @pageChanged="onPageChange"
      @perPageChange="onPerPageChange"
      @setWidth="onSetWidth"
      @rowClick="handleViewDetail"
    >
      <template v-slot:items="{ row }">
        <td class="text-primaryText px-2">
          <div class="column-container" :title="row.addendumNumber">
            {{ row.addendumNumber }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container" :title="row.addendumName">
            {{ row.addendumName }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div class="column-container" :title="row.note">
            {{ row.note }}
          </div>
        </td>
        <td class="text-primaryText px-2">
          <div
            class="flex justify-center"
            :title="formatDateTime(row.addendumStartDate, DATE_FORMAT.DATE)"
          >
            {{ formatDateTime(row.addendumStartDate, DATE_FORMAT.DATE) }}
          </div>
        </td>
      </template>
      <template v-slot:actions="{ row }">
        <li
          class="item min-w-[100px]"
          @click="handleViewDetail(row)"
          @keydown="handleViewDetail(row)"
        >
          <i class="pi pi-eye text-xs mr-3"></i>
          <span>Xem</span>
        </li>
      </template>
    </VOldTable>
    <FormAppendixPopup
      v-if="showAppendixPopup"
      v-model:visible="showAppendixPopup"
      :contractId="props.id"
      :addendumId="selectedAddendumId"
      :type="typeAppendixPopup"
      @success="getList"
      @close="showAppendixPopup = false"
    />
  </div>
</template>

<style lang="scss" scoped>
.item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 131px;
  font-size: 14px;
  font-weight: 400;
  &:hover {
    color: var(--main-color);
  }
}
:deep(.p-tag) {
  width: 120px;
}
</style>
