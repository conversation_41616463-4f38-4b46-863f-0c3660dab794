<template>
  <div
    class="mx-[10px] mt-[40px] flex justify-center h-[calc(100vh-322px)] mb-[50px] overflow-auto"
  >
    <el-form label-position="top" class="w-[70%] flex justify-center gap-[75px] pb-[52px]">
      <div class="flex flex-col gap-[15px] w-[50%]">
        <VElementInput
          name="contract_number"
          size="default"
          label="Số hợp đồng"
          id="contract-contract_number-input"
          :placeholder="getPlaceholder('Nhập số hợp đồng', type)"
          :disabled="props.type === PageType.Details"
          :maxlength="25"
          :showLimit="false"
          :required="true"
        />

        <VElementInput
          name="contract_name"
          size="default"
          label="Tên hợp đồng"
          id="contract-contract_name-input"
          :placeholder="getPlaceholder('Nhập tên hợp đồng', type)"
          :disabled="props.type === PageType.Details"
          :maxlength="50"
          :showLimit="false"
        />

        <VElementDropdown
          name="service"
          label="Dịch vụ"
          :filterable="false"
          :multiple="true"
          id="contract-service-dropdown"
          :placeholder="getPlaceholder('Chọn dịch vụ', type)"
          :disabled="props.type === PageType.Details"
          :option="SERVICE_OPTIONS"
          :style="'w-full'"
          :required="true"
        />

        <VUploadFileServerCommon
          ref="fileUploadServerRef"
          file="contract_files"
          listFile="contract_file_list"
          :formType="type"
          label="File hợp đồng"
          id="contract-files"
          :required="true"
          :maxFile="maxFileCount"
          :fileTypes="['pdf', 'doc', 'docx', 'png']"
          :sizeToValidate="5 * 1024 * 1024"
          msgErrType="Chỉ upload định dạng PDF, Doc, PNG"
          msgErrSize="File tối đa 5MB"
          :typeApi="FileUploadApiType.ImageFile"
        />
      </div>

      <div class="flex flex-col gap-[15px] w-[50%]">
        <VElementDropdown
          name="contract_type"
          label="Loại hợp đồng"
          :filterable="false"
          id="contract-contract_type-dropdown"
          :placeholder="getPlaceholder(PLACEHOLDER.SELECT, type)"
          :disabled="props.type === PageType.Details"
          :option="CONTRACT_TYPE_OPTIONS"
          :style="'w-full'"
        />

        <VElementDateTimePicker
          name="effective_date"
          type="date"
          format="DD/MM/YYYY"
          label="Ngày hiệu lực"
          :placeholder="getPlaceholder('Chọn ngày hiệu lực', type)"
          :disabled="props.type === PageType.Details"
          :required="true"
          :style="'!w-full'"
        />

        <VElementDateTimePicker
          v-if="values.contract_type === ContractDurationType.Limited"
          name="expiry_date"
          type="date"
          format="DD/MM/YYYY"
          label="Ngày hết hạn"
          :placeholder="getPlaceholder('Chọn ngày hết hạn', type)"
          :disabled="props.type === PageType.Details"
          :style="'!w-full'"
        />

        <VElementInput
          size="default"
          name="note"
          type="textarea"
          label="Ghi chú"
          :placeholder="getPlaceholder('Nhập ghi chú', type)"
          :maxlength="250"
          :showLimit="false"
          :disabled="props.type === PageType.Details"
        />

        <div v-if="type !== PageType.Add">
          <div class="text-[#6b7280] text-[14px] font-semibold mb-[7px]">Trạng thái</div>
          <Tag
            :class="getContractStatusColor(contractStatus)"
            :value="getContractStatusLabel(contractStatus)"
          />
        </div>
        <VElementDateTimePicker
          v-if="type === PageType.Details && contractStatus === ContractStatusType.Liquidated"
          name="liquidation_date"
          type="date"
          format="DD/MM/YYYY"
          label="Ngày thanh lý"
          :placeholder="getPlaceholder('Chọn Ngày thanh lý', type)"
          :disabled="true"
          :style="'!w-full'"
        />
        <VUploadFileServerCommon
          v-if="type === PageType.Details && contractStatus === ContractStatusType.Liquidated"
          ref="fileUploadLiquidationServerRef"
          file="liquidation_files"
          listFile="liquidation_file_list"
          :formType="type"
          label="File Biên bản thanh lý"
          id="liquidation-files"
          :required="true"
          :maxFile="maxFileLiquidationCount"
          :fileTypes="['pdf', 'doc', 'docx', 'png']"
          :sizeToValidate="5 * 1024 * 1024"
          msgErrType="Chỉ upload định dạng PDF, Doc, PNG"
          msgErrSize="File tối đa 5MB"
          :typeApi="FileUploadApiType.ImageFile"
          :disabled="true"
        />
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { FileUploadApiType, PageType } from '@/enums/common';
import { getPlaceholder } from '@/utils';
import { PLACEHOLDER } from '@/shared';
import { CONTRACT_TYPE_OPTIONS, SERVICE_OPTIONS } from '../../../index.constant';
import { ContractDurationType, ContractStatusType } from '@/enums/contract';
import { getContractStatusColor, getContractStatusLabel } from '../../../index.utils';
import { useForm } from 'vee-validate';
import { ref } from 'vue';
import { watch } from 'vue';
import VUploadFileServerCommon from '@/components/base/common/VUploadFileServerCommon.vue';

interface Props {
  id: number;
  contractStatus: number;
  type: PageType;
  initialData?: any;
  contractTypeId: number;
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
});
const fileUploadServerRef = ref();
const fileUploadLiquidationServerRef = ref();
const maxFileCount = ref(0);
const maxFileLiquidationCount = ref(0);

const { values, setFieldValue } = useForm({});

const setFormValues = (data: any) => {
  if (!data || Object.keys(data).length === 0) return;

  setFieldValue('enterprise_id', data?.partyBId);
  setFieldValue('agent_id', data?.partyBId);
  setFieldValue('contract_number', data?.contractNumber);
  setFieldValue('contract_name', data?.contractName);
  setFieldValue('service', data?.contractChannelIds);

  // Handle contract files
  if (data?.contractFile && Array.isArray(data.contractFile)) {
    maxFileCount.value = data?.contractFile?.length || 0;
    const transformedFiles = data.contractFile.map((file: any) => ({
      file_upload_id: file.file_upload_id,
      file_name: file.file_name,
      link_url: file.link_url,
    }));

    setFieldValue('contract_file_list', transformedFiles);

    setTimeout(() => {
      if (fileUploadServerRef.value && transformedFiles.length > 0) {
        fileUploadServerRef.value.listFileUpload = transformedFiles;
        setFieldValue('contract_files', 'true');
      } else {
        console.log('ContractFormFields - fileUploadServerRef not available or no files');
      }
    }, 100);
  } else {
    setFieldValue('contract_file_list', data?.contract_files || []);
  }

  // Handle liquidation files
  if (data?.liquidationFile && Array.isArray(data.liquidationFile)) {
    maxFileLiquidationCount.value = data?.liquidationFile?.length || 0;
    const transformedFiles = data.liquidationFile.map((file: any) => ({
      file_upload_id: file.file_upload_id,
      file_name: file.file_name,
      link_url: file.link_url,
    }));

    setFieldValue('liquidation_file_list', transformedFiles);

    setTimeout(() => {
      if (fileUploadLiquidationServerRef.value && transformedFiles.length > 0) {
        fileUploadLiquidationServerRef.value.listFileUpload = transformedFiles;
        setFieldValue('liquidation_files', 'true');
      } else {
        console.log(
          'ContractFormFields - fileUploadLiquidationServerRef not available or no files',
        );
      }
    }, 100);
  } else {
    setFieldValue('liquidation_file_list', data?.liquidation_files || []);
  }

  setFieldValue('contract_type', data?.isTimeLimited);
  setFieldValue('effective_date', data?.contractStartDate);
  setFieldValue('expiry_date', data?.contractEndDate);
  setFieldValue('liquidation_date', data?.liquidationDate);
  setFieldValue('note', data?.note);
};

watch(
  () => props.initialData,
  (newData) => {
    setFormValues(newData);
  },
  { immediate: true, deep: true },
);

onMounted(() => {});
</script>
