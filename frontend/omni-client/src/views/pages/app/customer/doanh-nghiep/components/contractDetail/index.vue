<script setup lang="ts">
import { ref, onMounted, inject, nextTick, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ROUTE_NAME, TabValue, TEXT } from '@/shared';

import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useUserSession, type UserData } from '@/store/userSession';
import { handleApiError } from '@/utils/useErrorHandler';
import GeneralInfoTab from './components/GeneralInfoTab.vue';
import { Icon } from '@iconify/vue';
import { PageType } from '@/enums/common';
import AppendixListTab from './components/AppendixListTab.vue';
import BaseTabs, { type TTab } from '@/components/base/new/BaseTabs.vue';

const props = defineProps<{
  contractId: number;
  type?: PageType;
}>();

const tabs: TTab[] = [
  {
    label: 'Thông tin chung',
    value: TabValue.Tab1,
  },
  {
    label: '<PERSON><PERSON> lục hợp đồng',
    value: TabValue.Tab2,
  },
];

const api = useApi();
const toast = inject('toast') as any;
const overlayLoading = useOverLayLoadingStore();
const router = useRouter();
const route = useRoute();
const user = useUserSession().user as UserData;

const activeTab = ref<TabValue>(TabValue.Tab1);
const contractStatus = ref<number>();
const contractTypeId = ref<number>();
const contractData = ref({});
const appendixListTabRef = ref();
const maxFileCount = ref();


const getDetail = async () => {
  try {
    const contractTypeIdFromQuery = route.query?.contractTypeId as string;
    const contractTypeIdToUse = parseInt(contractTypeIdFromQuery, 10) || 0;

    const response = await api.get(`/business/v1/api/admin/contract/${props.contractId}`, {
      params: { contractTypeId: contractTypeIdToUse },
    });
    if (response.data.code === 0) {
      const res = response.data.data;
      contractData.value = res;
      maxFileCount.value = res?.contractFile?.length || 0;
      contractStatus.value = res.contractStatusId;
    } else {
      toast('error', response.data.message || TEXT.ERROR_OCCURRED);
    }
  } catch (error: any) {
    console.error('getDetail error:', error);
    handleApiError(error);
  }
};

const handleNavigateToList = () => {
  router.push({
    name: ROUTE_NAME.DETAILS_ENTERPRISE,
    params: { id: user.userId },
    query: { tab: TabValue.Tab3 },
  });
};

const handleTabChanged = async (tabValue: TabValue) => {
  activeTab.value = tabValue;
  await triggerTabApi(tabValue);
};

const triggerTabApi = async (tabValue: TabValue) => {
  await nextTick();
  if (tabValue === TabValue.Tab1) {
    await getDetail();
  }
  if (tabValue === TabValue.Tab2 && appendixListTabRef.value) {
    appendixListTabRef.value.getList?.();
  }
};

watch(
  () => route.query.tab,
  async (newTab) => {
    if (newTab && typeof newTab === 'string') {
      const tabValue = +newTab as TabValue;
      activeTab.value = tabValue;
      await triggerTabApi(tabValue);
      router.replace({
        path: route.path,
        query: { ...route.query, tab: undefined },
      });
    }
  },
  { immediate: true },
);

onMounted(async () => {
  overlayLoading.toggleLoading(true);

  if (!route.query.tab) {
    await triggerTabApi(activeTab.value);
  }
  overlayLoading.toggleLoading(false);
});
</script>

<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:file-text" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          :to="{
            name: ROUTE_NAME.DETAILS_ENTERPRISE,
            params: { id: user.userId },
          }"
          >Thông tin doanh nghiệp</el-breadcrumb-item
        >
        <el-breadcrumb-item
          :to="{
            name: ROUTE_NAME.DETAILS_ENTERPRISE,
            params: { id: user.userId },
          }"
          >Xem chi tiết</el-breadcrumb-item
        >
        <el-breadcrumb-item>Xem chi tiết hợp đồng</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
  <BaseTabs :tabs :active-tab="activeTab" tabClass="w-[94%]" @click-tab="handleTabChanged">
    <template #tab-1>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <GeneralInfoTab
          :id="props.contractId"
          :type="type!"
          :initialData="contractData"
          :contractTypeId="contractTypeId!"
          :contractStatus="contractStatus!"
        />
      </div>
    </template>
    <template #tab-2>
      <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-hidden pt-[10px]">
        <AppendixListTab
          ref="appendixListTabRef"
          :id="props.contractId"
          :type="type!"
          :contractStatus="contractStatus!"
        />
      </div>
    </template>
  </BaseTabs>
  <div
    class="flex z-[999] py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke items-center justify-between"
  >
    <VElementButton
      icon="arrow-narrow-left"
      text="black"
      bgColor="#EBEBEB"
      label="Quay lại"
      @click="handleNavigateToList"
    />
  </div>
</template>
