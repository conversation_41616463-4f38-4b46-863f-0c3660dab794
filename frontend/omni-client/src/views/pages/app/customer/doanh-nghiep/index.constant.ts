import { ALIGN } from '@/shared';
import { ServiceType, ServiceTypeLabel } from '@/enums/common';
import { ContractDurationLabel, ContractDurationType } from '@/enums/contract';
import { PLACEHOLDER, type TDropdownItem } from '@/shared';


//#region Contract Status
export enum ContractStatusValue {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  TERMINATED = 'TERMINATED',
}

export enum ContractStatusLabel {
  ACTIVE = 'Hoạt động',
  EXPIRED = 'Hết hiệu lực',
  TERMINATED = 'Thanh lý',
}

export const contractStatusOptions = [
  { value: ContractStatusValue.ACTIVE, label: ContractStatusLabel.ACTIVE },
  { value: ContractStatusValue.EXPIRED, label: ContractStatusLabel.EXPIRED },
  { value: ContractStatusValue.TERMINATED, label: ContractStatusLabel.TERMINATED },
];
//#endregion

//#region Contract Type
export enum ContractTypeValue {
  FIXED_TERM = 'FIXED_TERM',
  INDEFINITE = 'INDEFINITE',
}

export enum ContractTypeLabel {
  FIXED_TERM = 'Có thời hạn',
  INDEFINITE = 'Vô thời hạn',
}

export const contractTypeOptions = [
  { value: ContractTypeValue.FIXED_TERM, label: ContractTypeLabel.FIXED_TERM },
  { value: ContractTypeValue.INDEFINITE, label: ContractTypeLabel.INDEFINITE },
];
//#endregion

//#region Table Headers
export const contractHeaders = [
  {
    name: 'Số hợp đồng',
    visible: true,
    pin: false,
  },
  {
    name: 'Tên hợp đồng',
    visible: true,
    pin: false,
  },
  {
    name: 'Dịch vụ',
    visible: true,
    pin: false,
  },
  {
    name: 'Loại hợp đồng',
    visible: true,
    pin: false,
    align: ALIGN.CENTER,
  },
  {
    name: 'Ngày hiệu lực',
    visible: true,
    pin: false,
    align: ALIGN.CENTER,
  },
  {
    name: 'Ngày hết hạn',
    visible: true,
    pin: false,
    align: ALIGN.CENTER,
  },
  {
    name: 'Trạng thái',
    visible: true,
    pin: false,
    align: ALIGN.CENTER,
  },
];

export const contractStyleHeaders = [
  {
    idx: 0,
    class: 'w-[8%]',
  },
  {
    idx: 1,
    class: 'w-[15%]',
  },
  {
    idx: 2,
    class: 'w-[20%]',
  },
  {
    idx: 3,
    class: 'w-[15%]',
  },
  {
    idx: 4,
    class: 'w-[12%]',
  },
  {
    idx: 5,
    class: 'w-[12%]',
  },
  {
    idx: 6,
    class: 'w-[12%]',
  },
  {
    idx: 7,
    class: 'w-[6%]',
  },
];

export const APPENDIX_HEADERS = [
  {
    name: 'Số phụ lục',
    visible: true,
    align: ALIGN.START,
  },
  {
    name: 'Tên phụ lục',
    visible: true,
    align: ALIGN.START,
  },
  {
    name: 'Ghi chú',
    visible: true,
    align: ALIGN.START,
  },
  {
    name: 'Ngày hiệu lực PL',
    visible: true,
    align: ALIGN.CENTER,
  },
];

export const APPENDIX_STYLE_HEADERS = [
  {
    idx: 0,
    class: 'w-[20%]',
  },
  {
    idx: 1,
    class: 'w-[25%]',
  },
  {
    idx: 2,
    class: 'w-[30%]',
  },
  {
    idx: 3,
    class: 'w-[15%]',
  },
];
//#endregion

export enum CONTRACT_STATUS {
  ACTIVE = 1,
  EXPIRED = 2,
  LIQUIDATED = 3,
}

export enum CONTRACT_TYPE {
  LIMITED = 1,
  UNLIMITED = 2,
}

export enum SERVICE_TYPE {
  SMS = 'SMS',
  ZNS = 'ZNS',
  ALL = 'ALL',
}

export const channelTypes: TDropdownItem[] = [
  {
    label: 'ZNS',
    value: 1,
  },
  {
    label: 'SMS',
    value: 2,
  },
];

export const baseDataFilter = [
  {
    label: 'Dịch vụ',
    valueName: 'service',
    type: 'dropdown',
    placeholder: PLACEHOLDER.SELECT,
    defaultValue: '',
    dropdownConfig: {
      option: [
        { value: ServiceType.SMS, label: ServiceTypeLabel.SMS },
        { value: ServiceType.ZNS, label: ServiceTypeLabel.ZNS },
      ],
    },
  },
  {
    label: 'Trạng thái',
    valueName: 'status',
    type: 'dropdown',
    placeholder: PLACEHOLDER.SELECT,
    defaultValue: '',
    dropdownConfig: {
      option: [
        { value: 1, label: 'Hoạt động' },
        { value: 2, label: 'Hết hiệu lực' },
        { value: 3, label: 'Thanh lý' },
      ],
    },
  },
];

export const SERVICE_OPTIONS: TDropdownItem[] = [
  { value: ServiceType.SMS, label: ServiceTypeLabel.SMS },
  { value: ServiceType.ZNS, label: ServiceTypeLabel.ZNS },
];

export const CONTRACT_TYPE_OPTIONS: TDropdownItem[] = [
  { value: ContractDurationType.Limited, label: ContractDurationLabel.Limited },
  { value: ContractDurationType.Unlimited, label: ContractDurationLabel.Unlimited },
];
