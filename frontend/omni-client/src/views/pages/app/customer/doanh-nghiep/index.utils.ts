// Utility functions for Contract management

import { color } from '@/constants/statusColor';
import { CONTRACT_STATUS } from './index.constant';
import {
  ContractStatusType,
  ContractStatusTypeLabel,
  ContractDurationType,
  ContractDurationLabel,
} from '@/enums/contract';
import { ServiceType, ServiceTypeLabel } from '@/enums/common';

/**
 * Get contract status label
 */
export const getContractStatusLabel = (status: number): string => {
  switch (status) {
    case ContractStatusType.Active:
      return ContractStatusTypeLabel.Active;
    case ContractStatusType.Expired:
      return ContractStatusTypeLabel.Expired;
    case ContractStatusType.Liquidated:
      return ContractStatusTypeLabel.Liquidated;
    default:
      return 'Không xác định';
  }
};

/**
 * Get contract status color class
 */
export const getContractStatusColor = (status: number): string => {
  switch (status) {
    case ContractStatusType.Active:
      return color.Green;
    case ContractStatusType.Expired:
      return color.Red;
    case ContractStatusType.Liquidated:
      return color.Yellow;
    default:
      return color.Dark;
  }
};

/**
 * Get contract type label
 */
export const getContractTypeLabel = (type: number): string => {
  switch (type) {
    case ContractDurationType.Limited:
      return ContractDurationLabel.Limited;
    case ContractDurationType.Unlimited:
      return ContractDurationLabel.Unlimited;
    default:
      return 'Không xác định';
  }
};

/**
 * Format date string
 */
export const formatDate = (dateString: string | null): string => {
  if (!dateString) return '-';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  } catch (error) {
    return dateString;
  }
};

/**
 * Check if contract is editable based on status
 */
export const isContractEditable = (status: number): boolean => {
  return status === CONTRACT_STATUS.ACTIVE || status === CONTRACT_STATUS.EXPIRED;
};

/**
 * Check if contract can be liquidated
 */
export const canLiquidateContract = (status: number): boolean => {
  return status === CONTRACT_STATUS.ACTIVE || status === CONTRACT_STATUS.EXPIRED;
};

/**
 * Get available actions for contract based on status and user type
 */
export const getAvailableActions = (status: number, userType: string) => {
  const actions = [];

  // View action is always available
  actions.push('view');

  if (userType === 'ADMIN') {
    // Admin has full permissions
    if (isContractEditable(status)) {
      actions.push('edit');
      actions.push('addAppendix');
    }
    if (canLiquidateContract(status)) {
      actions.push('liquidate');
    }
  } else {
    // Agent has limited permissions
    if (isContractEditable(status)) {
      actions.push('edit');
      actions.push('addAppendix');
    }
    // Agent cannot liquidate contracts
  }

  return actions;
};


/**
 * Service type mapping object
 */
const SERVICE_TYPE_MAPPING = {
  [ServiceType.SMS]: ServiceTypeLabel.SMS,
  [ServiceType.ZNS]: ServiceTypeLabel.ZNS,
} as const;

/**
 * Convert service IDs array to service names string
 * @param serviceIds - Array of service IDs [1, 2] or single value
 * @returns Service names joined by comma "SMS, ZNS"
 */
export const getServiceNamesFromIds = (serviceIds: number[] | number | string): string => {
  if (!serviceIds) return '';

  // Handle array case [1, 2]
  if (Array.isArray(serviceIds)) {
    return serviceIds
      .map((id: number) => SERVICE_TYPE_MAPPING[id as keyof typeof SERVICE_TYPE_MAPPING])
      .filter(Boolean)
      .join(', ');
  }

  // Handle single number case
  if (typeof serviceIds === 'number') {
    return SERVICE_TYPE_MAPPING[serviceIds as keyof typeof SERVICE_TYPE_MAPPING] || '';
  }

  // Fallback for string or other types
  return String(serviceIds);
};

/**
 * Validate contract number format
 */
export const validateContractNumber = (contractNumber: string): boolean => {
  if (!contractNumber || contractNumber.length > 25) {
    return false;
  }

  // Basic format validation (can be customized)
  const pattern = /^[A-Z0-9\/\-]+$/i;
  return pattern.test(contractNumber);
};

/**
 * Validate file upload
 */
export const validateFileUpload = (file: File): { isValid: boolean; error?: string } => {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/png',
    'image/jpg',
  ];

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'File không được vượt quá 10MB',
    };
  }

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Chỉ chấp nhận file PDF, DOC, DOCX, JPG, PNG',
    };
  }

  return { isValid: true };
};

/**
 * Generate contract search filter
 */
export const generateSearchFilter = (searchTerm: string, contracts: any[]) => {
  if (!searchTerm) return contracts;

  const term = searchTerm.toLowerCase();
  return contracts.filter(
    (contract) =>
      contract.contract_number?.toLowerCase().includes(term) ||
      contract.contract_name?.toLowerCase().includes(term) ||
      contract.enterprise_name?.toLowerCase().includes(term) ||
      contract.agent_name?.toLowerCase().includes(term),
  );
};

/**
 * Sort contracts by field
 */
export const sortContracts = (
  contracts: any[],
  field: string,
  direction: 'asc' | 'desc' = 'asc',
) => {
  return [...contracts].sort((a, b) => {
    let aValue = a[field];
    let bValue = b[field];

    // Handle date fields
    if (field.includes('date')) {
      aValue = new Date(aValue || 0).getTime();
      bValue = new Date(bValue || 0).getTime();
    }

    // Handle string fields
    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (direction === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
};

/**
 * Export contracts to CSV
 */
export const exportToCSV = (contracts: any[], filename: string = 'contracts.csv') => {
  const headers = [
    'Số hợp đồng',
    'Tên hợp đồng',
    'Doanh nghiệp/Đại lý',
    'Dịch vụ',
    'Loại hợp đồng',
    'Ngày hiệu lực',
    'Ngày hết hạn',
    'Trạng thái',
  ];

  const csvContent = [
    headers.join(','),
    ...contracts.map((contract) =>
      [
        contract.contract_number,
        contract.contract_name,
        contract.enterprise_name || contract.agent_name,
        contract.service,
        getContractTypeLabel(contract.contract_type),
        formatDate(contract.effective_date),
        formatDate(contract.expiry_date),
        getContractStatusLabel(contract.status),
      ].join(','),
    ),
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export const getApiUrl = (
  action: 'list' | 'create' | 'update' | 'detail',
  apiUrl: string,
  id?: number,
) => {
  const baseUrl = apiUrl;

  switch (action) {
    case 'list':
      return baseUrl;
    case 'create':
      return baseUrl;
    case 'update':
      return `${baseUrl}/${id}`;
    case 'detail':
      return `${baseUrl}/${id}`;
    default:
      return baseUrl;
  }
};
