<template>
  <div class="h-[calc(100vh-125px)] overflow-auto">
    <!-- Header -->
    <div
      class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
    >
      <div class="flex gap-[15px] items-center">
        <Icon icon="tabler:building-bank" class="text-[20px] text-primaryText" />
        <el-breadcrumb separator="/">
          <el-breadcrumb-item
            id="enterprise-breadcrumb"
            :to="{ path: `/customer/e-point/danh-sach/${id}` }"
            >E-Point</el-breadcrumb-item
          >
          <el-breadcrumb-item>Nạp tiền</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    <div class="view-height mb-[50px] overflow-auto">
      <div class="justify-center flex">
        <el-steps
          align-center
          class="pt-[40px] w-[500px]"
          finish-status="success"
          :active="EpointStore.active"
        >
          <el-step title="Thông tin thanh toán" class="cursor-pointer" :icon="Step1" />
          <el-step
            title="Thanh toán đơn hàng"
            class="cursor-pointer"
            :icon="EpointStore.active === 2 || EpointStore.active === 3 ? Step2Done : Step2"
          />
          <el-step
            title="Hoàn thành"
            class="cursor-pointer"
            :icon="EpointStore.active === 3 ? Step3Done : Step3"
          />
        </el-steps>
      </div>
      <StepOne
        v-if="EpointStore.active === 1"
        ref="stepOneRef"
        @update:epoint="handleEpointChange"
      />
      <StepTwo
        v-if="EpointStore.active === 2"
        ref="stepTwoRef"
        @countdownComplete="activeBtnReturn"
      />
      <StepThree v-if="EpointStore.active === 3" ref="stepThreeRef" />
    </div>
    <div
      v-if="EpointStore.active == 1 || EpointStore.active == 3"
      class="flex z-[999] py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke items-center justify-end"
    >
      <VElementButton
        v-if="
          EpointStore.active === 1 || (EpointStore.paymentType === 2 && EpointStore.active === 2)
        "
        label="Hủy"
        :bgColor="color.closeButton"
        @click="
          () => {
            cancel();
          }
        "
      />
      <VElementButton
        v-if="
          EpointStore.active === 1 || (EpointStore.paymentType === 2 && EpointStore.active === 2)
        "
        label="Tiếp tục"
        :bgColor="color.main"
        @click="() => onSubmit(EpointStore.active)"
      />
      <VElementButton
        v-if="EpointStore.active === 3"
        label="Đóng"
        :bgColor="color.closeButton"
        @click="back"
      />
    </div>
    <div
      v-else
      class="flex z-[999] justify-between py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke"
    >
      <VElementButton
        icon="arrow-narrow-left"
        text="black"
        bgColor="#EBEBEB"
        label="Quay lại"
        @click="back"
      />
      <VElementButton
        v-if="EpointStore.active === 2"
        label="Tiếp tục"
        :bgColor="color.main"
        @click="() => onSubmit(EpointStore.active)"
      />
    </div>
    <PopupCancel
      v-model:visible="visibleModalCancel"
      @onClose="() => (visibleModalCancel = false)"
      @onConfirm="back"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import Step1 from '@/components/icon/Step1.vue';
import Step2Done from '@/components/icon/Step2Done.vue';
import Step2 from '@/components/icon/Step2.vue';
import Step3Done from '@/components/icon/Step3Done.vue';
import Step3 from '@/components/icon/Step3.vue';
import StepOne from './StepOne.vue';
import StepTwo from './StepTwo.vue';
import StepThree from './StepThree.vue';
import PopupCancel from '@/views/pages/app/customer/e-point/components/PopupCancel.vue';
import { useEpointStep } from '@/store/useEPointStep';
import { ROUTE_NAME } from '@/shared';
import { useRouter } from 'vue-router';
import { color } from '@/constants/statusColor';

const router = useRouter();
const EpointStore = useEpointStep();
const stepOneRef = ref();
const stepTwoRef = ref();
const stepThreeRef = ref();
const isCountdownComplete = ref(false);
const visibleModalCancel = ref(false);
const isChangeEpoint = ref(false);

const id = router.currentRoute.value.params.id;

const onSubmit = (step: number) => {
  if (step === 1) {
    stepOneRef.value.onSubmit();
  } else if (step === 2) {
    stepTwoRef.value.onSubmit();
  } else if (step === 3) {
    stepThreeRef.value.onSubmit();
  }
};
const handleEpointChange = (val) => {
  if (val) {
    isChangeEpoint.value = true;
  } else {
    isChangeEpoint.value = false;
  }
};
const activeBtnReturn = (val: boolean) => {
  isCountdownComplete.value = val;
};

const back = () => {
  router.push({ name: ROUTE_NAME.E_POINT, params: { id } });
};
const cancel = () => {
  if (isChangeEpoint.value) {
    visibleModalCancel.value = true;
  } else {
    back();
  }
};
onMounted(() => {
  EpointStore.active = 1;
});
</script>
