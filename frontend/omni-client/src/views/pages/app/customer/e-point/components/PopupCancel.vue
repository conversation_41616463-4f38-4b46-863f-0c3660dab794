<template>
  <VDialog
    modal
    ref="refDialog"
    header="Xác nhận"
    :draggable="false"
    v-model:visible="isVisiblePopup"
    :pt="{
      content: { class: 'mb-[60px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: '!pl-[230px] !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
      },
    }"
    :style="{
      width: '550px',
      backgroundColor: '#fff',
      maxHeight: '100%',
    }"
  >
    <div class="pt-[15px]">
      <div class="flex flex-col mt-[20px] items-center">
        <img src="@/assets/images/warning.png" alt="homepage" class="w-[65px]" />
        <div class="font-normal text-[19px] mt-[15px] line-clamp-3 text-center">
          <PERSON><PERSON>n c<PERSON> chắc chắn muốn hủy bỏ hành động đang thực hiện mà không lưu?
        </div>
      </div>
    </div>
    <div class="save-container flex justify-center items-center gap-5 mt-5">
      <VElementButton
        label="Hủy"
        id="cancel-button"
        :bgColor="color.closeButton"
        @click="() => (isVisiblePopup = false)"
      />
      <VElementButton
        label="Đồng ý"
        id="confirm-button"
        :bgColor="color.main"
        @click="() => emit('onConfirm')"
      />
    </div>
  </VDialog>
</template>

<script setup lang="ts">
import { color } from '@/constants/statusColor';

const emit = defineEmits(['onConfirm']);

const isVisiblePopup = defineModel('visible');
</script>
