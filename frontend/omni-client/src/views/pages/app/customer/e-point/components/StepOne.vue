<template>
  <div class="flex justify-between items-start px-[8vw] py-[20px]">
    <!-- Nạp E-Point -->
    <div class="w-[45%]">
      <div class="justify-start text-zinc-800 text-sm font-semibold font-['Inter'] leading-snug">
        Nạp E-Point
      </div>
      <div class="pb-[10px] pt-[10px] text-[14px]">
        <div class="text-[#6B7280] pb-[10px] font-semibold">
          Nhập số E-Point bạn muốn nạp <span class="text-red-500">*</span>
        </div>
        <VElementInput
          name="epoint_number"
          size="default"
          label=""
          id="epoint_number-input"
          placeholder="Nhập giá trị"
          :maxlength="10"
          :showLimit="false"
          :required="true"
          :useAppendTemplate="true"
          :formatter="true"
          :parser="true"
          @onInput="handleInputEpoint"
          @change="onChangeNumberInput"
        >
          <template #append> E-Point </template>
        </VElementInput>
      </div>
      <div class="mb-[20px] text-[14px]">
        <span class="text-[#6B7280] font-semibold">Chọn nhanh:</span>
        <div class="mt-[10px]">
          <el-radio-group class="radio-group w-full" v-model="numberQuick" @change="onChangeNumber">
            <el-radio-button :label="500000">500.000</el-radio-button>
            <el-radio-button :label="1000000">1.000.000</el-radio-button>
            <el-radio-button :label="2000000">2.000.000</el-radio-button>
            <el-radio-button :label="5000000">5.000.000</el-radio-button>
            <el-radio-button :label="10000000">10.000.000</el-radio-button>
          </el-radio-group>
        </div>
        <div class="">
          <span class="text-red-500 text-sm font-medium leading-normal">Tỷ lệ quy đổi </span
          ><span class="text-red-500 text-sm font-semibold leading-normal"> 1 E-Point = 1 VNĐ</span>
        </div>
      </div>
    </div>
    <!-- Thông tin thanh toán -->
    <div class="w-[45%]">
      <div
        class="bg-indigo-50 rounded-md shadow-[0px_0px_40px_-11px_rgba(16,24,40,0.10)] border border-zinc-200 p-[16px]"
      >
        <div class="text-black text-sm font-semibold leading-relaxed pb-[10px]">
          Thông tin thanh toán
        </div>
        <div class="bg-white rounded-md border border-zinc-200 py-[19px]">
          <div class="flex justify-between pb-[20px] px-[19px]">
            <div class="text-black text-sm font-normal leading-relaxed">Số E-Point muốn nạp</div>
            <div class="text-black text-sm font-semibold leading-relaxed">
              {{ formatVND(values.epoint_number) }}
            </div>
          </div>
          <div
            class="bg-sky-100 rounded-[10px] border border-blue-300 p-[7px] flex items-center mx-[19px]"
          >
            <div class="mr-[10px] flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                class="mt-1"
                viewBox="0 0 24 24"
                fill="#1877F2"
              >
                <path
                  d="M12 2c5.523 0 10 4.477 10 10a10 10 0 0 1 -19.995 .324l-.005 -.324l.004 -.28c.148 -5.393 4.566 -9.72 9.996 -9.72zm0 9h-1l-.117 .007a1 1 0 0 0 0 1.986l.117 .007v3l.007 .117a1 1 0 0 0 .876 .876l.117 .007h1l.117 -.007a1 1 0 0 0 .876 -.876l.007 -.117l-.007 -.117a1 1 0 0 0 -.764 -.857l-.112 -.02l-.117 -.006v-3l-.007 -.117a1 1 0 0 0 -.876 -.876l-.117 -.007zm.01 -3l-.127 .007a1 1 0 0 0 0 1.986l.117 .007l.127 -.007a1 1 0 0 0 0 -1.986l-.117 -.007z"
                />
              </svg>
            </div>
            <div class="italic">
              <span class="text-zinc-800 text-sm leading-normal">Tỷ lệ quy đổi </span
              ><span class="text-zinc-800 text-sm font-semibold leading-normal">
                1 E-Point = 1 VNĐ</span
              >
            </div>
          </div>
          <div class="outline outline-1 outline-offset-[-0.50px] outline-zinc-200 mt-[20px]"></div>
          <div class="flex justify-between px-[19px] pt-[10px]">
            <div class="text-black text-sm font-normal leading-relaxed">Số tiền cần thanh toán</div>

            <div class="text-red-500 text-base font-semibold leading-relaxed">
              {{ formatVND(values.epoint_number) }} VNĐ
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import * as yup from 'yup';
import { useForm } from 'vee-validate';
import { useEpointStep } from '@/store/useEPointStep';

const emit = defineEmits(['update:epoint']);
const numberQuick = ref();
const epointStore = useEpointStep();

// function
function formatVND(value: string | number) {
  if (!value) return '0';
  const number = typeof value === 'string' ? parseInt(value.replace(/\./g, ''), 10) : value;
  return number.toLocaleString('vi-VN');
}
const validationSchema = yup.object({
  epoint_number: yup
    .string()
    .required('Vui lòng nhập số E-Point')
    .matches(/^\d+$/, 'Chỉ cho phép nhập số')
    .test(
      'max',
      'Hạn mức nạp E-Point tối đa là 30.000.000',
      (value) => !value || parseInt(value, 10) <= 30000000,
    ),
});
const { values, handleSubmit, setFieldValue } = useForm({
  validationSchema,
});

const onSubmit = handleSubmit(async (value) => {
  epointStore.setValueStep1(value);
  epointStore.next();
});

const onChangeNumber = (value: number) => {
  setFieldValue('epoint_number', value);
  emit('update:epoint', value);
};
const handleInputEpoint = () => {
  numberQuick.value = '';
  emit('update:epoint', value);
};
const onChangeNumberInput = (value: number) => {
  epointStore.setValueStep1({ epoint_number: value });
};

defineExpose({
  onSubmit,
});
</script>
<style scoped lang="scss">
:deep(.el-radio-button__inner) {
  width: 100px;
  // padding-left: 20px;
  // padding-right: 20px;
  margin-bottom: 10px;
  color: #292d32;
  border: 1px solid #f7f9fd !important;
  background-color: #f7f9fd;
  border-radius: 6px !important;
  position: relative;
  font-weight: 600;
}
:deep(.el-radio-button__inner:hover) {
  background-color: #fff !important;
  border-radius: 6px !important;
  border: 1px solid #f95341 !important;
  color: #000 !important;
}
:deep(.el-radio) {
  margin-right: 0 !important;
}
:deep(.radio-group) {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
:deep(.el-radio-button) {
  flex: 1 1 calc(20% - 10px);
  min-width: 100px;
  max-width: 120px;
  display: flex;
}
:deep(.is-active) {
  .el-radio-button__inner {
    background-color: #fff !important;
    border-radius: 6px !important;
    border: 1px solid #f95341 !important;
    color: #000 !important;
    box-shadow: none !important;
  }
}
// tích dưới button chọn nhanh
:deep(.is-active .el-radio-button__inner::after) {
  content: '';
  display: inline-block;
  width: 15px;
  height: 15px;
  margin-left: 8px;
  position: absolute;
  right: -1px;
  bottom: 0;
  background-image: url('/src/assets/icons/check-epoint.svg');
  background-size: contain;
  background-repeat: no-repeat;
}
</style>
