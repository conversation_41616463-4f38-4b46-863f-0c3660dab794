<template>
  <div class="relative w-full text-center container pt-[95px]">
    <div class="absolute left-1/2 -translate-x-1/2 -top-[-60px] z-10">
      <img :src="epointStore.step2Data.status === 'Đang xử lý' ? statusPending : statusFail" />
    </div>
    <div
      class="w-[30%] mx-auto bg-indigo-50 rounded-md shadow-[0px_0px_40px_-11px_rgba(16,24,40,0.10)] border border-zinc-200 pt-[70px] pb-[25px]"
    >
      <!-- Đang xử lý -->
      <div
        v-if="epointStore.step2Data.status === 'Đang xử lý'"
        class="text-center justify-start text-black text-base font-medium leading-relaxed"
      >
        Giao dịch đang được xử lý.<br />Vui lòng chờ đợi trong ít phút.
      </div>

      <!-- Thất bại -->
      <div
        v-else
        class="text-center justify-start text-black text-base font-medium leading-relaxed px-[17px]"
      >
        Có lỗi xảy ra!<br /><PERSON><PERSON> lòng thử lại sau hoặc liên hệ với quản trị viên
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useEpointStep } from '@/store/useEPointStep';
import statusPending from '@/assets/icons/status-pending.svg';
import statusFail from '@/assets/icons/status-fail.svg';

const epointStore = useEpointStep();
</script>
<style scoped>
.container {
  margin: 0 auto;
}
</style>
