<template>
  <div class="flex items-start justify-between px-[2vw] py-[20px] px-[10%]">
    <div class="w-[40%] pt-[80px]">
      <div
        class="bg-indigo-50 rounded-md shadow-[0px_0px_40px_-11px_rgba(16,24,40,0.10)] border border-zinc-200 p-[16px]"
      >
        <div class="text-black text-sm font-semibold leading-relaxed pb-[10px]">
          Thông tin đơn hàng
        </div>
        <div class="bg-white rounded-md border border-zinc-200 py-[19px]">
          <div class="flex justify-between pb-[20px] px-[19px]">
            <div class="text-black text-sm font-normal leading-relaxed">Nhà cung cấp</div>
            <div class="text-black text-sm font-semibold leading-relaxed">VNPT Omnichannel</div>
          </div>
          <div class="px-[19px]">
            <div class="text-black text-sm font-normal leading-relaxed"><PERSON><PERSON> tả:</div>
            <div
              class="w-80 justify-start text-zinc-800 text-sm font-semibold font-['Inter'] leading-tight"
            >
              Nạp tiền cho doanh nghiệp {{ epointStore.businessName }}. Người thực hiện:
              {{ user.email }}
            </div>
          </div>
          <div class="outline outline-1 outline-offset-[-0.50px] outline-zinc-200 mt-[20px]"></div>
          <div class="flex justify-between px-[19px] pt-[10px]">
            <div class="text-black text-sm font-normal leading-relaxed">Số tiền</div>

            <div class="text-red-500 text-base font-semibold leading-relaxed">
              {{ Number(epointStore.step1Data.epoint_number).toLocaleString('vi-VN') }} VNĐ
            </div>
          </div>
        </div>
      </div>
      <div class="pt-[25px] flex justify-start">
        <div class="mr-[10px] flex items-start">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            class="mt-1"
            viewBox="0 0 24 24"
            fill="#354052"
          >
            <path
              d="M12 2c5.523 0 10 4.477 10 10a10 10 0 0 1 -19.995 .324l-.005 -.324l.004 -.28c.148 -5.393 4.566 -9.72 9.996 -9.72zm0 9h-1l-.117 .007a1 1 0 0 0 0 1.986l.117 .007v3l.007 .117a1 1 0 0 0 .876 .876l.117 .007h1l.117 -.007a1 1 0 0 0 .876 -.876l.007 -.117l-.007 -.117a1 1 0 0 0 -.764 -.857l-.112 -.02l-.117 -.006v-3l-.007 -.117a1 1 0 0 0 -.876 -.876l-.117 -.007zm.01 -3l-.127 .007a1 1 0 0 0 0 1.986l.117 .007l.127 -.007a1 1 0 0 0 0 -1.986l-.117 -.007z"
            />
          </svg>
        </div>
        <div class=" ">
          <span class="text-black text-sm font-normal leading-snug"
            >Lưu ý cần nhập đúng, đầy đủ các thông tin: </span
          ><span class="text-black text-sm font-semibold leading-snug"
            >số tài khoản, số tiền, nội dung chuyển khoản</span
          ><span class="text-black text-sm font-normal leading-snug">
            để hệ thống xác nhận thông tin chuyển và ghi nhận đơn hàng thành công.<br /><br />Nếu
            gặp vấn đề, xin hãy liên hệ 0123456789 để được hỗ trợ.</span
          >
        </div>
      </div>
    </div>
    <div class="w-[40%] flex flex-col items-center pt-[50px]">
      <div class="self-stretch text-black text-base font-medium leading-snug text-center">
        Quét mã QR để thanh toán
      </div>
      <div class="w-60 h-60 my-[20px]">
        <img :src="infoQR?.qr" alt="" />
      </div>
      <div class="flex flex-col w-full items-center gap-y-[5px]">
        <div class="text-center justify-start text-black text-base font-semibold leading-snug">
          {{ infoQR?.provider }}
        </div>
        <div class="text-center justify-start text-black text-base font-semibold leading-snug">
          {{ infoQR?.bankNumber }}
        </div>
        <div class="text-center justify-start text-black text-base font-semibold leading-snug">
          Nội dung: TT OC [Số điện thoại]
        </div>
        <div
          class="w-96 h-5 text-center justify-start text-black text-base font-medium leading-snug"
        >
          Sử dụng camera hỗ trợ QR code để quét mã
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useEpointStep } from '@/store/useEPointStep';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useUserSession, type UserData } from '@/store/userSession';

const epointStore = useEpointStep();
const user = useUserSession().user as UserData;
const overlayLoading = useOverLayLoadingStore();
const api = useApi();
import { useApi } from '@/store/useApi';

const infoQR = ref();

const getQR = async () => {
  overlayLoading.toggleLoading(true);
  await api
    .get('wallet/v1/api/wallet/deposit/info')
    .then((res) => {
      if (res.data.code === 0) {
        overlayLoading.toggleLoading(false);
        infoQR.value = res.data.data;
      } else {
        overlayLoading.toggleLoading(false);
      }
    })
    .catch(() => {
      overlayLoading.toggleLoading(false);
    });
};
onMounted(async () => {
  await getQR();
});
// const user = JSON.parse(localStorage.getItem('user') || 'null');
const onSubmit = async () => {
  overlayLoading.toggleLoading(true);
  await api
    .post(`/wallet/v1/api/wallet/deposit`, {
      amount: Number(epointStore.step1Data.epoint_number),
    })
    .then((response) => {
      if (response.data.code === 0) {
        overlayLoading.toggleLoading(false);
        epointStore.setValueStep2(response.data.data);
        toast('success', response.data.message);
      } else {
        overlayLoading.toggleLoading(false);
        epointStore.setValueStep2({
          status: 'Thất bại',
        });
        toast('error', response.data.message);
      }
    })
    .catch(() => {
      overlayLoading.toggleLoading(false);
    });
  epointStore.next();
};

defineExpose({
  onSubmit,
});
</script>
