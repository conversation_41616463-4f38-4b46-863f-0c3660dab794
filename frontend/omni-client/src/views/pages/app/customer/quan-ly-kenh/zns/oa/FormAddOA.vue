<template class="relative">
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <ZnsIcon />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item @click="handleClickCancel" to="#">ZNS OA</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/customer/zns-oa/them' }">
          {{
            props.type === FORM_TYPE.ADD
              ? 'Kết nối OA'
              : props.type === FORM_TYPE.UPDATE
                ? 'Cập nhật OA'
                : 'Chi tiết OA'
          }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
  <div class="mx-[10px] mt-[20px] flex justify-center view-height mb-[50px] overflow-auto">
    <el-form label-position="top" class="w-[70%] flex-col flex pb-[52px] gap-[15px]">
      <div class="text-[16px] flex justify-center text-black mb-3">
        <div class="w-[60%] text-center mt-5">
          Vui lòng cung cấp thông tin chính xác để việc sử dụng dịch vụ không bị gián đoạn
        </div>
      </div>
      <VElementInput
        required
        id="oa-id"
        size="default"
        name="oa_id"
        :disabled="props.type !== FORM_TYPE.ADD"
        :label="'OA ID'"
        :maxlength="25"
        :showLimit="false"
        placeholder="Nhập OA ID"
      />
      <VElementInput
        required
        id="oa-name"
        size="default"
        name="oa_name"
        :disabled="props.type !== FORM_TYPE.ADD"
        :label="'OA'"
        :maxlength="40"
        :showLimit="false"
        placeholder="Nhập OA"
      />
      <VElementDropdown
        required
        id="label-type-oa"
        name="label_type_oa"
        :filterable="false"
        :label="'Lĩnh vực'"
        :option="listLabelTypeOA"
        :style="'w-[100%]'"
        :disabled="props.type !== FORM_TYPE.ADD"
        placeholder="Chọn lĩnh vực"
      />
      <div>
        <el-form-item label="Hình thức giao dịch gói duy trì OA">
          <el-radio-group
            v-model="type_pay"
            class="flex flex-col justify-start items-start"
            :disabled="props.type === FORM_TYPE.DETAILS"
            @change="handleChangeTypePay"
          >
            <el-radio class="w-[100%] !mr-0" :label="LIST_TYPE_PAY[0].value" size="large">
              {{ LIST_TYPE_PAY[0].label }}
            </el-radio>
            <el-radio class="w-[100%]" :label="LIST_TYPE_PAY[1].value" size="large">
              {{ LIST_TYPE_PAY[1].label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <VElementDropdown
        v-show="type_pay === LIST_TYPE_PAY[0].value"
        required
        id="oa-package"
        name="oa_package"
        :filterable="false"
        :label="'Gói OA'"
        :option="OA_PACKAGE_OPTIONS"
        :style="'w-[100%]'"
        :disabled="props.type === FORM_TYPE.DETAILS"
        placeholder="Chọn gói OA"
      />
      <VElementDateTimePicker
        v-show="
          type_pay === LIST_TYPE_PAY[0].value ||
          ((status === OA_STATUS_VALUE.ACTIVE || status === OA_STATUS_VALUE.INACTIVE) &&
            type !== FORM_TYPE.UPDATE)
        "
        required
        id="expired-package-at"
        size="default"
        name="expired_package_at"
        type="date"
        format="DD/MM/YYYY"
        :label="'Thời gian hết hạn'"
        :style="'!w-[100%]'"
        :disabled="props.type === FORM_TYPE.DETAILS"
        :disabledDates="disabledDatesStart"
        placeholder="Chọn thời gian hết hạn"
        :class="props.type === FORM_TYPE.ADD ? 'pb-[20px]' : ''"
      />
      <VElementDropdown
        v-show="type_pay === LIST_TYPE_PAY[1].value"
        :class="props.type === FORM_TYPE.ADD ? 'pb-[20px]' : ''"
        required
        id="package_vnpt_id"
        name="package_vnpt_id"
        :filterable="false"
        :label="'Gói OA muốn mua'"
        :option="OA_PACKAGE_OPTIONS_CONNECT"
        :style="'w-[100%]'"
        :disabled="props.type === FORM_TYPE.DETAILS"
        placeholder="Chọn gói OA muốn mua"
      />
      <div v-if="props.type !== FORM_TYPE.ADD" class="pb-[20px]">
        <div class="font-semibold text-[#6B7280] text-[14px]">Trạng thái</div>
        <div class="flex gap-3 items-start mt-2">
          <Tag :class="getOAColorStatus(status)" :value="getOALabelStatus(status)" />
          <el-tooltip
            v-if="status === OA_STATUS_VALUE.REJECT || status === OA_STATUS_VALUE.INACTIVE"
            class="box-item"
            effect="dark"
            placement="top-start"
            :content="reason || ''"
            :raw-content="true"
            popper-class="!max-w-[400px]"
          >
            <InfoCircle class="cursor-pointer" />
          </el-tooltip>
        </div>
      </div>
    </el-form>
  </div>
  <div
    class="flex z-[999] py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke items-center"
    :class="type === FORM_TYPE.DETAILS ? 'justify-between' : 'justify-end'"
  >
    <VElementButton
      v-if="type === FORM_TYPE.DETAILS"
      id="close-btn"
      styleButton="s"
      label="Quay lại"
      :bgColor="color.closeButton"
      @click="handleClickCancel"
    />
    <div class="flex gap-3">
      <VElementButton
        v-if="type !== FORM_TYPE.DETAILS"
        id="close-btn"
        styleButton="s"
        label="Huỷ"
        :bgColor="color.closeButton"
        @click="handleClickCancel"
      />
      <VElementButton
        v-if="type == FORM_TYPE.DETAILS"
        id="delete-btn"
        styleButton="s"
        label="Xoá"
        :bgColor="color.secondary"
        @click="deleteItem"
      />
      <VElementButton
        v-if="
          type === FORM_TYPE.DETAILS &&
          (status === OA_STATUS_VALUE.INACTIVE || status === OA_STATUS_VALUE.REJECT)
        "
        id="submit-btn"
        styleButton="s"
        :bgColor="color.main"
        :label="'Cập nhật'"
        @click="redirectUpdate"
      />
      <VElementButton
        v-if="type === FORM_TYPE.UPDATE"
        id="submit-btn"
        styleButton="s"
        :bgColor="color.main"
        :label="'Lưu'"
        @click="
          () => {
            typeSubmit = 2;
            onSubmit();
          }
        "
      />
      <VElementButton
        v-if="status !== OA_STATUS_VALUE.CONNECTING && type !== FORM_TYPE.DETAILS"
        id="submit-btn"
        styleButton="s"
        :bgColor="color.main"
        :label="'Kết nối'"
        @click="
          () => {
            typeSubmit = 1;
            onSubmit();
          }
        "
      />
    </div>
  </div>
  <Dialog
    modal
    header="Kết quả xử lý file"
    ref="refDialog"
    v-model:visible="popupResult"
    :draggable="false"
    :class="'h-240px'"
    :pt="{
      content: { class: 'mb-[60px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: '!pl-[190px] !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
      },
    }"
    :style="{
      width: '550px',
      backgroundColor: '#fff',
      maxHeight: '100%',
    }"
  >
    <div class="pt-[15px]">
      <div>
        <div class="text-[16px] font-bold">
          Chi Phí: <span class="text-[#0057FF]">{{ formatMoney(total_cost) }} E-Point</span>
        </div>
        <div class="text-[16px]">Lưu ý:</div>
        <ul class="list-disc pl-5 text-[14px]">
          <li class="ml-3">
            Chi Phí sẽ được hoàn lại vào ví doanh nghiệp nếu kết nối OA
            <span class="font-bold"> thất bại</span>
          </li>
          <li class="ml-3">Chi phí đã bao gồm VAT</li>
        </ul>
        <div class="text-[16px] font-bold mt-3">Thông tin chi tiết:</div>
      </div>
      <!-- Bảng chi tiết chi phí OA -->
      <VOldTable
        :headers="[
          { name: 'Hạng mục', visible: true },
          { name: 'Số E-Point', visible: true },
        ]"
        :rows="rowData"
        :pagination="{ totalPage: 1, total: 4, perPage: 10 }"
        :showAction="false"
        :showPagination="false"
        :styleHeaders="[
          { idx: 0, class: 'w-[70%]' },
          { idx: 1, class: 'w-[30%] text-right' },
        ]"
        idTable="oa-fee-table"
      >
        <template #items="{ row }">
          <td
            :class="[
              row.isTotal ? 'font-bold' : '',
              row.isCurrent ? 'font-bold' : '',
              row.isRemain ? 'font-bold text-[#0057FF]' : '',
            ]"
          >
            {{ row.name }}
          </td>
          <td
            class="text-right"
            :class="[
              row.isTotal ? 'text-[#0057FF]' : '',
              row.isCurrent ? 'text-[#0057FF]' : '',
              row.isRemain ? 'text-[#0057FF]' : '',
            ]"
          >
            {{ row.value }}
          </td>
        </template>
      </VOldTable>
    </div>
    <div class="save-container flex justify-center items-center gap-5 mt-5">
      <VElementButton label="Xác nhận" :bgColor="color.main" @click="sendData(2)" />
    </div>
  </Dialog>
  <PopupCancelConfirm
    v-model:popupVisible="isShowConfirmPopup"
    @onClose="isShowConfirmPopup = false"
    @onConfirm="backToZnsOA"
  />

  <Dialog
    modal
    ref="refDialog2"
    header="Xác nhận"
    v-model:visible="visibleModalDelete"
    :draggable="false"
    :pt="{
      content: { class: 'mb-[60px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: '!pl-[230px] !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
      },
    }"
    :style="{
      width: '550px',
      height: 'fit-content',
      backgroundColor: '#fff',
      maxHeight: '90%',
    }"
  >
    <PopupDelete
      :id="deleteId"
      :name="deleteName"
      @onClose="visibleModalDelete = false"
      @onConfirm="handleDelete"
    />
  </Dialog>
</template>

<script setup lang="ts">
import { inject, onMounted, ref, watch } from 'vue';
import * as yup from 'yup';

import { useForm } from 'vee-validate';
import { color } from '@/constants/statusColor';
import { useApi } from '@/store/useApi';
import { useOverLayLoadingStore } from '@/store/overlayLoading';
import { useUserSession } from '@/store/userSession';
import { FORM_TYPE, isDateBefore, REGEX, ROUTE_NAME, OA_STATUS_VALUE } from '@/shared';
import { timestampGetter } from '@/utils';
import { formatMoney } from '@/utils/formatMoney';
import { getOAColorStatus, getOALabelStatus } from '@/utils/getter.utils';
import { LIST_TYPE_PAY, OA_PACKAGE_OPTIONS, OA_PACKAGE_OPTIONS_CONNECT } from './index.constants';
import ZnsIcon from '@/components/icon/ZnsIcon.vue';
import Dialog from 'primevue/dialog';
import VOldTable from '@/components/base/VOldTable.vue';
import { useRouter } from 'vue-router';
import PopupCancelConfirm from '@/components/base/common/PopupCancelConfirm.vue';
import InfoCircle from '@/components/icon/InfoCircle.vue';
import PopupDelete from '@/components/base/common/PopupDelete.vue';

const props = defineProps<{
  id?: number;
  type?: string;
}>();

const overlayLoading = useOverLayLoadingStore();
const toast = inject('toast') as any;
const api = useApi();
const user = useUserSession() as any;
const router = useRouter();
const status = ref();
const reason = ref();
const type_pay = ref(1);
const listLabelTypeOA = ref<any[]>([]);
const popupResult = ref(false);
const payment_method = ref();
const rowData = ref();
const total_cost = ref(0);
const isShowConfirmPopup = ref(false);
const visibleModalDelete = ref(false);
const deleteId = ref();
const deleteName = ref();
const typeSubmit = ref(); // 1: cập nhật, 2: kết nối

// Store original values for comparison in UPDATE mode
const originalValues = ref<any>({});

const validationSchema = yup.object({
  oa_id: yup
    .string()
    .trim()
    .required('OA ID không được để trống')
    .matches(/^[0-9]+$/, 'Chỉ nhập số'),
  oa_name: yup
    .string()
    .trim()
    .required('OA không được để trống')
    .matches(REGEX.SPECIAL_CHARACTER, 'OA không được chứa ký tự đặc biệt'),
  label_type_oa: yup.number().required('Vui lòng chọn lĩnh vực'),
  oa_package: yup
    .string()
    .nullable()
    .notRequired()
    .test('oa-package-required', 'Vui lòng chọn gói OA', function (value) {
      if (type_pay.value === LIST_TYPE_PAY[0].value) {
        return !!value;
      }
      return true;
    }),

  expired_package_at: yup
    .string()
    .nullable()
    .notRequired()
    .test('expired-package-required', 'Vui lòng chọn thời gian hết hạn', function (value) {
      if (type_pay.value === LIST_TYPE_PAY[0].value) {
        return !!value;
      }
      return true;
    })
    .test('expired-package-date-check', 'Thời gian hết hạn không được ở quá khứ', function (value) {
      if (type_pay.value === LIST_TYPE_PAY[0].value && value) {
        const selectedDate = new Date(value);
        const now = new Date();
        now.setHours(0, 0, 0, 0);
        selectedDate.setHours(0, 0, 0, 0);
        return selectedDate >= now;
      }
      return true;
    }),

  package_vnpt_id: yup
    .string()
    .nullable()
    .notRequired()
    .test('vnpt-package-required', 'Vui lòng chọn gói OA muốn mua', function (value) {
      if (type_pay.value === LIST_TYPE_PAY[1].value) {
        return !!value;
      }
      return true;
    }),
});

const { values, handleSubmit, setFieldValue } = useForm({
  validationSchema,
});

const disabledDatesStart = (date: Date) => {
  const now = new Date();
  return isDateBefore(date, now);
};

const handleChangeTypePay = () => {
  setFieldValue('expired_package_at', null);
};

const getDetail = function () {
  overlayLoading.toggleLoading(true);
  api
    .get(`/business/v1/api/client/oa/${props?.id}`)
    .then((response) => {
      if (response.data.code === 0) {
        const res = response.data.data;
        setFieldValue('oa_id', res.oa_id);
        setFieldValue('oa_name', res.oa_name);
        setFieldValue('tenant_name', res.business_name);
        setFieldValue('expired_package_at', new Date(res.expired_at));
        setFieldValue('oa_package', res.package_id);
        setFieldValue('label_type_oa', res.label_type_oa);
        setFieldValue('package_vnpt_id', res.package_vnpt_id);
        status.value = res.status;
        type_pay.value = res.type_pay;
        reason.value = res.rejection_reason;

        // Store original values for comparison
        originalValues.value = {
          oa_id: res.oa_id,
          oa_name: res.oa_name,
          expired_package_at: new Date(res.expired_at),
          oa_package: res.package_id,
          label_type_oa: res.label_type_oa,
          package_vnpt_id: res.package_vnpt_id,
          type_pay: res.type_pay,
        };

        overlayLoading.toggleLoading(false);
      } else {
        overlayLoading.toggleLoading(false);
        toast('error', response.data.message);
      }
    })
    .catch(() => {
      overlayLoading.toggleLoading(false);
    });
};

const getListLabelTypePay = async () => {
  try {
    const res = await api.get('/business/v1/api/client/oa/label_type_OA');
    if (res.data.code === 0) {
      listLabelTypeOA.value = res.data.data.map((item: any) => ({
        value: item.id,
        label: item.label_type_name,
      }));
    } else {
      toast('error', res.data.message);
    }
  } catch (error) {
    console.error(error);
  }
};

const getPaymentMethod = async () => {
  try {
    const res = await api.get(`/business/v1/api/client/business/${user.user.id_business}`);
    if (res.data.code === 0) {
      payment_method.value = res.data.data.payment_method;
    } else {
      toast('error', res.data.message);
    }
  } catch (error) {
    console.error(error);
    overlayLoading.toggleLoading(false);
  }
};
const e_point = ref();
const getEPoint = async () => {
  try {
    const res = await api.get(
      `/business/v1/api/client/oa/get_EPoint?businessId=${user.user.id_business}`,
    );
    if (res.data.code === 0) {
      e_point.value = res.data.data.current_balance;
    } else {
      toast('error', res.data.message);
    }
  } catch (error) {
    console.error(error);
  }
};
const prize_list = ref();
const getPrizeList = async () => {
  try {
    const res = await api.get(`/business/v1/api/price-list/zns`);
    if (res.data.code === 0) {
      prize_list.value = res.data.data.capital;
    } else {
      toast('error', res.data.message);
    }
  } catch (error) {
    console.error(error);
    overlayLoading.toggleLoading(false);
  }
};

const backToZnsOA = () => {
  router.push({
    name: ROUTE_NAME.ZNS_OA,
  });
};

const handleClickCancel = () => {
  let hasChanges = false;

  if (props.type === FORM_TYPE.ADD) {
    // Check if form has been modified for ADD type
    hasChanges =
      values.oa_id ||
      values.oa_name ||
      values.label_type_oa ||
      values.oa_package ||
      values.expired_package_at ||
      values.package_vnpt_id;
  } else if (props.type === FORM_TYPE.UPDATE) {
    // Check if form has been modified for UPDATE type by comparing with original values
    const compareDate = (date1: any, date2: any) => {
      if (!date1 && !date2) return true;
      if (!date1 || !date2) return false;
      return new Date(date1).getTime() === new Date(date2).getTime();
    };

    hasChanges =
      values.oa_name !== originalValues.value.oa_name ||
      values.label_type_oa !== originalValues.value.label_type_oa ||
      values.oa_package !== originalValues.value.oa_package ||
      !compareDate(values.expired_package_at, originalValues.value.expired_package_at) ||
      values.package_vnpt_id !== originalValues.value.package_vnpt_id ||
      type_pay.value !== originalValues.value.type_pay;
  }

  if (hasChanges && props.type !== FORM_TYPE.DETAILS) {
    isShowConfirmPopup.value = true;
  } else {
    backToZnsOA();
  }
};

const redirectDetail = (id?: number) => {
  router.push({
    name: ROUTE_NAME.DETAILS_ZNS_OA,
    params: {
      id: id ?? props.id,
      type: FORM_TYPE.DETAILS,
    },
  });
};
const redirectUpdate = () => {
  router.push({
    name: ROUTE_NAME.UPDATE_ZNS_OA,
    params: {
      id: props.id,
      type: FORM_TYPE.UPDATE,
    },
  });
};
interface Params {
  id?: number;
  user_id?: number;
  oa_id?: number;
  oa_name?: string;
  package_id: number;
  expired_at?: Date;
  type?: number;
  label_type_oa?: number;
  type_pay?: number;
  fee_amount_oa?: number;
  total_cost?: number;
  package_vnpt_id?: number;
}

const onSubmit = handleSubmit(() => {
  if (payment_method.value === 2) {
    sendData(1);
  } else {
    if (typeSubmit.value === 1) {
      popupResult.value = true;
    } else {
      sendData(1);
    }
  }
});

const sendData = async (type: number = 1) => {
  overlayLoading.toggleLoading(true);
  const params: Params = {
    oa_id: values.oa_id,
    oa_name: values.oa_name,
    package_id: values.oa_package ?? null,
    expired_at: timestampGetter(values.expired_package_at) ?? null,
    user_id: user.user.userId,
    label_type_oa: values.label_type_oa,
    type_pay: type_pay.value,
    fee_amount_oa: prize_list.value.systemService.initOA.price ?? 0,
    total_cost: total_cost.value,
    package_vnpt_id: values.package_vnpt_id ?? null,
  };
  if (props.type === FORM_TYPE.ADD) {
    await api
      .post('/business/v1/api/client/oa', params)
      .then((response) => {
        if (response.data.code === 0) {
          overlayLoading.toggleLoading(false);
          toast('success', response.data.message);
          popupResult.value = false;
          redirectDetail(response.data.data.id);
        } else {
          overlayLoading.toggleLoading(false);
          toast('error', response.data.message);
          popupResult.value = false;
        }
      })
      .catch((error) => {
        console.error(error);
        overlayLoading.toggleLoading(false);
        popupResult.value = false;
      });
  } else {
    params.id = props.id;
    params.type = type;
    delete params.oa_id;
    delete params.oa_name;
    await api
      .put('/business/v1/api/client/oa', params)
      .then((response) => {
        if (response.data.code === 0) {
          overlayLoading.toggleLoading(false);
          toast('success', response.data.message);
          if (props.type === FORM_TYPE.DETAILS) {
            getDetail();
          } else {
            redirectDetail(props.id);
          }
          popupResult.value = false;
        } else {
          overlayLoading.toggleLoading(false);
          toast('error', response.data.message);
          popupResult.value = false;
        }
      })
      .catch((error) => {
        console.error(error);
        overlayLoading.toggleLoading(false);
        popupResult.value = false;
      });
  }
};
const deleteItem = () => {
  deleteId.value = props.id;
  deleteName.value = values.oa_name;
  visibleModalDelete.value = true;
};
const handleDelete = async () => {
  try {
    overlayLoading.toggleLoading(true);
    await api
      .delete(`/business/v1/api/client/oa/${deleteId.value}`)
      .then((response) => {
        if (response.data.code === 0) {
          overlayLoading.toggleLoading(false);
          visibleModalDelete.value = false;
          backToZnsOA();
          toast('success', response.data.message);
        } else {
          overlayLoading.toggleLoading(false);
          toast('error', response.data.message);
        }
      })
      .catch(() => {
        overlayLoading.toggleLoading(false);
      });
  } catch {
    overlayLoading.toggleLoading(false);
  }
};
onMounted(async () => {
  getListLabelTypePay();
  await Promise.all([getEPoint(), getPrizeList(), getPaymentMethod()]);
  if (props.type === FORM_TYPE.ADD) {
  } else {
    getDetail();
  }
  total_cost.value = prize_list.value.systemService?.initOA.price ?? 0;
  rowData.value = [
    {
      name: 'Phí khai báo OA',
      value: formatMoney(prize_list.value.systemService.initOA.price ?? 0),
    },
    {
      name: 'Tổng chi phí',
      value: formatMoney(prize_list.value.systemService.initOA.price ?? 0),
      isTotal: true,
    },
    { name: 'Số dư ví hiện tại', value: formatMoney(e_point.value), isCurrent: true },
    {
      name: 'Số dư còn lại',
      value: formatMoney(e_point.value - (prize_list.value.systemService.initOA.price ?? 0)),
      isRemain: true,
    },
  ];
});

watch(type_pay, () => {
  if (type_pay.value === 1) {
    if (prize_list.value.systemService?.initOA) {
      total_cost.value = prize_list.value.systemService?.initOA.price ?? 0;
      rowData.value = [
        {
          name: 'Phí khai báo OA',
          value: formatMoney(prize_list.value.systemService?.initOA.price ?? 0),
        },
        {
          name: 'Tổng chi phí',
          value: formatMoney(prize_list.value.systemService?.initOA.price ?? 0),
          isTotal: true,
        },
        { name: 'Số dư ví hiện tại', value: formatMoney(e_point.value), isCurrent: true },
        {
          name: 'Số dư còn lại',
          value: formatMoney(e_point.value - (prize_list.value.systemService.initOA.price ?? 0)),
          isRemain: true,
        },
      ];
    }
  } else {
    if (values.package_vnpt_id) {
      const price = getOAPackagePrice(values.package_vnpt_id) ?? 0;
      const totalPrice = prize_list.value.systemService.initOA.price + price;
      total_cost.value = totalPrice;
      rowData.value = [
        {
          name: 'Phí khai báo OA',
          value: formatMoney(prize_list.value.systemService.initOA.price ?? 0),
        },
        {
          name: 'Phí mua gói duy trì OA',
          value: formatMoney(price),
        },
        {
          name: 'Tổng chi phí',
          value: formatMoney(totalPrice),
          isTotal: true,
        },
        { name: 'Số dư ví hiện tại', value: formatMoney(e_point.value), isCurrent: true },
        {
          name: 'Số dư còn lại',
          value: formatMoney(e_point.value - prize_list.value.systemService.initOA.price - price),
          isRemain: true,
        },
      ];
    }
  }
});

const getOAPackagePrice = (id: string) => {
  switch (id) {
    case 'maintainAdvanceOA6month':
      return prize_list.value.systemService.maintainAdvanceOA.price;
    case 'maintainPremiumOA6month':
      return prize_list.value.systemService.maintainPremiumOA.price;
    case 'maintainAdvanceOA1year':
      return prize_list.value.systemService.maintainAdvanceOA.price * 2 * 0.9;
    case 'maintainPremiumOA1year':
      return prize_list.value.systemService.maintainPremiumOA.price * 2 * 0.9;
  }
};

watch(
  () => values.package_vnpt_id,
  (newValue) => {
    if (newValue) {
      const price = getOAPackagePrice(values.package_vnpt_id) ?? 0;
      const totalPrice = prize_list.value.systemService.initOA.price + price;
      total_cost.value = totalPrice;
      if (type_pay.value === 2) {
        rowData.value = [
          {
            name: 'Phí khai báo OA',
            value: formatMoney(prize_list.value.systemService.initOA.price ?? 0),
          },
          {
            name: 'Phí mua gói duy trì OA',
            value: formatMoney(price),
          },
          {
            name: 'Tổng chi phí',
            value: formatMoney(totalPrice),
            isTotal: true,
          },
          { name: 'Số dư ví hiện tại', value: formatMoney(e_point.value), isCurrent: true },
          {
            name: 'Số dư còn lại',
            value: formatMoney(e_point.value - prize_list.value.systemService.initOA.price - price),
            isRemain: true,
          },
        ];
      }
    }
  },
);

watch(
  () => props.type,
  (newValue) => {
    if (newValue === FORM_TYPE.DETAILS) {
      getDetail();
    }
  },
);
</script>

<style scoped>
:deep(.so-hop-dong .el-input__wrapper) {
  height: 30px !important;
}

:deep(.p-tag) {
  width: 120px;
}

.el-tag {
  aspect-ratio: 1;
}

:deep(.el-select-v2 .el-select-v2__selection .el-tag) {
  background-color: #1a34b5 !important;
  color: #fff !important;
}

:deep(.el-tag .el-icon) {
  background-color: red !important;
}

:deep(.el-tag .el-tag__close) {
  color: #fff !important;
}
.view-height {
  height: calc(100vh - 245px);
}
:deep(.v-old-table) {
  width: 500px;
  min-width: 500px;
}
</style>
