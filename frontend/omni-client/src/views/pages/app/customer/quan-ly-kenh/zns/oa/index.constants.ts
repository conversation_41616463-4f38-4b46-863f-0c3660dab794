import { ALIGN } from '@/shared';

export const headers = [
  {
    name: 'OA ID',
    visible: true,
    pin: false,
  },
  {
    name: '<PERSON>ên OA',
    visible: true,
    pin: false,
  },
  {
    name: '<PERSON><PERSON><PERSON> vực',
    visible: true,
    pin: false,
  },
  {
    key: '<PERSON><PERSON><PERSON>',
    name: '<PERSON><PERSON><PERSON>',
    visible: true,
    pin: false,
  },
  {
    name: 'Thời gian hết hạn',
    visible: true,
    pin: false,
    align: ALIGN.CENTER,
  },
  {
    name: 'Trạng thái',
    visible: true,
    pin: false,
    align: ALIGN.CENTER,
  },
];

export const styleHeaders = [
  {
    idx: 0,
    class: 'w-[12%]',
  },
  {
    idx: 1,
    class: 'w-[20%]',
  },
  {
    idx: 2,
    class: 'w-[15%]',
  },
  {
    idx: 3,
    class: 'w-[15%]',
  },
  {
    idx: 4,
    class: 'w-[20%]',
  },
  {
    idx: 5,
    class: 'w-[20%]',
  },
];

export const OA_PACKAGE_OPTIONS = [
  { value: 'maintainAdvanceOA', label: 'Nâng cao' },
  { value: 'maintainPremiumOA', label: 'Premium' },
];
export const OA_PACKAGE_OPTIONS_CONNECT = [
  { value: 'maintainAdvanceOA6month', label: 'Nâng cao 6 tháng' },
  { value: 'maintainAdvanceOA1year', label: 'Nâng cao 1 năm (tiết kiệm 10%)' },
  { value: 'maintainPremiumOA6month', label: 'Premium 6 tháng' },
  { value: 'maintainPremiumOA1year', label: 'Premium 1 năm (tiết kiệm 10%)' },
];

export const OA_STATUS_OPTIONS = [
  { value: 1, label: 'Hoạt động' },
  { value: 2, label: 'Tạm dừng' },
  { value: 3, label: 'Đang kết nối' },
  { value: 4, label: 'Từ chối' },
];

export const getOAPackage = (id: string): string => {
  switch (id) {
    case 'maintainAdvanceOA':
      return 'Nâng cao';
    case 'maintainPremiumOA':
      return 'Premium';
    default:
      return '';
  }
};

export const LIST_TYPE_PAY = [
  {
    value: 1,
    label: 'Mua gói tại Zalo',
  },
  {
    value: 2,
    label: 'Mua gói tại VNPT Omnichannel',
  },
];
