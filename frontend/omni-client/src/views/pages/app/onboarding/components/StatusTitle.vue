<template>
  <div v-if="status" class="flex flex-col justify-center items-center mx-auto pt-8">
    <h2 class="text-lg font-bold text-center">{{ ONBOARDING_STATUS_TITLE[status]?.title }}</h2>
    <p
      class="text-md text-center max-w-md"
      v-html="ONBOARDING_STATUS_TITLE[status]?.description"
    ></p>
  </div>
</template>

<script setup lang="ts">
import { OnBoardingStatus } from '@/enums/onBoarding';
import { ONBOARDING_STATUS_TITLE } from '../index.constants';

const props = withDefaults(
  defineProps<{
    status: OnBoardingStatus | null;
  }>(),
  {
    status: null,
  },
);
</script>
