<template>
  <Dialog
    modal
    v-model:visible="isVisible"
    :draggable="false"
    :closable="true"
    :pt="{
      content: { class: 'mt-[10px] mb-[50px]' },
      root: { class: 'bg-[#fff]' },
      header: {
        class: 'text-center !border-b-[1px] !border-solid !border-stroke !bg-[#fbfaff] !h-[54px]',
        style: {
          justifyContent: 'center',
        },
      },
    }"
    header="Thông báo"
    :style="{
      width: '480px',
      height: 'auto',
      backgroundColor: '#fff',
      maxHeight: '90vh',
    }"
    @hide="handleClose"
  >
    <div class="pt-[15px]">
      <div class="flex flex-col mt-[20px] items-center" id="changePassword-old-password-group">
        <div class="mb-6">
          <h4 class="text-lg font-medium text-gray-800 mb-3 text-center">
            Y<PERSON><PERSON> cầu tư vấn của bạn đã được gửi thành công!
          </h4>
          <p class="text-gray-600 leading-relaxed text-sm text-center">
            Chúng tôi sẽ liên hệ với bạn <strong>trong vòng 24 giờ làm việc</strong><br />
            để hỗ trợ thông tin chi tiết.
          </p>
        </div>
      </div>
    </div>
    <div class="save-container flex justify-center items-center gap-5 mt-5"></div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import Dialog from 'primevue/dialog';

interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const isVisible = ref(props.modelValue);

watch(
  () => props.modelValue,
  (newValue) => {
    isVisible.value = newValue;
  },
);

watch(isVisible, (newValue) => {
  emit('update:modelValue', newValue);
});

const handleClose = () => {
  isVisible.value = false;
  emit('close');
};
</script>

<style scoped></style>
