import { OnBoardingStatus } from '@/enums/onBoarding';

export const ONBOARDING_STATUS_TITLE = {
  [OnBoardingStatus.Pending]: {
    title: '<PERSON><PERSON><PERSON> cầu tư vấn của bạn đã được gửi thành công!',
    description:
      '<PERSON>úng tôi sẽ liên hệ với bạn <b> trong vòng  24 giờ làm việc </b> để hỗ trợ thông tin chi tiết.',
  },
  [OnBoardingStatus.Rejected]: {
    title: 'R<PERSON><PERSON> tiế<PERSON>, hệ thống chưa thể xác thực được thông tin doanh nghiệp của Quý khách!',
    description:
      '<PERSON><PERSON><PERSON> nghiệp hiện <b> chưa đủ điều kiện </b> để sử dụng dịch vụ. <br> Vui lòng kiểm tra lại thông tin đăng ký hoặc liên hệ bộ phận hỗ trợ để được hướng dẫn cụ thể.',
  },
};
