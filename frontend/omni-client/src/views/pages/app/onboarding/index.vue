<template>
  <div
    class="flex items-center justify-between border-[#F2F2F7] border-[1px] h-[52px] bg-[#FBFAFF] p-[20px] rounded-t-[15px]"
  >
    <div class="flex gap-[15px] items-center">
      <Icon icon="tabler:settings" class="text-[20px] text-primaryText" />
      <el-breadcrumb separator="/">
        <el-breadcrumb-item id="enterprise-breadcrumb"
          ><b>Đăng ký sử dụng dịch vụ VNPT Omnichannel</b></el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
  </div>

  <div class="h-[calc(100vh-80px-42px-53px)] overflow-hidden">
    <div class="h-[calc(100vh-80px-42px-53px-60px)] overflow-auto">
      <StatusTitle :status="enterpriseStatus" />
      <div class="flex flex-col items-center gap-3 w-[100%]">
        <div class="w-[80%] p-5">
          <div
            class="border-1 border-solid px-[15px] py-[6px] border-[#1A34B5] rounded-[30px] h-[34px] w-fit text-[#1A34B5] text-[14px] font-semibold flex items-center"
          >
            Thông tin doanh nghiệp
          </div>
          <el-form label-position="top" class="w-[100%] flex justify-center gap-[75px] mt-5">
            <div class="flex flex-col gap-[15px] w-[50%]">
              <VElementInput
                name="business_name"
                size="default"
                label="Tên doanh nghiệp"
                id="onboarding-business_name-input"
                :placeholder="
                  getPlaceholder(
                    PLACEHOLDER.BUSINESS.NAME,
                    isDisabled ? PageType.Details : PageType.Add,
                  )
                "
                :disabled="isDisabled"
                required
                :maxlength="200"
                :showLimit="false"
              />
              <VElementInput
                name="address"
                size="default"
                label="Địa chỉ"
                id="onboarding-address-input"
                :placeholder="
                  getPlaceholder(
                    PLACEHOLDER.BUSINESS.ADDRESS,
                    isDisabled ? PageType.Details : PageType.Add,
                  )
                "
                :disabled="isDisabled"
                :maxlength="200"
                :showLimit="false"
              />
              <VElementInput
                name="tax_code"
                size="default"
                label="Mã số thuế"
                id="onboarding-tax_code-input"
                :placeholder="
                  getPlaceholder(
                    PLACEHOLDER.BUSINESS.TAX_CODE,
                    isDisabled ? PageType.Details : PageType.Add,
                  )
                "
                :disabled="isDisabled"
                :maxlength="50"
                :showLimit="false"
              />

              <VElementInput
                size="default"
                name="business_email"
                label="Email doanh nghiệp"
                id="onboarding-business_email-input"
                :placeholder="
                  getPlaceholder(
                    PLACEHOLDER.BUSINESS.EMAIL,
                    isDisabled ? PageType.Details : PageType.Add,
                  )
                "
                :disabled="isDisabled"
                :maxlength="50"
                :showLimit="false"
              />
            </div>
            <div class="flex flex-col gap-[15px] w-[50%]">
              <VElementInput
                name="business_phone"
                size="default"
                label="SĐT doanh nghiệp"
                id="onboarding-business_phone-input"
                :placeholder="
                  getPlaceholder(
                    PLACEHOLDER.BUSINESS.PHONE,
                    isDisabled ? PageType.Details : PageType.Add,
                  )
                "
                :disabled="isDisabled"
                :maxlength="50"
                :showLimit="false"
              />
              <VElementDropdown
                name="business_label_type"
                label="Lĩnh vực hoạt động"
                required
                multiple
                clearable
                :formType="type"
                :filterable="false"
                id="onboarding-business_label_type-dropdown"
                :placeholder="
                  getPlaceholder(
                    PLACEHOLDER.BUSINESS.BUSINESS_LABEL_TYPE,
                    isDisabled ? PageType.Details : PageType.Add,
                  )
                "
                :disabled="isDisabled"
                :option="labelTypeOptions"
              />
              <VElementDropdown
                name="channel_types"
                label="Dịch vụ sử dụng"
                id="onboarding-channel_type-dropdown"
                multiple
                clearable
                required
                :placeholder="
                  getPlaceholder(
                    PLACEHOLDER.BUSINESS.SERVICE,
                    isDisabled ? PageType.Details : PageType.Add,
                  )
                "
                :disabled="isDisabled"
                :formType="type"
                :filterable="false"
                :option="SERVICE_OPTIONS"
              />
            </div>
          </el-form>
        </div>
        <div
          class="w-[80%] p-5 w-[80%] mt-2 bg-[#fbfaff] p-5 border-t-2 border-b-2 border-solid border-[#e4e7ec]"
        >
          <div
            class="border-1 border-solid px-[15px] py-[6px] border-[#1A34B5] rounded-[30px] h-[34px] w-fit text-[#1A34B5] text-[14px] font-semibold flex items-center"
          >
            Thông tin người liên hệ
          </div>
          <el-form label-position="top" class="w-[100%] flex justify-center gap-[75px] mt-5">
            <div class="flex flex-col gap-[15px] w-[50%]">
              <VElementInput
                name="contact_name"
                size="default"
                label="Người liên hệ"
                id="onboarding-contact_name-input"
                :placeholder="
                  getPlaceholder(
                    PLACEHOLDER.BUSINESS.CONTACT_NAME,
                    isDisabled ? PageType.Details : PageType.Add,
                  )
                "
                :disabled="isDisabled"
                required
                :maxlength="50"
                :showLimit="false"
              />
              <VElementInput
                name="contact_email"
                size="default"
                label="Email người liên hệ"
                id="onboarding-contact_email-input"
                :placeholder="
                  getPlaceholder(
                    PLACEHOLDER.BUSINESS.CONTACT_EMAIL,
                    isDisabled ? PageType.Details : PageType.Add,
                  )
                "
                :disabled="isDisabled"
                required
                :maxlength="50"
                :showLimit="false"
              />
            </div>
            <div class="flex flex-col gap-[15px] w-[50%]">
              <VElementInput
                name="contact_phone"
                size="default"
                label="SĐT người liên hệ"
                id="onboarding-contact_phone-input"
                :placeholder="
                  getPlaceholder(
                    PLACEHOLDER.BUSINESS.CONTACT_PHONE,
                    isDisabled ? PageType.Details : PageType.Add,
                  )
                "
                :disabled="isDisabled"
                required
                :maxlength="50"
                :showLimit="false"
              />
            </div>
          </el-form>
        </div>
        <div class="w-[80%] p-5">
          <div
            class="border-1 border-solid px-[15px] py-[6px] border-[#1A34B5] rounded-[30px] h-[34px] w-fit text-[#1A34B5] text-[14px] font-semibold flex items-center"
          >
            Thông tin khác
          </div>
          <el-form label-position="top" class="w-[100%] flex gap-[75px] mt-5">
            <div class="flex flex-col gap-[15px] w-[50%]">
              <VElementInput
                name="referral_code"
                size="default"
                label="Mã giới thiệu"
                id="onboarding-referral_code-input"
                :placeholder="
                  getPlaceholder(
                    PLACEHOLDER.ONBOARDING.REFERRAL_CODE,
                    isDisabled ? PageType.Details : PageType.Add,
                  )
                "
                :disabled="isDisabled"
                :maxlength="50"
                :showLimit="false"
              />
            </div>
            <div class="flex flex-col gap-[15px] w-[50%]"></div>
          </el-form>
        </div>
      </div>
    </div>
  </div>

  <div
    class="flex z-[999] py-[9px] px-[15px] w-[100%] h-[53px] absolute bottom-0 bg-fourth rounded-b-[16px] border-t-[1px] border-stroke items-center justify-center"
  >
    <VElementButton
      v-if="
        enterpriseStatus !== OnBoardingStatus.Pending &&
        enterpriseStatus !== OnBoardingStatus.Rejected
      "
      label="Yêu cầu tư vấn"
      :bgColor="color.main"
      @click="onSubmit"
    />
  </div>

  <SendSuccessPopup v-model="showSuccessPopup" @close="handleClosePopup" />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useForm } from 'vee-validate';
import * as yup from 'yup';
import { useRouter } from 'vue-router';
import { PLACEHOLDER, REGEX, ROUTE_NAME, type TDropdownItem } from '@/shared';
import { getNullableString, getPlaceholder } from '@/utils';
import { toast } from '@/utils/useToast';
import { handleApiError } from '@/utils/useErrorHandler';
import { PageType } from '@/enums/common';
import { OnBoardingStatus } from '@/enums/onBoarding';
import { ERROR_MESSAGE } from '@/constants/templateText';
import { TEXT } from '@/shared/text.shared';
import { color } from '@/constants/statusColor';
import { SERVICE_OPTIONS } from '@/constants/common';
import { useApi } from '@/store/useApi';
import { useUserSession } from '@/store/userSession';
import StatusTitle from './components/StatusTitle.vue';
import SendSuccessPopup from './components/popup/SendSuccessPopup.vue';
import { useOverLayLoadingStore } from '@/store/overlayLoading';

interface Props {
  type: PageType;
  listAgent: TDropdownItem[];
}

const props = defineProps<Props>();

const api = useApi();
const router = useRouter();
const userSession = useUserSession();
const overlayLoading = useOverLayLoadingStore();

const validationSchema = yup.object({
  business_name: yup.string().trim().required(ERROR_MESSAGE.REQUIRED_FIELD('Tên doanh nghiệp')),
  business_email: yup
    .string()
    .nullable()
    .trim()
    .test('valid-email', TEXT.INVALID_EMAIL, (value) => {
      if (!value || value.trim() === '') {
        return true;
      }
      const emailRegex = REGEX.EMAIL;
      return emailRegex.test(value);
    }),
  business_label_type: yup
    .array()
    .min(1, ERROR_MESSAGE.REQUIRED_SELECT('Lĩnh vực hoạt động'))
    .required(ERROR_MESSAGE.REQUIRED_SELECT('Lĩnh vực hoạt động')),
  channel_types: yup
    .array()
    .min(1, ERROR_MESSAGE.REQUIRED_SELECT('Dịch vụ sử dụng'))
    .required(ERROR_MESSAGE.REQUIRED_SELECT('Dịch vụ sử dụng')),
  contact_name: yup.string().trim().required(ERROR_MESSAGE.REQUIRED_FIELD('Người liên hệ')),
  contact_phone: yup.string().trim().required(ERROR_MESSAGE.REQUIRED_FIELD('SĐT người liên hệ')),
  contact_email: yup
    .string()
    .trim()
    .email(TEXT.INVALID_EMAIL)
    .required(ERROR_MESSAGE.REQUIRED_FIELD('Email'))
    .test('valid-email', TEXT.INVALID_EMAIL, (value) => {
      if (value === undefined || value === null || value === '') {
        return true;
      }
      const emailRegex = REGEX.EMAIL;
      return emailRegex.test(value);
    })
    .when((email, schema) => {
      if (!email[0]) {
        return schema;
      }
      return schema.matches(REGEX.EMAIL, TEXT.INVALID_EMAIL);
    }),
});

const { handleSubmit, values, setFieldValue, setFieldError } = useForm({
  validationSchema,
});

const enterpriseStatus = ref<OnBoardingStatus>();
const enterprise = ref<any>({});
const isDisabled = ref<boolean>(false);
const labelTypeOptions = ref<TDropdownItem[]>([]);
const showSuccessPopup = ref(false);

const getLabels = async () => {
  try {
    const res = await api.get('/business/v1/api/client/onboarding/label-types');
    if (res.data.code === 0) {
      labelTypeOptions.value = res.data.data;
    }
  } catch (error) {
    console.error('getLabels error:', error);
    handleApiError(error);
  }
};

const getDetail = async () => {
  overlayLoading.toggleLoading(true);

  try {
    const res = await api.get('/business/v1/api/client/onboarding/detail');
    if (res.data.code === 0) {
      await getLabels();

      const dataRes = res.data.data;
      if (dataRes.has_onboarding) {
        userSession.setUser({
          ...userSession.user,
          id_business: dataRes.business_id,
        });
        userSession.clearHasOnboarding();

        return router.push({
          name: ROUTE_NAME.SMS_BRANDNAME,
        });
      }

      enterprise.value = dataRes;

      if (!dataRes.status) {
        setFieldValue('contact_name', enterprise.value?.contact_name);
        setFieldValue('contact_email', enterprise.value?.contact_email);
        setFieldValue('contact_phone', enterprise.value?.contact_phone);
        return;
      }

      enterpriseStatus.value = dataRes.status;
      if (
        enterpriseStatus.value == OnBoardingStatus.Pending ||
        enterpriseStatus.value == OnBoardingStatus.Rejected
      ) {
        isDisabled.value = true;
      }
      setFieldValue('business_name', enterprise.value?.business_name);
      setFieldValue('address', enterprise.value?.address);
      setFieldValue('tax_code', enterprise.value?.tax_code);
      setFieldValue('business_email', enterprise.value?.business_email);
      setFieldValue('business_phone', enterprise.value?.business_phone);
      setFieldValue('business_label_type', enterprise.value?.business_label_type);
      setFieldValue('channel_types', enterprise.value?.channel_types);
      setFieldValue('contact_name', enterprise.value?.contact_name);
      setFieldValue('contact_email', enterprise.value?.contact_email);
      setFieldValue('contact_phone', enterprise.value?.contact_phone);
      setFieldValue('referral_code', enterprise.value?.referral_code);
    } else {
      toast('error', res.data.message);
    }
  } catch (error: any) {
    console.error('getEnterprise error:', error);
    handleApiError(error);
  } finally {
    overlayLoading.toggleLoading(false);
  }
};

const validateReferralCode = async (referralCode: string) => {
  try {
    const response = await api.get('/business/v1/api/client/onboarding/validate-referral-code', {
      params: { referralCode },
    });

    if (response.data.code === 0) {
      return {
        isValid: true,
        agentId: response.data.data.agent_id,
        agentName: response.data.data.agent_name,
      };
    } else {
      return {
        isValid: false,
        message: response.data.message,
      };
    }
  } catch (error: any) {
    console.error('validateReferralCode error:', error);
    handleApiError(error);
    return {
      isValid: false,
      message: 'Có lỗi xảy ra khi kiểm tra mã giới thiệu',
    };
  }
};

const handleClosePopup = async () => {
  showSuccessPopup.value = false;
  await getDetail();
};

const onSubmit = handleSubmit(async () => {
  overlayLoading.toggleLoading(true);

  try {
    let agentId = null;

    if (values.referral_code && values.referral_code.trim()) {
      const validation = await validateReferralCode(values.referral_code.trim());

      if (!validation.isValid) {
        setFieldError('referral_code', validation.message);
        return;
      }

      agentId = validation.agentId;
      console.log('Referral code validated:', validation);
    }

    const payload = {
      business_name: getNullableString(values.business_name),
      address: getNullableString(values.address),
      tax_code: getNullableString(values.tax_code),
      business_phone: getNullableString(values.business_phone),
      business_email: getNullableString(values.business_email),
      contact_name: getNullableString(values.contact_name),
      contact_phone: getNullableString(values.contact_phone),
      contact_email: getNullableString(values.contact_email),
      referral_code: getNullableString(values.referral_code),
      label_types: values.business_label_type || [],
      channel_types: values.channel_types || [],
      agent_id: agentId,
    };
    const res = await api.post('/business/v1/api/client/onboarding/submit', payload);

    if (res.data.code === 0) {
      showSuccessPopup.value = true;
      enterpriseStatus.value = OnBoardingStatus.Pending;
      isDisabled.value = true;
    } else {
      toast('error', res.data.message || 'Đăng ký doanh nghiệp thất bại!');
    }
  } catch (error: any) {
    console.error('onSubmit error:', error);
    handleApiError(error);
  } finally {
    overlayLoading.toggleLoading(false);
  }
});

onMounted(async () => {
  await getDetail();
});
</script>
